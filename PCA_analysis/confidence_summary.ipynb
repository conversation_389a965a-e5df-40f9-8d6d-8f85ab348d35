{"cells": [{"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["from matplotlib import pyplot as plt\n", "from utils import error_analysis\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from plotly import graph_objects as go\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.decomposition import PCA\n", "from matplotlib import pyplot as plt\n", "from sklearn.metrics.pairwise import euclidean_distances\n", "from scipy.stats import pearsonr\n", "import seaborn as sns\n", "import matplotlib\n", "matplotlib.rcParams['mathtext.fontset'] = 'stix'\n", "matplotlib.rcParams['font.family'] = 'STIXGeneral'\n", "\n", "plt.rcParams['xtick.labelsize']=8\n", "plt.rcParams['ytick.labelsize']=8\n", "\n", "class error_analysis:\n", "    \n", "    def __init__(self, name, key,n_feat=10000,multi=None):\n", "        self.x_train = []\n", "        self.x_test = []\n", "        self.errors = []\n", "\n", "        for f in range(5):\n", "            self.df_train = pd.read_csv('../{}/folds/train_f{}.csv'.format(name,f+1),index_col=0)\n", "            self.df_test = pd.read_csv('../{}/folds/test_f{}.csv'.format(name,f+1),index_col=0)\n", "            x_train_f =  self.df_train.values\n", "            if multi is not None:\n", "                x_test_f =  self.df_test.drop(multi,axis=1).values\n", "            else:\n", "                x_test_f =  self.df_test.drop(key,axis=1).values\n", "            errors_f =  self.df_test[key].values\n", "\n", "            self.x_train.append(x_train_f[:,:n_feat])\n", "            self.x_test.append(x_test_f[:,:n_feat])\n", "            self.errors.append(errors_f)\n", "\n", "        self.x = np.concatenate(self.x_test)\n", "\n", "        self.x_train_sc = []\n", "        self.x_test_sc = []\n", "        self.scaler = StandardScaler()\n", "        self.x_sc = self.scaler.fit_transform(self.x)\n", "        for f in range(5):\n", "            self.x_train_sc.append(self.scaler.fit_transform(self.x_train[f]))\n", "            self.x_test_sc.append(self.scaler.fit_transform(self.x_test[f]))\n", "\n", "        self.x_train_scmm = []\n", "        self.x_test_scmm = []\n", "        self.scaler = MinMaxScaler()\n", "        self.x_scmm = self.scaler.fit_transform(self.x)\n", "        for f in range(5):\n", "            self.x_train_scmm.append(self.scaler.fit_transform(self.x_train[f]))\n", "            self.x_test_scmm.append(self.scaler.fit_transform(self.x_test[f]))\n", "        \n", "        \n", "    def run_pca(self, n_pc=10):\n", "        self.pca = PCA(n_components=n_pc)\n", "        self.pc = self.pca.fit_transform(self.x_sc)\n", "        \n", "        self.x_train_pc = []\n", "        self.x_test_pc = []\n", "        for f in range(5):\n", "            self.x_train_pc.append(self.pca.transform(self.x_train_sc[f]))\n", "            self.x_test_pc.append(self.pca.transform(self.x_test_sc[f]))\n", "        \n", "        for comp_n in range(3):\n", "            print('-'*50)\n", "            print('Component {}'.format(comp_n+1))\n", "            print('-'*50)\n", "            max_ids = np.abs(self.pca.components_[comp_n]).argsort()[::-1][:10]\n", "            for f,a in zip(self.df_test.columns[max_ids],self.pca.components_[comp_n,max_ids]):\n", "                print('{:.1f}% of {}'.format(a*100,f))\n", "            print('\\n')\n", "            \n", "        print('Explained variance')\n", "        for i,v in enumerate(self.pca.explained_variance_ratio_):\n", "            print('PC {}: {:.1f}%'.format(i+1,v*100))\n", "        \n", "    def plot_pca(self,pc1=1,pc2=2):\n", "        fig, axs = plt.subplots(2,3,figsize=(25,15))\n", "\n", "        for fold in range(5):\n", "            ax = (axs.flat)[fold]\n", "            ax.scatter(self.x_train_pc[fold][:,pc1-1],self.x_train_pc[fold][:,pc2-1],c='k',alpha=0.5)\n", "            sc = ax.scatter(self.x_test_pc[fold][:,pc1-1],self.x_test_pc[fold][:,pc2-1],marker='X',c=np.log(1+np.abs(self.errors[fold])))\n", "            fig.colorbar(sc, ax=ax)\n", "            ax.set_xlabel('PC {}'.format(pc1))\n", "            ax.set_ylabel('PC {}'.format(pc2))\n", "            ax.set_title('fold {}'.format(fold+1))\n", "\n", "            #MAE\n", "            mae = np.mean(np.absolute(self.errors[fold]))\n", "            ax.text(0.6,0.8,'MAE: {:.2f}'.format(mae),transform=ax.transAxes,fontsize=14)\n", "        return fig, axs\n", "        \n", "        \n", "    def plot_pca_3D(self,fold=0,classification=False):\n", "        xd = np.concatenate([self.x_train_pc[fold],self.x_test_pc[fold]])\n", "        if classification:\n", "            colors = np.concatenate([np.ones(len(self.x_train_pc[fold]))*-1,np.log(1+np.abs(self.errors[fold]))])\n", "        else:\n", "            colors = np.concatenate([np.ones(len(self.x_train_pc[fold]))*-0.1,np.log(1+np.abs(self.errors[fold]))])\n", "        symbols = ['circle']*len(self.x_train_pc[fold]) + ['x']*len(self.x_test_pc[fold])\n", "        \n", "        fig=go.Figure(data=go.Scatter3d(x=xd[:,0],y=xd[:,1],z=xd[:,2],\n", "                                        mode='markers',marker=dict(size=4,color=colors,symbol=symbols,showscale=True)))\n", "        fig.show()\n", "        return fig\n", "    \n", "    def plot_pca_distance(self,n_neighbours = 5, n_pc = 10, xmax=None, ymax=None):\n", "\n", "        fig, axs = plt.subplots(2,3,figsize=(25,15))\n", "        all_closest = []\n", "        for fold in range(6):\n", "            if fold == 5:\n", "                closest = np.concatenate(all_closest)\n", "                errs = np.abs(np.concatenate(self.errors))\n", "            else:\n", "                train = self.x_test_pc[fold]\n", "                test = self.x_test_pc[fold]\n", "                dist = euclidean_distances(test[:,:n_pc],train[:,:n_pc])\n", "                closest = np.sort(dist,axis=1)[:,:n_neighbours].mean(axis=1)\n", "                all_closest.append(closest)\n", "                errs = np.abs(self.errors[fold])\n", "\n", "\n", "            ax = axs.flat[fold]\n", "            ax.plot(closest,errs,'o',alpha=0.5)\n", "\n", "            # 1st order fit\n", "            pfit = np.polyval(np.polyfit(closest,errs,1),closest)\n", "            ax.plot(closest,pfit,'--')\n", "\n", "            # Pearson correlation\n", "            corr = pearsonr(closest,errs)[0]\n", "            ax.text(0.6,0.9,'corr: {:.2f}'.format(corr),transform=ax.transAxes,fontsize=14)\n", "\n", "            #MAE\n", "            mae = np.mean(errs)\n", "            ax.text(0.6,0.8,'MAE: {:.2f}'.format(mae),transform=ax.transAxes,fontsize=14)\n", "\n", "            ax.set_xlabel('Euclidean PCA distance')\n", "            ax.set_ylabel('Absolute error')\n", "            if xmax is not None:\n", "                ax.set_xlim(0,xmax)\n", "            if ymax is not None:\n", "                ax.set_ylim(0,ymax)\n", "        return fig, axs\n", "    \n", "    \n", "    def plot_feat_distance(self, n_neighbours = 5, n_feat = 50, scaling= 'mm', abs_v=True, xmax=None, ymax=None):\n", "        \n", "        #####\n", "        fig, axs = plt.subplots(2,3,figsize=(25,15))\n", "\n", "        if scaling == 'mm':\n", "            xd_train = self.x_train_scmm\n", "            xd_test = self.x_test_scmm\n", "        else:\n", "            xd_train = self.x_train_sc\n", "            xd_test = self.x_test_sc\n", "\n", "        all_closest = []\n", "        for fold in range(6):\n", "            if fold == 5:\n", "                closest = np.concatenate(all_closest)\n", "                if abs_v:\n", "                    errs = np.abs(np.concatenate(self.errors))\n", "                else:\n", "                    errs = np.concatenate(self.errors)\n", "            else:\n", "                train = xd_test[fold]\n", "                test = xd_test[fold]\n", "                dist = euclidean_distances(test[:,:n_feat],train[:,:n_feat])\n", "                closest = np.sort(dist,axis=1)[:,:n_neighbours].mean(axis=1)\n", "                all_closest.append(closest)\n", "                if abs_v:\n", "                    errs = np.abs(self.errors[fold])\n", "                else:\n", "                    errs =self.errors[fold]\n", "\n", "\n", "            ax = axs.flat[fold]\n", "            ax.plot(closest,errs,'o',alpha=0.5)\n", "\n", "            # 1st order fit\n", "            pfit = np.polyval(np.polyfit(closest,errs,1),closest)\n", "            ax.plot(closest,pfit,'-')\n", "\n", "            # Pearson correlation\n", "            corr = pearsonr(closest,errs)[0]\n", "            ax.text(0.6,0.9,'corr: {:.2f}'.format(corr),transform=ax.transAxes,fontsize=14)\n", "\n", "            #MAE\n", "            mae = np.mean(errs)\n", "            ax.text(0.6,0.8,'MAE: {:.2f}'.format(mae),transform=ax.transAxes,fontsize=14)\n", "\n", "            ax.set_xlabel('Euclidean feature distance')\n", "            ax.set_ylabel('Absolute error')\n", "            if xmax is not None:\n", "                ax.set_xlim(0,xmax)\n", "            if ymax is not None:\n", "                ax.set_ylim(0,ymax)\n", "        return fig, axs\n", "    \n", "    def upper_bound(self,distances,errors,threshold=0.9):\n", "        coeffs = errors/distances\n", "        return np.sort(coeffs)[int(len(distances)*threshold)]\n", "    \n", "    def plot_feat_distance_all(self, ax=None, name='', upper_bound=1, n_neighbours = 5,\n", "                               n_feat = 50, scaling= 'mm', abs_v=True, xmax=None, ymax=None, jointplot=False,\n", "                              xlog=False,\n", "                              ylog=False,\n", "                              bins=20):\n", "\n", "        if scaling == 'mm':\n", "            xd_train = self.x_train_scmm\n", "            xd_test = self.x_test_scmm\n", "        else:\n", "            xd_train = self.x_train_sc\n", "            xd_test = self.x_test_sc\n", "\n", "        all_closest = []\n", "        for fold in range(6):\n", "            if fold == 5:\n", "                closest = np.concatenate(all_closest)\n", "                if abs_v:\n", "                    errs = np.abs(np.concatenate(self.errors))\n", "                else:\n", "                    errs = np.concatenate(self.errors)\n", "                \n", "                upper_coeff = self.upper_bound(closest,errs,threshold=0.90)\n", "                \n", "                if jointplot:\n", "                    \n", "                    \n", "                    #g = sns.jointplot(closest,errs,kind='kde')\n", "                    \n", "                    sns.set_theme(style=\"ticks\")\n", "                    g = sns.JointGrid(x=closest, y=errs, marginal_ticks=False)\n", "\n", "                    # Set a log scaling on the y axis\n", "                    if xlog:\n", "                        g.ax_joint.set(xscale=\"log\")\n", "                    if ylog:\n", "                        g.ax_joint.set(yscale=\"log\")\n", "\n", "                    # Create an inset legend for the histogram colorbar\n", "                    cax = g.fig.add_axes([.15, .55, .02, .2])\n", "\n", "                    # Add the joint and marginal histogram plots\n", "                    g.plot_joint(\n", "                        sns.histplot, discrete=(False, False),\n", "                         cbar=True, cbar_ax=cax\n", "                    )\n", "                    #g.plot_marginals(sns.histplot, element=\"step\", color=\"#03012d\")\n", "                    #pfit = np.polyval([upper_coeff,0],closest)\n", "                    #g.ax_joint.plot(closest,pfit,':r')\n", "                    \n", "                    g.set_axis_labels('Euclidean feature distance', 'Absolute error', fontsize=16)\n", "                    \n", "                    # Pearson correlation\n", "                    corr = pearsonr(closest,errs)[0]\n", "                    g.ax_joint.text(0.05,0.9,'r = {:.2f}'.format(corr),transform=g.ax_joint.transAxes,fontsize=12)\n", "                    \n", "                    # MAE\n", "                    mae = np.mean(errs)\n", "                    g.ax_joint.axhline(y=mae, color='r', linestyle='-')\n", "                    \n", "\n", "                else:\n", "                    #ylog = True\n", "                    ax.set_xscale('log')\n", "                    if ylog:\n", "                        ax.set_yscale('log')\n", "                    #ax.hist2d(closest,errs,bins=bins,cmin=1)\n", "                    ax.plot(closest,errs,'+',ms=2,mew=0.2,alpha=0.3,color='midnightblue',rasterized=True)\n", "                    # 1st order fit\n", "                    if ylog:\n", "                        pfit = 10**(np.polyval(np.polyfit(np.log10(closest),np.log10(errs),1),np.log10(closest)))\n", "                    else:\n", "                        pfit = np.polyval(np.polyfit(np.log10(closest),errs,1),np.log10(closest))\n", "                        #pfit = np.polyval([upper_coeff,0],closest)\n", "                    ax.plot(closest,pfit,':',lw=0.5,color='midnightblue')\n", "                    \n", "                    \n", "                    #Draw a red circle around highest error\n", "                    highest_idx = errs.argmax()\n", "                    ax.plot(closest[highest_idx],errs[highest_idx],'o',markerfacecolor=\"None\",\n", "                            markeredgecolor='red', markeredgewidth=0.5, ms=4, alpha=0.6)\n", "                    \n", "                    # Pearson correlation\n", "                    corr = pearsonr(closest,errs)[0]\n", "                    ax.text(0.05,0.8,'r = {:.2f}'.format(corr),transform=ax.transAxes,fontsize=8)\n", "\n", "                    # MAE\n", "                    mae = np.mean(errs)\n", "                    if 'bulk' in name: # without oulier\n", "                        mae = 0.0633\n", "                    ax.axhline(y=mae, color='green', linestyle=':',lw=0.7)\n", "                    #ax.text(0.8,0.02,'MAE',transform=ax.transAxes,color='green',fontsize=5)\n", "\n", "                    #ax.text(0.5,0.8, name, transform=ax.transAxes, fontsize=8)\n", "                    ax.set_title(name)\n", "                    \n", "                    ax.set_xlabel('Euclidean feature distance',fontsize=10)\n", "                    ax.set_ylabel('Absolute error',fontsize=10)\n", "                    if xmax is not None:\n", "                        ax.set_xlim(0,xmax)\n", "                    if ymax is not None:\n", "                        ax.set_ylim(0,ymax)\n", "                    #ax.get_xaxis().set_major_formatter(matplotlib.ticker.ScalarFormatter())\n", "                    \n", "                    ### BINNING ####\n", "                    \n", "                    max_cl = closest.max()\n", "                    min_cl = closest.min()\n", "                    v = np.geomspace(min_cl,max_cl,8)\n", "                    v[0]-=10e-9\n", "                    m_closest=[]\n", "                    m_errs=[]\n", "                    #std_errs=[]\n", "                    for i in range(len(v)-1):\n", "                        idx = (closest>v[i]) & (closest<=v[i+1])\n", "                        #m_errs.append(np.sqrt((errs[idx]**2).mean()))\n", "                        m_errs.append(errs[idx].mean())\n", "                        #std_errs.append(errs[idx].std())\n", "                        m_closest.append((v[i]+v[i+1])/2)\n", "                    ax.plot(m_closest,m_errs,'o',ms=0.7)\n", "                    #ax.plot(m_closest,std_errs,'x',ms=0.7)\n", "                \n", "            else:\n", "                train = xd_test[fold]\n", "                test = xd_test[fold]\n", "                dist = euclidean_distances(test[:,:n_feat],train[:,:n_feat])\n", "                closest = np.sort(dist,axis=1)[:,:n_neighbours].mean(axis=1)\n", "                all_closest.append(closest)\n", "                if abs_v:\n", "                    errs = np.abs(self.errors[fold])\n", "                else:\n", "                    errs =self.errors[fold]"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [], "source": ["from matplotlib.ticker import (MultipleLocator, FormatStrFormatter,AutoMinorLocator)"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<ipython-input-90-eb15672731ad>:328: RuntimeWarning: Mean of empty slice.\n", "  m_errs.append(errs[idx].mean())\n", "/Users/<USER>/anaconda3/envs/modnet/lib/python3.8/site-packages/numpy/core/_methods.py:170: RuntimeWarning: invalid value encountered in double_scalars\n", "  ret = ret.dtype.type(ret / rcount)\n", "<ipython-input-90-eb15672731ad>:328: RuntimeWarning: Mean of empty slice.\n", "  m_errs.append(errs[idx].mean())\n", "/Users/<USER>/anaconda3/envs/modnet/lib/python3.8/site-packages/numpy/core/_methods.py:170: RuntimeWarning: invalid value encountered in double_scalars\n", "  ret = ret.dtype.type(ret / rcount)\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 468x302.4 with 6 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig,axs = plt.subplots(2,3,figsize=(6.5,4.2))\n", "axs = axs.flat\n", "\n", "steels = error_analysis('matbench_steels','sigma_error')\n", "steels.plot_feat_distance_all(axs[0],name='steels', n_neighbours = 25, n_feat = 200, scaling= 'n',bins=30)\n", "\n", "dielectric = error_analysis('matbench_dielectric','n_error')\n", "dielectric.plot_feat_distance_all(axs[1], name='dielectric', n_neighbours = 5, n_feat = 7, scaling= 'n',bins=100)\n", "\n", "phonons = error_analysis('matbench_phonons','w_error')\n", "phonons.plot_feat_distance_all(axs[2], name='phonons', n_neighbours = 5, n_feat = 50, scaling= 'n')\n", "\n", "exf = error_analysis('matbench_jdft2d','E_error')\n", "exf.plot_feat_distance_all(axs[3], name='exf. energy', n_neighbours = 5, n_feat = 500, scaling= 'n')\n", "\n", "expt_gap = error_analysis('matbench_expt_gap','E_g_error')\n", "expt_gap.plot_feat_distance_all(axs[4], name='expt. gap', n_neighbours = 25, n_feat = 500, scaling= 'n')\n", "\n", "k = error_analysis('matbench_elastic','K_error',multi=['K_error','G_error'])\n", "k.plot_feat_distance_all(axs[5], name='bulk mod.', n_neighbours = 5, n_feat = 150, scaling= 'n', ylog=True)\n", "\n", "axs[0].set_ylabel('')\n", "axs[1].set_ylabel('')\n", "axs[2].set_ylabel('')\n", "axs[3].set_ylabel('')\n", "axs[4].set_ylabel('')\n", "axs[5].set_ylabel('')\n", "\n", "axs[0].set_xlabel('')\n", "axs[1].set_xlabel('')\n", "axs[2].set_xlabel('')\n", "axs[3].set_xlabel('')\n", "axs[4].set_xlabel('')\n", "axs[5].set_xlabel('')\n", "\n", "#axs[3].xaxis.set_ticks([2,3])\n", "#axs[3].xaxis.set_minor_formatter(FormatStrFormatter(\"%.1f\"))\n", "#axs[3].xaxis.set_minor_locator(matplotlib.ticker.MultipleLocator(4))\n", "\n", "axs[0].xaxis.set_major_locator(matplotlib.ticker.NullLocator())\n", "axs[0].xaxis.set_minor_locator(matplotlib.ticker.NullLocator())\n", "axs[0].xaxis.set_major_formatter(matplotlib.ticker.ScalarFormatter())\n", "axs[0].set_xticks([6,9,13,20])\n", "\n", "axs[1].xaxis.set_major_locator(matplotlib.ticker.NullLocator())\n", "axs[1].xaxis.set_minor_locator(matplotlib.ticker.NullLocator())\n", "axs[1].xaxis.set_major_formatter(matplotlib.ticker.ScalarFormatter())\n", "axs[1].set_xticks([0.05,0.46,4.3,40])\n", "\n", "axs[2].xaxis.set_major_locator(matplotlib.ticker.NullLocator())\n", "axs[2].xaxis.set_minor_locator(matplotlib.ticker.NullLocator())\n", "axs[2].xaxis.set_major_formatter(matplotlib.ticker.ScalarFormatter())\n", "axs[2].set_xticks([1,2.5,6.3,16])\n", "\n", "axs[3].xaxis.set_major_locator(matplotlib.ticker.NullLocator())\n", "axs[3].xaxis.set_minor_locator(matplotlib.ticker.NullLocator())\n", "axs[3].xaxis.set_major_formatter(matplotlib.ticker.ScalarFormatter())\n", "axs[3].set_xticks([2,4,8,16])\n", "\n", "axs[4].xaxis.set_major_locator(matplotlib.ticker.NullLocator())\n", "axs[4].xaxis.set_minor_locator(matplotlib.ticker.NullLocator())\n", "axs[4].xaxis.set_major_formatter(matplotlib.ticker.ScalarFormatter())\n", "axs[4].set_xticks([4,8.6, 18.6,40])\n", "\n", "axs[5].xaxis.set_major_locator(matplotlib.ticker.NullLocator())\n", "axs[5].xaxis.set_minor_locator(matplotlib.ticker.NullLocator())\n", "axs[5].xaxis.set_major_formatter(matplotlib.ticker.ScalarFormatter())\n", "axs[5].set_xticks([0.4,2.5,15.9,100])\n", "\n", "\n", "axs[0].text(0.8,0.06,'MAE',transform=axs[0].transAxes,color='green',fontsize=5)\n", "axs[1].text(0.8,0.1,'MAE',transform=axs[1].transAxes,color='green',fontsize=5)\n", "axs[2].text(0.8,0.02,'MAE',transform=axs[2].transAxes,color='green',fontsize=5)\n", "axs[3].text(0.8,0.02,'MAE',transform=axs[3].transAxes,color='green',fontsize=5)\n", "axs[4].text(0.8,0.02,'MAE',transform=axs[4].transAxes,color='green',fontsize=5)\n", "axs[5].text(0.8,0.32,'MAE',transform=axs[5].transAxes,color='green',fontsize=5)\n", "\n", "#axs[5].ticklabel_format(style='sci', axis='y', scilimits=(0,3))\n", "#axs[5].set_ylim([0.1,40000])\n", "fig.tight_layout(pad=3,w_pad=1,h_pad=0.3)\n", "\n", "fig.text(0.5, 0.04, 'KNN feature distance', ha='center')\n", "fig.text(0.04, 0.5, 'Absolute error', va='center', rotation='vertical')\n", "\n", "fig.savefig('feat_distance.pdf',dpi=300)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([False, <PERSON>alse,  <PERSON>])"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([1,2,30])\n", "a>3"]}, {"cell_type": "code", "execution_count": 292, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([  0.4       ,   2.5198421 ,  15.87401052, 100.        ])"]}, "execution_count": 292, "metadata": {}, "output_type": "execute_result"}], "source": ["np.geomspace(0.4,100,4)"]}, {"cell_type": "code", "execution_count": 192, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df_train = pd.read_csv('../matbench_elastic/folds/train_f4.csv',index_col=0)\n", "df_test = pd.read_csv('../matbench_elastic/folds/test_f4.csv',index_col=0)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["-54107.83727485296"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df_test.loc['id6951','K_error']"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["df_test = df_test.drop(['G_error','K_error'],axis=1)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["df = df_train.append(df_test)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["dev = (df.mean() - df.loc['id6951']).abs()/df.std()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["VoronoiFingerprint|std_dev Voro_area_sum                                   104.809349\n", "VoronoiFingerprint|std_dev Voro_vol_sum                                    104.809343\n", "VoronoiFingerprint|std_dev Voro_vol_mean                                   104.809340\n", "VoronoiFingerprint|std_dev Voro_vol_std_dev                                104.809339\n", "VoronoiFingerprint|mean Voro_area_maximum                                  104.809230\n", "VoronoiFingerprint|mean Voro_area_mean                                     104.809219\n", "VoronoiFingerprint|mean Voro_area_std_dev                                  104.809213\n", "VoronoiFingerprint|mean Voro_vol_maximum                                   104.809202\n", "VoronoiFingerprint|mean Voro_area_sum                                      104.809191\n", "VoronoiFingerprint|mean Voro_vol_std_dev                                   104.809173\n", "VoronoiFingerprint|mean Voro_vol_mean                                      104.809069\n", "VoronoiFingerprint|mean Voro_vol_sum                                       104.808990\n", "BondOrientationParameter|std_dev BOOP Q l=1                                  5.888046\n", "BondOrientationParameter|mean BOOP Q l=1                                     5.854968\n", "StructuralHeterogeneity|max relative bond length                             5.813304\n", "StructuralHeterogeneity|mean absolute deviation in relative bond length      5.760764\n", "StructuralHeterogeneity|minimum neighbor distance variation                  5.760719\n", "StructuralHeterogeneity|min relative bond length                             5.699434\n", "StructuralHeterogeneity|mean neighbor distance variation                     5.669007\n", "StructuralHeterogeneity|range neighbor distance variation                    5.443935\n", "BondOrientationParameter|std_dev BOOP Q l=4                                  5.427536\n", "StructuralHeterogeneity|maximum neighbor distance variation                  5.425455\n", "StructuralHeterogeneity|mean absolute deviation in relative cell size        5.420867\n", "BondOrientationParameter|mean BOOP Q l=2                                     5.420549\n", "BondOrientationParameter|std_dev BOOP Q l=5                                  5.357154\n", "dtype: float64"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["dev.nlargest(n=25)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["id\n", "id2704    9.901901\n", "id580     8.627182\n", "id2037    6.951901\n", "id2968    6.151901\n", "id3717    5.652894\n", "Name: E_g_error, dtype: float64"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "pd.read_csv('../matbench_expt_gap/folds/test_f3.csv',index_col=0)['E_g_error'].abs().nlargest(n=5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["5 10-5"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Joint plots"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x432 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["steels = error_analysis('matbench_steels','sigma_error')\n", "steels.plot_feat_distance_all(name='steels', n_neighbours = 200, n_feat = 200, scaling= 'n',ymax=1000, jointplot=True,xlog=True)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x432 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["dielectric = error_analysis('matbench_dielectric','n_error')\n", "dielectric.plot_feat_distance_all(name='dielectric', n_neighbours = 5, n_feat = 7, scaling= 'n',ymax=100, jointplot=True,xlog=True)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x432 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["expt_gap = error_analysis('matbench_expt_gap','E_g_error')\n", "expt_gap.plot_feat_distance_all(name='expt. gap', n_neighbours = 6000, n_feat = 500, scaling= 'n', jointplot=True,xlog=True)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x432 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["exf = error_analysis('matbench_jdft2d','E_error')\n", "exf.plot_feat_distance_all(name='exf. energy', n_neighbours = 5, n_feat = 500, scaling= 'n', jointplot=True,xlog=True)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x432 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["phonons = error_analysis('matbench_phonons','w_error')\n", "phonons.plot_feat_distance_all(name='phonons', n_neighbours = 5, n_feat = 50, scaling= 'n', jointplot=True, xlog=True)"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-58-c73a37204936>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0mk\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0merror_analysis\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'matbench_elastic'\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m'K_error'\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mmulti\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'K_error'\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m'G_error'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0mk\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mplot_feat_distance_all\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mname\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'bulk mod.'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mn_neighbours\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;36m5\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mn_feat\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;36m150\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mscaling\u001b[0m\u001b[0;34m=\u001b[0m \u001b[0;34m'n'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mjointplot\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mxlog\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m<ipython-input-55-aa0e1a11a44b>\u001b[0m in \u001b[0;36mplot_feat_distance_all\u001b[0;34m(self, ax, name, upper_bound, n_neighbours, n_feat, scaling, abs_v, xmax, ymax, jointplot, xlog, ylog, bins)\u001b[0m\n\u001b[1;32m    249\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    250\u001b[0m                     \u001b[0;31m# Add the joint and marginal histogram plots\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 251\u001b[0;31m                     g.plot_joint(\n\u001b[0m\u001b[1;32m    252\u001b[0m                         \u001b[0msns\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mhistplot\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdiscrete\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;32mFalse\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;32mFalse\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    253\u001b[0m                          \u001b[0mcbar\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mcbar_ax\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mcax\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/modnet/lib/python3.8/site-packages/seaborn/axisgrid.py\u001b[0m in \u001b[0;36mplot_joint\u001b[0;34m(self, func, **kwargs)\u001b[0m\n\u001b[1;32m   1690\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1691\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mstr\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfunc\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__module__\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mstartswith\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"seaborn\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1692\u001b[0;31m             \u001b[0mfunc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0my\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0my\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1693\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1694\u001b[0m             \u001b[0mfunc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0my\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/modnet/lib/python3.8/site-packages/seaborn/distributions.py\u001b[0m in \u001b[0;36mhistplot\u001b[0;34m(data, x, y, hue, weights, stat, bins, binwidth, binrange, discrete, cumulative, common_bins, common_norm, multiple, element, fill, shrink, kde, kde_kws, line_kws, thresh, pthresh, pmax, cbar, cbar_ax, cbar_kws, palette, hue_order, hue_norm, color, log_scale, legend, ax, **kwargs)\u001b[0m\n\u001b[1;32m   1438\u001b[0m     \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1439\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1440\u001b[0;31m         p.plot_bivariate_histogram(\n\u001b[0m\u001b[1;32m   1441\u001b[0m             \u001b[0mcommon_bins\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mcommon_bins\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1442\u001b[0m             \u001b[0mcommon_norm\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mcommon_norm\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/modnet/lib/python3.8/site-packages/seaborn/distributions.py\u001b[0m in \u001b[0;36mplot_bivariate_histogram\u001b[0;34m(self, common_bins, common_norm, thresh, pthresh, pmax, color, legend, cbar, cbar_ax, cbar_kws, estimate_kws, **plot_kws)\u001b[0m\n\u001b[1;32m    747\u001b[0m         \u001b[0;32mfor\u001b[0m \u001b[0m_\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0msub_data\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0miter_data\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfrom_comp_data\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    748\u001b[0m             \u001b[0msub_data\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0msub_data\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdropna\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 749\u001b[0;31m             sub_heights, _ = estimator(\n\u001b[0m\u001b[1;32m    750\u001b[0m                 \u001b[0msub_data\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"x\"\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0msub_data\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"y\"\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0msub_data\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"weights\"\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    751\u001b[0m             )\n", "\u001b[0;32m~/anaconda3/envs/modnet/lib/python3.8/site-packages/seaborn/_statistics.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(self, x1, x2, weights)\u001b[0m\n\u001b[1;32m    371\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_eval_univariate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx1\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mweights\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    372\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 373\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_eval_bivariate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx1\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mx2\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mweights\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    374\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    375\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/modnet/lib/python3.8/site-packages/seaborn/_statistics.py\u001b[0m in \u001b[0;36m_eval_bivariate\u001b[0;34m(self, x1, x2, weights)\u001b[0m\n\u001b[1;32m    324\u001b[0m         )\n\u001b[1;32m    325\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 326\u001b[0;31m         area = np.outer(\n\u001b[0m\u001b[1;32m    327\u001b[0m             \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdiff\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mbin_edges\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    328\u001b[0m             \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdiff\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mbin_edges\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m<__array_function__ internals>\u001b[0m in \u001b[0;36mouter\u001b[0;34m(*args, **kwargs)\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/modnet/lib/python3.8/site-packages/numpy/core/numeric.py\u001b[0m in \u001b[0;36mouter\u001b[0;34m(a, b, out)\u001b[0m\n\u001b[1;32m    907\u001b[0m     \u001b[0ma\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0masarray\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0ma\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    908\u001b[0m     \u001b[0mb\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0masarray\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mb\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 909\u001b[0;31m     \u001b[0;32mreturn\u001b[0m \u001b[0mmultiply\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0ma\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mravel\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnewaxis\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mb\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mravel\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mnewaxis\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m:\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mout\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    910\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    911\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x432 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["k = error_analysis('matbench_elastic','K_error',multi=['K_error','G_error'])\n", "k.plot_feat_distance_all(name='bulk mod.', n_neighbours = 5, n_feat = 150, scaling= 'n', jointplot=True,xlog=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python (modnet)", "language": "python", "name": "modnet"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}