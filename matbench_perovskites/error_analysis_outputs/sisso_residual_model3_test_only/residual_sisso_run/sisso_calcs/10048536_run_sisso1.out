Job started on Fri Aug  8 18:16:02 CEST 2025
Running on node(s): cns262
The following modules were not unloaded:
  (Use "module --force purge" to unload all):

  1) EasyBuild/2024a
Starting batch execution of sisso++
Entering folder: sisso_calc_target_residual
sisso++: error while loading shared libraries: libbz2.so.1.0: cannot open shared object file: No such file or directory
sisso++: error while loading shared libraries: libbz2.so.1.0: cannot open shared object file: No such file or directory
sisso++: error while loading shared libraries: libbz2.so.1.0: cannot open shared object file: No such file or directory
sisso++: error while loading shared libraries: libbz2.so.1.0: cannot open shared object file: No such file or directory
--------------------------------------------------------------------------
p<PERSON><PERSON> detected that one or more processes exited with non-zero status,
thus causing the job to be terminated. The first process to do so was:

   Process name: [prterun-cns262-3352760@1,16] Exit code:    127
--------------------------------------------------------------------------
All tasks completed.

Resources Used

Total Memory used                        - MEM              : 0B
Total CPU Time                           - CPU_Time         : 00:01:12
Execution Time                           - Wall_Time        : 00:00:03
total programme cpu time                 - Total_CPU        : 00:01.185
Total_CPU / CPU_Time  (%)                - ETA              : 1%
Number of alloc CPU                      - NCPUS            : 24
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 27
Mobilized Resources x Execution Time     - R_Wall_Time      : 00:01:21
CPU_Time / R_Wall_Time (%)               - ALPHA            : 88%
Energy (Joules)                                             : unknown

