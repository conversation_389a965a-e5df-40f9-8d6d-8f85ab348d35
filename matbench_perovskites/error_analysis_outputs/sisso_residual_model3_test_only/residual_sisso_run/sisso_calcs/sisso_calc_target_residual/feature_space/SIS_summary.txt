# FEAT_ID     Score                   Feature Expression
0             0.281305464381513326    ((feature_50^2) / (feature_36 + feature_31))
1             0.281307865641618859    ((feature_50 * feature_48) / (feature_36 + feature_31))
2             0.281309475540728549    ((feature_48^2) / (feature_36 + feature_31))
3             0.281920496799298481    ((feature_50^2) / (|feature_31 - feature_28|))
4             0.281920757434869274    ((feature_50 * feature_48) / (|feature_31 - feature_28|))
5             0.281920948748246081    ((feature_48^2) / (|feature_31 - feature_28|))
6             0.281921974558882571    ((feature_50^2) / (feature_31 + feature_28))
7             0.281922231816849778    ((feature_50 * feature_48) / (feature_31 + feature_28))
8             0.281922420796424655    ((feature_48^2) / (feature_31 + feature_28))
9             0.282075897458164693    ((feature_48^2) / (feature_31 - feature_29))
10            0.282076039974515913    ((feature_50 * feature_48) / (feature_31 - feature_29))
11            0.282076212178266506    ((feature_50^2) / (feature_31 - feature_29))
12            0.282501770821244669    ((feature_48^2) / (feature_36 - feature_31))
13            0.282501978871402382    ((feature_50^2) / (feature_36 - feature_31))
14            0.282502039871063648    ((feature_50 * feature_48) / (feature_36 - feature_31))
#-----------------------------------------------------------------------
15            0.0904260778441622615   ((feature_49 * feature_36) / (feature_25 - feature_3))
16            0.0905003014033230468   ((feature_50 * feature_29) / (|feature_25 - feature_3|))
17            0.0905004654865856351   ((feature_48 * feature_29) / (|feature_25 - feature_3|))
18            0.0906278122271909486   ((feature_40 + feature_11) / (feature_25 - feature_3))
19            0.0906729546505242318   ((feature_49 * feature_48) / (|feature_25 - feature_3|))
20            0.090715089720824621    ((feature_48 * feature_33) / (|feature_25 - feature_3|))
21            0.0907337089143025743   ((feature_49 * feature_40) / (feature_25 - feature_3))
22            0.0907491196729601629   ((feature_50 * feature_29) / (feature_25 - feature_3))
23            0.0907492948773280794   ((feature_48 * feature_29) / (feature_25 - feature_3))
24            0.0907617104602948949   ((feature_48 * feature_38) / (|feature_25 - feature_3|))
25            0.0908217488874184936   ((feature_49 * feature_33) / (feature_25 - feature_3))
26            0.0908378398503174589   ((feature_49 * feature_38) / (feature_25 - feature_3))
27            0.0911038689013000946   ((feature_49 * feature_48) / (feature_25 - feature_3))
28            0.0915488471489982331   ((feature_48 * feature_38) / (feature_25 - feature_3))
29            0.0915666291867177073   ((feature_48 * feature_33) / (feature_25 - feature_3))
#-----------------------------------------------------------------------
30            0.0789713816984684291   (|(|feature_47 - feature_4|) - (feature_21 / feature_20)|)
31            0.0789734901923733051   (|(feature_42 + feature_3) - (feature_21 / feature_20)|)
32            0.0791303810886016951   (|(|feature_39 - feature_5|) - (feature_21 / feature_20)|)
33            0.079260546471805568    (|(feature_41 + feature_5) - (feature_21 / feature_20)|)
34            0.0795056128233182779   (|(|feature_49 - feature_5|) - (feature_21 / feature_20)|)
35            0.0796299023330422351   (|(feature_21 / feature_20) - (|feature_17 - feature_3|)|)
36            0.0797363250824226627   (|(feature_21 / feature_20) - (|feature_17 - feature_4|)|)
37            0.0799115380763340127   (|(feature_43 - feature_6) - (|feature_21 - feature_10|)|)
38            0.0801607111382977994   (|(feature_41 + feature_4) - (feature_21 / feature_20)|)
39            0.0805786656876927715   (|(feature_41 + feature_3) - (feature_21 / feature_20)|)
40            0.0812610531179684681   (|(|feature_38 - feature_3|) - (feature_21 / feature_20)|)
41            0.0814966221251561124   (|(|feature_38 - feature_4|) - (feature_21 / feature_20)|)
42            0.0815294037005014627   (|(|feature_33 - feature_4|) - (feature_21 / feature_20)|)
43            0.081657808486647887    (|(|feature_33 - feature_1|) - (feature_21 / feature_20)|)
44            0.0859094862303951406   ((feature_26 / feature_37) * (feature_16 + feature_11))
#-----------------------------------------------------------------------
45            0.0357537437219433499   ((feature_10^6) / (feature_37 - feature_1))
46            0.0358705165346509675   ((feature_41^6) / (|feature_37 - feature_1|))
47            0.0359330428396115106   ((feature_41^6) / (feature_37 - feature_1))
48            0.0359657357564279953   ((feature_10^6) / (|feature_25 - feature_5|))
49            0.036012803582589209    ((feature_41^6) / (|feature_25 - feature_5|))
50            0.0361028460513759808   ((feature_50 * feature_10) / (feature_27 - feature_18))
51            0.0361116740580651185   ((feature_41^6) / (feature_25 - feature_5))
52            0.0361308455543449952   ((feature_10^6) / (feature_25 - feature_5))
53            0.036564022110865145    ((feature_50 * feature_41) / (feature_27 - feature_18))
54            0.0385611995235825983   ((feature_41 - feature_38) / (|feature_27 - feature_18|))
55            0.0389128992832365669   ((feature_41 - feature_33) / (|feature_27 - feature_18|))
56            0.0397238105280621132   ((feature_30 - feature_25) / (feature_45 - feature_4))
57            0.0402043064588556223   ((feature_30 - feature_25) / (|feature_45 - feature_4|))
58            0.0412784359958489194   ((feature_30 - feature_27) / (feature_45 - feature_3))
59            0.049803865800525815    ((feature_30 - feature_25) / (feature_45 - feature_3))
#-----------------------------------------------------------------------
60            0.0256420550176906792   ((|feature_49 - feature_23|) / (|feature_39 - feature_23|))
61            0.0258989580749902845   ((|feature_49 - feature_42|) / (|feature_39 - feature_18|))
62            0.0263153171655810952   ((feature_49 + feature_9) / (feature_39 + feature_23))
63            0.0264025875639613376   ((feature_22 + feature_10) / (|feature_18 - feature_17|))
64            0.0265022022522601458   ((feature_49 - feature_9) / (feature_39 - feature_23))
65            0.0266152026932859297   ((|feature_22 - feature_10|) / (|feature_18 - feature_17|))
66            0.0267858022338924064   ((feature_22 - feature_10) / (feature_18 - feature_17))
67            0.0267996885641360628   ((|feature_49 - feature_9|) / (|feature_39 - feature_23|))
68            0.0270319833771054049   ((feature_22 + feature_9) / (|feature_18 - feature_17|))
69            0.0273355687505063109   ((|feature_22 - feature_9|) / (|feature_18 - feature_17|))
70            0.027435351792902709    ((feature_22 - feature_9) / (feature_18 - feature_17))
71            0.0274957276319959913   ((feature_22^6) / sqrt(feature_31))
72            0.0277343012449235357   ((feature_22^6) / cbrt(feature_31))
73            0.0334238386624777575   ((feature_46 / feature_31) * (feature_22^6))
74            0.0334299261487881888   ((feature_47 / feature_31) * (feature_22^6))
#-----------------------------------------------------------------------
75            0.0213338790541783424   ((feature_48 / feature_31) + (feature_32 * feature_29))
76            0.0214633990477472482   ((feature_48 / feature_31) + (feature_35 * feature_28))
77            0.0214999419876030946   ((feature_7 * feature_1) / (|feature_40 - feature_16|))
78            0.0215171585476875808   ((feature_7 * feature_3) / (|feature_40 - feature_16|))
79            0.0215327711485674908   ((feature_7 * feature_4) / (|feature_40 - feature_16|))
80            0.0215517679335318693   ((feature_7 * feature_5) / (|feature_40 - feature_16|))
81            0.0217750840006507053   ((feature_50 / feature_31) + (feature_29 * feature_14))
82            0.0220160411885016657   ((feature_40 * feature_7) / (|feature_40 - feature_16|))
83            0.0220449175838577476   ((feature_15 * feature_7) / (|feature_40 - feature_16|))
84            0.0222784567076047789   ((feature_7 / feature_6) / (|feature_40 - feature_16|))
85            0.0224017407260188942   ((|feature_29 - feature_20|) * (feature_15 * feature_7))
86            0.0261461986214771812   ((feature_41 / feature_31) / (feature_27 - feature_22))
87            0.0262457447248078332   ((feature_42 / feature_31) / (feature_27 - feature_22))
88            0.0296515125132113186   ((feature_42 / feature_31) / (|feature_27 - feature_22|))
89            0.0297054613557746483   ((feature_41 / feature_31) / (|feature_27 - feature_22|))
#-----------------------------------------------------------------------
90            0.0223798614045667733   ((feature_50 / feature_31) * (feature_42 - feature_41))
91            0.0224696012885991052   ((feature_29 - feature_12) * (feature_15 * feature_7))
92            0.0225196938170608221   ((feature_50 / feature_31) / (feature_16 - feature_10))
93            0.0225207380377443443   ((feature_48 / feature_31) / (feature_16 - feature_10))
94            0.02252185435363269     ((feature_50 / feature_31) * (feature_10 - feature_9))
95            0.0225984957634327646   ((feature_22 * feature_8) / (feature_37 - feature_1))
96            0.0229104144598480421   ((feature_37 * feature_28) / (|feature_37 - feature_8|))
97            0.0229251575303356311   ((feature_8 / feature_21) / (feature_37 - feature_1))
98            0.0233076999027074135   ((feature_48 / feature_31) + (feature_29 * feature_14))
99            0.0241301239774033352   ((feature_48 / feature_31) + (feature_29 * feature_13))
100           0.0243005652153376883   ((feature_50 / feature_31) * (feature_23 - feature_12))
101           0.0243055809488880691   ((feature_48 / feature_31) * (feature_23 - feature_12))
102           0.0246312900331155764   ((feature_50 / feature_31) + (feature_35 * feature_28))
103           0.0253793820852809757   ((feature_50 / feature_31) + (feature_32 * feature_29))
104           0.0258992314428506676   ((feature_50 / feature_31) + (feature_29 * feature_13))
#-----------------------------------------------------------------------
