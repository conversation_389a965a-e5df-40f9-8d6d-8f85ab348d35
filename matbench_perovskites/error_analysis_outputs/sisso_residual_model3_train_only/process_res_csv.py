import pandas as pd
import io

df = pd.read_csv('../error_analysis_outputs/residuals_model_3_train_top50_correlated_features.csv', index_col=0)

# Rename the first column to 'material_id'
df.index.name = 'material_id'
# Drop the 'prediction_model_3' and 'abs_error_model_3' columns
df = df.drop(columns=['target','prediction_model_3', 'abs_error_model_3'])
# Rename 'signed_error_model_3' to 'target'
df = df.rename(columns={'signed_error_model_3': 'target'})



df.to_csv('./residuals_model_3_train.csv')