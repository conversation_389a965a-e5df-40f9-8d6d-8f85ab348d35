Job started on Fri Aug  8 17:01:19 CEST 2025
Running on node(s): cns262
The following modules were not unloaded:
  (Use "module --force purge" to unload all):

  1) EasyBuild/2024a
Starting batch execution of sisso++
Entering folder: sisso_calc_target_residual
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Warning: program compiled against libxml 212 using older 209
Commit: 686a99a5c703c438e64a2fe5b256efb0579c1986
Compiler: GNU version 13.3.0
Compiler flags:  -fopenmp
time input_parsing: 1.68328 s
Time to generate feat space: 204.109 s
Projection time: 67.3188 s
Time to get best features on rank : 0.00196596 s
|---Complete final combination/selection from all ranks: 0.199958 s
Time for SIS: 69.0663 s
Time for l0-norm: 0.00446631 s
Projection time: 73.9035 s
Time to get best features on rank : 0.0081051 s
|---Complete final combination/selection from all ranks: 0.300872 s
Time for SIS: 75.7315 s
Time for l0-norm: 0.0101409 s
Projection time: 73.9657 s
Time to get best features on rank : 0.00186991 s
|---Complete final combination/selection from all ranks: 0.386884 s
Time for SIS: 75.9031 s
Time for l0-norm: 0.495899 s
Projection time: 73.9417 s
Time to get best features on rank : 0.0335402 s
|---Complete final combination/selection from all ranks: 0.284207 s
Time for SIS: 75.8176 s
Time for l0-norm: 39.4228 s
Projection time: 73.9117 s
Time to get best features on rank : 0.00198204 s
|---Complete final combination/selection from all ranks: 0.383293 s
Time for SIS: 75.8824 s
Time for l0-norm: 3130.6 s
Projection time: 73.9175 s
Time to get best features on rank : 0.00933613 s
|---Complete final combination/selection from all ranks: 0.385963 s
Time for SIS: 75.9011 s
slurmstepd: error: *** JOB 10048303 ON cns262 CANCELLED AT 2025-08-08T19:01:41 DUE TO TIME LIMIT ***

Resources Used

Total Memory used                        - MEM              : 14GiB
Total CPU Time                           - CPU_Time         : 2-00:09:12
Execution Time                           - Wall_Time        : 02:00:23
total programme cpu time                 - Total_CPU        : 1-23:53:29
Total_CPU / CPU_Time  (%)                - ETA              : 99%
Number of alloc CPU                      - NCPUS            : 24
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 27
Mobilized Resources x Execution Time     - R_Wall_Time      : 2-06:10:21
CPU_Time / R_Wall_Time (%)               - ALPHA            : 88%
Energy (Joules)                                             : unknown

