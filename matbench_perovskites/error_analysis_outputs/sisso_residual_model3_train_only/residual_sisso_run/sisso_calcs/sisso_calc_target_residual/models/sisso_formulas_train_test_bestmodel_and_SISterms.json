{"SISSO_bestmodel_residuals_train_term_01": "(-0.001483329697996118) * ((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] - df[\"OFM:_d^10_-_p^5\"]) / np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_bestmodel_residuals_train_term_02": "(-0.0005259817899481455) * ((df[\"GlobalSymmetryFeatures_spacegroup_num\"] / df[\"DensityFeatures_packing_fraction\"]) * (df[\"ElementProperty_MagpieData_mean_Electronegativity\"] + df[\"BandCenter_band_center\"]))", "SISSO_bestmodel_residuals_train_term_03": "(-0.0008559240534673412) * ((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"ElementProperty_MagpieData_minimum_Column\"]) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_bestmodel_residuals_train_term_04": "(-0.08216630808328743) * ((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] + df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_bestmodel_residuals_train_term_05": "(-0.0001476840423358978) * ((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) * (df[\"DensityFeatures_packing_fraction\"]**6))", "SISSO_bestmodel_residuals_train_full": "(0.01245014884892721) + ((-0.001483329697996118) * ((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] - df[\"OFM:_d^10_-_p^5\"]) / np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))) + ((-0.0005259817899481455) * ((df[\"GlobalSymmetryFeatures_spacegroup_num\"] / df[\"DensityFeatures_packing_fraction\"]) * (df[\"ElementProperty_MagpieData_mean_Electronegativity\"] + df[\"BandCenter_band_center\"]))) + ((-0.0008559240534673412) * ((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"ElementProperty_MagpieData_minimum_Column\"]) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))) + ((-0.08216630808328743) * ((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] + df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))) + ((-0.0001476840423358978) * ((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) * (df[\"DensityFeatures_packing_fraction\"]**6)))", "SISSO_SISterm_residuals_train_feat_000": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / np.abs(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"YangSolidSolution_Yang_delta\"]))", "SISSO_SISterm_residuals_train_feat_001": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]**2) / np.abs(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"YangSolidSolution_Yang_delta\"]))", "SISSO_SISterm_residuals_train_feat_002": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] + df[\"YangSolidSolution_Yang_delta\"]))", "SISSO_SISterm_residuals_train_feat_003": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] + df[\"YangSolidSolution_Yang_delta\"]))", "SISSO_SISterm_residuals_train_feat_004": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]**2) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] + df[\"YangSolidSolution_Yang_delta\"]))", "SISSO_SISterm_residuals_train_feat_005": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) * (df[\"ElementFraction_F\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_train_feat_006": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"YangSolidSolution_Yang_delta\"]))", "SISSO_SISterm_residuals_train_feat_007": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"YangSolidSolution_Yang_delta\"]))", "SISSO_SISterm_residuals_train_feat_008": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]**2) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"YangSolidSolution_Yang_delta\"]))", "SISSO_SISterm_residuals_train_feat_009": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) * (df[\"DensityFeatures_packing_fraction\"]**3))", "SISSO_SISterm_residuals_train_feat_010": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) * (df[\"DensityFeatures_packing_fraction\"]**3))", "SISSO_SISterm_residuals_train_feat_011": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) * (df[\"DensityFeatures_packing_fraction\"]**6))", "SISSO_SISterm_residuals_train_feat_012": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / np.abs(df[\"ValenceOrbital_frac_d_valence_electrons\"] - df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_train_feat_013": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / np.abs(df[\"ValenceOrbital_frac_d_valence_electrons\"] - df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_train_feat_014": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]**2) / np.abs(df[\"ValenceOrbital_frac_d_valence_electrons\"] - df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_train_feat_015": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] + df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_train_feat_016": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] + df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_train_feat_017": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]**2) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] + df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_train_feat_018": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / np.abs(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"ValenceOrbital_avg_d_valence_electrons\"]))", "SISSO_SISterm_residuals_train_feat_019": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / np.abs(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"ValenceOrbital_avg_d_valence_electrons\"]))", "SISSO_SISterm_residuals_train_feat_020": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]**2) / np.abs(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"ValenceOrbital_avg_d_valence_electrons\"]))", "SISSO_SISterm_residuals_train_feat_021": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] + df[\"ElementProperty_MagpieData_mean_NdValence\"]))", "SISSO_SISterm_residuals_train_feat_022": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] + df[\"ElementProperty_MagpieData_mean_NdValence\"]))", "SISSO_SISterm_residuals_train_feat_023": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]**2) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] + df[\"ElementProperty_MagpieData_mean_NdValence\"]))", "SISSO_SISterm_residuals_train_feat_024": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]**2) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"ElementProperty_MagpieData_mean_NdValence\"]))", "SISSO_SISterm_residuals_train_feat_025": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"ElementProperty_MagpieData_mean_NdValence\"]))", "SISSO_SISterm_residuals_train_feat_026": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"ElementProperty_MagpieData_mean_NdValence\"]))", "SISSO_SISterm_residuals_train_feat_027": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]**2) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] - df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_train_feat_028": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] - df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_train_feat_029": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] - df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_train_feat_030": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"OFM:_d^10_-_p^5\"]) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_031": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"ElementProperty_MagpieData_minimum_NValence\"]) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_032": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] - df[\"CrystalNNFingerprint_std_dev_L-shaped_CN_2\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_033": "((df[\"OFM:_p^5_-_f^14\"] * df[\"ElementProperty_MagpieData_minimum_Column\"]) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_034": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] - df[\"CrystalNNFingerprint_mean_L-shaped_CN_2\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_035": "((df[\"OFM:_p^5_-_f^14\"] * df[\"ValenceOrbital_frac_d_valence_electrons\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_036": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"ElementProperty_MagpieData_minimum_Column\"]) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_037": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"ElementProperty_MagpieData_minimum_Column\"]) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_038": "((df[\"OFM:_p^5_-_f^14\"] * df[\"ValenceOrbital_avg_d_valence_electrons\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_039": "((df[\"ElementProperty_MagpieData_minimum_Column\"] - df[\"VoronoiFingerprint_std_dev_Voro_index_5\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_040": "(np.abs(df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] - df[\"YangSolidSolution_Yang_delta\"]) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_041": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"OFM:_p^5_-_d^10\"]) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_042": "((df[\"OFM:_p^5_-_f^14\"] * df[\"OFM:_d^10_-_p^5\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_043": "((df[\"ElementProperty_MagpieData_minimum_Column\"] - df[\"ElementProperty_MagpieData_mean_Electronegativity\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_044": "((df[\"OFM:_p^5_-_f^14\"] * df[\"OFM:_p^5_-_d^10\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_045": "((df[\"OFM:_p^5_-_f^14\"] * df[\"ElementProperty_MagpieData_minimum_NValence\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_046": "((np.exp(df[\"ElementProperty_MagpieData_minimum_NValence\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"])))", "SISSO_SISterm_residuals_train_feat_047": "(np.abs(df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] - df[\"YangSolidSolution_Yang_delta\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_048": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"ValenceOrbital_avg_d_valence_electrons\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_049": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"ElementProperty_MagpieData_mean_NdValence\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_050": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"ElementProperty_MagpieData_minimum_NValence\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_051": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"ElementProperty_MagpieData_minimum_NValence\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_052": "((df[\"OFM:_p^5_-_f^14\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_053": "((df[\"ElementProperty_MagpieData_minimum_Column\"] + df[\"BandCenter_band_center\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_054": "((df[\"OFM:_p^5_-_f^14\"] * df[\"ElementProperty_MagpieData_minimum_Column\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_055": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"ElementProperty_MagpieData_minimum_Column\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_056": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"ElementProperty_MagpieData_minimum_Column\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_057": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"OFM:_p^5_-_d^10\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_058": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"OFM:_d^10_-_p^5\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_059": "((df[\"OFM:_p^5_-_f^14\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_060": "((df[\"GlobalSymmetryFeatures_spacegroup_num\"] / df[\"ElementProperty_MagpieData_mean_MendeleevNumber\"]) + (df[\"ElementProperty_MagpieData_maximum_NpValence\"] - df[\"Stoichiometry_0-norm\"]))", "SISSO_SISterm_residuals_train_feat_061": "(np.abs((df[\"VoronoiFingerprint_std_dev_Symmetry_index_5\"] + df[\"Stoichiometry_7-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_062": "(np.abs((df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"]) - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_063": "(np.abs((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] + df[\"Stoichiometry_2-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_Electronegativity\"])))", "SISSO_SISterm_residuals_train_feat_064": "(np.abs((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] + df[\"Stoichiometry_7-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_065": "(np.abs(np.abs(df[\"OFM:_p^4_-_p^5\"] - df[\"Stoichiometry_3-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_066": "(np.abs((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] + df[\"Stoichiometry_10-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_067": "(np.abs(np.abs(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"Stoichiometry_7-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_068": "(np.abs(np.abs(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"Stoichiometry_10-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_069": "(np.abs((df[\"VoronoiFingerprint_std_dev_Symmetry_index_5\"] + df[\"Stoichiometry_10-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_070": "(np.abs((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] + df[\"Stoichiometry_3-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_Electronegativity\"])))", "SISSO_SISterm_residuals_train_feat_071": "((df[\"GlobalSymmetryFeatures_spacegroup_num\"] / df[\"DensityFeatures_packing_fraction\"]) * (df[\"YangSolidSolution_Yang_delta\"]**3))", "SISSO_SISterm_residuals_train_feat_072": "(np.abs(np.abs(df[\"CrystalNNFingerprint_mean_L-shaped_CN_2\"] - df[\"Stoichiometry_10-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_073": "(np.abs((df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"]) - np.abs(df[\"OFM:_p^5_-_p^5\"] - df[\"Stoichiometry_3-norm\"])))", "SISSO_SISterm_residuals_train_feat_074": "((df[\"ElementProperty_MagpieData_range_NpValence\"] - df[\"Stoichiometry_0-norm\"]) - np.abs(df[\"GlobalSymmetryFeatures_crystal_system_int\"] - df[\"VoronoiFingerprint_mean_Voro_index_5\"]))", "SISSO_SISterm_residuals_train_feat_075": "(np.abs(np.abs(df[\"OFM:_p^5_-_f^14\"] - df[\"Stoichiometry_5-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_076": "(np.abs((df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"]) - np.abs(df[\"OFM:_p^5_-_p^5\"] - df[\"Stoichiometry_7-norm\"])))", "SISSO_SISterm_residuals_train_feat_077": "(np.abs(np.abs(df[\"OFM:_p^5_-_f^14\"] - df[\"Stoichiometry_10-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_078": "((df[\"ElementProperty_MagpieData_mean_MendeleevNumber\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"]) + (df[\"GlobalSymmetryFeatures_crystal_system_int\"] * df[\"Stoichiometry_0-norm\"]))", "SISSO_SISterm_residuals_train_feat_079": "(np.abs(np.abs(df[\"OFM:_p^5_-_f^14\"] - df[\"Stoichiometry_7-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_080": "(np.abs((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] + df[\"Stoichiometry_5-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_081": "(np.abs((df[\"ElementProperty_MagpieData_range_NpValence\"] - df[\"Stoichiometry_0-norm\"]) - np.abs(df[\"GlobalSymmetryFeatures_crystal_system_int\"] - df[\"VoronoiFingerprint_mean_Voro_index_5\"])))", "SISSO_SISterm_residuals_train_feat_082": "(np.abs(np.abs(df[\"OFM:_p^5_-_d^10\"] - df[\"Stoichiometry_7-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_083": "(np.abs((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] + df[\"Stoichiometry_7-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_084": "(np.abs(np.abs(df[\"OFM:_p^5_-_d^10\"] - df[\"Stoichiometry_3-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_085": "(np.abs(np.abs(df[\"OFM:_d^10_-_p^5\"] - df[\"Stoichiometry_3-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_086": "(np.abs(np.abs(df[\"OFM:_d^10_-_p^5\"] - df[\"Stoichiometry_5-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_087": "(np.abs(np.abs(df[\"OFM:_p^5_-_d^10\"] - df[\"Stoichiometry_5-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_088": "(np.abs((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] + df[\"Stoichiometry_10-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_train_feat_089": "((df[\"GlobalSymmetryFeatures_spacegroup_num\"] / df[\"DensityFeatures_packing_fraction\"]) * (df[\"ElementProperty_MagpieData_mean_Electronegativity\"] + df[\"BandCenter_band_center\"]))", "SISSO_SISterm_residuals_train_feat_090": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"OFM:_p^5_-_p^5\"]) / (df[\"DensityFeatures_packing_fraction\"] - df[\"Stoichiometry_3-norm\"]))", "SISSO_SISterm_residuals_train_feat_091": "(np.abs(df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] - df[\"OFM:_p^5_-_d^10\"]) / np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_train_feat_092": "((df[\"VoronoiFingerprint_mean_Voro_index_6\"] - df[\"GlobalSymmetryFeatures_crystal_system_int\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_5-norm\"]))", "SISSO_SISterm_residuals_train_feat_093": "((df[\"VoronoiFingerprint_mean_Voro_index_5\"]**3) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_5-norm\"]))", "SISSO_SISterm_residuals_train_feat_094": "((df[\"OFM:_p^5_-_p^5\"]**6) / (df[\"DensityFeatures_packing_fraction\"] - df[\"Stoichiometry_3-norm\"]))", "SISSO_SISterm_residuals_train_feat_095": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"OFM:_p^4_-_p^5\"]) / np.abs(df[\"DensityFeatures_packing_fraction\"] - df[\"Stoichiometry_3-norm\"]))", "SISSO_SISterm_residuals_train_feat_096": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"OFM:_p^4_-_p^5\"]) / (df[\"DensityFeatures_packing_fraction\"] - df[\"Stoichiometry_3-norm\"]))", "SISSO_SISterm_residuals_train_feat_097": "(np.abs(df[\"VoronoiFingerprint_mean_Voro_index_6\"] - df[\"Stoichiometry_0-norm\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_5-norm\"]))", "SISSO_SISterm_residuals_train_feat_098": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"VoronoiFingerprint_std_dev_Symmetry_index_5\"]) / (df[\"OFM:_p^5_-_s^2\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_train_feat_099": "((df[\"OFM:_p^5_-_p^5\"]**6) / np.abs(df[\"DensityFeatures_packing_fraction\"] - df[\"Stoichiometry_3-norm\"]))", "SISSO_SISterm_residuals_train_feat_100": "((df[\"VoronoiFingerprint_mean_Voro_index_5\"]**6) / np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"Stoichiometry_7-norm\"]))", "SISSO_SISterm_residuals_train_feat_101": "((df[\"VoronoiFingerprint_mean_Voro_index_5\"]**6) / np.abs(df[\"DensityFeatures_packing_fraction\"] - df[\"Stoichiometry_3-norm\"]))", "SISSO_SISterm_residuals_train_feat_102": "((df[\"VoronoiFingerprint_mean_Voro_index_5\"]**6) / (df[\"DensityFeatures_packing_fraction\"] - df[\"Stoichiometry_3-norm\"]))", "SISSO_SISterm_residuals_train_feat_103": "((df[\"VoronoiFingerprint_mean_Symmetry_index_6\"] - df[\"Stoichiometry_3-norm\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_5-norm\"]))", "SISSO_SISterm_residuals_train_feat_104": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"]**3) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_5-norm\"]))", "SISSO_SISterm_residuals_train_feat_105": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"]**6) / np.abs(df[\"DensityFeatures_packing_fraction\"] - df[\"Stoichiometry_3-norm\"]))", "SISSO_SISterm_residuals_train_feat_106": "(np.abs(df[\"VoronoiFingerprint_mean_Symmetry_index_6\"] - df[\"Stoichiometry_3-norm\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_5-norm\"]))", "SISSO_SISterm_residuals_train_feat_107": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"VoronoiFingerprint_mean_Voro_index_5\"]) / (df[\"OFM:_p^5_-_s^2\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_train_feat_108": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"]**6) / np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"Stoichiometry_7-norm\"]))", "SISSO_SISterm_residuals_train_feat_109": "((df[\"VoronoiFingerprint_mean_Voro_index_5\"]**6) / (df[\"OFM:_p^5_-_s^2\"] - df[\"Stoichiometry_7-norm\"]))", "SISSO_SISterm_residuals_train_feat_110": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"]**6) / (df[\"DensityFeatures_packing_fraction\"] - df[\"Stoichiometry_3-norm\"]))", "SISSO_SISterm_residuals_train_feat_111": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"]**6) / (df[\"OFM:_p^5_-_s^2\"] - df[\"Stoichiometry_7-norm\"]))", "SISSO_SISterm_residuals_train_feat_112": "((df[\"VoronoiFingerprint_std_dev_Symmetry_index_5\"]**6) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_5-norm\"]))", "SISSO_SISterm_residuals_train_feat_113": "((df[\"VoronoiFingerprint_mean_Voro_index_5\"]**6) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_5-norm\"]))", "SISSO_SISterm_residuals_train_feat_114": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"VoronoiFingerprint_mean_Symmetry_index_5\"]) / (df[\"OFM:_p^5_-_s^2\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_train_feat_115": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"]**6) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_5-norm\"]))", "SISSO_SISterm_residuals_train_feat_116": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"]**6) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_5-norm\"]))", "SISSO_SISterm_residuals_train_feat_117": "((df[\"VoronoiFingerprint_mean_Voro_index_5\"]**6) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_5-norm\"]))", "SISSO_SISterm_residuals_train_feat_118": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] - df[\"OFM:_p^5_-_d^10\"]) / np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_train_feat_119": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] - df[\"OFM:_d^10_-_p^5\"]) / np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_train_feat_120": "(np.abs(df[\"OFM:_p^5_-_f^14\"] - df[\"VoronoiFingerprint_mean_Voro_index_5\"]) / np.abs(df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_train_feat_121": "(np.abs((df[\"VoronoiFingerprint_mean_Voro_index_3\"] * df[\"GlobalSymmetryFeatures_is_centrosymmetric\"]) - (df[\"ElementProperty_MagpieData_maximum_NpValence\"] * df[\"Stoichiometry_2-norm\"])))", "SISSO_SISterm_residuals_train_feat_122": "((df[\"CrystalNNFingerprint_std_dev_L-shaped_CN_2\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / np.abs(df[\"DensityFeatures_packing_fraction\"] - df[\"GlobalSymmetryFeatures_is_centrosymmetric\"]))", "SISSO_SISterm_residuals_train_feat_123": "((df[\"CrystalNNFingerprint_mean_L-shaped_CN_2\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / np.abs(df[\"DensityFeatures_packing_fraction\"] - df[\"GlobalSymmetryFeatures_is_centrosymmetric\"]))", "SISSO_SISterm_residuals_train_feat_124": "((df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]**3) / (df[\"DensityFeatures_packing_fraction\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_train_feat_125": "((df[\"OFM:_p^5_-_f^14\"] * df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]) / (df[\"OFM:_p^4_-_p^5\"] + df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_train_feat_126": "((df[\"OFM:_p^5_-_f^14\"] * df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]) / (df[\"OFM:_p^4_-_p^5\"] + df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_train_feat_127": "((df[\"OFM:_p^5_-_f^14\"] * df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]) / (df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_train_feat_128": "((df[\"OFM:_p^5_-_f^14\"] * df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]) / (df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_train_feat_129": "(np.abs(df[\"OFM:_p^5_-_f^14\"] - df[\"VoronoiFingerprint_std_dev_Symmetry_index_5\"]) / np.abs(df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_train_feat_130": "((df[\"CrystalNNFingerprint_mean_L-shaped_CN_2\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / np.abs(df[\"DensityFeatures_packing_fraction\"] - df[\"GlobalSymmetryFeatures_crystal_system_int\"]))", "SISSO_SISterm_residuals_train_feat_131": "((np.sqrt(df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / np.abs(df[\"OFM:_d^10_-_p^5\"] - df[\"ElementProperty_MagpieData_mean_NdValence\"])))", "SISSO_SISterm_residuals_train_feat_132": "((np.sqrt(df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]) / np.abs(df[\"ValenceOrbital_frac_d_valence_electrons\"] - df[\"OFM:_d^10_-_p^5\"])))", "SISSO_SISterm_residuals_train_feat_133": "((np.sqrt(df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / np.abs(df[\"ValenceOrbital_frac_d_valence_electrons\"] - df[\"OFM:_d^10_-_p^5\"])))", "SISSO_SISterm_residuals_train_feat_134": "((np.cbrt(df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / np.abs(df[\"OFM:_p^5_-_d^10\"] - df[\"ValenceOrbital_avg_d_valence_electrons\"])))", "SISSO_SISterm_residuals_train_feat_135": "((np.cbrt(df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / np.abs(df[\"OFM:_p^5_-_d^10\"] - df[\"ValenceOrbital_frac_d_valence_electrons\"])))", "SISSO_SISterm_residuals_train_feat_136": "(np.abs((df[\"ElementProperty_MagpieData_range_NpValence\"] * df[\"Stoichiometry_2-norm\"]) - (df[\"VoronoiFingerprint_mean_Voro_index_3\"] * df[\"GlobalSymmetryFeatures_is_centrosymmetric\"])))", "SISSO_SISterm_residuals_train_feat_137": "((df[\"OFM:_p^5_-_f^14\"] + df[\"VoronoiFingerprint_std_dev_Voro_index_5\"]) / (df[\"OFM:_p^4_-_p^5\"] + df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_train_feat_138": "((df[\"OFM:_p^5_-_f^14\"] - df[\"VoronoiFingerprint_std_dev_Voro_index_5\"]) / (df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_train_feat_139": "((np.cbrt(df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / np.abs(df[\"OFM:_d^10_-_p^5\"] - df[\"ElementProperty_MagpieData_mean_NdValence\"])))", "SISSO_SISterm_residuals_train_feat_140": "((np.cbrt(df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / np.abs(df[\"ValenceOrbital_frac_d_valence_electrons\"] - df[\"OFM:_d^10_-_p^5\"])))", "SISSO_SISterm_residuals_train_feat_141": "((df[\"GlobalSymmetryFeatures_is_centrosymmetric\"] / df[\"YangSolidSolution_Yang_omega\"]) / (df[\"ElementProperty_MagpieData_minimum_NValence\"] + df[\"BandCenter_band_center\"]))", "SISSO_SISterm_residuals_train_feat_142": "((df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / (df[\"ElementProperty_MagpieData_mean_MendeleevNumber\"]**6))", "SISSO_SISterm_residuals_train_feat_143": "((df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"] / df[\"YangSolidSolution_Yang_omega\"]) / (df[\"DensityFeatures_packing_fraction\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_train_feat_144": "((df[\"OFM:_p^5_-_f^14\"] / df[\"OFM:_s^2_-_p^5\"]) / np.cbrt(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_train_feat_145": "(np.abs(df[\"OFM:_p^5_-_f^14\"] - df[\"VoronoiFingerprint_std_dev_Voro_index_5\"]) / np.abs(df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_train_feat_146": "((df[\"GlobalSymmetryFeatures_is_centrosymmetric\"] / df[\"YangSolidSolution_Yang_omega\"]) / (df[\"ElementProperty_MagpieData_minimum_Column\"] + df[\"BandCenter_band_center\"]))", "SISSO_SISterm_residuals_train_feat_147": "((df[\"GlobalSymmetryFeatures_is_centrosymmetric\"] / df[\"YangSolidSolution_Yang_omega\"]) / (df[\"ElementProperty_MagpieData_minimum_Column\"] - df[\"ElementProperty_MagpieData_mean_Electronegativity\"]))", "SISSO_SISterm_residuals_train_feat_148": "(np.abs(df[\"OFM:_p^5_-_f^14\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]) / np.abs(df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_train_feat_149": "((df[\"OFM:_p^5_-_f^14\"] / df[\"OFM:_p^4_-_p^5\"]) / np.cbrt(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_train_feat_150": "((df[\"ElementProperty_MagpieData_maximum_NpValence\"] - df[\"Stoichiometry_0-norm\"]) * (df[\"GlobalSymmetryFeatures_is_centrosymmetric\"] - df[\"Stoichiometry_7-norm\"]))", "SISSO_SISterm_residuals_train_feat_151": "((df[\"ElementProperty_MagpieData_maximum_NpValence\"] - df[\"Stoichiometry_0-norm\"]) * (df[\"GlobalSymmetryFeatures_is_centrosymmetric\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_train_feat_152": "((df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"] / df[\"GlobalSymmetryFeatures_crystal_system_int\"]) / np.cbrt(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_train_feat_153": "((df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"] / df[\"ElementProperty_MagpieData_range_NpValence\"]) / np.sqrt(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_train_feat_154": "((df[\"CrystalNNFingerprint_mean_L-shaped_CN_2\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / np.abs(df[\"DensityFeatures_packing_fraction\"] - df[\"ElementProperty_MagpieData_minimum_NValence\"]))", "SISSO_SISterm_residuals_train_feat_155": "((df[\"CrystalNNFingerprint_std_dev_L-shaped_CN_2\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / np.abs(df[\"DensityFeatures_packing_fraction\"] - df[\"ElementProperty_MagpieData_minimum_NValence\"]))", "SISSO_SISterm_residuals_train_feat_156": "((df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"] / df[\"DensityFeatures_packing_fraction\"]) / np.cbrt(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_train_feat_157": "(np.abs(df[\"ElementProperty_MagpieData_range_NpValence\"] - df[\"GlobalSymmetryFeatures_crystal_system_int\"]) * (df[\"VoronoiFingerprint_mean_Voro_index_6\"] / df[\"ElementProperty_MagpieData_minimum_Column\"]))", "SISSO_SISterm_residuals_train_feat_158": "((df[\"ElementProperty_MagpieData_maximum_NpValence\"] - df[\"Stoichiometry_0-norm\"]) / (df[\"VoronoiFingerprint_mean_Symmetry_index_6\"] - df[\"GlobalSymmetryFeatures_is_centrosymmetric\"]))", "SISSO_SISterm_residuals_train_feat_159": "((df[\"VoronoiFingerprint_std_dev_Symmetry_index_5\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]))", "SISSO_SISterm_residuals_train_feat_160": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]))", "SISSO_SISterm_residuals_train_feat_161": "(np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]) / (df[\"OFM:_s^2_-_p^5\"] + df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_train_feat_162": "((df[\"OFM:_p^5_-_f^14\"] + df[\"VoronoiFingerprint_mean_Voro_index_6\"]) / (df[\"OFM:_p^4_-_p^5\"] + df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_train_feat_163": "((df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"] / df[\"YangSolidSolution_Yang_omega\"]) / np.abs(df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_train_feat_164": "((df[\"CrystalNNFingerprint_std_dev_L-shaped_CN_2\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / (df[\"DensityFeatures_packing_fraction\"] - df[\"GlobalSymmetryFeatures_is_centrosymmetric\"]))", "SISSO_SISterm_residuals_train_feat_165": "((df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"] / df[\"YangSolidSolution_Yang_omega\"]) / np.abs(df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_train_feat_166": "((df[\"CrystalNNFingerprint_mean_L-shaped_CN_2\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / (df[\"DensityFeatures_packing_fraction\"] - df[\"GlobalSymmetryFeatures_is_centrosymmetric\"]))", "SISSO_SISterm_residuals_train_feat_167": "(np.abs(df[\"GlobalSymmetryFeatures_crystal_system_int\"] - df[\"ElementProperty_MagpieData_maximum_NpValence\"]) * (df[\"VoronoiFingerprint_mean_Symmetry_index_6\"] / df[\"ElementProperty_MagpieData_minimum_NValence\"]))", "SISSO_SISterm_residuals_train_feat_168": "((df[\"OFM:_p^5_-_f^14\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]) / (df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_train_feat_169": "((df[\"CrystalNNFingerprint_mean_L-shaped_CN_2\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / (df[\"DensityFeatures_packing_fraction\"] - df[\"GlobalSymmetryFeatures_crystal_system_int\"]))", "SISSO_SISterm_residuals_train_feat_170": "((df[\"CrystalNNFingerprint_mean_L-shaped_CN_2\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / np.log(df[\"DensityFeatures_packing_fraction\"]))", "SISSO_SISterm_residuals_train_feat_171": "((df[\"CrystalNNFingerprint_std_dev_L-shaped_CN_2\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / np.log(df[\"DensityFeatures_packing_fraction\"]))", "SISSO_SISterm_residuals_train_feat_172": "((df[\"VoronoiFingerprint_mean_Voro_index_6\"] / df[\"ElementProperty_MagpieData_minimum_NValence\"]) * np.abs(df[\"GlobalSymmetryFeatures_crystal_system_int\"] - df[\"ElementProperty_MagpieData_maximum_NpValence\"]))", "SISSO_SISterm_residuals_train_feat_173": "((df[\"OFM:_p^5_-_f^14\"] / df[\"YangSolidSolution_Yang_omega\"]) / (df[\"OFM:_p^4_-_p^5\"] + df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_train_feat_174": "((df[\"OFM:_p^5_-_f^14\"] / df[\"YangSolidSolution_Yang_omega\"]) / (df[\"OFM:_p^4_-_p^5\"] + df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_train_feat_175": "((df[\"OFM:_p^5_-_f^14\"] / df[\"YangSolidSolution_Yang_omega\"]) / (df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_train_feat_176": "((df[\"OFM:_p^5_-_f^14\"] / df[\"YangSolidSolution_Yang_omega\"]) / (df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_train_feat_177": "((df[\"VoronoiFingerprint_mean_Symmetry_index_6\"] * df[\"GlobalSymmetryFeatures_is_centrosymmetric\"]) + (df[\"Stoichiometry_0-norm\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"]))", "SISSO_SISterm_residuals_train_feat_178": "(np.abs(df[\"ElementProperty_MagpieData_range_NpValence\"] - df[\"GlobalSymmetryFeatures_crystal_system_int\"]) * (df[\"VoronoiFingerprint_mean_Symmetry_index_6\"] / df[\"ElementProperty_MagpieData_minimum_NValence\"]))", "SISSO_SISterm_residuals_train_feat_179": "(np.abs(df[\"ElementProperty_MagpieData_range_NpValence\"] - df[\"GlobalSymmetryFeatures_crystal_system_int\"]) * (df[\"VoronoiFingerprint_mean_Voro_index_6\"] / df[\"ElementProperty_MagpieData_minimum_NValence\"]))", "SISSO_bestmodel_residuals_test_term_01": "(-1.323434474511454) * ((df[\"CrystalNNFingerprint_std_dev_L-shaped_CN_2\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) * (df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]**6))", "SISSO_bestmodel_residuals_test_term_02": "(0.01262486924060857) * ((df[\"ElementFraction_F\"] - df[\"OFM:_s^2_-_p^5\"]) / (df[\"VoronoiFingerprint_mean_Symmetry_index_3\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_bestmodel_residuals_test_term_03": "(0.04701055636199612) * (np.abs((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] + df[\"Stoichiometry_10-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_bestmodel_residuals_test_term_04": "(0.05323481721491959) * ((df[\"OFM:_p^5_-_f^14\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_bestmodel_residuals_test_term_05": "(-0.1301175149987) * ((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] + df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_bestmodel_residuals_test_full": "(-0.02588172351678976) + ((-1.323434474511454) * ((df[\"CrystalNNFingerprint_std_dev_L-shaped_CN_2\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) * (df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]**6))) + ((0.01262486924060857) * ((df[\"ElementFraction_F\"] - df[\"OFM:_s^2_-_p^5\"]) / (df[\"VoronoiFingerprint_mean_Symmetry_index_3\"] - df[\"Stoichiometry_10-norm\"]))) + ((0.04701055636199612) * (np.abs((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] + df[\"Stoichiometry_10-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))) + ((0.05323481721491959) * ((df[\"OFM:_p^5_-_f^14\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))) + ((-0.1301175149987) * ((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] + df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"])))", "SISSO_SISterm_residuals_test_feat_000": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] + df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_test_feat_001": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] + df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_test_feat_002": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]**2) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] + df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_test_feat_003": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / np.abs(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"ElementProperty_MagpieData_mean_NdValence\"]))", "SISSO_SISterm_residuals_test_feat_004": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / np.abs(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"ElementProperty_MagpieData_mean_NdValence\"]))", "SISSO_SISterm_residuals_test_feat_005": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]**2) / np.abs(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"ElementProperty_MagpieData_mean_NdValence\"]))", "SISSO_SISterm_residuals_test_feat_006": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] + df[\"ElementProperty_MagpieData_mean_NdValence\"]))", "SISSO_SISterm_residuals_test_feat_007": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] + df[\"ElementProperty_MagpieData_mean_NdValence\"]))", "SISSO_SISterm_residuals_test_feat_008": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]**2) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] + df[\"ElementProperty_MagpieData_mean_NdValence\"]))", "SISSO_SISterm_residuals_test_feat_009": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]**2) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"ValenceOrbital_avg_d_valence_electrons\"]))", "SISSO_SISterm_residuals_test_feat_010": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"ValenceOrbital_avg_d_valence_electrons\"]))", "SISSO_SISterm_residuals_test_feat_011": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"] - df[\"ValenceOrbital_avg_d_valence_electrons\"]))", "SISSO_SISterm_residuals_test_feat_012": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]**2) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] - df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_test_feat_013": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] - df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_test_feat_014": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] - df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_test_feat_015": "((df[\"OFM:_p^5_-_f^14\"] * df[\"ValenceOrbital_frac_d_valence_electrons\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_016": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"ValenceOrbital_avg_d_valence_electrons\"]) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_017": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"ValenceOrbital_avg_d_valence_electrons\"]) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_018": "((df[\"ElementProperty_MagpieData_minimum_Column\"] + df[\"BandCenter_band_center\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_019": "((df[\"OFM:_p^5_-_f^14\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_020": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"OFM:_d^10_-_p^5\"]) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_021": "((df[\"OFM:_p^5_-_f^14\"] * df[\"ElementProperty_MagpieData_minimum_Column\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_022": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"ValenceOrbital_avg_d_valence_electrons\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_023": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"ValenceOrbital_avg_d_valence_electrons\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_024": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"OFM:_p^5_-_d^10\"]) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_025": "((df[\"OFM:_p^5_-_f^14\"] * df[\"OFM:_d^10_-_p^5\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_026": "((df[\"OFM:_p^5_-_f^14\"] * df[\"OFM:_p^5_-_d^10\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_027": "((df[\"OFM:_p^5_-_f^14\"] * df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_028": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"OFM:_p^5_-_d^10\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_029": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] * df[\"OFM:_d^10_-_p^5\"]) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_030": "(np.abs(np.abs(df[\"CrystalNNFingerprint_std_dev_L-shaped_CN_2\"] - df[\"Stoichiometry_7-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_test_feat_031": "(np.abs((df[\"VoronoiFingerprint_std_dev_Symmetry_index_5\"] + df[\"Stoichiometry_10-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_test_feat_032": "(np.abs(np.abs(df[\"OFM:_p^4_-_p^5\"] - df[\"Stoichiometry_5-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_test_feat_033": "(np.abs((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] + df[\"Stoichiometry_5-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_test_feat_034": "(np.abs(np.abs(df[\"OFM:_p^5_-_f^14\"] - df[\"Stoichiometry_5-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_test_feat_035": "(np.abs((df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"]) - np.abs(df[\"OFM:_p^5_-_p^5\"] - df[\"Stoichiometry_10-norm\"])))", "SISSO_SISterm_residuals_test_feat_036": "(np.abs((df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"]) - np.abs(df[\"OFM:_p^5_-_p^5\"] - df[\"Stoichiometry_7-norm\"])))", "SISSO_SISterm_residuals_test_feat_037": "(np.abs((df[\"ElementProperty_MagpieData_range_NpValence\"] - df[\"Stoichiometry_0-norm\"]) - np.abs(df[\"GlobalSymmetryFeatures_crystal_system_int\"] - df[\"VoronoiFingerprint_mean_Voro_index_5\"])))", "SISSO_SISterm_residuals_test_feat_038": "(np.abs((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] + df[\"Stoichiometry_7-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_test_feat_039": "(np.abs((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] + df[\"Stoichiometry_10-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_test_feat_040": "(np.abs(np.abs(df[\"OFM:_p^5_-_d^10\"] - df[\"Stoichiometry_10-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_test_feat_041": "(np.abs(np.abs(df[\"OFM:_p^5_-_d^10\"] - df[\"Stoichiometry_7-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_test_feat_042": "(np.abs(np.abs(df[\"OFM:_d^10_-_p^5\"] - df[\"Stoichiometry_7-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_test_feat_043": "(np.abs(np.abs(df[\"OFM:_d^10_-_p^5\"] - df[\"Stoichiometry_3-norm\"]) - (df[\"GlobalSymmetryFeatures_crystal_system_int\"] / df[\"ElementProperty_MagpieData_maximum_NpValence\"])))", "SISSO_SISterm_residuals_test_feat_044": "((df[\"GlobalSymmetryFeatures_spacegroup_num\"] / df[\"DensityFeatures_packing_fraction\"]) * (df[\"ElementProperty_MagpieData_mean_Electronegativity\"] + df[\"BandCenter_band_center\"]))", "SISSO_SISterm_residuals_test_feat_045": "((df[\"VoronoiFingerprint_mean_Voro_index_5\"]**6) / (df[\"DensityFeatures_packing_fraction\"] - df[\"Stoichiometry_3-norm\"]))", "SISSO_SISterm_residuals_test_feat_046": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"]**6) / np.abs(df[\"DensityFeatures_packing_fraction\"] - df[\"Stoichiometry_3-norm\"]))", "SISSO_SISterm_residuals_test_feat_047": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"]**6) / (df[\"DensityFeatures_packing_fraction\"] - df[\"Stoichiometry_3-norm\"]))", "SISSO_SISterm_residuals_test_feat_048": "((df[\"VoronoiFingerprint_mean_Voro_index_5\"]**6) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_5-norm\"]))", "SISSO_SISterm_residuals_test_feat_049": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"]**6) / np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_5-norm\"]))", "SISSO_SISterm_residuals_test_feat_050": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"VoronoiFingerprint_mean_Voro_index_5\"]) / (df[\"OFM:_p^5_-_s^2\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_test_feat_051": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"]**6) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_5-norm\"]))", "SISSO_SISterm_residuals_test_feat_052": "((df[\"VoronoiFingerprint_mean_Voro_index_5\"]**6) / (df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_5-norm\"]))", "SISSO_SISterm_residuals_test_feat_053": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"VoronoiFingerprint_mean_Symmetry_index_5\"]) / (df[\"OFM:_p^5_-_s^2\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_test_feat_054": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] - df[\"OFM:_p^5_-_d^10\"]) / np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_test_feat_055": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] - df[\"OFM:_d^10_-_p^5\"]) / np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_test_feat_056": "((df[\"ElementFraction_F\"] - df[\"OFM:_s^2_-_p^5\"]) / (df[\"VoronoiFingerprint_mean_Symmetry_index_3\"] - df[\"Stoichiometry_7-norm\"]))", "SISSO_SISterm_residuals_test_feat_057": "((df[\"ElementFraction_F\"] - df[\"OFM:_s^2_-_p^5\"]) / np.abs(df[\"VoronoiFingerprint_mean_Symmetry_index_3\"] - df[\"Stoichiometry_7-norm\"]))", "SISSO_SISterm_residuals_test_feat_058": "((df[\"ElementFraction_F\"] - df[\"OFM:_p^5_-_s^2\"]) / (df[\"VoronoiFingerprint_mean_Symmetry_index_3\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_059": "((df[\"ElementFraction_F\"] - df[\"OFM:_s^2_-_p^5\"]) / (df[\"VoronoiFingerprint_mean_Symmetry_index_3\"] - df[\"Stoichiometry_10-norm\"]))", "SISSO_SISterm_residuals_test_feat_060": "(np.abs(df[\"OFM:_p^5_-_f^14\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]) / np.abs(df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_test_feat_061": "(np.abs(df[\"OFM:_p^5_-_f^14\"] - df[\"VoronoiFingerprint_std_dev_Symmetry_index_5\"]) / np.abs(df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"]))", "SISSO_SISterm_residuals_test_feat_062": "((df[\"OFM:_p^5_-_f^14\"] + df[\"VoronoiFingerprint_std_dev_Voro_index_5\"]) / (df[\"OFM:_p^4_-_p^5\"] + df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_test_feat_063": "((df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"] + df[\"VoronoiFingerprint_mean_Voro_index_5\"]) / np.abs(df[\"VoronoiFingerprint_mean_Symmetry_index_6\"] - df[\"OFM:_p^5_-_p^5\"]))", "SISSO_SISterm_residuals_test_feat_064": "((df[\"OFM:_p^5_-_f^14\"] - df[\"VoronoiFingerprint_std_dev_Voro_index_5\"]) / (df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_test_feat_065": "(np.abs(df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"] - df[\"VoronoiFingerprint_mean_Voro_index_5\"]) / np.abs(df[\"VoronoiFingerprint_mean_Symmetry_index_6\"] - df[\"OFM:_p^5_-_p^5\"]))", "SISSO_SISterm_residuals_test_feat_066": "((df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"] - df[\"VoronoiFingerprint_mean_Voro_index_5\"]) / (df[\"VoronoiFingerprint_mean_Symmetry_index_6\"] - df[\"OFM:_p^5_-_p^5\"]))", "SISSO_SISterm_residuals_test_feat_067": "(np.abs(df[\"OFM:_p^5_-_f^14\"] - df[\"VoronoiFingerprint_std_dev_Voro_index_5\"]) / np.abs(df[\"OFM:_p^4_-_p^5\"] - df[\"VoronoiFingerprint_mean_Voro_index_6\"]))", "SISSO_SISterm_residuals_test_feat_068": "((df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"] + df[\"VoronoiFingerprint_std_dev_Voro_index_5\"]) / np.abs(df[\"VoronoiFingerprint_mean_Symmetry_index_6\"] - df[\"OFM:_p^5_-_p^5\"]))", "SISSO_SISterm_residuals_test_feat_069": "(np.abs(df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"] - df[\"VoronoiFingerprint_std_dev_Voro_index_5\"]) / np.abs(df[\"VoronoiFingerprint_mean_Symmetry_index_6\"] - df[\"OFM:_p^5_-_p^5\"]))", "SISSO_SISterm_residuals_test_feat_070": "((df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"] - df[\"VoronoiFingerprint_std_dev_Voro_index_5\"]) / (df[\"VoronoiFingerprint_mean_Symmetry_index_6\"] - df[\"OFM:_p^5_-_p^5\"]))", "SISSO_SISterm_residuals_test_feat_071": "((df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]**6) / np.sqrt(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_test_feat_072": "((df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]**6) / np.cbrt(df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_SISterm_residuals_test_feat_073": "((df[\"CrystalNNFingerprint_mean_L-shaped_CN_2\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) * (df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]**6))", "SISSO_SISterm_residuals_test_feat_074": "((df[\"CrystalNNFingerprint_std_dev_L-shaped_CN_2\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) * (df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]**6))", "SISSO_SISterm_residuals_test_feat_075": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) + (df[\"ElementProperty_MagpieData_mean_MendeleevNumber\"] * df[\"ValenceOrbital_avg_d_valence_electrons\"]))", "SISSO_SISterm_residuals_test_feat_076": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) + (df[\"ElementProperty_MagpieData_maximum_MendeleevNumber\"] * df[\"ElementProperty_MagpieData_mean_NdValence\"]))", "SISSO_SISterm_residuals_test_feat_077": "((df[\"YangSolidSolution_Yang_delta\"] * df[\"Stoichiometry_3-norm\"]) / np.abs(df[\"ElementProperty_MagpieData_minimum_Column\"] - df[\"ElementProperty_MagpieData_mean_Electronegativity\"]))", "SISSO_SISterm_residuals_test_feat_078": "((df[\"YangSolidSolution_Yang_delta\"] * df[\"Stoichiometry_10-norm\"]) / np.abs(df[\"ElementProperty_MagpieData_minimum_Column\"] - df[\"ElementProperty_MagpieData_mean_Electronegativity\"]))", "SISSO_SISterm_residuals_test_feat_079": "((df[\"YangSolidSolution_Yang_delta\"] * df[\"Stoichiometry_7-norm\"]) / np.abs(df[\"ElementProperty_MagpieData_minimum_Column\"] - df[\"ElementProperty_MagpieData_mean_Electronegativity\"]))", "SISSO_SISterm_residuals_test_feat_080": "((df[\"YangSolidSolution_Yang_delta\"] * df[\"Stoichiometry_5-norm\"]) / np.abs(df[\"ElementProperty_MagpieData_minimum_Column\"] - df[\"ElementProperty_MagpieData_mean_Electronegativity\"]))", "SISSO_SISterm_residuals_test_feat_081": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) + (df[\"ValenceOrbital_avg_d_valence_electrons\"] * df[\"ElementProperty_MagpieData_minimum_MeltingT\"]))", "SISSO_SISterm_residuals_test_feat_082": "((df[\"ElementProperty_MagpieData_minimum_Column\"] * df[\"YangSolidSolution_Yang_delta\"]) / np.abs(df[\"ElementProperty_MagpieData_minimum_Column\"] - df[\"ElementProperty_MagpieData_mean_Electronegativity\"]))", "SISSO_SISterm_residuals_test_feat_083": "((df[\"ElementProperty_MagpieData_minimum_NValence\"] * df[\"YangSolidSolution_Yang_delta\"]) / np.abs(df[\"ElementProperty_MagpieData_minimum_Column\"] - df[\"ElementProperty_MagpieData_mean_Electronegativity\"]))", "SISSO_SISterm_residuals_test_feat_084": "((df[\"YangSolidSolution_Yang_delta\"] / df[\"Stoichiometry_0-norm\"]) / np.abs(df[\"ElementProperty_MagpieData_minimum_Column\"] - df[\"ElementProperty_MagpieData_mean_Electronegativity\"]))", "SISSO_SISterm_residuals_test_feat_085": "(np.abs(df[\"ValenceOrbital_avg_d_valence_electrons\"] - df[\"ElementProperty_MagpieData_maximum_NpValence\"]) * (df[\"ElementProperty_MagpieData_minimum_NValence\"] * df[\"YangSolidSolution_Yang_delta\"]))", "SISSO_SISterm_residuals_test_feat_086": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / (df[\"OFM:_p^5_-_s^2\"] - df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]))", "SISSO_SISterm_residuals_test_feat_087": "((df[\"VoronoiFingerprint_std_dev_Symmetry_index_5\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / (df[\"OFM:_p^5_-_s^2\"] - df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]))", "SISSO_SISterm_residuals_test_feat_088": "((df[\"VoronoiFingerprint_std_dev_Symmetry_index_5\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]))", "SISSO_SISterm_residuals_test_feat_089": "((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"]))", "SISSO_SISterm_residuals_test_feat_090": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) * (df[\"VoronoiFingerprint_std_dev_Symmetry_index_5\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_5\"]))", "SISSO_SISterm_residuals_test_feat_091": "((df[\"ValenceOrbital_avg_d_valence_electrons\"] - df[\"ElementProperty_MagpieData_maximum_Electronegativity\"]) * (df[\"ElementProperty_MagpieData_minimum_NValence\"] * df[\"YangSolidSolution_Yang_delta\"]))", "SISSO_SISterm_residuals_test_feat_092": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / (df[\"ElementProperty_MagpieData_mean_Electronegativity\"] - df[\"VoronoiFingerprint_mean_Voro_index_5\"]))", "SISSO_SISterm_residuals_test_feat_093": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) / (df[\"ElementProperty_MagpieData_mean_Electronegativity\"] - df[\"VoronoiFingerprint_mean_Voro_index_5\"]))", "SISSO_SISterm_residuals_test_feat_094": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) * (df[\"VoronoiFingerprint_mean_Voro_index_5\"] - df[\"VoronoiFingerprint_std_dev_Voro_index_5\"]))", "SISSO_SISterm_residuals_test_feat_095": "((df[\"AtomicPackingEfficiency_mean_abs_simul__packing_efficiency\"] * df[\"GlobalSymmetryFeatures_is_centrosymmetric\"]) / (df[\"DensityFeatures_packing_fraction\"] - df[\"Stoichiometry_3-norm\"]))", "SISSO_SISterm_residuals_test_feat_096": "((df[\"DensityFeatures_packing_fraction\"] * df[\"ElementProperty_MagpieData_mean_NdValence\"]) / np.abs(df[\"DensityFeatures_packing_fraction\"] - df[\"GlobalSymmetryFeatures_is_centrosymmetric\"]))", "SISSO_SISterm_residuals_test_feat_097": "((df[\"GlobalSymmetryFeatures_is_centrosymmetric\"] / df[\"GlobalSymmetryFeatures_crystal_system_int\"]) / (df[\"DensityFeatures_packing_fraction\"] - df[\"Stoichiometry_3-norm\"]))", "SISSO_SISterm_residuals_test_feat_098": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) + (df[\"ValenceOrbital_avg_d_valence_electrons\"] * df[\"ElementProperty_MagpieData_minimum_MeltingT\"]))", "SISSO_SISterm_residuals_test_feat_099": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) + (df[\"ValenceOrbital_avg_d_valence_electrons\"] * df[\"ElementProperty_MagpieData_minimum_CovalentRadius\"]))", "SISSO_SISterm_residuals_test_feat_100": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) * (df[\"VoronoiFingerprint_mean_Voro_index_6\"] - df[\"ElementProperty_MagpieData_maximum_Electronegativity\"]))", "SISSO_SISterm_residuals_test_feat_101": "((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) * (df[\"VoronoiFingerprint_mean_Voro_index_6\"] - df[\"ElementProperty_MagpieData_maximum_Electronegativity\"]))", "SISSO_SISterm_residuals_test_feat_102": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) + (df[\"ElementProperty_MagpieData_maximum_MendeleevNumber\"] * df[\"ElementProperty_MagpieData_mean_NdValence\"]))", "SISSO_SISterm_residuals_test_feat_103": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) + (df[\"ElementProperty_MagpieData_mean_MendeleevNumber\"] * df[\"ValenceOrbital_avg_d_valence_electrons\"]))", "SISSO_SISterm_residuals_test_feat_104": "((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) + (df[\"ValenceOrbital_avg_d_valence_electrons\"] * df[\"ElementProperty_MagpieData_minimum_CovalentRadius\"]))"}