{"SISSO_dim5_term_01": "(-0.001483329697996118) * ((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] - df[\"OFM:_d^10_-_p^5\"]) / (np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"])))", "SISSO_dim5_term_02": "(-0.0005259817899481455) * ((df[\"GlobalSymmetryFeatures_spacegroup_num\"] / df[\"DensityFeatures_packing_fraction\"]) * (df[\"ElementProperty_MagpieData_mean_Electronegativity\"] + df[\"BandCenter_band_center\"]))", "SISSO_dim5_term_03": "(-0.0008559240534673412) * ((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"ElementProperty_MagpieData_minimum_Column\"]) / (np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"])))", "SISSO_dim5_term_04": "(-0.08216630808328743) * ((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] + df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))", "SISSO_dim5_term_05": "(-0.0001476840423358978) * ((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) * (df[\"DensityFeatures_packing_fraction\"]**6))", "SISSO_dim5_full": "(0.01245014884892721) + ((-0.001483329697996118) * ((df[\"VoronoiFingerprint_mean_Symmetry_index_5\"] - df[\"OFM:_d^10_-_p^5\"]) / (np.abs(df[\"OFM:_p^5_-_s^2\"] - df[\"VoronoiFingerprint_mean_Symmetry_index_6\"])))) + ((-0.0005259817899481455) * ((df[\"GlobalSymmetryFeatures_spacegroup_num\"] / df[\"DensityFeatures_packing_fraction\"]) * (df[\"ElementProperty_MagpieData_mean_Electronegativity\"] + df[\"BandCenter_band_center\"]))) + ((-0.0008559240534673412) * ((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"] * df[\"ElementProperty_MagpieData_minimum_Column\"]) / (np.abs(df[\"OFM:_s^2_-_p^5\"] - df[\"Stoichiometry_10-norm\"])))) + ((-0.08216630808328743) * ((df[\"CrystalNNFingerprint_mean_T-shaped_CN_3\"]**2) / (df[\"ValenceOrbital_frac_d_valence_electrons\"] + df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]))) + ((-0.0001476840423358978) * ((df[\"CrystalNNFingerprint_std_dev_T-shaped_CN_3\"] / df[\"AGNIFingerPrint_std_dev_AGNI_dir=x_eta=8_00e-01\"]) * (df[\"DensityFeatures_packing_fraction\"]**6)))"}