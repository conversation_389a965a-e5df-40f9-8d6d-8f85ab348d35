# FEAT_ID     Score                   Feature Expression
0             0.251987293094250719    ((feature_50 * feature_48) / (|feature_31 - feature_7|))
1             0.25198774952714631     ((feature_48^2) / (|feature_31 - feature_7|))
2             0.251997663933965887    ((feature_50^2) / (feature_31 + feature_7))
3             0.251998315944182982    ((feature_50 * feature_48) / (feature_31 + feature_7))
4             0.251998746437150423    ((feature_48^2) / (feature_31 + feature_7))
5             0.252259786435241218    ((feature_50^2) * (feature_30 / feature_31))
6             0.252498634399059729    ((feature_50^2) / (feature_31 - feature_7))
7             0.252498857693776613    ((feature_50 * feature_48) / (feature_31 - feature_7))
8             0.252498910923351416    ((feature_48^2) / (feature_31 - feature_7))
9             0.260526280453517334    ((feature_50 / feature_31) * (feature_37^3))
10            0.260526618142598865    ((feature_48 / feature_31) * (feature_37^3))
11            0.275370120870489732    ((feature_48 / feature_31) * (feature_37^6))
12            0.294167853211140695    ((feature_50^2) / (|feature_36 - feature_31|))
13            0.294170805395047585    ((feature_50 * feature_48) / (|feature_36 - feature_31|))
14            0.294172781990857612    ((feature_48^2) / (|feature_36 - feature_31|))
15            0.29426129337138518     ((feature_50^2) / (feature_36 + feature_31))
16            0.294263988524273545    ((feature_50 * feature_48) / (feature_36 + feature_31))
17            0.294265805698592398    ((feature_48^2) / (feature_36 + feature_31))
18            0.29493208366386775     ((feature_50^2) / (|feature_31 - feature_29|))
19            0.294932387356212966    ((feature_50 * feature_48) / (|feature_31 - feature_29|))
20            0.294932610541420392    ((feature_48^2) / (|feature_31 - feature_29|))
21            0.294933729694508318    ((feature_50^2) / (feature_31 + feature_28))
22            0.294934029478214177    ((feature_50 * feature_48) / (feature_31 + feature_28))
23            0.294934249944932891    ((feature_48^2) / (feature_31 + feature_28))
24            0.29510516538070708     ((feature_48^2) / (feature_31 - feature_28))
25            0.295105336457627432    ((feature_50 * feature_48) / (feature_31 - feature_28))
26            0.295105545832337168    ((feature_50^2) / (feature_31 - feature_28))
27            0.295597702345690327    ((feature_48^2) / (feature_36 - feature_31))
28            0.2955980903429134      ((feature_50 * feature_48) / (feature_36 - feature_31))
29            0.295598163040684869    ((feature_50^2) / (feature_36 - feature_31))
#-----------------------------------------------------------------------
30            0.0915918019659552884   ((feature_48 * feature_33) / (|feature_25 - feature_3|))
31            0.0915918934350754554   ((feature_48 * feature_15) / (|feature_25 - feature_3|))
32            0.0916224616043176399   ((feature_48 - feature_47) / (feature_25 - feature_3))
33            0.0916248841133855407   ((feature_49 * feature_40) / (|feature_25 - feature_3|))
34            0.0916336935470428299   ((feature_50 - feature_46) / (feature_25 - feature_3))
35            0.091654436801020478    ((feature_49 * feature_36) / (feature_25 - feature_3))
36            0.0916587324754188915   ((feature_50 * feature_40) / (|feature_25 - feature_3|))
37            0.0916588378983685947   ((feature_48 * feature_40) / (|feature_25 - feature_3|))
38            0.0916639192253735219   ((feature_49 * feature_29) / (feature_25 - feature_3))
39            0.0916654134047898      ((feature_40 - feature_9) / (feature_25 - feature_3))
40            0.0916663015006596305   ((|feature_48 - feature_7|) / (|feature_25 - feature_3|))
41            0.0916716818242453296   ((feature_48 * feature_38) / (|feature_25 - feature_3|))
42            0.0916809562804187933   ((feature_49 * feature_33) / (feature_25 - feature_3))
43            0.0917042076104706561   ((feature_40 - feature_16) / (feature_25 - feature_3))
44            0.0917225634781462101   ((feature_49 * feature_38) / (feature_25 - feature_3))
45            0.0917234021291132018   ((feature_49 * feature_15) / (feature_25 - feature_3))
46            0.091739363337673685    (exp(feature_15) / (feature_25 - feature_3))
47            0.0917478952148056554   ((|feature_48 - feature_7|) / (feature_25 - feature_3))
48            0.091903170937018025    ((feature_50 * feature_29) / (feature_25 - feature_3))
49            0.091903374279181424    ((feature_48 * feature_28) / (feature_25 - feature_3))
50            0.0919134296310050031   ((feature_50 * feature_15) / (feature_25 - feature_3))
51            0.0919135824264068896   ((feature_48 * feature_15) / (feature_25 - feature_3))
52            0.0920307305237775541   ((feature_49 * feature_48) / (|feature_25 - feature_3|))
53            0.0920703955497495369   ((feature_40 + feature_11) / (feature_25 - feature_3))
54            0.092285505472280191    ((feature_49 * feature_40) / (feature_25 - feature_3))
55            0.0922987265201440366   ((feature_50 * feature_40) / (feature_25 - feature_3))
56            0.0922988422029598909   ((feature_48 * feature_40) / (feature_25 - feature_3))
57            0.0925366954366702715   ((feature_48 * feature_38) / (feature_25 - feature_3))
58            0.0925372779267746165   ((feature_48 * feature_33) / (feature_25 - feature_3))
59            0.0927999091237398377   ((feature_49 * feature_48) / (feature_25 - feature_3))
#-----------------------------------------------------------------------
60            0.0811327854106507068   ((feature_26 / feature_32) + (feature_20 - feature_6))
61            0.0812013136521481244   (|(feature_42 + feature_4) - (feature_21 / feature_20)|)
62            0.0812035860790943642   (|(feature_21 / feature_20) - feature_3|)
63            0.0813016189570209824   (|(feature_41 + feature_2) - (feature_21 / feature_12)|)
64            0.0813324998871150368   (|(feature_50 + feature_4) - (feature_21 / feature_20)|)
65            0.0813959483588158894   (|(|feature_39 - feature_1|) - (feature_21 / feature_20)|)
66            0.0814018647485088565   (|(feature_50 + feature_3) - (feature_21 / feature_20)|)
67            0.0814317067646651016   (|(|feature_31 - feature_4|) - (feature_21 / feature_20)|)
68            0.0815333745408481497   (|(|feature_31 - feature_3|) - (feature_21 / feature_20)|)
69            0.0815528720263259538   (|(feature_42 + feature_3) - (feature_21 / feature_20)|)
70            0.0816866321852405264   (|(feature_41 + feature_1) - (feature_21 / feature_12)|)
71            0.0818068909236178421   ((feature_26 / feature_37) * (feature_7^3))
72            0.081924243900238003    (|(|feature_46 - feature_3|) - (feature_21 / feature_20)|)
73            0.0823086098241104097   (|(feature_21 / feature_20) - (|feature_17 - feature_1|)|)
74            0.0823248959910695827   ((feature_43 - feature_6) - (|feature_21 - feature_10|))
75            0.0826421654166333841   (|(|feature_49 - feature_5|) - (feature_21 / feature_20)|)
76            0.0826868723564918168   (|(feature_21 / feature_20) - (|feature_17 - feature_4|)|)
77            0.0827492906689261676   (|(|feature_49 - feature_3|) - (feature_21 / feature_20)|)
78            0.0827763986909428734   ((feature_32 / feature_20) + (feature_21 * feature_6))
79            0.0827903578621097674   (|(|feature_49 - feature_4|) - (feature_21 / feature_20)|)
80            0.0831610333364492665   (|(feature_41 + feature_5) - (feature_21 / feature_20)|)
81            0.0833910468506610103   (|(feature_43 - feature_6) - (|feature_21 - feature_10|)|)
82            0.0839058403623594712   (|(|feature_38 - feature_4|) - (feature_21 / feature_20)|)
83            0.0839343235598117854   (|(feature_41 + feature_4) - (feature_21 / feature_20)|)
84            0.0839909575339132797   (|(|feature_38 - feature_1|) - (feature_21 / feature_20)|)
85            0.0840384874910154872   (|(|feature_33 - feature_1|) - (feature_21 / feature_20)|)
86            0.0841633893377251308   (|(|feature_33 - feature_5|) - (feature_21 / feature_20)|)
87            0.084176074944402865    (|(|feature_38 - feature_5|) - (feature_21 / feature_20)|)
88            0.0842185015986109398   (|(feature_41 + feature_3) - (feature_21 / feature_20)|)
89            0.0914721324120552071   ((feature_26 / feature_37) * (feature_16 + feature_11))
#-----------------------------------------------------------------------
90            0.0355813203571724232   ((feature_48 * feature_17) / (feature_37 - feature_1))
91            0.0356150985657396543   ((|feature_41 - feature_38|) / (|feature_27 - feature_18|))
92            0.0358009941410599858   ((feature_23 - feature_21) / (feature_25 - feature_5))
93            0.0358863011042016358   ((feature_10^3) / (feature_25 - feature_5))
94            0.0360112737920425599   ((feature_17^6) / (feature_37 - feature_1))
95            0.0360273741019480065   ((feature_48 * feature_39) / (|feature_37 - feature_1|))
96            0.0360745578806220962   ((feature_48 * feature_39) / (feature_37 - feature_1))
97            0.0361268065751164519   ((|feature_23 - feature_6|) / (feature_25 - feature_5))
98            0.036151628128821181    ((feature_50 * feature_42) / (feature_27 - feature_18))
99            0.036187231100775312    ((feature_17^6) / (|feature_37 - feature_1|))
100           0.0365072715482766957   ((feature_10^6) / (|feature_27 - feature_4|))
101           0.0365486610730792333   ((feature_10^6) / (|feature_37 - feature_1|))
102           0.0366662340675266152   ((feature_10^6) / (feature_37 - feature_1))
103           0.0366792953459000176   ((feature_18 - feature_1) / (feature_25 - feature_5))
104           0.0367290189528879513   ((feature_41^3) / (feature_25 - feature_5))
105           0.0367608543967573795   ((feature_41^6) / (|feature_37 - feature_1|))
106           0.0367943670036040907   ((|feature_18 - feature_1|) / (feature_25 - feature_5))
107           0.0368272072132279638   ((feature_50 * feature_10) / (feature_27 - feature_18))
108           0.0368291258058331891   ((feature_41^6) / (|feature_27 - feature_4|))
109           0.0368472951008513641   ((feature_10^6) / (feature_27 - feature_4))
110           0.0368498673571097599   ((feature_41^6) / (feature_37 - feature_1))
111           0.0370349806400813997   ((feature_41^6) / (feature_27 - feature_4))
112           0.037051991477237603    ((feature_42^6) / (feature_25 - feature_5))
113           0.0371322796546685041   ((feature_10^6) / (|feature_25 - feature_5|))
114           0.0371888984823597266   ((feature_50 * feature_41) / (feature_27 - feature_18))
115           0.0372242145039742012   ((feature_41^6) / (|feature_25 - feature_5|))
116           0.0374620621928950445   ((feature_41^6) / (feature_25 - feature_5))
117           0.0375302319771034301   ((feature_10^6) / (feature_25 - feature_5))
118           0.0388458797310531762   ((feature_41 - feature_38) / (|feature_27 - feature_18|))
119           0.039117008677343662    ((feature_41 - feature_33) / (|feature_27 - feature_18|))
#-----------------------------------------------------------------------
120           0.0215539484605281814   ((|feature_49 - feature_10|) / (|feature_39 - feature_23|))
121           0.0217374018495698797   (|(feature_34 * feature_8) - (feature_20 * feature_2)|)
122           0.0219475352119293601   ((feature_47 / feature_31) / (|feature_37 - feature_8|))
123           0.0219498098625226408   ((feature_46 / feature_31) / (|feature_37 - feature_8|))
124           0.0219616909968671642   ((feature_22^3) / (feature_37 - feature_23))
125           0.0220118778079185055   ((feature_49 * feature_22) / (feature_39 + feature_18))
126           0.0220505220671132207   ((feature_49 * feature_22) / (feature_39 + feature_23))
127           0.0220557532139085098   ((feature_49 * feature_22) / (feature_39 - feature_23))
128           0.0220934332039163933   ((feature_49 * feature_22) / (feature_39 - feature_18))
129           0.0221148758696655748   ((|feature_49 - feature_42|) / (|feature_39 - feature_18|))
130           0.0221832718663032315   ((feature_46 / feature_31) / (|feature_37 - feature_21|))
131           0.0222374695568420787   (sqrt(feature_48) / (|feature_33 - feature_28|))
132           0.0222715815405331967   (sqrt(feature_50) / (|feature_36 - feature_33|))
133           0.0222717339774665402   (sqrt(feature_48) / (|feature_36 - feature_33|))
134           0.0224297604037691466   (cbrt(feature_48) / (|feature_38 - feature_29|))
135           0.0224539465556738461   (cbrt(feature_48) / (|feature_38 - feature_36|))
136           0.0225782195812185282   (|(feature_43 * feature_2) - (feature_34 * feature_8)|)
137           0.0227431407513670955   ((feature_49 + feature_9) / (feature_39 + feature_23))
138           0.0227793535670368294   ((feature_49 - feature_9) / (feature_39 - feature_23))
139           0.0229738404038455045   (cbrt(feature_48) / (|feature_33 - feature_28|))
140           0.0230047414051015066   (cbrt(feature_48) / (|feature_36 - feature_33|))
141           0.0230932233812463847   ((feature_8 / feature_24) / (feature_15 + feature_11))
142           0.023525978794417389    ((feature_22 / feature_31) / (feature_32^6))
143           0.0237720694120616728   ((feature_22 / feature_24) / (feature_37 - feature_23))
144           0.0238241188260094397   ((feature_49 / feature_25) / cbrt(feature_31))
145           0.0241747205718031086   ((|feature_49 - feature_9|) / (|feature_39 - feature_23|))
146           0.0251828819492058847   ((feature_8 / feature_24) / (feature_40 + feature_11))
147           0.0260412737811626348   ((feature_8 / feature_24) / (feature_40 - feature_16))
148           0.0267023706141892268   ((|feature_49 - feature_23|) / (|feature_39 - feature_23|))
149           0.0279424105246273285   ((feature_49 / feature_39) / cbrt(feature_31))
#-----------------------------------------------------------------------
150           0.0203401366682116475   ((feature_20 - feature_6) * (feature_8 - feature_4))
151           0.0203659392433736541   ((feature_20 - feature_6) * (feature_8 - feature_3))
152           0.020432999353189582    ((feature_22 / feature_21) / cbrt(feature_31))
153           0.0204367328103852615   ((feature_22 / feature_43) / sqrt(feature_31))
154           0.0205284778846166263   ((feature_46 / feature_31) / (|feature_37 - feature_15|))
155           0.0205327275786361223   ((feature_47 / feature_31) / (|feature_37 - feature_15|))
156           0.0206271259278598858   ((feature_22 / feature_37) / cbrt(feature_31))
157           0.0206284889600833188   ((|feature_43 - feature_21|) * (feature_23 / feature_40))
158           0.0206709309816467054   ((feature_20 - feature_6) / (feature_18 - feature_8))
159           0.020836258052382553    ((feature_42 / feature_31) / (|feature_27 - feature_22|))
160           0.0208712464465032645   ((feature_41 / feature_31) / (|feature_27 - feature_22|))
161           0.0210234619224002606   ((|feature_27 - feature_23|) / (feature_25 + feature_23))
162           0.0212753586329985575   ((feature_49 + feature_23) / (feature_39 + feature_23))
163           0.0213776966469475284   ((feature_22 / feature_24) / (|feature_39 - feature_18|))
164           0.0213904003968981675   ((feature_47 / feature_31) / (feature_37 - feature_8))
165           0.0213927989758163281   ((feature_22 / feature_24) / (|feature_39 - feature_23|))
166           0.0213954546236145397   ((feature_46 / feature_31) / (feature_37 - feature_8))
167           0.0215962633012966101   ((|feature_21 - feature_20|) * (feature_18 / feature_15))
168           0.0216325757911869955   ((feature_49 - feature_23) / (feature_39 - feature_23))
169           0.0216354748990884443   ((feature_46 / feature_31) / (feature_37 - feature_21))
170           0.021731488215038907    ((feature_46 / feature_31) / ln(feature_37))
171           0.0217334093515447434   ((feature_47 / feature_31) / ln(feature_37))
172           0.0219767485612909381   ((feature_23 / feature_15) * (|feature_21 - feature_20|))
173           0.0219877862815880225   ((feature_49 / feature_24) / (feature_39 + feature_18))
174           0.0220051601239617811   ((feature_49 / feature_24) / (feature_39 + feature_23))
175           0.0220075439296033466   ((feature_49 / feature_24) / (feature_39 - feature_23))
176           0.0220245759330537395   ((feature_49 / feature_24) / (feature_39 - feature_18))
177           0.0221536963147707378   ((feature_18 * feature_8) + (feature_6 / feature_20))
178           0.0221923767747414652   ((|feature_43 - feature_21|) * (feature_18 / feature_15))
179           0.0225868517824576627   ((|feature_43 - feature_21|) * (feature_23 / feature_15))
#-----------------------------------------------------------------------
