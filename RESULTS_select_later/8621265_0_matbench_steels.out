Job started on <PERSON><PERSON> May 20 17:02:17 CEST 2025
Running on node(s): cns262
2025-05-20 17:02:26.673162: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 64 jobs
Preparing nested CV run for task 'matbench_steels_matminer_roost_lmp'
Found 6 matching files for matbench_steels_matminer_roost_lmp
Found multiple data files ['./precomputed/matbench_steels_matminer_roost_lmp_sisso_v2_featurizedMM2020Struct.pkl.gz', './precomputed/matbench_steels_matminer_roost_lmp_threshold50_featurizedMM2020Struct.pkl copy.gz', './precomputed/matbench_steels_matminer_roost_lmp_threshold30_featurizedMM2020Struct.pkl copy.gz', './precomputed/matbench_steels_matminer_roost_lmp_featurizedMM2020Struct.pkl.gz', './precomputed/matbench_steels_matminer_roost_lmp_threshold90_featurizedMM2020Struct.pkl.gz', './precomputed/matbench_steels_matminer_roost_lmp_threshold70_featurizedMM2020Struct.pkl copy.gz'], loading the first ./precomputed/matbench_steels_matminer_roost_lmp_featurizedMM2020Struct.pkl.gz
2025-05-20 17:02:33,012 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f59a7c03ee0> object, created with modnet version 0.1.13
GA settings:
    Population size: 50
    Number of generations: 20
    Early stopping: 8
    Refit: False
Preparing fold 1 ...
2025-05-20 17:02:33,036 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f59a7c132e0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_steels_matminer_roost_lmp_train_moddata_f1
2025-05-20 17:02:33,060 - modnet - INFO - Data successfully saved as folds/matbench_steels_matminer_roost_lmp_train_moddata_f1!
Preparing fold 2 ...
2025-05-20 17:02:33,079 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f59a7600be0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_steels_matminer_roost_lmp_train_moddata_f2
2025-05-20 17:02:33,201 - modnet - INFO - Data successfully saved as folds/matbench_steels_matminer_roost_lmp_train_moddata_f2!
Preparing fold 3 ...
2025-05-20 17:02:33,221 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f59a6bd7760> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_steels_matminer_roost_lmp_train_moddata_f3
2025-05-20 17:02:33,245 - modnet - INFO - Data successfully saved as folds/matbench_steels_matminer_roost_lmp_train_moddata_f3!
Preparing fold 4 ...
2025-05-20 17:02:33,265 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f59a710b4c0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_steels_matminer_roost_lmp_train_moddata_f4
2025-05-20 17:02:33,290 - modnet - INFO - Data successfully saved as folds/matbench_steels_matminer_roost_lmp_train_moddata_f4!
Preparing fold 5 ...
2025-05-20 17:02:33,314 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f59a6c00250> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_steels_matminer_roost_lmp_train_moddata_f5
2025-05-20 17:02:33,338 - modnet - INFO - Data successfully saved as folds/matbench_steels_matminer_roost_lmp_train_moddata_f5!
2025-05-20 17:02:33.348881: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-20 17:02:33.349349: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/8.4.1.50-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-20 17:02:33.349375: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-20 17:02:33.350464: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns262.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-20 17:02:33.350690: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
Training fold 1 ...
Processing fold 1 ...
2025-05-20 17:02:33,444 - modnet - INFO - Targets:
2025-05-20 17:02:33,444 - modnet - INFO - 1) target: regression
/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py:353: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  rf_model.fit(self.train_data.df_featurized, self.train_data.df_targets)
2025-05-20 17:02:36,022 - modnet - INFO - Multiprocessing on 64 cores. Total of 128 cores available.
2025-05-20 17:02:36,022 - modnet - INFO - Generation number 0
Population initialized with 100 individuals.
2025-05-20 17:02:36,029 - modnet - INFO - Initialized generation 0 with population size: 100

  0%|          | 0/500 [00:00<?, ?it/s]
  0%|          | 0/500 [00:09<?, ?it/s]
Traceback (most recent call last):
  File "run_benchmark.py", line 869, in <module>
    results = benchmark(data, settings, n_jobs=n_jobs, fast=fast)
  File "run_benchmark.py", line 146, in benchmark
    return matbench_benchmark(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 147, in matbench_benchmark
    fold_results.append(train_fold(fold, *args, **model_kwargs))
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 291, in train_fold
    model = ga.run(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 706, in run
    val_loss, models, individuals = self.function_fitness(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 485, in function_fitness
    individual.model._restore_model()
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/models/vanilla.py", line 707, in _restore_model
    self.model = tf.keras.models.model_from_json(model_json)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tensorflow/python/keras/saving/model_config.py", line 131, in model_from_json
    return deserialize(config, custom_objects=custom_objects)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tensorflow/python/keras/layers/serialization.py", line 173, in deserialize
    return generic_utils.deserialize_keras_object(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tensorflow/python/keras/utils/generic_utils.py", line 354, in deserialize_keras_object
    return cls.from_config(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tensorflow/python/keras/engine/functional.py", line 668, in from_config
    input_tensors, output_tensors, created_layers = reconstruct_from_config(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tensorflow/python/keras/engine/functional.py", line 1275, in reconstruct_from_config
    process_layer(layer_data)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tensorflow/python/keras/engine/functional.py", line 1257, in process_layer
    layer = deserialize_layer(layer_data, custom_objects=custom_objects)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tensorflow/python/keras/layers/serialization.py", line 173, in deserialize
    return generic_utils.deserialize_keras_object(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tensorflow/python/keras/utils/generic_utils.py", line 360, in deserialize_keras_object
    return cls.from_config(cls_config)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tensorflow/python/keras/engine/base_layer.py", line 720, in from_config
    return cls(**config)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tensorflow/python/keras/layers/core.py", line 1161, in __init__
    self.activation = activations.get(activation)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tensorflow/python/util/dispatch.py", line 201, in wrapper
    return target(*args, **kwargs)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tensorflow/python/keras/activations.py", line 573, in get
    return deserialize(identifier)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tensorflow/python/util/dispatch.py", line 201, in wrapper
    return target(*args, **kwargs)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tensorflow/python/keras/activations.py", line 532, in deserialize
    return deserialize_keras_object(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tensorflow/python/keras/utils/generic_utils.py", line 377, in deserialize_keras_object
    raise ValueError(
ValueError: Unknown activation function: leaky_relu
Job finished on Tue May 20 17:02:46 CEST 2025

Resources Used

Total Memory used                        - MEM              : 0B
Total CPU Time                           - CPU_Time         : 00:30:56
Execution Time                           - Wall_Time        : 00:00:29
total programme cpu time                 - Total_CPU        : 09:36.868
Total_CPU / CPU_Time  (%)                - ETA              : 31%
Number of alloc CPU                      - NCPUS            : 64
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 64
Mobilized Resources x Execution Time     - R_Wall_Time      : 00:30:56
CPU_Time / R_Wall_Time (%)               - ALPHA            : 100%
Energy (Joules)                                             : 8424

