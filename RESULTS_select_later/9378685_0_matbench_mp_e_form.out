Job started on Thu Jul  3 12:22:17 CEST 2025
Running on node(s): cns263
2025-07-03 12:22:37.473496: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 24 jobs
Preparing nested CV run for task 'matbench_mp_e_form_matminer_megnet_ofm_mvl_all_orb_v3'
Found 1 matching files for matbench_mp_e_form_matminer_megnet_ofm_mvl_all_orb_v3
2025-07-03 12:23:01,586 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f4f2628ea00> object, created with modnet version 0.1.13
GA settings:
    Population size: 50
    Number of generations: 20
    Early stopping: 8
    Refit: False
Preparing fold 1 ...
2025-07-03 12:23:02,241 - modnet - INFO - Multiprocessing on 24 workers.
2025-07-03 12:23:02,241 - modnet - INFO - Computing "self" MI (i.e. information entropy) of features

  0%|          | 0/800 [00:00<?, ?it/s]
  0%|          | 1/800 [00:04<58:55,  4.42s/it]
 21%|██        | 168/800 [00:04<00:12, 52.45it/s]
 63%|██████▎   | 501/800 [00:04<00:01, 192.10it/s]
 87%|████████▋ | 698/800 [00:05<00:00, 251.70it/s]
100%|██████████| 800/800 [00:05<00:00, 139.16it/s]
2025-07-03 12:23:08,027 - modnet - INFO - Computing cross NMI between all features...

  0%|          | 0/239086 [00:00<?, ?it/s]
  0%|          | 1/239086 [00:09<609:38:01,  9.18s/it]
  0%|          | 201/239086 [00:09<2:10:08, 30.59it/s]
  0%|          | 301/239086 [00:09<1:18:29, 50.70it/s]
  0%|          | 401/239086 [00:09<53:08, 74.85it/s]  
  0%|          | 501/239086 [00:10<43:04, 92.32it/s]
  0%|          | 801/239086 [00:11<23:12, 171.17it/s]
  0%|          | 901/239086 [00:11<19:10, 207.09it/s]
  0%|          | 1001/239086 [00:11<18:22, 215.85it/s]
  0%|          | 1101/239086 [00:11<16:23, 242.03it/s]
  1%|          | 1201/239086 [00:12<13:42, 289.32it/s]
  1%|          | 1401/239086 [00:13<16:27, 240.81it/s]
  1%|          | 1601/239086 [00:13<14:33, 271.73it/s]
  1%|          | 1701/239086 [00:14<15:12, 260.09it/s]
  1%|          | 1884/239086 [00:14<10:33, 374.38it/s]
  1%|          | 2001/239086 [00:14<12:35, 313.71it/s]
  1%|          | 2101/239086 [00:15<18:48, 210.09it/s]
  1%|          | 2201/239086 [00:16<18:07, 217.92it/s]
  1%|          | 2301/239086 [00:17<25:14, 156.33it/s]
  1%|          | 2601/239086 [00:17<12:48, 307.56it/s]
  1%|          | 2701/239086 [00:19<25:48, 152.68it/s]
  1%|          | 2801/239086 [00:19<21:59, 179.08it/s]
  1%|          | 2901/239086 [00:19<18:44, 210.06it/s]
  1%|▏         | 3001/239086 [00:20<24:29, 160.68it/s]
  1%|▏         | 3101/239086 [00:20<19:01, 206.77it/s]
  1%|▏         | 3301/239086 [00:23<32:20, 121.50it/s]
  1%|▏         | 3401/239086 [00:24<29:58, 131.06it/s]
  1%|▏         | 3501/239086 [00:24<23:30, 167.01it/s]
  2%|▏         | 3601/239086 [00:24<19:24, 202.23it/s]
  2%|▏         | 3701/239086 [00:24<17:08, 228.90it/s]
  2%|▏         | 3901/239086 [00:25<12:24, 315.70it/s]
  2%|▏         | 4001/239086 [00:25<12:02, 325.19it/s]
  2%|▏         | 4101/239086 [00:25<10:01, 390.71it/s]
  2%|▏         | 4301/239086 [00:25<09:15, 422.98it/s]
  2%|▏         | 4501/239086 [00:26<08:37, 452.98it/s]
  2%|▏         | 4601/239086 [00:26<12:29, 312.70it/s]
  2%|▏         | 4701/239086 [00:27<13:38, 286.20it/s]
  2%|▏         | 4801/239086 [00:28<16:37, 234.79it/s]
  2%|▏         | 4994/239086 [00:28<10:40, 365.42it/s]
  2%|▏         | 5074/239086 [00:28<11:51, 329.02it/s]
  2%|▏         | 5137/239086 [00:28<12:53, 302.63it/s]
  2%|▏         | 5201/239086 [00:28<11:57, 325.80it/s]
  2%|▏         | 5301/239086 [00:29<09:49, 396.68it/s]
  2%|▏         | 5401/239086 [00:31<37:36, 103.54it/s]
  2%|▏         | 5501/239086 [00:32<32:56, 118.18it/s]
  2%|▏         | 5601/239086 [00:32<24:08, 161.21it/s]
  2%|▏         | 5701/239086 [00:32<22:27, 173.22it/s]
  2%|▏         | 5801/239086 [00:33<21:48, 178.23it/s]
  2%|▏         | 5901/239086 [00:33<18:53, 205.72it/s]
  3%|▎         | 6001/239086 [00:33<14:47, 262.55it/s]
  3%|▎         | 6101/239086 [00:34<14:27, 268.43it/s]
  3%|▎         | 6201/239086 [00:34<15:57, 243.32it/s]
  3%|▎         | 6401/239086 [00:35<13:33, 285.91it/s]
  3%|▎         | 6501/239086 [00:35<18:47, 206.20it/s]
  3%|▎         | 6701/239086 [00:36<13:07, 295.03it/s]
  3%|▎         | 6801/239086 [00:36<14:54, 259.65it/s]
  3%|▎         | 6901/239086 [00:37<15:54, 243.17it/s]
  3%|▎         | 7101/239086 [00:37<13:27, 287.40it/s]
  3%|▎         | 7201/239086 [00:38<15:45, 245.13it/s]
  3%|▎         | 7301/239086 [00:38<17:04, 226.34it/s]
  3%|▎         | 7401/239086 [00:40<29:21, 131.55it/s]
  3%|▎         | 7501/239086 [00:47<1:28:33, 43.58it/s]
  3%|▎         | 7601/239086 [00:47<1:06:21, 58.13it/s]
  3%|▎         | 7701/239086 [00:47<53:48, 71.66it/s]  
  3%|▎         | 7801/239086 [00:49<51:09, 75.35it/s]
  3%|▎         | 7901/239086 [00:49<38:23, 100.36it/s]
  3%|▎         | 8001/239086 [00:49<30:27, 126.46it/s]
  3%|▎         | 8101/239086 [00:50<35:20, 108.93it/s]
  3%|▎         | 8201/239086 [00:51<30:14, 127.24it/s]
  3%|▎         | 8301/239086 [00:53<43:11, 89.04it/s] 
  4%|▎         | 8401/239086 [00:53<34:36, 111.07it/s]
  4%|▎         | 8501/239086 [00:53<27:36, 139.22it/s]
  4%|▎         | 8601/239086 [00:56<52:01, 73.83it/s] 
  4%|▎         | 8701/239086 [00:56<37:51, 101.44it/s]
  4%|▎         | 8801/239086 [00:57<31:08, 123.27it/s]
  4%|▎         | 8901/239086 [00:57<29:33, 129.79it/s]
  4%|▍         | 9001/239086 [01:00<49:57, 76.77it/s] 
  4%|▍         | 9101/239086 [01:00<36:20, 105.48it/s]
  4%|▍         | 9201/239086 [01:01<33:09, 115.56it/s]
  4%|▍         | 9301/239086 [01:01<28:05, 136.33it/s]
  4%|▍         | 9401/239086 [01:03<37:59, 100.76it/s]
  4%|▍         | 9501/239086 [01:03<28:58, 132.05it/s]
  4%|▍         | 9671/239086 [01:03<17:43, 215.76it/s]
  4%|▍         | 9742/239086 [01:04<23:05, 165.51it/s]
  4%|▍         | 9801/239086 [01:04<21:52, 174.75it/s]
  4%|▍         | 9901/239086 [01:06<34:44, 109.96it/s]
  4%|▍         | 10001/239086 [01:07<32:58, 115.80it/s]
  4%|▍         | 10201/239086 [01:07<25:01, 152.49it/s]
  4%|▍         | 10401/239086 [01:08<17:09, 222.19it/s]
  4%|▍         | 10601/239086 [01:09<17:45, 214.48it/s]
  4%|▍         | 10701/239086 [01:10<22:44, 167.35it/s]
  5%|▍         | 10801/239086 [01:10<20:41, 183.84it/s]
  5%|▍         | 10901/239086 [01:10<18:34, 204.67it/s]
  5%|▍         | 11001/239086 [01:12<27:32, 138.07it/s]
  5%|▍         | 11101/239086 [01:12<24:44, 153.56it/s]
  5%|▍         | 11201/239086 [01:13<25:20, 149.87it/s]
  5%|▍         | 11401/239086 [01:13<16:28, 230.35it/s]
  5%|▍         | 11501/239086 [01:14<20:27, 185.39it/s]
  5%|▍         | 11601/239086 [01:15<27:12, 139.36it/s]
  5%|▍         | 11701/239086 [01:17<32:50, 115.40it/s]
  5%|▍         | 11871/239086 [01:17<20:50, 181.76it/s]
  5%|▌         | 12101/239086 [01:17<13:36, 277.89it/s]
  5%|▌         | 12201/239086 [01:18<17:30, 215.98it/s]
  5%|▌         | 12401/239086 [01:18<11:44, 321.67it/s]
  5%|▌         | 12501/239086 [01:20<26:31, 142.39it/s]
  5%|▌         | 12601/239086 [01:21<27:05, 139.33it/s]
  5%|▌         | 12701/239086 [01:21<24:23, 154.69it/s]
  5%|▌         | 12801/239086 [01:22<22:26, 168.05it/s]
  5%|▌         | 12901/239086 [01:22<19:17, 195.39it/s]
  5%|▌         | 13001/239086 [01:23<22:30, 167.41it/s]
  5%|▌         | 13101/239086 [01:23<22:11, 169.72it/s]
  6%|▌         | 13201/239086 [01:24<26:14, 143.50it/s]
  6%|▌         | 13301/239086 [01:26<31:32, 119.29it/s]
  6%|▌         | 13401/239086 [01:26<29:45, 126.40it/s]
  6%|▌         | 13501/239086 [01:28<41:16, 91.07it/s] 
  6%|▌         | 13601/239086 [01:40<2:45:23, 22.72it/s]
  6%|▌         | 13701/239086 [01:45<2:50:21, 22.05it/s]
  6%|▌         | 13801/239086 [01:47<2:21:20, 26.57it/s]
  6%|▌         | 13901/239086 [01:48<1:52:35, 33.33it/s]
  6%|▌         | 14001/239086 [01:50<1:40:02, 37.50it/s]
  6%|▌         | 14101/239086 [01:51<1:15:17, 49.80it/s]
  6%|▌         | 14201/239086 [01:52<1:10:31, 53.14it/s]
  6%|▌         | 14301/239086 [01:54<1:10:34, 53.09it/s]
  6%|▌         | 14401/239086 [01:56<1:09:10, 54.14it/s]
  6%|▌         | 14501/239086 [01:57<58:44, 63.73it/s]  
  6%|▌         | 14601/239086 [01:59<1:09:37, 53.74it/s]
  6%|▌         | 14701/239086 [02:00<50:29, 74.08it/s]  
  6%|▌         | 14801/239086 [02:01<49:44, 75.15it/s]
  6%|▌         | 14901/239086 [02:05<1:19:36, 46.93it/s]
  6%|▋         | 15001/239086 [02:07<1:16:46, 48.65it/s]
  6%|▋         | 15101/239086 [02:08<1:08:58, 54.12it/s]
  6%|▋         | 15201/239086 [02:09<1:01:30, 60.66it/s]
  6%|▋         | 15301/239086 [02:11<1:03:31, 58.71it/s]
  6%|▋         | 15401/239086 [02:12<54:44, 68.11it/s]  
  6%|▋         | 15501/239086 [02:15<1:12:09, 51.64it/s]
  7%|▋         | 15601/239086 [02:18<1:25:10, 43.73it/s]
  7%|▋         | 15701/239086 [02:20<1:23:16, 44.71it/s]
  7%|▋         | 15801/239086 [02:21<1:05:35, 56.74it/s]
  7%|▋         | 15901/239086 [02:23<1:07:40, 54.96it/s]
  7%|▋         | 16001/239086 [02:26<1:17:49, 47.77it/s]
  7%|▋         | 16101/239086 [02:26<57:22, 64.77it/s]  
  7%|▋         | 16201/239086 [02:27<50:32, 73.49it/s]
  7%|▋         | 16313/239086 [02:27<35:12, 105.44it/s]
  7%|▋         | 16401/239086 [02:27<27:30, 134.93it/s]
  7%|▋         | 16501/239086 [02:28<25:42, 144.29it/s]
  7%|▋         | 16601/239086 [02:28<21:28, 172.61it/s]
  7%|▋         | 16701/239086 [02:29<23:45, 155.98it/s]
  7%|▋         | 16801/239086 [02:30<25:58, 142.59it/s]
  7%|▋         | 16901/239086 [02:30<25:20, 146.13it/s]
  7%|▋         | 17001/239086 [02:32<32:49, 112.77it/s]
  7%|▋         | 17101/239086 [02:33<41:22, 89.42it/s] 
  7%|▋         | 17201/239086 [02:35<43:34, 84.87it/s]
  7%|▋         | 17301/239086 [02:35<32:45, 112.85it/s]
  7%|▋         | 17401/239086 [02:35<30:32, 120.96it/s]
  7%|▋         | 17601/239086 [02:36<18:09, 203.20it/s]
  7%|▋         | 17701/239086 [02:36<21:18, 173.15it/s]
  7%|▋         | 17801/239086 [02:37<23:23, 157.64it/s]
  7%|▋         | 17901/239086 [02:38<21:46, 169.33it/s]
  8%|▊         | 18101/239086 [02:38<15:45, 233.81it/s]
  8%|▊         | 18301/239086 [02:39<14:31, 253.25it/s]
  8%|▊         | 18401/239086 [02:39<16:25, 224.01it/s]
  8%|▊         | 18501/239086 [02:40<14:50, 247.74it/s]
  8%|▊         | 18601/239086 [02:40<12:26, 295.41it/s]
  8%|▊         | 18701/239086 [02:40<10:29, 349.86it/s]
  8%|▊         | 18801/239086 [02:40<12:08, 302.32it/s]
  8%|▊         | 18901/239086 [02:41<09:58, 368.16it/s]
  8%|▊         | 19101/239086 [02:41<07:57, 461.08it/s]
  8%|▊         | 19201/239086 [02:42<17:27, 209.83it/s]
  8%|▊         | 19301/239086 [02:43<18:17, 200.25it/s]
  8%|▊         | 19401/239086 [02:44<30:16, 120.92it/s]
  8%|▊         | 19501/239086 [02:45<27:19, 133.92it/s]
  8%|▊         | 19601/239086 [02:46<28:41, 127.48it/s]
  8%|▊         | 19701/239086 [02:47<28:01, 130.51it/s]
  8%|▊         | 19801/239086 [02:47<21:59, 166.20it/s]
  8%|▊         | 19901/239086 [02:48<24:02, 152.00it/s]
  8%|▊         | 20001/239086 [02:48<21:51, 167.04it/s]
  8%|▊         | 20201/239086 [02:48<13:09, 277.36it/s]
  9%|▊         | 20501/239086 [02:48<07:46, 468.97it/s]
  9%|▊         | 20701/239086 [02:49<10:33, 344.54it/s]
  9%|▊         | 20801/239086 [02:50<13:39, 266.47it/s]
  9%|▉         | 21001/239086 [02:50<09:59, 363.56it/s]
  9%|▉         | 21201/239086 [02:50<07:55, 458.65it/s]
  9%|▉         | 21401/239086 [02:51<06:59, 518.90it/s]
  9%|▉         | 21501/239086 [02:51<10:11, 355.82it/s]
  9%|▉         | 21601/239086 [02:53<20:01, 181.04it/s]
  9%|▉         | 21701/239086 [02:55<30:19, 119.50it/s]
  9%|▉         | 21901/239086 [02:55<20:12, 179.06it/s]
  9%|▉         | 22001/239086 [02:55<18:42, 193.43it/s]
  9%|▉         | 22101/239086 [02:56<20:28, 176.58it/s]
  9%|▉         | 22201/239086 [02:57<19:06, 189.13it/s]
  9%|▉         | 22301/239086 [02:57<18:28, 195.53it/s]
  9%|▉         | 22401/239086 [02:58<19:10, 188.29it/s]
  9%|▉         | 22601/239086 [02:58<11:39, 309.65it/s]
  9%|▉         | 22701/239086 [02:59<19:45, 182.60it/s]
 10%|▉         | 22801/239086 [02:59<18:02, 199.84it/s]
 10%|▉         | 22996/239086 [02:59<11:18, 318.61it/s]
 10%|▉         | 23101/239086 [03:00<09:39, 372.54it/s]
 10%|▉         | 23201/239086 [03:00<12:36, 285.30it/s]
 10%|▉         | 23301/239086 [03:00<10:39, 337.49it/s]
 10%|▉         | 23401/239086 [03:01<11:25, 314.56it/s]
 10%|▉         | 23501/239086 [03:01<11:03, 325.14it/s]
 10%|▉         | 23601/239086 [03:01<10:28, 342.90it/s]
 10%|▉         | 23701/239086 [03:02<10:52, 329.87it/s]
 10%|▉         | 23801/239086 [03:02<14:00, 256.01it/s]
 10%|▉         | 23901/239086 [03:03<17:05, 209.88it/s]
 10%|█         | 24001/239086 [03:03<15:46, 227.30it/s]
 10%|█         | 24101/239086 [03:04<19:18, 185.53it/s]
 10%|█         | 24201/239086 [03:05<24:44, 144.76it/s]
 10%|█         | 24301/239086 [03:05<18:52, 189.62it/s]
 10%|█         | 24401/239086 [03:05<15:04, 237.27it/s]
 10%|█         | 24501/239086 [03:06<13:09, 271.96it/s]
 10%|█         | 24601/239086 [03:06<14:50, 240.74it/s]
 10%|█         | 24801/239086 [03:08<20:45, 172.08it/s]
 10%|█         | 24901/239086 [03:08<21:24, 166.71it/s]
 10%|█         | 25001/239086 [03:09<18:10, 196.41it/s]
 10%|█         | 25101/239086 [03:09<14:20, 248.58it/s]
 11%|█         | 25301/239086 [03:09<09:00, 395.83it/s]
 11%|█         | 25401/239086 [03:09<07:52, 451.93it/s]
 11%|█         | 25501/239086 [03:10<13:04, 272.18it/s]
 11%|█         | 25601/239086 [03:10<15:33, 228.70it/s]
 11%|█         | 25701/239086 [03:11<13:05, 271.73it/s]
 11%|█         | 25801/239086 [03:11<12:00, 295.83it/s]
 11%|█         | 25901/239086 [03:11<14:21, 247.46it/s]
 11%|█         | 26001/239086 [03:12<13:03, 272.01it/s]
 11%|█         | 26101/239086 [03:12<11:03, 320.91it/s]
 11%|█         | 26201/239086 [03:12<11:20, 312.83it/s]
 11%|█         | 26301/239086 [03:13<19:11, 184.74it/s]
 11%|█         | 26401/239086 [03:14<20:51, 169.95it/s]
 11%|█         | 26501/239086 [03:14<16:09, 219.17it/s]
 11%|█         | 26601/239086 [03:15<17:11, 205.97it/s]
 11%|█         | 26701/239086 [03:15<16:50, 210.27it/s]
 11%|█         | 26801/239086 [03:16<20:37, 171.50it/s]
 11%|█▏        | 26901/239086 [03:17<23:44, 148.96it/s]
 11%|█▏        | 27001/239086 [03:19<36:06, 97.90it/s] 
 11%|█▏        | 27101/239086 [03:19<26:35, 132.84it/s]
 11%|█▏        | 27201/239086 [03:19<23:01, 153.38it/s]
 11%|█▏        | 27401/239086 [03:19<13:48, 255.66it/s]
 12%|█▏        | 27501/239086 [03:22<33:36, 104.91it/s]
 12%|█▏        | 27671/239086 [03:22<21:46, 161.82it/s]
 12%|█▏        | 27749/239086 [03:24<33:47, 104.25it/s]
 12%|█▏        | 27805/239086 [03:25<35:59, 97.84it/s] 
 12%|█▏        | 28001/239086 [03:25<24:53, 141.34it/s]
 12%|█▏        | 28101/239086 [03:26<22:53, 153.62it/s]
 12%|█▏        | 28201/239086 [03:26<20:02, 175.38it/s]
 12%|█▏        | 28301/239086 [03:27<19:19, 181.74it/s]
 12%|█▏        | 28501/239086 [03:27<12:15, 286.31it/s]
 12%|█▏        | 28601/239086 [03:27<10:24, 336.84it/s]
 12%|█▏        | 28801/239086 [03:27<07:44, 452.50it/s]
 12%|█▏        | 28901/239086 [03:27<07:58, 439.71it/s]
 12%|█▏        | 29001/239086 [03:28<08:05, 432.50it/s]
 12%|█▏        | 29101/239086 [03:28<13:11, 265.19it/s]
 12%|█▏        | 29201/239086 [03:30<23:16, 150.29it/s]
 12%|█▏        | 29301/239086 [03:31<24:02, 145.42it/s]
 12%|█▏        | 29401/239086 [03:31<22:16, 156.84it/s]
 12%|█▏        | 29501/239086 [03:31<17:28, 199.82it/s]
 12%|█▏        | 29601/239086 [03:31<13:32, 257.99it/s]
 12%|█▏        | 29701/239086 [03:32<12:42, 274.49it/s]
 12%|█▏        | 29801/239086 [03:32<14:44, 236.50it/s]
 13%|█▎        | 29901/239086 [03:33<14:43, 236.71it/s]
 13%|█▎        | 30001/239086 [03:35<38:21, 90.86it/s] 
 13%|█▎        | 30101/239086 [03:36<29:04, 119.78it/s]
 13%|█▎        | 30301/239086 [03:36<16:35, 209.75it/s]
 13%|█▎        | 30401/239086 [03:36<17:02, 204.11it/s]
 13%|█▎        | 30501/239086 [03:37<15:08, 229.54it/s]
 13%|█▎        | 30601/239086 [03:38<22:13, 156.33it/s]
 13%|█▎        | 30701/239086 [03:38<21:37, 160.58it/s]
 13%|█▎        | 30801/239086 [03:38<16:54, 205.41it/s]
 13%|█▎        | 31001/239086 [03:39<10:23, 333.58it/s]
 13%|█▎        | 31101/239086 [03:39<12:37, 274.55it/s]
 13%|█▎        | 31201/239086 [03:40<15:31, 223.08it/s]
 13%|█▎        | 31301/239086 [03:40<16:21, 211.63it/s]
 13%|█▎        | 31401/239086 [03:43<37:58, 91.16it/s] 
 13%|█▎        | 31501/239086 [03:44<31:09, 111.01it/s]
 13%|█▎        | 31601/239086 [03:44<31:40, 109.19it/s]
 13%|█▎        | 31701/239086 [03:45<23:45, 145.45it/s]
 13%|█▎        | 31801/239086 [03:45<20:37, 167.56it/s]
 13%|█▎        | 31901/239086 [03:45<16:18, 211.78it/s]
 13%|█▎        | 32001/239086 [03:46<15:42, 219.68it/s]
 13%|█▎        | 32101/239086 [03:46<20:05, 171.76it/s]
 13%|█▎        | 32201/239086 [03:47<17:49, 193.51it/s]
 14%|█▎        | 32301/239086 [03:47<15:59, 215.42it/s]
 14%|█▎        | 32401/239086 [03:47<13:00, 264.90it/s]
 14%|█▎        | 32501/239086 [03:48<12:38, 272.52it/s]
 14%|█▎        | 32601/239086 [03:49<20:38, 166.76it/s]
 14%|█▎        | 32701/239086 [03:51<35:06, 97.98it/s] 
 14%|█▎        | 32801/239086 [03:51<28:57, 118.76it/s]
 14%|█▍        | 32901/239086 [03:52<25:05, 136.92it/s]
 14%|█▍        | 33001/239086 [03:52<22:03, 155.70it/s]
 14%|█▍        | 33101/239086 [03:53<20:50, 164.70it/s]
 14%|█▍        | 33201/239086 [03:55<40:18, 85.14it/s] 
 14%|█▍        | 33301/239086 [03:56<35:03, 97.85it/s]
 14%|█▍        | 33501/239086 [03:56<22:21, 153.19it/s]
 14%|█▍        | 33601/239086 [03:56<18:15, 187.66it/s]
 14%|█▍        | 33901/239086 [03:57<11:52, 287.86it/s]
 14%|█▍        | 34001/239086 [03:58<14:45, 231.63it/s]
 14%|█▍        | 34101/239086 [03:58<15:35, 219.22it/s]
 14%|█▍        | 34201/239086 [03:59<19:05, 178.92it/s]
 14%|█▍        | 34301/239086 [03:59<16:05, 212.12it/s]
 14%|█▍        | 34402/239086 [04:00<12:40, 269.22it/s]
 14%|█▍        | 34501/239086 [04:00<16:20, 208.60it/s]
 14%|█▍        | 34601/239086 [04:03<37:34, 90.72it/s] 
 15%|█▍        | 34701/239086 [04:03<31:22, 108.60it/s]
 15%|█▍        | 34801/239086 [04:04<29:03, 117.17it/s]
 15%|█▍        | 34901/239086 [04:05<26:08, 130.19it/s]
 15%|█▍        | 35001/239086 [04:06<30:33, 111.31it/s]
 15%|█▍        | 35201/239086 [04:07<22:02, 154.17it/s]
 15%|█▍        | 35301/239086 [04:07<20:09, 168.43it/s]
 15%|█▍        | 35401/239086 [04:08<19:59, 169.86it/s]
 15%|█▍        | 35501/239086 [04:08<17:38, 192.28it/s]
 15%|█▍        | 35601/239086 [04:09<18:41, 181.40it/s]
 15%|█▍        | 35701/239086 [04:09<16:16, 208.24it/s]
 15%|█▍        | 35801/239086 [04:09<13:58, 242.46it/s]
 15%|█▌        | 35901/239086 [04:10<17:36, 192.40it/s]
 15%|█▌        | 36001/239086 [04:10<14:58, 226.11it/s]
 15%|█▌        | 36101/239086 [04:10<11:48, 286.48it/s]
 15%|█▌        | 36201/239086 [04:11<15:52, 212.94it/s]
 15%|█▌        | 36301/239086 [04:12<17:50, 189.37it/s]
 15%|█▌        | 36401/239086 [04:12<17:29, 193.20it/s]
 15%|█▌        | 36501/239086 [04:13<18:47, 179.68it/s]
 15%|█▌        | 36601/239086 [04:13<19:20, 174.49it/s]
 15%|█▌        | 36701/239086 [04:15<28:45, 117.29it/s]
 15%|█▌        | 36801/239086 [04:15<22:57, 146.86it/s]
 15%|█▌        | 36901/239086 [04:15<18:32, 181.80it/s]
 15%|█▌        | 37001/239086 [04:16<15:46, 213.44it/s]
 16%|█▌        | 37101/239086 [04:17<24:19, 138.42it/s]
 16%|█▌        | 37201/239086 [04:17<20:29, 164.21it/s]
 16%|█▌        | 37401/239086 [04:18<18:56, 177.50it/s]
 16%|█▌        | 37501/239086 [04:19<16:03, 209.21it/s]
 16%|█▌        | 37701/239086 [04:19<12:08, 276.57it/s]
 16%|█▌        | 37801/239086 [04:19<12:06, 277.05it/s]
 16%|█▌        | 37901/239086 [04:20<11:16, 297.31it/s]
 16%|█▌        | 38001/239086 [04:21<15:50, 211.67it/s]
 16%|█▌        | 38201/239086 [04:21<11:07, 301.12it/s]
 16%|█▌        | 38301/239086 [04:21<11:41, 286.02it/s]
 16%|█▌        | 38401/239086 [04:21<10:29, 318.67it/s]
 16%|█▌        | 38501/239086 [04:22<12:43, 262.69it/s]
 16%|█▌        | 38601/239086 [04:22<11:55, 280.12it/s]
 16%|█▌        | 38701/239086 [04:23<15:09, 220.27it/s]
 16%|█▌        | 38801/239086 [04:24<22:37, 147.55it/s]
 16%|█▋        | 38901/239086 [04:25<26:37, 125.29it/s]
 16%|█▋        | 39076/239086 [04:25<16:14, 205.15it/s]
 16%|█▋        | 39140/239086 [04:26<15:52, 209.97it/s]
 16%|█▋        | 39201/239086 [04:26<17:12, 193.54it/s]
 16%|█▋        | 39301/239086 [04:27<16:17, 204.32it/s]
 16%|█▋        | 39401/239086 [04:27<14:35, 228.18it/s]
 17%|█▋        | 39501/239086 [04:27<11:30, 288.90it/s]
 17%|█▋        | 39601/239086 [04:28<20:57, 158.58it/s]
 17%|█▋        | 39701/239086 [04:30<30:21, 109.47it/s]
 17%|█▋        | 39801/239086 [04:30<23:16, 142.65it/s]
 17%|█▋        | 39901/239086 [04:30<17:19, 191.59it/s]
 17%|█▋        | 40001/239086 [04:30<15:06, 219.58it/s]
 17%|█▋        | 40101/239086 [04:31<18:38, 177.96it/s]
 17%|█▋        | 40201/239086 [04:32<16:06, 205.76it/s]
 17%|█▋        | 40301/239086 [04:32<14:10, 233.67it/s]
 17%|█▋        | 40401/239086 [04:34<26:43, 123.92it/s]
 17%|█▋        | 40601/239086 [04:34<16:07, 205.23it/s]
 17%|█▋        | 40701/239086 [04:34<15:48, 209.26it/s]
 17%|█▋        | 40801/239086 [04:34<13:22, 247.18it/s]
 17%|█▋        | 40901/239086 [04:45<1:43:47, 31.82it/s]
 17%|█▋        | 41001/239086 [04:46<1:23:48, 39.39it/s]
 17%|█▋        | 41201/239086 [04:47<54:18, 60.74it/s]  
 17%|█▋        | 41301/239086 [04:47<44:50, 73.50it/s]
 17%|█▋        | 41401/239086 [04:57<1:51:09, 29.64it/s]
 17%|█▋        | 41501/239086 [04:57<1:25:18, 38.60it/s]
 17%|█▋        | 41801/239086 [04:58<47:14, 69.60it/s]  
 18%|█▊        | 42001/239086 [04:59<34:30, 95.17it/s]
 18%|█▊        | 42101/239086 [04:59<30:38, 107.13it/s]
 18%|█▊        | 42201/239086 [05:01<31:42, 103.51it/s]
 18%|█▊        | 42301/239086 [05:04<48:14, 67.98it/s] 
 18%|█▊        | 42401/239086 [05:04<41:11, 79.58it/s]
 18%|█▊        | 42501/239086 [05:06<48:10, 68.00it/s]
 18%|█▊        | 42601/239086 [05:07<37:53, 86.43it/s]
 18%|█▊        | 42701/239086 [05:07<28:43, 113.97it/s]
 18%|█▊        | 42801/239086 [05:07<22:40, 144.33it/s]
 18%|█▊        | 42901/239086 [05:07<17:36, 185.75it/s]
 18%|█▊        | 43001/239086 [05:07<14:17, 228.58it/s]
 18%|█▊        | 43101/239086 [05:08<11:25, 285.92it/s]
 18%|█▊        | 43201/239086 [05:09<18:43, 174.41it/s]
 18%|█▊        | 43301/239086 [05:09<17:23, 187.67it/s]
 18%|█▊        | 43401/239086 [05:09<13:29, 241.79it/s]
 18%|█▊        | 43601/239086 [05:09<08:49, 368.98it/s]
 18%|█▊        | 43701/239086 [05:09<07:27, 436.98it/s]
 18%|█▊        | 43801/239086 [05:11<21:02, 154.72it/s]
 18%|█▊        | 43901/239086 [05:11<16:18, 199.49it/s]
 18%|█▊        | 44101/239086 [05:13<18:32, 175.25it/s]
 19%|█▊        | 44301/239086 [05:13<15:45, 205.91it/s]
 19%|█▊        | 44401/239086 [05:15<22:11, 146.22it/s]
 19%|█▊        | 44501/239086 [05:16<23:22, 138.74it/s]
 19%|█▊        | 44601/239086 [05:16<20:37, 157.17it/s]
 19%|█▊        | 44701/239086 [05:17<19:10, 169.01it/s]
 19%|█▊        | 44801/239086 [05:17<14:51, 217.89it/s]
 19%|█▉        | 44901/239086 [05:17<14:58, 216.19it/s]
 19%|█▉        | 45001/239086 [05:18<15:28, 209.12it/s]
 19%|█▉        | 45101/239086 [05:18<14:40, 220.31it/s]
 19%|█▉        | 45201/239086 [05:19<15:52, 203.54it/s]
 19%|█▉        | 45301/239086 [05:19<17:38, 183.05it/s]
 19%|█▉        | 45401/239086 [05:20<14:24, 224.12it/s]
 19%|█▉        | 45501/239086 [05:20<16:16, 198.29it/s]
 19%|█▉        | 45601/239086 [05:20<14:06, 228.47it/s]
 19%|█▉        | 45801/239086 [05:21<08:37, 373.33it/s]
 19%|█▉        | 45966/239086 [05:21<06:16, 512.82it/s]
 19%|█▉        | 46201/239086 [05:21<07:36, 422.14it/s]
 19%|█▉        | 46301/239086 [05:22<09:09, 351.05it/s]
 19%|█▉        | 46401/239086 [05:22<09:18, 345.11it/s]
 19%|█▉        | 46601/239086 [05:23<10:40, 300.58it/s]
 20%|█▉        | 46701/239086 [05:23<10:57, 292.39it/s]
 20%|█▉        | 46801/239086 [05:24<11:15, 284.52it/s]
 20%|█▉        | 46901/239086 [05:25<15:20, 208.75it/s]slurmstepd: error: *** JOB 9378685 ON cns263 CANCELLED AT 2025-07-03T12:28:34 ***

Resources Used

Total Memory used                        - MEM              : 12GiB
Total CPU Time                           - CPU_Time         : 02:30:48
Execution Time                           - Wall_Time        : 00:06:17
total programme cpu time                 - Total_CPU        : 02:03:01
Total_CPU / CPU_Time  (%)                - ETA              : 81%
Number of alloc CPU                      - NCPUS            : 24
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 24
Mobilized Resources x Execution Time     - R_Wall_Time      : 02:30:48
CPU_Time / R_Wall_Time (%)               - ALPHA            : 100%
Energy (Joules)                                             : unknown

