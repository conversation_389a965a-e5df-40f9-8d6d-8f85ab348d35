Job started on Fri May 16 13:21:13 CEST 2025
Running on node(s): cns269
2025-05-16 13:21:34.621007: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 32 jobs
Preparing nested CV run for task 'matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso'
Found 1 matching files for matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso
2025-05-16 13:21:55,185 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff434689910> object, created with modnet version 0.1.13
Preparing fold 1 ...
2025-05-16 13:21:55,233 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff434689640> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f1
2025-05-16 13:21:55,267 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f1!
Preparing fold 2 ...
2025-05-16 13:21:55,309 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff434301ac0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f2
2025-05-16 13:21:55,342 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f2!
Preparing fold 3 ...
2025-05-16 13:21:55,375 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff4345d5d60> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f3
2025-05-16 13:21:55,514 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f3!
Preparing fold 4 ...
2025-05-16 13:21:55,559 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff4327d8520> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f4
2025-05-16 13:21:55,591 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f4!
Preparing fold 5 ...
2025-05-16 13:21:55,625 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff433a01070> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f5
2025-05-16 13:21:55,658 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f5!
Training fold 1 ...
Processing fold 1 ...
2025-05-16 13:21:55.682328: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 13:21:55.682839: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 13:21:55.682884: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 13:21:55.682927: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 13:21:55.683249: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 13:21:55,874 - modnet - INFO - Targets:
2025-05-16 13:21:55,875 - modnet - INFO - 1) target: regression
2025-05-16 13:21:56,306 - modnet - INFO - Multiprocessing on 32 cores. Total of 128 cores available.
2025-05-16 13:21:56,306 - modnet - INFO - Generation number 0

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:12<49:50, 12.01s/it]
  1%|          | 2/250 [00:12<22:28,  5.44s/it]
  1%|          | 3/250 [00:13<13:29,  3.28s/it]
  2%|▏         | 5/250 [00:13<06:14,  1.53s/it]
  3%|▎         | 7/250 [00:14<04:13,  1.04s/it]
  3%|▎         | 8/250 [00:15<04:05,  1.01s/it]
  4%|▎         | 9/250 [00:16<03:34,  1.12it/s]
  4%|▍         | 10/250 [00:16<02:50,  1.41it/s]
  4%|▍         | 11/250 [00:16<02:13,  1.80it/s]
  5%|▍         | 12/250 [00:16<01:49,  2.17it/s]
  5%|▌         | 13/250 [00:17<02:30,  1.58it/s]
  6%|▌         | 14/250 [00:19<03:12,  1.23it/s]
  6%|▌         | 15/250 [00:19<02:50,  1.38it/s]
  7%|▋         | 17/250 [00:20<02:11,  1.77it/s]
  7%|▋         | 18/250 [00:20<01:53,  2.04it/s]
  8%|▊         | 19/250 [00:20<01:30,  2.54it/s]
  8%|▊         | 20/250 [00:20<01:14,  3.11it/s]
  8%|▊         | 21/250 [00:21<01:26,  2.65it/s]
  9%|▉         | 22/250 [00:22<02:19,  1.64it/s]
  9%|▉         | 23/250 [00:24<03:45,  1.00it/s]
 10%|█         | 25/250 [00:26<03:19,  1.13it/s]
 10%|█         | 26/250 [00:28<04:18,  1.16s/it]
 11%|█         | 27/250 [00:28<03:47,  1.02s/it]
 11%|█         | 28/250 [00:28<02:51,  1.29it/s]
 12%|█▏        | 29/250 [00:31<04:37,  1.26s/it]
 12%|█▏        | 30/250 [00:32<04:09,  1.14s/it]
 12%|█▏        | 31/250 [00:32<03:06,  1.18it/s]
 13%|█▎        | 32/250 [00:33<03:33,  1.02it/s]
 13%|█▎        | 33/250 [00:35<04:12,  1.16s/it]
 14%|█▎        | 34/250 [00:36<03:48,  1.06s/it]
 14%|█▍        | 35/250 [00:36<03:25,  1.05it/s]
 14%|█▍        | 36/250 [00:38<03:44,  1.05s/it]
 15%|█▍        | 37/250 [00:38<02:45,  1.28it/s]
 15%|█▌        | 38/250 [00:38<02:12,  1.60it/s]
 16%|█▌        | 39/250 [00:40<03:12,  1.10it/s]
 16%|█▌        | 40/250 [00:40<03:00,  1.16it/s]
 16%|█▋        | 41/250 [00:41<02:42,  1.29it/s]
 17%|█▋        | 43/250 [00:41<01:42,  2.01it/s]
 18%|█▊        | 44/250 [00:41<01:26,  2.38it/s]
 18%|█▊        | 45/250 [00:43<02:33,  1.33it/s]
 18%|█▊        | 46/250 [00:43<02:06,  1.62it/s]
 19%|█▉        | 47/250 [00:44<02:33,  1.32it/s]
 20%|█▉        | 49/250 [00:45<01:30,  2.22it/s]
 20%|██        | 51/250 [00:45<01:19,  2.50it/s]
 21%|██        | 52/250 [00:46<01:24,  2.34it/s]
 21%|██        | 53/250 [00:47<01:46,  1.84it/s]
 22%|██▏       | 54/250 [00:47<01:57,  1.66it/s]
 22%|██▏       | 55/250 [00:49<03:13,  1.01it/s]
 22%|██▏       | 56/250 [00:50<02:39,  1.22it/s]
 23%|██▎       | 57/250 [00:50<02:05,  1.54it/s]
 23%|██▎       | 58/250 [00:51<01:54,  1.67it/s]
 24%|██▎       | 59/250 [00:52<02:21,  1.35it/s]
 24%|██▍       | 61/250 [00:53<01:54,  1.65it/s]
 25%|██▍       | 62/250 [00:53<01:43,  1.82it/s]
 26%|██▌       | 65/250 [00:53<00:54,  3.37it/s]
 26%|██▋       | 66/250 [00:54<01:14,  2.47it/s]
 27%|██▋       | 68/250 [00:54<01:01,  2.95it/s]
 28%|██▊       | 69/250 [00:55<00:55,  3.28it/s]
 28%|██▊       | 70/250 [00:55<01:01,  2.91it/s]
 28%|██▊       | 71/250 [00:56<01:26,  2.07it/s]
 29%|██▉       | 72/250 [00:56<01:10,  2.51it/s]
 29%|██▉       | 73/250 [00:57<01:29,  1.97it/s]
 30%|███       | 75/250 [00:58<01:18,  2.24it/s]
 31%|███       | 77/250 [01:00<02:17,  1.26it/s]
 31%|███       | 78/250 [01:01<02:04,  1.38it/s]
 32%|███▏      | 79/250 [01:01<01:43,  1.65it/s]
 32%|███▏      | 81/250 [01:01<01:09,  2.42it/s]
 33%|███▎      | 82/250 [01:02<01:15,  2.22it/s]
 33%|███▎      | 83/250 [01:03<01:58,  1.41it/s]
 34%|███▎      | 84/250 [01:04<01:49,  1.51it/s]
 34%|███▍      | 86/250 [01:05<01:44,  1.57it/s]
 35%|███▍      | 87/250 [01:05<01:28,  1.84it/s]
 35%|███▌      | 88/250 [01:06<01:47,  1.50it/s]
 36%|███▌      | 89/250 [01:06<01:25,  1.89it/s]
 36%|███▌      | 90/250 [01:07<01:09,  2.30it/s]
 37%|███▋      | 92/250 [01:08<01:08,  2.30it/s]
 37%|███▋      | 93/250 [01:08<01:05,  2.40it/s]
 38%|███▊      | 95/250 [01:08<00:56,  2.75it/s]
 39%|███▉      | 97/250 [01:09<00:40,  3.74it/s]
 39%|███▉      | 98/250 [01:09<00:40,  3.78it/s]
 40%|███▉      | 99/250 [01:10<00:56,  2.69it/s]
 40%|████      | 100/250 [01:10<00:48,  3.10it/s]
 40%|████      | 101/250 [01:10<00:45,  3.26it/s]
 41%|████      | 103/250 [01:11<00:44,  3.29it/s]
 42%|████▏     | 104/250 [01:11<00:40,  3.61it/s]
 42%|████▏     | 106/250 [01:11<00:29,  4.94it/s]
 43%|████▎     | 107/250 [01:12<00:37,  3.78it/s]
 43%|████▎     | 108/250 [01:12<00:32,  4.41it/s]
 44%|████▎     | 109/250 [01:12<00:29,  4.79it/s]
 44%|████▍     | 110/250 [01:12<00:36,  3.83it/s]
 45%|████▍     | 112/250 [01:12<00:25,  5.44it/s]
 45%|████▌     | 113/250 [01:12<00:22,  5.97it/s]
 46%|████▌     | 114/250 [01:13<00:38,  3.50it/s]
 46%|████▌     | 115/250 [01:13<00:33,  4.03it/s]
 47%|████▋     | 117/250 [01:14<00:26,  4.96it/s]
 47%|████▋     | 118/250 [01:14<00:24,  5.41it/s]
 48%|████▊     | 120/250 [01:14<00:26,  4.90it/s]
 48%|████▊     | 121/250 [01:16<01:25,  1.52it/s]
 49%|████▉     | 122/250 [01:17<01:33,  1.37it/s]
 49%|████▉     | 123/250 [01:18<01:25,  1.48it/s]
 50%|████▉     | 124/250 [01:18<01:11,  1.76it/s]
 50%|█████     | 125/250 [01:18<00:56,  2.20it/s]
 50%|█████     | 126/250 [01:19<01:04,  1.92it/s]
 51%|█████     | 128/250 [01:19<00:40,  3.03it/s]
 52%|█████▏    | 129/250 [01:19<00:35,  3.39it/s]
 52%|█████▏    | 130/250 [01:20<00:34,  3.49it/s]
 52%|█████▏    | 131/250 [01:20<00:41,  2.84it/s]
 53%|█████▎    | 132/250 [01:20<00:37,  3.12it/s]
 53%|█████▎    | 133/250 [01:21<00:33,  3.49it/s]
 54%|█████▎    | 134/250 [01:22<01:08,  1.70it/s]
 54%|█████▍    | 135/250 [01:22<00:52,  2.17it/s]
 54%|█████▍    | 136/250 [01:22<00:42,  2.67it/s]
 55%|█████▍    | 137/250 [01:23<00:39,  2.85it/s]
 55%|█████▌    | 138/250 [01:23<00:37,  2.95it/s]
 56%|█████▌    | 139/250 [01:23<00:35,  3.16it/s]
 56%|█████▌    | 140/250 [01:23<00:28,  3.92it/s]
 57%|█████▋    | 142/250 [01:24<00:29,  3.69it/s]
 57%|█████▋    | 143/250 [01:25<00:49,  2.17it/s]
 58%|█████▊    | 144/250 [01:25<00:40,  2.61it/s]
 58%|█████▊    | 145/250 [01:25<00:42,  2.47it/s]
 59%|█████▉    | 147/250 [01:26<00:26,  3.90it/s]
 59%|█████▉    | 148/250 [01:26<00:33,  3.06it/s]
 60%|█████▉    | 149/250 [01:26<00:31,  3.23it/s]
 60%|██████    | 150/250 [01:27<00:32,  3.09it/s]
 60%|██████    | 151/250 [01:28<00:46,  2.12it/s]
 61%|██████    | 152/250 [01:28<00:36,  2.65it/s]
 61%|██████    | 153/250 [01:28<00:38,  2.50it/s]
 62%|██████▏   | 154/250 [01:28<00:30,  3.16it/s]
 62%|██████▏   | 155/250 [01:29<00:29,  3.18it/s]
 62%|██████▏   | 156/250 [01:29<00:24,  3.88it/s]
 63%|██████▎   | 158/250 [01:29<00:22,  4.15it/s]
 64%|██████▎   | 159/250 [01:30<00:29,  3.06it/s]
 64%|██████▍   | 160/250 [01:31<00:41,  2.19it/s]
 64%|██████▍   | 161/250 [01:31<00:41,  2.12it/s]
 65%|██████▍   | 162/250 [01:32<01:02,  1.42it/s]
 65%|██████▌   | 163/250 [01:33<00:57,  1.52it/s]
 66%|██████▌   | 164/250 [01:33<00:44,  1.95it/s]
 66%|██████▌   | 165/250 [01:35<01:12,  1.18it/s]
 67%|██████▋   | 167/250 [01:35<00:43,  1.92it/s]
 68%|██████▊   | 169/250 [01:35<00:28,  2.82it/s]
 68%|██████▊   | 170/250 [01:36<00:40,  1.98it/s]
 68%|██████▊   | 171/250 [01:37<00:42,  1.85it/s]
 69%|██████▉   | 172/250 [01:37<00:35,  2.18it/s]
 69%|██████▉   | 173/250 [01:38<00:35,  2.16it/s]
 70%|██████▉   | 174/250 [01:38<00:28,  2.69it/s]
 70%|███████   | 175/250 [01:39<00:37,  2.01it/s]
 70%|███████   | 176/250 [01:39<00:35,  2.07it/s]
 71%|███████   | 177/250 [01:39<00:33,  2.15it/s]
 71%|███████   | 178/250 [01:40<00:36,  1.96it/s]
 72%|███████▏  | 179/250 [01:41<00:39,  1.80it/s]
 72%|███████▏  | 180/250 [01:41<00:35,  1.98it/s]
 72%|███████▏  | 181/250 [01:42<00:33,  2.07it/s]
 73%|███████▎  | 182/250 [01:45<01:30,  1.33s/it]
 73%|███████▎  | 183/250 [01:45<01:10,  1.05s/it]
 74%|███████▎  | 184/250 [01:45<00:50,  1.30it/s]
 74%|███████▍  | 185/250 [01:46<00:37,  1.72it/s]
 75%|███████▍  | 187/250 [01:46<00:21,  2.94it/s]
 75%|███████▌  | 188/250 [01:46<00:18,  3.31it/s]
 76%|███████▌  | 189/250 [01:46<00:21,  2.88it/s]
 76%|███████▌  | 190/250 [01:47<00:20,  2.97it/s]
 76%|███████▋  | 191/250 [01:47<00:20,  2.88it/s]
 77%|███████▋  | 192/250 [01:47<00:22,  2.63it/s]
 77%|███████▋  | 193/250 [01:48<00:21,  2.59it/s]
 78%|███████▊  | 194/250 [01:49<00:32,  1.70it/s]
 78%|███████▊  | 195/250 [01:50<00:45,  1.21it/s]
 78%|███████▊  | 196/250 [01:51<00:39,  1.38it/s]
 79%|███████▉  | 198/250 [01:51<00:24,  2.14it/s]
 80%|████████  | 200/250 [01:52<00:20,  2.42it/s]
 80%|████████  | 201/250 [01:53<00:28,  1.73it/s]
 81%|████████  | 202/250 [01:54<00:29,  1.64it/s]
 81%|████████  | 203/250 [01:54<00:30,  1.54it/s]
 82%|████████▏ | 204/250 [01:55<00:23,  1.92it/s]
 82%|████████▏ | 206/250 [01:55<00:18,  2.43it/s]
 83%|████████▎ | 207/250 [01:56<00:23,  1.83it/s]
 83%|████████▎ | 208/250 [01:56<00:18,  2.27it/s]
 84%|████████▎ | 209/250 [01:57<00:22,  1.85it/s]
 84%|████████▍ | 210/250 [01:58<00:21,  1.84it/s]
 84%|████████▍ | 211/250 [01:58<00:16,  2.31it/s]
 85%|████████▍ | 212/250 [01:58<00:16,  2.34it/s]
 85%|████████▌ | 213/250 [01:58<00:14,  2.61it/s]
 86%|████████▌ | 214/250 [01:59<00:10,  3.31it/s]
 86%|████████▌ | 215/250 [01:59<00:10,  3.42it/s]
 86%|████████▋ | 216/250 [01:59<00:08,  4.18it/s]
 87%|████████▋ | 217/250 [01:59<00:07,  4.14it/s]
 87%|████████▋ | 218/250 [02:01<00:17,  1.78it/s]
 88%|████████▊ | 219/250 [02:02<00:24,  1.27it/s]
 88%|████████▊ | 220/250 [02:02<00:20,  1.49it/s]
 89%|████████▉ | 222/250 [02:02<00:11,  2.37it/s]
 89%|████████▉ | 223/250 [02:03<00:10,  2.60it/s]
 90%|████████▉ | 224/250 [02:03<00:11,  2.35it/s]
 90%|█████████ | 225/250 [02:03<00:09,  2.77it/s]
 91%|█████████ | 227/250 [02:04<00:05,  3.84it/s]
 91%|█████████ | 228/250 [02:04<00:05,  4.03it/s]
 92%|█████████▏| 229/250 [02:04<00:04,  4.35it/s]
 92%|█████████▏| 231/250 [02:04<00:03,  5.90it/s]
 93%|█████████▎| 232/250 [02:05<00:04,  4.26it/s]
 93%|█████████▎| 233/250 [02:05<00:04,  3.57it/s]
 94%|█████████▎| 234/250 [02:05<00:03,  4.11it/s]
 94%|█████████▍| 235/250 [02:05<00:03,  4.58it/s]
 94%|█████████▍| 236/250 [02:06<00:03,  4.49it/s]
 95%|█████████▍| 237/250 [02:07<00:07,  1.82it/s]
 96%|█████████▌| 239/250 [02:08<00:04,  2.27it/s]
 96%|█████████▌| 240/250 [02:08<00:03,  2.51it/s]
 96%|█████████▋| 241/250 [02:08<00:03,  2.47it/s]
 97%|█████████▋| 242/250 [02:10<00:05,  1.36it/s]
 97%|█████████▋| 243/250 [02:12<00:07,  1.11s/it]
 98%|█████████▊| 244/250 [02:13<00:06,  1.07s/it]
 98%|█████████▊| 245/250 [02:15<00:06,  1.25s/it]
 98%|█████████▊| 246/250 [02:19<00:08,  2.14s/it]
 99%|█████████▉| 247/250 [02:21<00:06,  2.01s/it]
100%|█████████▉| 249/250 [02:22<00:01,  1.27s/it]
100%|██████████| 250/250 [02:22<00:00,  1.04s/it]
100%|██████████| 250/250 [02:22<00:00,  1.76it/s]
2025-05-16 13:24:18,705 - modnet - INFO - Loss per individual: ind 0: 130.324 	ind 1: 42.812 	ind 2: 45.330 	ind 3: 524.945 	ind 4: 306.232 	ind 5: 301.124 	ind 6: 61.416 	ind 7: 55.181 	ind 8: 44.827 	ind 9: 546.289 	ind 10: 546.996 	ind 11: 301.083 	ind 12: 55.717 	ind 13: 60.318 	ind 14: 58.859 	ind 15: 163.007 	ind 16: 41.919 	ind 17: 301.738 	ind 18: 47.829 	ind 19: 109.709 	ind 20: 79.535 	ind 21: 115.952 	ind 22: 56.245 	ind 23: 301.242 	ind 24: 45.136 	ind 25: 54.571 	ind 26: 47.899 	ind 27: 108.662 	ind 28: 375.580 	ind 29: 52.736 	ind 30: 42.274 	ind 31: 132.650 	ind 32: 283.102 	ind 33: 115.832 	ind 34: 67.356 	ind 35: 55.794 	ind 36: 50.367 	ind 37: 106.689 	ind 38: 44.069 	ind 39: 223.714 	ind 40: 45.036 	ind 41: 41.499 	ind 42: 66.416 	ind 43: 50.796 	ind 44: 212.124 	ind 45: 192.050 	ind 46: 74.686 	ind 47: 38.504 	ind 48: 89.381 	ind 49: 199.902 	
2025-05-16 13:24:18,706 - modnet - INFO - Initial best model details:
2025-05-16 13:24:18,706 - modnet - INFO - Best individual genes: {'act': 'relu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 224, 'fraction1': 0.25, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 0.5, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 64, 'n_feat': 360, 'n_layers': 5, 'layer_mults': None}
2025-05-16 13:24:18,706 - modnet - INFO - Initial validation loss: 38.5040
2025-05-16 13:24:18,706 - modnet - INFO - Generation number 1

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:05<24:00,  5.78s/it]
  1%|          | 2/250 [00:06<11:26,  2.77s/it]
  1%|          | 3/250 [00:07<07:15,  1.76s/it]
  2%|▏         | 4/250 [00:07<04:34,  1.12s/it]
  2%|▏         | 5/250 [00:07<03:30,  1.16it/s]
  2%|▏         | 6/250 [00:08<04:05,  1.00s/it]
  3%|▎         | 7/250 [00:08<02:52,  1.41it/s]
  3%|▎         | 8/250 [00:10<03:31,  1.15it/s]
  4%|▍         | 10/250 [00:10<01:56,  2.05it/s]
  4%|▍         | 11/250 [00:11<02:51,  1.39it/s]
  5%|▍         | 12/250 [00:12<02:36,  1.52it/s]
  5%|▌         | 13/250 [00:12<02:01,  1.95it/s]
  6%|▌         | 14/250 [00:12<02:06,  1.87it/s]
  6%|▌         | 15/250 [00:13<01:46,  2.20it/s]
  6%|▋         | 16/250 [00:13<02:02,  1.91it/s]
  7%|▋         | 17/250 [00:13<01:33,  2.48it/s]
  7%|▋         | 18/250 [00:14<01:17,  2.99it/s]
  8%|▊         | 20/250 [00:14<00:48,  4.76it/s]
  8%|▊         | 21/250 [00:14<01:07,  3.39it/s]
  9%|▉         | 22/250 [00:15<01:11,  3.17it/s]
  9%|▉         | 23/250 [00:15<00:58,  3.87it/s]
 10%|▉         | 24/250 [00:15<01:05,  3.43it/s]
 10%|█         | 25/250 [00:16<01:23,  2.69it/s]
 10%|█         | 26/250 [00:17<02:16,  1.64it/s]
 11%|█         | 27/250 [00:17<01:48,  2.06it/s]
 11%|█         | 28/250 [00:17<01:31,  2.42it/s]
 12%|█▏        | 29/250 [00:18<02:03,  1.79it/s]
 12%|█▏        | 30/250 [00:19<02:14,  1.64it/s]
 12%|█▏        | 31/250 [00:19<02:05,  1.74it/s]
 13%|█▎        | 32/250 [00:20<02:04,  1.75it/s]
 13%|█▎        | 33/250 [00:21<02:55,  1.24it/s]
 14%|█▎        | 34/250 [00:24<04:36,  1.28s/it]
 14%|█▍        | 35/250 [00:24<03:29,  1.02it/s]
 14%|█▍        | 36/250 [00:28<06:23,  1.79s/it]
 15%|█▍        | 37/250 [00:28<04:49,  1.36s/it]
 15%|█▌        | 38/250 [00:29<04:17,  1.22s/it]
 16%|█▌        | 39/250 [00:29<03:31,  1.00s/it]
 16%|█▌        | 40/250 [00:30<02:37,  1.34it/s]
 16%|█▋        | 41/250 [00:30<02:01,  1.71it/s]
 17%|█▋        | 42/250 [00:31<02:28,  1.40it/s]
 17%|█▋        | 43/250 [00:31<02:14,  1.54it/s]
 18%|█▊        | 44/250 [00:31<01:39,  2.06it/s]
 18%|█▊        | 45/250 [00:32<01:46,  1.92it/s]
 18%|█▊        | 46/250 [00:34<03:35,  1.05s/it]
 19%|█▉        | 47/250 [00:35<02:48,  1.20it/s]
 19%|█▉        | 48/250 [00:35<02:10,  1.55it/s]
 20%|█▉        | 49/250 [00:35<02:08,  1.56it/s]
 20%|██        | 50/250 [00:36<01:39,  2.00it/s]
 20%|██        | 51/250 [00:38<03:22,  1.02s/it]
 21%|██        | 52/250 [00:40<04:12,  1.28s/it]
 21%|██        | 53/250 [00:40<03:33,  1.08s/it]
 22%|██▏       | 54/250 [00:41<02:45,  1.18it/s]
 22%|██▏       | 55/250 [00:41<02:40,  1.22it/s]
 22%|██▏       | 56/250 [00:42<02:08,  1.51it/s]
 23%|██▎       | 58/250 [00:44<03:03,  1.05it/s]
 24%|██▎       | 59/250 [00:45<02:43,  1.17it/s]
 24%|██▍       | 60/250 [00:46<03:03,  1.03it/s]
 24%|██▍       | 61/250 [00:48<03:36,  1.15s/it]
 25%|██▍       | 62/250 [00:50<04:08,  1.32s/it]
 25%|██▌       | 63/250 [00:50<03:03,  1.02it/s]
 26%|██▌       | 64/250 [00:50<02:40,  1.16it/s]
 26%|██▋       | 66/250 [00:50<01:35,  1.94it/s]
 27%|██▋       | 67/250 [00:52<02:04,  1.47it/s]
 27%|██▋       | 68/250 [00:52<02:01,  1.50it/s]
 28%|██▊       | 69/250 [00:54<02:47,  1.08it/s]
 28%|██▊       | 71/250 [00:55<01:59,  1.50it/s]
 29%|██▉       | 72/250 [00:56<02:15,  1.31it/s]
 29%|██▉       | 73/250 [00:56<01:55,  1.54it/s]
 30%|███       | 75/250 [00:56<01:10,  2.50it/s]
 30%|███       | 76/250 [00:57<01:24,  2.07it/s]
 31%|███       | 77/250 [00:57<01:20,  2.14it/s]
 31%|███       | 78/250 [00:58<01:10,  2.43it/s]
 32%|███▏      | 79/250 [00:58<00:55,  3.05it/s]
 32%|███▏      | 80/250 [00:58<01:18,  2.17it/s]
 32%|███▏      | 81/250 [00:59<01:32,  1.82it/s]
 33%|███▎      | 82/250 [00:59<01:11,  2.34it/s]
 34%|███▍      | 85/250 [01:00<00:40,  4.10it/s]
 34%|███▍      | 86/250 [01:00<00:45,  3.59it/s]
 35%|███▍      | 87/250 [01:01<01:06,  2.44it/s]
 35%|███▌      | 88/250 [01:01<00:59,  2.72it/s]
 36%|███▌      | 89/250 [01:01<00:53,  3.01it/s]
 36%|███▌      | 90/250 [01:02<00:46,  3.46it/s]
 37%|███▋      | 92/250 [01:02<00:29,  5.35it/s]
 37%|███▋      | 93/250 [01:02<00:37,  4.21it/s]
 38%|███▊      | 94/250 [01:02<00:42,  3.69it/s]
 38%|███▊      | 95/250 [01:03<00:54,  2.85it/s]
 38%|███▊      | 96/250 [01:04<01:04,  2.38it/s]
 39%|███▉      | 97/250 [01:04<01:00,  2.53it/s]
 40%|███▉      | 99/250 [01:05<01:05,  2.29it/s]
 40%|████      | 100/250 [01:05<00:58,  2.57it/s]
 40%|████      | 101/250 [01:07<01:47,  1.38it/s]
 41%|████      | 102/250 [01:07<01:43,  1.43it/s]
 41%|████      | 103/250 [01:08<01:40,  1.46it/s]
 42%|████▏     | 104/250 [01:09<01:56,  1.26it/s]
 42%|████▏     | 106/250 [01:11<02:16,  1.06it/s]
 43%|████▎     | 107/250 [01:12<02:02,  1.17it/s]
 43%|████▎     | 108/250 [01:12<01:34,  1.50it/s]
 44%|████▍     | 110/250 [01:12<00:57,  2.45it/s]
 44%|████▍     | 111/250 [01:13<01:08,  2.03it/s]
 45%|████▍     | 112/250 [01:13<00:55,  2.47it/s]
 45%|████▌     | 113/250 [01:14<01:09,  1.97it/s]
 46%|████▌     | 114/250 [01:15<01:42,  1.33it/s]
 46%|████▋     | 116/250 [01:17<01:49,  1.22it/s]
 47%|████▋     | 117/250 [01:18<01:36,  1.38it/s]
 47%|████▋     | 118/250 [01:18<01:20,  1.64it/s]
 48%|████▊     | 119/250 [01:18<01:18,  1.66it/s]
 48%|████▊     | 120/250 [01:20<01:38,  1.31it/s]
 48%|████▊     | 121/250 [01:21<02:02,  1.05it/s]
 49%|████▉     | 122/250 [01:22<02:07,  1.00it/s]
 49%|████▉     | 123/250 [01:24<02:50,  1.34s/it]
 50%|████▉     | 124/250 [01:25<02:37,  1.25s/it]
 50%|█████     | 125/250 [01:26<02:16,  1.09s/it]
 50%|█████     | 126/250 [01:29<03:24,  1.65s/it]
 51%|█████     | 127/250 [01:30<02:59,  1.46s/it]
 51%|█████     | 128/250 [01:30<02:18,  1.14s/it]
 52%|█████▏    | 130/250 [01:31<01:26,  1.38it/s]
 52%|█████▏    | 131/250 [01:33<02:08,  1.08s/it]
 53%|█████▎    | 132/250 [01:35<02:25,  1.24s/it]
 53%|█████▎    | 133/250 [01:35<01:53,  1.03it/s]
 54%|█████▎    | 134/250 [01:35<01:33,  1.24it/s]
 54%|█████▍    | 135/250 [01:36<01:19,  1.45it/s]
 54%|█████▍    | 136/250 [01:37<01:24,  1.35it/s]
 55%|█████▍    | 137/250 [01:37<01:02,  1.80it/s]
 55%|█████▌    | 138/250 [01:37<00:47,  2.36it/s]
 56%|█████▋    | 141/250 [01:37<00:31,  3.43it/s]
 57%|█████▋    | 142/250 [01:39<00:51,  2.09it/s]
 57%|█████▋    | 143/250 [01:39<00:43,  2.44it/s]
 58%|█████▊    | 144/250 [01:39<00:39,  2.68it/s]
 58%|█████▊    | 145/250 [01:39<00:38,  2.71it/s]
 58%|█████▊    | 146/250 [01:40<00:48,  2.15it/s]
 59%|█████▉    | 147/250 [01:40<00:42,  2.44it/s]
 59%|█████▉    | 148/250 [01:42<01:13,  1.40it/s]
 60%|█████▉    | 149/250 [01:42<00:55,  1.82it/s]
 60%|██████    | 150/250 [01:43<01:07,  1.48it/s]
 60%|██████    | 151/250 [01:43<00:51,  1.92it/s]
 61%|██████    | 153/250 [01:44<00:39,  2.45it/s]
 62%|██████▏   | 154/250 [01:45<00:57,  1.67it/s]
 62%|██████▏   | 155/250 [01:46<00:58,  1.63it/s]
 62%|██████▏   | 156/250 [01:46<00:52,  1.79it/s]
 63%|██████▎   | 157/250 [01:47<00:53,  1.73it/s]
 63%|██████▎   | 158/250 [01:47<00:53,  1.71it/s]
 64%|██████▍   | 160/250 [01:48<00:47,  1.88it/s]
 64%|██████▍   | 161/250 [01:48<00:40,  2.18it/s]
 65%|██████▍   | 162/250 [01:49<00:37,  2.37it/s]
 65%|██████▌   | 163/250 [01:50<00:46,  1.88it/s]
 66%|██████▌   | 165/250 [01:51<00:56,  1.52it/s]
 66%|██████▋   | 166/250 [01:52<00:54,  1.55it/s]
 67%|██████▋   | 167/250 [01:52<00:44,  1.85it/s]
 67%|██████▋   | 168/250 [01:53<00:53,  1.54it/s]
 68%|██████▊   | 169/250 [01:54<00:57,  1.41it/s]
 68%|██████▊   | 171/250 [01:55<00:46,  1.70it/s]
 69%|██████▉   | 172/250 [01:55<00:40,  1.95it/s]
 69%|██████▉   | 173/250 [01:55<00:31,  2.44it/s]
 70%|██████▉   | 174/250 [01:56<00:37,  2.05it/s]
 70%|███████   | 175/250 [01:56<00:34,  2.15it/s]
 70%|███████   | 176/250 [01:57<00:40,  1.82it/s]
 71%|███████   | 177/250 [01:58<00:42,  1.73it/s]
 72%|███████▏  | 179/250 [01:58<00:30,  2.30it/s]
 72%|███████▏  | 180/250 [01:59<00:31,  2.23it/s]
 72%|███████▏  | 181/250 [01:59<00:37,  1.82it/s]
 73%|███████▎  | 182/250 [02:00<00:36,  1.87it/s]
 73%|███████▎  | 183/250 [02:00<00:28,  2.34it/s]
 74%|███████▎  | 184/250 [02:01<00:28,  2.29it/s]
 74%|███████▍  | 185/250 [02:02<00:45,  1.42it/s]
 74%|███████▍  | 186/250 [02:02<00:34,  1.85it/s]
 75%|███████▌  | 188/250 [02:03<00:36,  1.70it/s]
 76%|███████▌  | 189/250 [02:04<00:36,  1.68it/s]
 76%|███████▌  | 190/250 [02:05<00:36,  1.64it/s]
 76%|███████▋  | 191/250 [02:05<00:29,  1.98it/s]
 77%|███████▋  | 192/250 [02:05<00:25,  2.29it/s]
 77%|███████▋  | 193/250 [02:05<00:21,  2.60it/s]
 78%|███████▊  | 194/250 [02:05<00:16,  3.30it/s]
 78%|███████▊  | 196/250 [02:06<00:10,  5.16it/s]
 79%|███████▉  | 197/250 [02:06<00:11,  4.69it/s]
 79%|███████▉  | 198/250 [02:06<00:10,  5.15it/s]
 80%|███████▉  | 199/250 [02:06<00:08,  5.87it/s]
 80%|████████  | 200/250 [02:07<00:13,  3.62it/s]
 80%|████████  | 201/250 [02:07<00:18,  2.64it/s]
 81%|████████  | 202/250 [02:08<00:17,  2.76it/s]
 81%|████████  | 203/250 [02:09<00:26,  1.78it/s]
 82%|████████▏ | 204/250 [02:09<00:21,  2.18it/s]
 82%|████████▏ | 206/250 [02:09<00:12,  3.39it/s]
 83%|████████▎ | 207/250 [02:11<00:25,  1.70it/s]
 84%|████████▎ | 209/250 [02:11<00:21,  1.92it/s]
 84%|████████▍ | 210/250 [02:12<00:22,  1.76it/s]
 84%|████████▍ | 211/250 [02:12<00:19,  2.02it/s]
 85%|████████▍ | 212/250 [02:13<00:23,  1.63it/s]
 86%|████████▌ | 214/250 [02:14<00:15,  2.35it/s]
 86%|████████▋ | 216/250 [02:14<00:13,  2.56it/s]
 87%|████████▋ | 217/250 [02:15<00:11,  2.79it/s]
 87%|████████▋ | 218/250 [02:15<00:10,  3.00it/s]
 88%|████████▊ | 219/250 [02:15<00:11,  2.68it/s]
 88%|████████▊ | 220/250 [02:16<00:09,  3.01it/s]
 88%|████████▊ | 221/250 [02:16<00:08,  3.41it/s]
 89%|████████▉ | 222/250 [02:16<00:07,  3.77it/s]
 89%|████████▉ | 223/250 [02:16<00:08,  3.33it/s]
 90%|█████████ | 225/250 [02:17<00:10,  2.45it/s]
 90%|█████████ | 226/250 [02:18<00:08,  2.68it/s]
 91%|█████████ | 227/250 [02:18<00:11,  2.09it/s]
 92%|█████████▏| 230/250 [02:19<00:05,  3.93it/s]
 92%|█████████▏| 231/250 [02:20<00:08,  2.36it/s]
 93%|█████████▎| 233/250 [02:20<00:04,  3.41it/s]
 94%|█████████▍| 235/250 [02:20<00:03,  4.24it/s]
 94%|█████████▍| 236/250 [02:20<00:03,  3.90it/s]
 95%|█████████▍| 237/250 [02:21<00:03,  3.81it/s]
 95%|█████████▌| 238/250 [02:21<00:03,  3.59it/s]
 96%|█████████▌| 239/250 [02:21<00:02,  3.88it/s]
 96%|█████████▋| 241/250 [02:22<00:03,  2.91it/s]
 97%|█████████▋| 242/250 [02:23<00:03,  2.47it/s]
 97%|█████████▋| 243/250 [02:23<00:03,  2.07it/s]
 98%|█████████▊| 244/250 [02:24<00:03,  1.70it/s]
 98%|█████████▊| 245/250 [02:26<00:04,  1.04it/s]
 99%|█████████▉| 247/250 [02:27<00:02,  1.39it/s]
 99%|█████████▉| 248/250 [02:28<00:01,  1.56it/s]
100%|█████████▉| 249/250 [02:29<00:00,  1.09it/s]
100%|██████████| 250/250 [02:33<00:00,  1.57s/it]
100%|██████████| 250/250 [02:33<00:00,  1.63it/s]
2025-05-16 13:26:51,866 - modnet - INFO - Loss per individual: ind 0: 68.643 	ind 1: 38.426 	ind 2: 50.858 	ind 3: 45.575 	ind 4: 50.864 	ind 5: 41.498 	ind 6: 42.799 	ind 7: 44.021 	ind 8: 44.004 	ind 9: 37.460 	ind 10: 244.142 	ind 11: 42.933 	ind 12: 50.437 	ind 13: 43.862 	ind 14: 40.374 	ind 15: 45.382 	ind 16: 41.280 	ind 17: 56.459 	ind 18: 47.708 	ind 19: 45.442 	ind 20: 37.042 	ind 21: 50.105 	ind 22: 46.642 	ind 23: 70.930 	ind 24: 42.309 	ind 25: 47.208 	ind 26: 46.263 	ind 27: 39.853 	ind 28: 41.993 	ind 29: 38.537 	ind 30: 42.000 	ind 31: 44.123 	ind 32: 39.004 	ind 33: 52.873 	ind 34: 41.325 	ind 35: 39.670 	ind 36: 47.188 	ind 37: 49.442 	ind 38: 54.897 	ind 39: 53.338 	ind 40: 54.336 	ind 41: 50.670 	ind 42: 58.214 	ind 43: 61.203 	ind 44: 55.711 	ind 45: 50.063 	ind 46: 52.069 	ind 47: 41.133 	ind 48: 38.774 	ind 49: 54.163 	
2025-05-16 13:26:51,867 - modnet - INFO - After generation 1:
2025-05-16 13:26:51,867 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 224, 'fraction1': 0.25, 'fraction2': 1, 'fraction3': 0.5, 'fraction4': 0.5, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 32, 'n_feat': 360, 'n_layers': 2, 'layer_mults': None}
2025-05-16 13:26:51,867 - modnet - INFO - Best validation loss: 37.0416
2025-05-16 13:26:51,867 - modnet - INFO - Generation number 2

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:03<13:52,  3.34s/it]
  1%|          | 2/250 [00:06<13:06,  3.17s/it]
  1%|          | 3/250 [00:06<07:35,  1.85s/it]
  2%|▏         | 4/250 [00:07<05:29,  1.34s/it]
  2%|▏         | 5/250 [00:08<04:57,  1.22s/it]
  3%|▎         | 7/250 [00:08<02:52,  1.41it/s]
  3%|▎         | 8/250 [00:09<02:54,  1.39it/s]
  4%|▎         | 9/250 [00:10<03:04,  1.31it/s]
  4%|▍         | 10/250 [00:10<02:26,  1.64it/s]
  4%|▍         | 11/250 [00:11<02:34,  1.54it/s]
  5%|▍         | 12/250 [00:11<02:27,  1.61it/s]
  5%|▌         | 13/250 [00:12<02:09,  1.83it/s]
  6%|▌         | 15/250 [00:12<01:34,  2.48it/s]
  7%|▋         | 17/250 [00:12<01:01,  3.77it/s]
  7%|▋         | 18/250 [00:13<01:49,  2.12it/s]
  8%|▊         | 19/250 [00:14<01:35,  2.43it/s]
  8%|▊         | 20/250 [00:14<01:45,  2.18it/s]
  9%|▉         | 22/250 [00:14<01:10,  3.24it/s]
 10%|█         | 25/250 [00:16<01:29,  2.52it/s]
 10%|█         | 26/250 [00:16<01:28,  2.54it/s]
 11%|█         | 27/250 [00:17<01:41,  2.20it/s]
 11%|█         | 28/250 [00:17<01:38,  2.26it/s]
 12%|█▏        | 30/250 [00:18<01:44,  2.11it/s]
 12%|█▏        | 31/250 [00:19<02:08,  1.70it/s]
 13%|█▎        | 32/250 [00:20<01:42,  2.12it/s]
 13%|█▎        | 33/250 [00:20<01:23,  2.59it/s]
 14%|█▎        | 34/250 [00:20<01:27,  2.46it/s]
 14%|█▍        | 36/250 [00:20<00:57,  3.71it/s]
 15%|█▍        | 37/250 [00:21<01:04,  3.31it/s]
 15%|█▌        | 38/250 [00:22<01:29,  2.37it/s]
 16%|█▌        | 39/250 [00:25<03:56,  1.12s/it]
 16%|█▋        | 41/250 [00:25<02:19,  1.49it/s]
 17%|█▋        | 42/250 [00:26<02:24,  1.44it/s]
 18%|█▊        | 44/250 [00:26<01:32,  2.24it/s]
 18%|█▊        | 45/250 [00:26<01:42,  1.99it/s]
 18%|█▊        | 46/250 [00:27<01:25,  2.39it/s]
 19%|█▉        | 47/250 [00:27<01:11,  2.82it/s]
 19%|█▉        | 48/250 [00:27<01:22,  2.44it/s]
 20%|█▉        | 49/250 [00:28<01:16,  2.63it/s]
 20%|██        | 50/250 [00:28<01:15,  2.65it/s]
 20%|██        | 51/250 [00:28<01:13,  2.72it/s]
 21%|██        | 52/250 [00:29<01:38,  2.02it/s]
 21%|██        | 53/250 [00:29<01:24,  2.33it/s]
 22%|██▏       | 54/250 [00:31<02:32,  1.28it/s]
 22%|██▏       | 55/250 [00:32<02:36,  1.24it/s]
 22%|██▏       | 56/250 [00:33<02:45,  1.17it/s]
 23%|██▎       | 57/250 [00:33<02:08,  1.51it/s]
 23%|██▎       | 58/250 [00:33<01:37,  1.96it/s]
 24%|██▎       | 59/250 [00:33<01:26,  2.20it/s]
 24%|██▍       | 60/250 [00:34<01:07,  2.83it/s]
 24%|██▍       | 61/250 [00:35<02:21,  1.33it/s]
 25%|██▍       | 62/250 [00:36<02:42,  1.16it/s]
 25%|██▌       | 63/250 [00:37<02:16,  1.36it/s]
 26%|██▌       | 64/250 [00:38<02:47,  1.11it/s]
 26%|██▌       | 65/250 [00:38<02:10,  1.41it/s]
 27%|██▋       | 67/250 [00:39<01:21,  2.25it/s]
 27%|██▋       | 68/250 [00:39<01:13,  2.47it/s]
 28%|██▊       | 69/250 [00:39<01:18,  2.31it/s]
 28%|██▊       | 70/250 [00:41<02:34,  1.16it/s]
 28%|██▊       | 71/250 [00:42<02:21,  1.27it/s]
 29%|██▉       | 72/250 [00:42<01:47,  1.66it/s]
 29%|██▉       | 73/250 [00:43<01:54,  1.55it/s]
 30%|██▉       | 74/250 [00:44<02:20,  1.25it/s]
 30%|███       | 75/250 [00:45<02:08,  1.36it/s]
 30%|███       | 76/250 [00:45<01:36,  1.81it/s]
 31%|███       | 77/250 [00:45<01:14,  2.31it/s]
 31%|███       | 78/250 [00:45<01:02,  2.74it/s]
 32%|███▏      | 79/250 [00:45<00:55,  3.06it/s]
 32%|███▏      | 81/250 [00:46<01:11,  2.37it/s]
 33%|███▎      | 82/250 [00:47<01:34,  1.78it/s]
 34%|███▎      | 84/250 [00:48<00:59,  2.78it/s]
 34%|███▍      | 85/250 [00:48<01:08,  2.41it/s]
 34%|███▍      | 86/250 [00:49<01:17,  2.12it/s]
 35%|███▍      | 87/250 [00:49<01:10,  2.31it/s]
 35%|███▌      | 88/250 [00:49<00:59,  2.72it/s]
 36%|███▌      | 89/250 [00:50<00:49,  3.27it/s]
 36%|███▌      | 90/250 [00:50<00:48,  3.30it/s]
 36%|███▋      | 91/250 [00:50<00:53,  3.00it/s]
 37%|███▋      | 92/250 [00:51<00:52,  3.01it/s]
 37%|███▋      | 93/250 [00:51<00:43,  3.61it/s]
 38%|███▊      | 94/250 [00:51<00:43,  3.58it/s]
 38%|███▊      | 95/250 [00:51<00:39,  3.91it/s]
 39%|███▉      | 97/250 [00:51<00:24,  6.15it/s]
 39%|███▉      | 98/250 [00:52<00:37,  4.05it/s]
 40%|███▉      | 99/250 [00:52<00:36,  4.17it/s]
 40%|████      | 101/250 [00:52<00:25,  5.94it/s]
 41%|████      | 102/250 [00:53<00:32,  4.56it/s]
 41%|████      | 103/250 [00:53<00:57,  2.54it/s]
 42%|████▏     | 104/250 [00:54<00:57,  2.54it/s]
 42%|████▏     | 105/250 [00:54<00:57,  2.52it/s]
 42%|████▏     | 106/250 [00:56<01:35,  1.51it/s]
 43%|████▎     | 107/250 [00:57<02:00,  1.19it/s]
 43%|████▎     | 108/250 [00:59<03:03,  1.29s/it]
 44%|████▎     | 109/250 [01:00<02:37,  1.12s/it]
 44%|████▍     | 111/250 [01:02<02:40,  1.15s/it]
 45%|████▍     | 112/250 [01:03<02:33,  1.11s/it]
 45%|████▌     | 113/250 [01:04<01:59,  1.15it/s]
 46%|████▌     | 114/250 [01:04<01:42,  1.32it/s]
 46%|████▌     | 115/250 [01:04<01:17,  1.74it/s]
 46%|████▋     | 116/250 [01:05<01:11,  1.86it/s]
 47%|████▋     | 118/250 [01:05<00:52,  2.49it/s]
 48%|████▊     | 119/250 [01:05<00:46,  2.84it/s]
 48%|████▊     | 120/250 [01:05<00:42,  3.02it/s]
 48%|████▊     | 121/250 [01:07<01:27,  1.48it/s]
 50%|████▉     | 124/250 [01:08<00:58,  2.17it/s]
 50%|█████     | 126/250 [01:08<00:43,  2.85it/s]
 51%|█████     | 127/250 [01:09<00:52,  2.36it/s]
 51%|█████     | 128/250 [01:10<01:05,  1.86it/s]
 52%|█████▏    | 129/250 [01:10<00:56,  2.13it/s]
 52%|█████▏    | 130/250 [01:10<00:54,  2.21it/s]
 52%|█████▏    | 131/250 [01:11<00:57,  2.08it/s]
 53%|█████▎    | 132/250 [01:11<00:52,  2.23it/s]
 53%|█████▎    | 133/250 [01:13<01:19,  1.48it/s]
 54%|█████▎    | 134/250 [01:13<01:07,  1.72it/s]
 54%|█████▍    | 135/250 [01:13<01:02,  1.83it/s]
 55%|█████▍    | 137/250 [01:15<01:02,  1.82it/s]
 55%|█████▌    | 138/250 [01:15<00:51,  2.18it/s]
 56%|█████▌    | 139/250 [01:16<01:02,  1.77it/s]
 56%|█████▌    | 140/250 [01:16<00:51,  2.14it/s]
 56%|█████▋    | 141/250 [01:17<01:01,  1.77it/s]
 57%|█████▋    | 142/250 [01:17<00:52,  2.04it/s]
 58%|█████▊    | 144/250 [01:18<00:56,  1.87it/s]
 58%|█████▊    | 145/250 [01:19<01:03,  1.67it/s]
 59%|█████▉    | 147/250 [01:20<00:54,  1.90it/s]
 59%|█████▉    | 148/250 [01:20<00:54,  1.86it/s]
 60%|█████▉    | 149/250 [01:21<01:05,  1.53it/s]
 60%|██████    | 150/250 [01:26<02:56,  1.77s/it]
 60%|██████    | 151/250 [01:28<02:49,  1.71s/it]
 61%|██████    | 152/250 [01:28<02:15,  1.39s/it]
 61%|██████    | 153/250 [01:29<01:41,  1.04s/it]
 62%|██████▏   | 154/250 [01:29<01:17,  1.24it/s]
 62%|██████▏   | 155/250 [01:30<01:24,  1.12it/s]
 62%|██████▏   | 156/250 [01:31<01:20,  1.17it/s]
 63%|██████▎   | 157/250 [01:31<01:12,  1.28it/s]
 63%|██████▎   | 158/250 [01:32<01:06,  1.38it/s]
 64%|██████▎   | 159/250 [01:32<00:58,  1.55it/s]
 64%|██████▍   | 160/250 [01:33<00:51,  1.75it/s]
 64%|██████▍   | 161/250 [01:34<00:57,  1.56it/s]
 65%|██████▍   | 162/250 [01:34<01:00,  1.46it/s]
 66%|██████▌   | 164/250 [01:36<01:08,  1.26it/s]
 66%|██████▌   | 165/250 [01:37<01:02,  1.35it/s]
 66%|██████▋   | 166/250 [01:37<00:53,  1.56it/s]
 67%|██████▋   | 167/250 [01:38<00:52,  1.58it/s]
 67%|██████▋   | 168/250 [01:38<00:51,  1.59it/s]
 68%|██████▊   | 169/250 [01:39<01:00,  1.33it/s]
 68%|██████▊   | 170/250 [01:40<00:47,  1.68it/s]
 68%|██████▊   | 171/250 [01:41<00:58,  1.36it/s]
 69%|██████▉   | 172/250 [01:42<00:58,  1.32it/s]
 69%|██████▉   | 173/250 [01:42<00:46,  1.64it/s]
 70%|██████▉   | 174/250 [01:42<00:37,  2.03it/s]
 70%|███████   | 175/250 [01:43<00:38,  1.97it/s]
 70%|███████   | 176/250 [01:43<00:34,  2.13it/s]
 71%|███████   | 177/250 [01:43<00:28,  2.54it/s]
 71%|███████   | 178/250 [01:44<00:46,  1.55it/s]
 72%|███████▏  | 179/250 [01:45<00:36,  1.94it/s]
 72%|███████▏  | 180/250 [01:45<00:33,  2.11it/s]
 72%|███████▏  | 181/250 [01:45<00:26,  2.56it/s]
 73%|███████▎  | 182/250 [01:46<00:31,  2.19it/s]
 74%|███████▎  | 184/250 [01:47<00:36,  1.83it/s]
 74%|███████▍  | 185/250 [01:48<00:37,  1.75it/s]
 74%|███████▍  | 186/250 [01:49<00:41,  1.55it/s]
 75%|███████▍  | 187/250 [01:49<00:34,  1.82it/s]
 75%|███████▌  | 188/250 [01:50<00:44,  1.39it/s]
 76%|███████▌  | 189/250 [01:50<00:34,  1.76it/s]
 76%|███████▌  | 190/250 [01:52<00:48,  1.23it/s]
 77%|███████▋  | 192/250 [01:53<00:39,  1.45it/s]
 77%|███████▋  | 193/250 [01:53<00:39,  1.44it/s]
 78%|███████▊  | 194/250 [01:55<00:48,  1.14it/s]
 78%|███████▊  | 195/250 [01:56<00:58,  1.07s/it]
 78%|███████▊  | 196/250 [01:58<01:09,  1.29s/it]
 79%|███████▉  | 197/250 [01:58<00:51,  1.04it/s]
 79%|███████▉  | 198/250 [02:02<01:27,  1.69s/it]
 80%|████████  | 200/250 [02:02<00:48,  1.04it/s]
 80%|████████  | 201/250 [02:02<00:38,  1.29it/s]
 81%|████████  | 202/250 [02:02<00:29,  1.63it/s]
 82%|████████▏ | 204/250 [02:04<00:30,  1.52it/s]
 82%|████████▏ | 205/250 [02:04<00:24,  1.87it/s]
 82%|████████▏ | 206/250 [02:05<00:27,  1.61it/s]
 83%|████████▎ | 207/250 [02:07<00:39,  1.08it/s]
 83%|████████▎ | 208/250 [02:07<00:32,  1.27it/s]
 84%|████████▎ | 209/250 [02:08<00:31,  1.31it/s]
 84%|████████▍ | 210/250 [02:10<00:43,  1.08s/it]
 84%|████████▍ | 211/250 [02:10<00:34,  1.13it/s]
 85%|████████▌ | 213/250 [02:10<00:20,  1.79it/s]
 86%|████████▌ | 214/250 [02:10<00:16,  2.22it/s]
 86%|████████▋ | 216/250 [02:11<00:12,  2.69it/s]
 87%|████████▋ | 217/250 [02:11<00:13,  2.48it/s]
 88%|████████▊ | 219/250 [02:12<00:12,  2.45it/s]
 88%|████████▊ | 220/250 [02:14<00:18,  1.62it/s]
 88%|████████▊ | 221/250 [02:14<00:15,  1.87it/s]
 89%|████████▉ | 222/250 [02:15<00:18,  1.55it/s]
 89%|████████▉ | 223/250 [02:15<00:16,  1.67it/s]
 90%|████████▉ | 224/250 [02:16<00:12,  2.02it/s]
 90%|█████████ | 225/250 [02:17<00:19,  1.28it/s]
 90%|█████████ | 226/250 [02:18<00:20,  1.19it/s]
 91%|█████████ | 227/250 [02:19<00:19,  1.19it/s]
 91%|█████████ | 228/250 [02:20<00:17,  1.27it/s]
 92%|█████████▏| 229/250 [02:20<00:13,  1.59it/s]
 92%|█████████▏| 230/250 [02:21<00:14,  1.38it/s]
 92%|█████████▏| 231/250 [02:21<00:11,  1.71it/s]
 93%|█████████▎| 232/250 [02:21<00:08,  2.07it/s]
 93%|█████████▎| 233/250 [02:21<00:06,  2.50it/s]
 94%|█████████▍| 235/250 [02:22<00:03,  4.09it/s]
 94%|█████████▍| 236/250 [02:22<00:04,  3.05it/s]
 95%|█████████▍| 237/250 [02:23<00:04,  2.80it/s]
 95%|█████████▌| 238/250 [02:23<00:04,  2.79it/s]
 96%|█████████▌| 239/250 [02:25<00:08,  1.24it/s]
 96%|█████████▌| 240/250 [02:26<00:08,  1.20it/s]
 96%|█████████▋| 241/250 [02:26<00:06,  1.34it/s]
 97%|█████████▋| 242/250 [02:27<00:04,  1.67it/s]
 97%|█████████▋| 243/250 [02:27<00:03,  1.86it/s]
 98%|█████████▊| 244/250 [02:31<00:09,  1.62s/it]
 98%|█████████▊| 245/250 [02:31<00:05,  1.18s/it]
 98%|█████████▊| 246/250 [02:33<00:04,  1.20s/it]
100%|█████████▉| 249/250 [02:33<00:00,  1.53it/s]
100%|██████████| 250/250 [02:37<00:00,  1.28s/it]
100%|██████████| 250/250 [02:37<00:00,  1.59it/s]
2025-05-16 13:29:29,292 - modnet - INFO - Loss per individual: ind 0: 51.759 	ind 1: 39.917 	ind 2: 48.694 	ind 3: 41.569 	ind 4: 38.971 	ind 5: 203.396 	ind 6: 38.087 	ind 7: 116.377 	ind 8: 51.748 	ind 9: 50.385 	ind 10: 52.116 	ind 11: 43.648 	ind 12: 69.681 	ind 13: 42.238 	ind 14: 44.100 	ind 15: 81.822 	ind 16: 38.878 	ind 17: 46.066 	ind 18: 44.490 	ind 19: 53.075 	ind 20: 51.501 	ind 21: 41.309 	ind 22: 38.849 	ind 23: 39.112 	ind 24: 40.836 	ind 25: 43.269 	ind 26: 41.044 	ind 27: 40.572 	ind 28: 59.700 	ind 29: 38.356 	ind 30: 39.151 	ind 31: 44.590 	ind 32: 41.489 	ind 33: 43.290 	ind 34: 36.617 	ind 35: 60.943 	ind 36: 93.234 	ind 37: 61.408 	ind 38: 45.437 	ind 39: 45.824 	ind 40: 40.273 	ind 41: 42.986 	ind 42: 38.662 	ind 43: 40.422 	ind 44: 51.481 	ind 45: 41.340 	ind 46: 53.187 	ind 47: 55.134 	ind 48: 49.279 	ind 49: 45.002 	
2025-05-16 13:29:29,296 - modnet - INFO - After generation 2:
2025-05-16 13:29:29,296 - modnet - INFO - Best individual genes: {'act': 'relu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 320, 'fraction1': 1, 'fraction2': 1, 'fraction3': 0.5, 'fraction4': 0.5, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 490, 'n_layers': 4, 'layer_mults': None}
2025-05-16 13:29:29,296 - modnet - INFO - Best validation loss: 36.6165
2025-05-16 13:29:29,296 - modnet - INFO - Generation number 3

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:07<29:22,  7.08s/it]
  1%|          | 2/250 [00:08<16:20,  3.95s/it]
  1%|          | 3/250 [00:09<10:43,  2.61s/it]
  2%|▏         | 5/250 [00:10<05:16,  1.29s/it]
  2%|▏         | 6/250 [00:11<04:32,  1.12s/it]
  3%|▎         | 7/250 [00:11<03:51,  1.05it/s]
  3%|▎         | 8/250 [00:12<03:33,  1.13it/s]
  4%|▎         | 9/250 [00:13<03:32,  1.13it/s]
  4%|▍         | 10/250 [00:13<02:40,  1.50it/s]
  5%|▍         | 12/250 [00:14<02:08,  1.85it/s]
  5%|▌         | 13/250 [00:14<02:03,  1.92it/s]
  6%|▌         | 14/250 [00:15<02:32,  1.55it/s]
  6%|▌         | 15/250 [00:17<03:19,  1.18it/s]
  6%|▋         | 16/250 [00:17<03:18,  1.18it/s]
  7%|▋         | 17/250 [00:18<02:35,  1.50it/s]
  7%|▋         | 18/250 [00:18<02:01,  1.92it/s]
  8%|▊         | 19/250 [00:19<02:19,  1.66it/s]
  8%|▊         | 20/250 [00:21<04:05,  1.07s/it]
  9%|▉         | 22/250 [00:22<02:55,  1.30it/s]
  9%|▉         | 23/250 [00:23<03:31,  1.08it/s]
 10%|▉         | 24/250 [00:23<02:47,  1.35it/s]
 10%|█         | 25/250 [00:24<02:50,  1.32it/s]
 10%|█         | 26/250 [00:24<02:15,  1.65it/s]
 11%|█         | 27/250 [00:24<01:43,  2.15it/s]
 11%|█         | 28/250 [00:25<02:03,  1.80it/s]
 12%|█▏        | 29/250 [00:26<01:54,  1.94it/s]
 12%|█▏        | 30/250 [00:26<01:29,  2.46it/s]
 12%|█▏        | 31/250 [00:28<03:14,  1.12it/s]
 13%|█▎        | 32/250 [00:29<03:25,  1.06it/s]
 14%|█▎        | 34/250 [00:29<01:59,  1.80it/s]
 14%|█▍        | 35/250 [00:29<01:48,  1.97it/s]
 14%|█▍        | 36/250 [00:32<03:47,  1.06s/it]
 15%|█▌        | 38/250 [00:32<02:24,  1.46it/s]
 16%|█▌        | 40/250 [00:33<01:58,  1.77it/s]
 16%|█▋        | 41/250 [00:34<02:17,  1.52it/s]
 17%|█▋        | 43/250 [00:34<01:40,  2.06it/s]
 18%|█▊        | 45/250 [00:35<01:10,  2.91it/s]
 18%|█▊        | 46/250 [00:35<01:14,  2.73it/s]
 19%|█▉        | 47/250 [00:35<01:14,  2.71it/s]
 19%|█▉        | 48/250 [00:37<01:54,  1.76it/s]
 20%|█▉        | 49/250 [00:37<01:42,  1.97it/s]
 20%|██        | 50/250 [00:37<01:30,  2.21it/s]
 20%|██        | 51/250 [00:37<01:12,  2.75it/s]
 21%|██        | 52/250 [00:38<01:04,  3.06it/s]
 21%|██        | 53/250 [00:38<00:51,  3.80it/s]
 22%|██▏       | 54/250 [00:38<01:13,  2.67it/s]
 22%|██▏       | 55/250 [00:39<01:39,  1.95it/s]
 22%|██▏       | 56/250 [00:42<03:54,  1.21s/it]
 23%|██▎       | 57/250 [00:43<03:12,  1.00it/s]
 23%|██▎       | 58/250 [00:43<02:28,  1.29it/s]
 24%|██▎       | 59/250 [00:43<02:10,  1.46it/s]
 24%|██▍       | 60/250 [00:43<01:37,  1.95it/s]
 24%|██▍       | 61/250 [00:44<01:51,  1.70it/s]
 25%|██▍       | 62/250 [00:45<01:50,  1.70it/s]
 26%|██▌       | 64/250 [00:47<02:51,  1.09it/s]
 26%|██▌       | 65/250 [00:48<02:28,  1.25it/s]
 26%|██▋       | 66/250 [00:48<02:12,  1.39it/s]
 27%|██▋       | 67/250 [00:49<02:09,  1.41it/s]
 27%|██▋       | 68/250 [00:51<03:14,  1.07s/it]
 28%|██▊       | 69/250 [00:51<02:25,  1.25it/s]
 28%|██▊       | 71/250 [00:51<01:24,  2.13it/s]
 29%|██▉       | 72/250 [00:52<01:31,  1.95it/s]
 29%|██▉       | 73/250 [00:52<01:20,  2.19it/s]
 30%|███       | 75/250 [00:52<00:56,  3.12it/s]
 30%|███       | 76/250 [00:53<01:01,  2.81it/s]
 31%|███       | 77/250 [00:54<01:45,  1.65it/s]
 31%|███       | 78/250 [00:55<01:42,  1.68it/s]
 32%|███▏      | 79/250 [00:55<01:21,  2.11it/s]
 32%|███▏      | 80/250 [00:56<01:25,  1.99it/s]
 33%|███▎      | 82/250 [00:57<01:32,  1.81it/s]
 33%|███▎      | 83/250 [00:58<01:51,  1.50it/s]
 34%|███▎      | 84/250 [00:59<02:29,  1.11it/s]
 34%|███▍      | 85/250 [01:00<02:08,  1.28it/s]
 34%|███▍      | 86/250 [01:01<02:20,  1.17it/s]
 35%|███▍      | 87/250 [01:01<01:49,  1.48it/s]
 35%|███▌      | 88/250 [01:02<01:38,  1.64it/s]
 36%|███▌      | 90/250 [01:02<01:13,  2.18it/s]
 36%|███▋      | 91/250 [01:02<00:59,  2.68it/s]
 37%|███▋      | 92/250 [01:03<01:10,  2.25it/s]
 38%|███▊      | 94/250 [01:04<01:15,  2.06it/s]
 38%|███▊      | 95/250 [01:05<01:29,  1.73it/s]
 38%|███▊      | 96/250 [01:05<01:26,  1.78it/s]
 39%|███▉      | 97/250 [01:08<02:48,  1.10s/it]
 39%|███▉      | 98/250 [01:09<02:23,  1.06it/s]
 40%|███▉      | 99/250 [01:09<01:53,  1.33it/s]
 40%|████      | 100/250 [01:09<01:37,  1.54it/s]
 40%|████      | 101/250 [01:10<01:37,  1.52it/s]
 41%|████      | 102/250 [01:10<01:24,  1.74it/s]
 42%|████▏     | 104/250 [01:10<00:53,  2.74it/s]
 42%|████▏     | 106/250 [01:11<00:48,  2.96it/s]
 43%|████▎     | 107/250 [01:11<00:46,  3.08it/s]
 44%|████▎     | 109/250 [01:12<00:41,  3.40it/s]
 44%|████▍     | 110/250 [01:12<00:49,  2.82it/s]
 45%|████▍     | 112/250 [01:13<00:34,  4.00it/s]
 45%|████▌     | 113/250 [01:13<00:38,  3.53it/s]
 46%|████▌     | 114/250 [01:13<00:44,  3.06it/s]
 46%|████▌     | 115/250 [01:14<00:39,  3.41it/s]
 46%|████▋     | 116/250 [01:15<01:04,  2.06it/s]
 47%|████▋     | 117/250 [01:15<01:09,  1.92it/s]
 47%|████▋     | 118/250 [01:16<00:58,  2.24it/s]
 48%|████▊     | 119/250 [01:16<01:00,  2.16it/s]
 48%|████▊     | 120/250 [01:16<00:52,  2.47it/s]
 48%|████▊     | 121/250 [01:18<01:22,  1.56it/s]
 49%|████▉     | 123/250 [01:18<00:52,  2.41it/s]
 50%|████▉     | 124/250 [01:18<00:51,  2.44it/s]
 50%|█████     | 125/250 [01:18<00:44,  2.83it/s]
 50%|█████     | 126/250 [01:19<00:45,  2.75it/s]
 51%|█████     | 127/250 [01:21<01:37,  1.27it/s]
 52%|█████▏    | 129/250 [01:21<00:56,  2.13it/s]
 52%|█████▏    | 130/250 [01:21<00:58,  2.06it/s]
 52%|█████▏    | 131/250 [01:21<00:46,  2.55it/s]
 53%|█████▎    | 132/250 [01:23<01:10,  1.68it/s]
 53%|█████▎    | 133/250 [01:23<00:55,  2.12it/s]
 54%|█████▎    | 134/250 [01:24<01:24,  1.37it/s]
 54%|█████▍    | 135/250 [01:24<01:05,  1.76it/s]
 54%|█████▍    | 136/250 [01:29<03:38,  1.91s/it]
 55%|█████▍    | 137/250 [01:31<03:06,  1.65s/it]
 55%|█████▌    | 138/250 [01:33<03:32,  1.90s/it]
 56%|█████▌    | 139/250 [01:33<02:38,  1.43s/it]
 56%|█████▌    | 140/250 [01:34<02:11,  1.20s/it]
 57%|█████▋    | 142/250 [01:34<01:12,  1.49it/s]
 57%|█████▋    | 143/250 [01:34<00:59,  1.81it/s]
 58%|█████▊    | 144/250 [01:35<00:59,  1.79it/s]
 58%|█████▊    | 145/250 [01:35<00:54,  1.92it/s]
 58%|█████▊    | 146/250 [01:36<01:02,  1.66it/s]
 59%|█████▉    | 147/250 [01:38<01:39,  1.04it/s]
 60%|█████▉    | 149/250 [01:38<00:58,  1.72it/s]
 60%|██████    | 150/250 [01:39<00:52,  1.90it/s]
 61%|██████    | 152/250 [01:41<01:27,  1.12it/s]
 61%|██████    | 153/250 [01:45<02:16,  1.40s/it]
 62%|██████▏   | 154/250 [01:45<01:45,  1.10s/it]
 62%|██████▏   | 155/250 [01:46<01:41,  1.07s/it]
 62%|██████▏   | 156/250 [01:46<01:21,  1.16it/s]
 63%|██████▎   | 158/250 [01:46<00:50,  1.82it/s]
 64%|██████▎   | 159/250 [01:47<00:50,  1.79it/s]
 64%|██████▍   | 161/250 [01:47<00:41,  2.16it/s]
 65%|██████▌   | 163/250 [01:49<00:41,  2.08it/s]
 66%|██████▌   | 165/250 [01:49<00:33,  2.51it/s]
 66%|██████▋   | 166/250 [01:50<00:36,  2.33it/s]
 67%|██████▋   | 167/250 [01:51<01:03,  1.32it/s]
 67%|██████▋   | 168/250 [01:53<01:14,  1.10it/s]
 68%|██████▊   | 169/250 [01:53<01:00,  1.34it/s]
 68%|██████▊   | 170/250 [01:54<00:51,  1.55it/s]
 68%|██████▊   | 171/250 [01:54<00:45,  1.74it/s]
 69%|██████▉   | 172/250 [01:55<00:57,  1.36it/s]
 69%|██████▉   | 173/250 [01:55<00:42,  1.79it/s]
 70%|██████▉   | 174/250 [01:57<01:09,  1.10it/s]
 70%|███████   | 175/250 [01:58<01:01,  1.23it/s]
 71%|███████   | 177/250 [01:58<00:44,  1.63it/s]
 71%|███████   | 178/250 [02:01<01:13,  1.02s/it]
 72%|███████▏  | 179/250 [02:02<01:15,  1.06s/it]
 72%|███████▏  | 181/250 [02:02<00:44,  1.54it/s]
 73%|███████▎  | 182/250 [02:04<01:04,  1.06it/s]
 73%|███████▎  | 183/250 [02:04<00:50,  1.33it/s]
 74%|███████▎  | 184/250 [02:06<01:09,  1.05s/it]
 74%|███████▍  | 185/250 [02:06<00:54,  1.19it/s]
 74%|███████▍  | 186/250 [02:07<00:50,  1.26it/s]
 75%|███████▍  | 187/250 [02:08<00:54,  1.15it/s]
 75%|███████▌  | 188/250 [02:09<00:50,  1.22it/s]
 76%|███████▌  | 189/250 [02:09<00:37,  1.62it/s]
 76%|███████▌  | 190/250 [02:11<01:10,  1.17s/it]
 76%|███████▋  | 191/250 [02:12<01:07,  1.15s/it]
 77%|███████▋  | 192/250 [02:14<01:24,  1.46s/it]
 77%|███████▋  | 193/250 [02:15<01:10,  1.23s/it]
 78%|███████▊  | 194/250 [02:17<01:13,  1.32s/it]
 78%|███████▊  | 195/250 [02:17<00:56,  1.03s/it]
 78%|███████▊  | 196/250 [02:18<00:51,  1.05it/s]
 79%|███████▉  | 197/250 [02:18<00:44,  1.19it/s]
 79%|███████▉  | 198/250 [02:19<00:43,  1.20it/s]
 80%|███████▉  | 199/250 [02:20<00:36,  1.39it/s]
 80%|████████  | 200/250 [02:20<00:28,  1.76it/s]
 80%|████████  | 201/250 [02:23<00:57,  1.18s/it]
 81%|████████  | 202/250 [02:23<00:43,  1.10it/s]
 81%|████████  | 203/250 [02:23<00:34,  1.37it/s]
 82%|████████▏ | 204/250 [02:23<00:25,  1.80it/s]
 82%|████████▏ | 205/250 [02:24<00:27,  1.63it/s]
 82%|████████▏ | 206/250 [02:24<00:21,  2.05it/s]
 83%|████████▎ | 208/250 [02:24<00:13,  3.19it/s]
 84%|████████▎ | 209/250 [02:25<00:12,  3.26it/s]
 84%|████████▍ | 210/250 [02:26<00:22,  1.77it/s]
 84%|████████▍ | 211/250 [02:27<00:26,  1.49it/s]
 85%|████████▌ | 213/250 [02:27<00:15,  2.44it/s]
 86%|████████▌ | 215/250 [02:27<00:09,  3.66it/s]
 87%|████████▋ | 217/250 [02:29<00:15,  2.18it/s]
 87%|████████▋ | 218/250 [02:31<00:26,  1.20it/s]
 88%|████████▊ | 219/250 [02:31<00:21,  1.42it/s]
 88%|████████▊ | 220/250 [02:31<00:16,  1.77it/s]
 89%|████████▉ | 222/250 [02:32<00:12,  2.19it/s]
 90%|█████████ | 225/250 [02:33<00:08,  3.07it/s]
 90%|█████████ | 226/250 [02:34<00:10,  2.20it/s]
 91%|█████████ | 227/250 [02:34<00:11,  2.04it/s]
 91%|█████████ | 228/250 [02:35<00:09,  2.29it/s]
 92%|█████████▏| 229/250 [02:35<00:08,  2.47it/s]
 92%|█████████▏| 231/250 [02:36<00:07,  2.45it/s]
 93%|█████████▎| 232/250 [02:36<00:08,  2.13it/s]
 93%|█████████▎| 233/250 [02:37<00:10,  1.68it/s]
 94%|█████████▎| 234/250 [02:37<00:07,  2.11it/s]
 94%|█████████▍| 235/250 [02:38<00:07,  2.03it/s]
 94%|█████████▍| 236/250 [02:39<00:07,  1.88it/s]
 95%|█████████▍| 237/250 [02:39<00:05,  2.41it/s]
 95%|█████████▌| 238/250 [02:39<00:04,  3.00it/s]
 96%|█████████▌| 239/250 [02:39<00:03,  3.44it/s]
 96%|█████████▌| 240/250 [02:40<00:04,  2.08it/s]
 96%|█████████▋| 241/250 [02:40<00:03,  2.27it/s]
 97%|█████████▋| 242/250 [02:41<00:03,  2.37it/s]
 97%|█████████▋| 243/250 [02:42<00:03,  1.88it/s]
 98%|█████████▊| 244/250 [02:42<00:03,  1.89it/s]
 98%|█████████▊| 245/250 [02:43<00:03,  1.46it/s]
 98%|█████████▊| 246/250 [02:50<00:10,  2.68s/it]
 99%|█████████▉| 247/250 [02:51<00:05,  1.99s/it]
 99%|█████████▉| 248/250 [02:52<00:03,  1.71s/it]
100%|█████████▉| 249/250 [02:53<00:01,  1.52s/it]
100%|██████████| 250/250 [02:53<00:00,  1.21s/it]
100%|██████████| 250/250 [02:53<00:00,  1.44it/s]
2025-05-16 13:32:23,249 - modnet - INFO - Loss per individual: ind 0: 45.507 	ind 1: 42.590 	ind 2: 39.249 	ind 3: 52.451 	ind 4: 39.029 	ind 5: 37.630 	ind 6: 48.585 	ind 7: 48.395 	ind 8: 41.734 	ind 9: 37.379 	ind 10: 37.456 	ind 11: 50.004 	ind 12: 49.690 	ind 13: 56.022 	ind 14: 50.468 	ind 15: 50.122 	ind 16: 45.010 	ind 17: 51.128 	ind 18: 48.381 	ind 19: 39.837 	ind 20: 36.851 	ind 21: 37.364 	ind 22: 43.637 	ind 23: 44.494 	ind 24: 47.213 	ind 25: 61.768 	ind 26: 58.756 	ind 27: 46.002 	ind 28: 45.378 	ind 29: 38.532 	ind 30: 42.948 	ind 31: 37.956 	ind 32: 37.455 	ind 33: 46.572 	ind 34: 40.829 	ind 35: 37.939 	ind 36: 42.310 	ind 37: 47.453 	ind 38: 43.152 	ind 39: 39.985 	ind 40: 42.944 	ind 41: 39.796 	ind 42: 46.722 	ind 43: 37.027 	ind 44: 50.397 	ind 45: 37.037 	ind 46: 48.826 	ind 47: 40.879 	ind 48: 43.243 	ind 49: 44.548 	
2025-05-16 13:32:23,250 - modnet - INFO - After generation 3:
2025-05-16 13:32:23,250 - modnet - INFO - Best individual genes: {'act': 'relu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 320, 'fraction1': 1, 'fraction2': 1, 'fraction3': 0.5, 'fraction4': 0.5, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 490, 'n_layers': 4, 'layer_mults': None}
2025-05-16 13:32:23,250 - modnet - INFO - Best validation loss: 36.6165
2025-05-16 13:32:23,250 - modnet - INFO - Generation number 4

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:06<27:51,  6.71s/it]
  1%|          | 2/250 [00:08<15:44,  3.81s/it]
  1%|          | 3/250 [00:09<10:34,  2.57s/it]
  2%|▏         | 4/250 [00:10<07:48,  1.90s/it]
  2%|▏         | 5/250 [00:12<07:25,  1.82s/it]
  2%|▏         | 6/250 [00:12<05:34,  1.37s/it]
  3%|▎         | 7/250 [00:13<04:27,  1.10s/it]
  3%|▎         | 8/250 [00:14<04:08,  1.03s/it]
  4%|▎         | 9/250 [00:15<04:56,  1.23s/it]
  4%|▍         | 11/250 [00:16<02:54,  1.37it/s]
  5%|▍         | 12/250 [00:20<06:20,  1.60s/it]
  5%|▌         | 13/250 [00:20<05:06,  1.29s/it]
  6%|▌         | 14/250 [00:20<04:01,  1.02s/it]
  6%|▌         | 15/250 [00:22<04:34,  1.17s/it]
  6%|▋         | 16/250 [00:22<03:25,  1.14it/s]
  7%|▋         | 17/250 [00:24<04:04,  1.05s/it]
  7%|▋         | 18/250 [00:24<03:34,  1.08it/s]
  8%|▊         | 19/250 [00:25<02:53,  1.33it/s]
  8%|▊         | 20/250 [00:26<03:14,  1.18it/s]
  8%|▊         | 21/250 [00:26<02:55,  1.30it/s]
  9%|▉         | 22/250 [00:27<02:54,  1.30it/s]
  9%|▉         | 23/250 [00:27<02:23,  1.59it/s]
 10%|▉         | 24/250 [00:29<03:11,  1.18it/s]
 10%|█         | 25/250 [00:29<02:54,  1.29it/s]
 10%|█         | 26/250 [00:30<02:16,  1.64it/s]
 11%|█         | 27/250 [00:31<02:39,  1.40it/s]
 11%|█         | 28/250 [00:31<02:25,  1.52it/s]
 12%|█▏        | 29/250 [00:31<02:05,  1.77it/s]
 12%|█▏        | 30/250 [00:33<02:52,  1.27it/s]
 12%|█▏        | 31/250 [00:33<02:47,  1.31it/s]
 13%|█▎        | 32/250 [00:34<02:49,  1.28it/s]
 13%|█▎        | 33/250 [00:35<02:22,  1.52it/s]
 14%|█▎        | 34/250 [00:36<03:35,  1.00it/s]
 14%|█▍        | 35/250 [00:37<02:41,  1.33it/s]
 14%|█▍        | 36/250 [00:37<02:09,  1.66it/s]
 15%|█▌        | 38/250 [00:38<01:49,  1.94it/s]
 16%|█▌        | 39/250 [00:38<01:54,  1.84it/s]
 16%|█▌        | 40/250 [00:39<01:40,  2.08it/s]
 16%|█▋        | 41/250 [00:39<01:39,  2.09it/s]
 17%|█▋        | 43/250 [00:40<01:25,  2.41it/s]
 18%|█▊        | 44/250 [00:41<01:57,  1.75it/s]
 18%|█▊        | 45/250 [00:42<02:21,  1.45it/s]
 18%|█▊        | 46/250 [00:42<02:04,  1.64it/s]
 19%|█▉        | 47/250 [00:47<05:59,  1.77s/it]
 19%|█▉        | 48/250 [00:49<05:44,  1.70s/it]
 20%|█▉        | 49/250 [00:50<05:46,  1.73s/it]
 20%|██        | 50/250 [00:51<05:08,  1.54s/it]
 20%|██        | 51/250 [00:53<05:07,  1.55s/it]
 21%|██        | 52/250 [00:56<06:46,  2.05s/it]
 21%|██        | 53/250 [00:57<05:14,  1.59s/it]
 22%|██▏       | 54/250 [00:57<03:59,  1.22s/it]
 22%|██▏       | 55/250 [00:58<03:12,  1.01it/s]
 22%|██▏       | 56/250 [00:59<03:29,  1.08s/it]
 23%|██▎       | 57/250 [00:59<02:31,  1.27it/s]
 23%|██▎       | 58/250 [00:59<01:57,  1.64it/s]
 24%|██▎       | 59/250 [01:00<02:06,  1.51it/s]
 24%|██▍       | 61/250 [01:00<01:26,  2.19it/s]
 25%|██▍       | 62/250 [01:00<01:12,  2.61it/s]
 26%|██▌       | 64/250 [01:01<00:46,  4.02it/s]
 26%|██▋       | 66/250 [01:01<00:35,  5.22it/s]
 27%|██▋       | 68/250 [01:02<00:48,  3.78it/s]
 28%|██▊       | 69/250 [01:02<00:51,  3.48it/s]
 28%|██▊       | 70/250 [01:03<01:09,  2.57it/s]
 28%|██▊       | 71/250 [01:04<01:51,  1.61it/s]
 29%|██▉       | 72/250 [01:05<01:57,  1.52it/s]
 29%|██▉       | 73/250 [01:06<01:56,  1.52it/s]
 30%|██▉       | 74/250 [01:06<01:53,  1.55it/s]
 30%|███       | 75/250 [01:06<01:36,  1.81it/s]
 30%|███       | 76/250 [01:07<02:00,  1.44it/s]
 31%|███       | 77/250 [01:11<03:57,  1.37s/it]
 31%|███       | 78/250 [01:11<03:20,  1.17s/it]
 32%|███▏      | 80/250 [01:13<02:50,  1.00s/it]
 32%|███▏      | 81/250 [01:13<02:13,  1.26it/s]
 33%|███▎      | 83/250 [01:13<01:31,  1.82it/s]
 34%|███▎      | 84/250 [01:15<01:57,  1.41it/s]
 34%|███▍      | 85/250 [01:17<03:12,  1.17s/it]
 34%|███▍      | 86/250 [01:18<02:51,  1.05s/it]
 35%|███▍      | 87/250 [01:19<02:44,  1.01s/it]
 35%|███▌      | 88/250 [01:20<02:45,  1.02s/it]
 36%|███▌      | 90/250 [01:21<02:00,  1.33it/s]
 36%|███▋      | 91/250 [01:21<01:58,  1.34it/s]
 37%|███▋      | 92/250 [01:23<02:47,  1.06s/it]
 37%|███▋      | 93/250 [01:25<03:06,  1.19s/it]
 38%|███▊      | 95/250 [01:33<06:04,  2.35s/it]
 38%|███▊      | 96/250 [01:33<04:54,  1.92s/it]
 39%|███▉      | 97/250 [01:33<03:47,  1.48s/it]
 39%|███▉      | 98/250 [01:35<03:57,  1.56s/it]
 40%|███▉      | 99/250 [01:35<03:04,  1.22s/it]
 40%|████      | 100/250 [01:36<02:28,  1.01it/s]
 40%|████      | 101/250 [01:36<01:58,  1.25it/s]
 41%|████      | 102/250 [01:36<01:29,  1.65it/s]
 41%|████      | 103/250 [01:37<01:14,  1.98it/s]
 42%|████▏     | 105/250 [01:37<01:08,  2.12it/s]
 42%|████▏     | 106/250 [01:40<02:16,  1.06it/s]
 43%|████▎     | 107/250 [01:41<02:21,  1.01it/s]
 43%|████▎     | 108/250 [01:41<01:50,  1.29it/s]
 44%|████▎     | 109/250 [01:42<01:43,  1.36it/s]
 44%|████▍     | 110/250 [01:42<01:23,  1.68it/s]
 44%|████▍     | 111/250 [01:43<01:55,  1.20it/s]
 45%|████▌     | 113/250 [01:44<01:27,  1.57it/s]
 46%|████▌     | 114/250 [01:45<01:19,  1.70it/s]
 46%|████▌     | 115/250 [01:46<01:39,  1.35it/s]
 47%|████▋     | 117/250 [01:47<01:27,  1.52it/s]
 47%|████▋     | 118/250 [01:47<01:10,  1.87it/s]
 48%|████▊     | 119/250 [01:48<01:15,  1.73it/s]
 48%|████▊     | 120/250 [01:49<01:25,  1.53it/s]
 48%|████▊     | 121/250 [01:51<02:33,  1.19s/it]
 49%|████▉     | 122/250 [01:53<02:57,  1.38s/it]
 50%|████▉     | 124/250 [01:55<02:16,  1.08s/it]
 50%|█████     | 125/250 [01:55<01:58,  1.05it/s]
 50%|█████     | 126/250 [01:55<01:33,  1.32it/s]
 51%|█████     | 127/250 [01:57<01:56,  1.06it/s]
 51%|█████     | 128/250 [01:59<02:47,  1.38s/it]
 52%|█████▏    | 129/250 [02:01<03:00,  1.49s/it]
 52%|█████▏    | 131/250 [02:02<02:11,  1.10s/it]
 53%|█████▎    | 132/250 [02:02<01:44,  1.13it/s]
 53%|█████▎    | 133/250 [02:03<01:23,  1.41it/s]
 54%|█████▎    | 134/250 [02:03<01:11,  1.62it/s]
 54%|█████▍    | 135/250 [02:05<01:43,  1.12it/s]
 55%|█████▍    | 137/250 [02:05<01:07,  1.68it/s]
 56%|█████▌    | 139/250 [02:06<01:04,  1.73it/s]
 56%|█████▌    | 140/250 [02:06<00:54,  2.00it/s]
 56%|█████▋    | 141/250 [02:08<01:11,  1.53it/s]
 57%|█████▋    | 142/250 [02:08<01:07,  1.61it/s]
 57%|█████▋    | 143/250 [02:09<01:11,  1.50it/s]
 58%|█████▊    | 145/250 [02:09<00:49,  2.13it/s]
 58%|█████▊    | 146/250 [02:10<00:45,  2.30it/s]
 59%|█████▉    | 147/250 [02:10<00:40,  2.52it/s]
 59%|█████▉    | 148/250 [02:11<01:08,  1.50it/s]
 60%|█████▉    | 149/250 [02:12<01:02,  1.62it/s]
 60%|██████    | 150/250 [02:12<00:51,  1.96it/s]
 60%|██████    | 151/250 [02:13<00:52,  1.89it/s]
 61%|██████    | 152/250 [02:13<00:40,  2.45it/s]
 62%|██████▏   | 155/250 [02:13<00:25,  3.67it/s]
 62%|██████▏   | 156/250 [02:13<00:22,  4.16it/s]
 63%|██████▎   | 157/250 [02:14<00:39,  2.35it/s]
 63%|██████▎   | 158/250 [02:15<00:40,  2.29it/s]
 64%|██████▎   | 159/250 [02:16<00:57,  1.59it/s]
 64%|██████▍   | 160/250 [02:16<00:49,  1.83it/s]
 64%|██████▍   | 161/250 [02:17<01:00,  1.48it/s]
 65%|██████▍   | 162/250 [02:18<00:58,  1.50it/s]
 65%|██████▌   | 163/250 [02:18<00:47,  1.84it/s]
 66%|██████▌   | 164/250 [02:19<00:47,  1.80it/s]
 66%|██████▌   | 165/250 [02:20<01:08,  1.24it/s]
 66%|██████▋   | 166/250 [02:21<01:00,  1.38it/s]
 67%|██████▋   | 167/250 [02:22<00:59,  1.39it/s]
 67%|██████▋   | 168/250 [02:22<00:51,  1.61it/s]
 68%|██████▊   | 169/250 [02:23<00:55,  1.47it/s]
 68%|██████▊   | 170/250 [02:23<00:54,  1.47it/s]
 68%|██████▊   | 171/250 [02:24<00:42,  1.86it/s]
 69%|██████▉   | 172/250 [02:24<00:41,  1.88it/s]
 69%|██████▉   | 173/250 [02:24<00:31,  2.47it/s]
 70%|██████▉   | 174/250 [02:25<00:31,  2.41it/s]
 70%|███████   | 176/250 [02:26<00:35,  2.07it/s]
 71%|███████   | 177/250 [02:28<00:57,  1.27it/s]
 71%|███████   | 178/250 [02:30<01:19,  1.11s/it]
 72%|███████▏  | 179/250 [02:31<01:20,  1.13s/it]
 72%|███████▏  | 180/250 [02:31<01:03,  1.10it/s]
 72%|███████▏  | 181/250 [02:33<01:16,  1.11s/it]
 73%|███████▎  | 182/250 [02:34<01:13,  1.08s/it]
 73%|███████▎  | 183/250 [02:34<00:59,  1.13it/s]
 74%|███████▎  | 184/250 [02:34<00:43,  1.52it/s]
 74%|███████▍  | 185/250 [02:35<00:38,  1.69it/s]
 74%|███████▍  | 186/250 [02:36<00:54,  1.18it/s]
 75%|███████▌  | 188/250 [02:37<00:43,  1.42it/s]
 76%|███████▌  | 189/250 [02:38<00:38,  1.59it/s]
 76%|███████▋  | 191/250 [02:38<00:23,  2.47it/s]
 77%|███████▋  | 192/250 [02:38<00:27,  2.10it/s]
 77%|███████▋  | 193/250 [02:39<00:24,  2.37it/s]
 78%|███████▊  | 194/250 [02:39<00:24,  2.29it/s]
 78%|███████▊  | 196/250 [02:39<00:15,  3.56it/s]
 79%|███████▉  | 198/250 [02:40<00:11,  4.36it/s]
 80%|███████▉  | 199/250 [02:40<00:10,  4.92it/s]
 80%|████████  | 200/250 [02:42<00:35,  1.40it/s]
 80%|████████  | 201/250 [02:43<00:31,  1.56it/s]
 81%|████████  | 202/250 [02:43<00:28,  1.68it/s]
 81%|████████  | 203/250 [02:43<00:25,  1.88it/s]
 82%|████████▏ | 204/250 [02:44<00:20,  2.26it/s]
 82%|████████▏ | 205/250 [02:44<00:23,  1.94it/s]
 82%|████████▏ | 206/250 [02:45<00:26,  1.69it/s]
 83%|████████▎ | 207/250 [02:46<00:35,  1.20it/s]
 83%|████████▎ | 208/250 [02:47<00:27,  1.54it/s]
 84%|████████▎ | 209/250 [02:47<00:24,  1.69it/s]
 84%|████████▍ | 210/250 [02:47<00:19,  2.02it/s]
 84%|████████▍ | 211/250 [02:48<00:17,  2.25it/s]
 85%|████████▍ | 212/250 [02:49<00:24,  1.55it/s]
 86%|████████▌ | 214/250 [02:50<00:23,  1.51it/s]
 86%|████████▌ | 215/250 [02:51<00:20,  1.69it/s]
 86%|████████▋ | 216/250 [02:51<00:20,  1.66it/s]
 87%|████████▋ | 217/250 [02:52<00:23,  1.38it/s]
 87%|████████▋ | 218/250 [02:54<00:36,  1.14s/it]
 88%|████████▊ | 219/250 [02:58<00:53,  1.74s/it]
 88%|████████▊ | 220/250 [03:00<00:54,  1.82s/it]
 88%|████████▊ | 221/250 [03:00<00:42,  1.47s/it]
 89%|████████▉ | 222/250 [03:03<00:50,  1.81s/it]
 89%|████████▉ | 223/250 [03:03<00:38,  1.42s/it]
 90%|████████▉ | 224/250 [03:04<00:26,  1.04s/it]
 90%|█████████ | 225/250 [03:04<00:21,  1.16it/s]
 90%|█████████ | 226/250 [03:06<00:25,  1.06s/it]
 91%|█████████ | 227/250 [03:06<00:20,  1.14it/s]
 91%|█████████ | 228/250 [03:08<00:27,  1.25s/it]
 92%|█████████▏| 229/250 [03:09<00:21,  1.04s/it]
 92%|█████████▏| 230/250 [03:13<00:42,  2.12s/it]
 92%|█████████▏| 231/250 [03:14<00:31,  1.65s/it]
 93%|█████████▎| 233/250 [03:14<00:15,  1.07it/s]
 94%|█████████▎| 234/250 [03:15<00:13,  1.18it/s]
 94%|█████████▍| 236/250 [03:15<00:07,  1.92it/s]
 95%|█████████▍| 237/250 [03:15<00:06,  2.13it/s]
 95%|█████████▌| 238/250 [03:16<00:05,  2.12it/s]
 96%|█████████▌| 239/250 [03:16<00:04,  2.57it/s]
 96%|█████████▌| 240/250 [03:16<00:03,  2.96it/s]
 97%|█████████▋| 242/250 [03:16<00:01,  4.71it/s]
 98%|█████████▊| 244/250 [03:17<00:01,  3.31it/s]
 98%|█████████▊| 245/250 [03:18<00:02,  2.20it/s]
 99%|█████████▉| 247/250 [03:19<00:01,  2.52it/s]
100%|█████████▉| 249/250 [03:19<00:00,  3.18it/s]
100%|██████████| 250/250 [03:20<00:00,  2.46it/s]
100%|██████████| 250/250 [03:20<00:00,  1.25it/s]
2025-05-16 13:35:43,392 - modnet - INFO - Loss per individual: ind 0: 43.545 	ind 1: 41.574 	ind 2: 38.919 	ind 3: 39.253 	ind 4: 41.849 	ind 5: 51.057 	ind 6: 132.048 	ind 7: 41.825 	ind 8: 60.584 	ind 9: 42.873 	ind 10: 56.297 	ind 11: 48.480 	ind 12: 51.810 	ind 13: 46.764 	ind 14: 44.280 	ind 15: 38.057 	ind 16: 38.203 	ind 17: 40.337 	ind 18: 39.308 	ind 19: 38.845 	ind 20: 48.817 	ind 21: 36.656 	ind 22: 41.706 	ind 23: 37.499 	ind 24: 39.377 	ind 25: 41.403 	ind 26: 37.545 	ind 27: 38.166 	ind 28: 41.782 	ind 29: 38.001 	ind 30: 41.150 	ind 31: 40.200 	ind 32: 52.430 	ind 33: 53.114 	ind 34: 39.383 	ind 35: 62.076 	ind 36: 37.598 	ind 37: 80.839 	ind 38: 38.733 	ind 39: 47.584 	ind 40: 43.023 	ind 41: 46.814 	ind 42: 43.230 	ind 43: 46.082 	ind 44: 38.661 	ind 45: 41.121 	ind 46: 40.936 	ind 47: 38.262 	ind 48: 47.886 	ind 49: 46.222 	
2025-05-16 13:35:43,394 - modnet - INFO - After generation 4:
2025-05-16 13:35:43,394 - modnet - INFO - Best individual genes: {'act': 'relu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 320, 'fraction1': 1, 'fraction2': 1, 'fraction3': 0.5, 'fraction4': 0.5, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 490, 'n_layers': 4, 'layer_mults': None}
2025-05-16 13:35:43,394 - modnet - INFO - Best validation loss: 36.6165
2025-05-16 13:35:43,394 - modnet - INFO - Generation number 5

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:10<44:34, 10.74s/it]
  1%|          | 2/250 [00:13<23:48,  5.76s/it]
  1%|          | 3/250 [00:13<13:47,  3.35s/it]
  2%|▏         | 4/250 [00:13<08:51,  2.16s/it]
  2%|▏         | 6/250 [00:14<04:47,  1.18s/it]
  3%|▎         | 7/250 [00:14<03:40,  1.10it/s]
  3%|▎         | 8/250 [00:14<02:46,  1.45it/s]
  4%|▎         | 9/250 [00:15<02:21,  1.70it/s]
  4%|▍         | 10/250 [00:15<01:49,  2.19it/s]
  4%|▍         | 11/250 [00:15<01:58,  2.01it/s]
  5%|▍         | 12/250 [00:16<01:49,  2.16it/s]
  5%|▌         | 13/250 [00:16<02:10,  1.81it/s]
  6%|▌         | 14/250 [00:17<01:40,  2.36it/s]
  6%|▋         | 16/250 [00:18<02:08,  1.83it/s]
  7%|▋         | 17/250 [00:19<02:08,  1.82it/s]
  7%|▋         | 18/250 [00:19<01:44,  2.23it/s]
  8%|▊         | 20/250 [00:19<01:29,  2.56it/s]
  8%|▊         | 21/250 [00:20<01:24,  2.71it/s]
  9%|▉         | 22/250 [00:25<06:08,  1.62s/it]
  9%|▉         | 23/250 [00:30<09:41,  2.56s/it]
 10%|▉         | 24/250 [00:32<08:48,  2.34s/it]
 10%|█         | 25/250 [00:33<06:58,  1.86s/it]
 10%|█         | 26/250 [00:33<05:44,  1.54s/it]
 11%|█         | 28/250 [00:35<04:26,  1.20s/it]
 12%|█▏        | 29/250 [00:37<05:23,  1.46s/it]
 12%|█▏        | 31/250 [00:37<03:14,  1.13it/s]
 13%|█▎        | 32/250 [00:38<02:51,  1.27it/s]
 13%|█▎        | 33/250 [00:38<02:22,  1.52it/s]
 14%|█▎        | 34/250 [00:40<03:16,  1.10it/s]
 14%|█▍        | 35/250 [00:40<02:59,  1.19it/s]
 14%|█▍        | 36/250 [00:40<02:16,  1.56it/s]
 15%|█▌        | 38/250 [00:41<01:47,  1.97it/s]
 16%|█▌        | 39/250 [00:42<01:44,  2.01it/s]
 16%|█▌        | 40/250 [00:43<02:18,  1.52it/s]
 16%|█▋        | 41/250 [00:43<02:21,  1.47it/s]
 17%|█▋        | 42/250 [00:47<05:25,  1.56s/it]
 17%|█▋        | 43/250 [00:47<04:03,  1.18s/it]
 18%|█▊        | 44/250 [00:48<03:17,  1.05it/s]
 18%|█▊        | 45/250 [00:50<04:27,  1.31s/it]
 18%|█▊        | 46/250 [00:50<03:24,  1.00s/it]
 19%|█▉        | 47/250 [00:51<03:20,  1.01it/s]
 19%|█▉        | 48/250 [00:52<02:58,  1.13it/s]
 20%|█▉        | 49/250 [00:52<02:20,  1.43it/s]
 20%|██        | 51/250 [00:53<01:32,  2.14it/s]
 21%|██        | 52/250 [00:55<03:02,  1.08it/s]
 21%|██        | 53/250 [00:56<03:13,  1.02it/s]
 22%|██▏       | 54/250 [00:57<02:58,  1.10it/s]
 22%|██▏       | 56/250 [00:57<02:03,  1.57it/s]
 23%|██▎       | 58/250 [00:58<01:32,  2.08it/s]
 24%|██▎       | 59/250 [00:58<01:24,  2.25it/s]
 24%|██▍       | 60/250 [00:59<01:25,  2.23it/s]
 24%|██▍       | 61/250 [01:00<01:50,  1.70it/s]
 25%|██▍       | 62/250 [01:00<01:50,  1.70it/s]
 25%|██▌       | 63/250 [01:00<01:25,  2.20it/s]
 26%|██▌       | 64/250 [01:00<01:08,  2.72it/s]
 26%|██▌       | 65/250 [01:01<01:47,  1.72it/s]
 27%|██▋       | 67/250 [01:02<01:24,  2.17it/s]
 27%|██▋       | 68/250 [01:04<02:07,  1.43it/s]
 28%|██▊       | 69/250 [01:04<01:51,  1.62it/s]
 28%|██▊       | 70/250 [01:04<01:28,  2.02it/s]
 28%|██▊       | 71/250 [01:06<02:51,  1.04it/s]
 29%|██▉       | 72/250 [01:07<02:39,  1.12it/s]
 29%|██▉       | 73/250 [01:07<02:11,  1.35it/s]
 30%|██▉       | 74/250 [01:11<04:23,  1.50s/it]
 30%|███       | 75/250 [01:11<03:17,  1.13s/it]
 30%|███       | 76/250 [01:11<02:24,  1.20it/s]
 31%|███       | 78/250 [01:12<01:37,  1.77it/s]
 32%|███▏      | 80/250 [01:12<01:09,  2.43it/s]
 32%|███▏      | 81/250 [01:12<01:01,  2.76it/s]
 33%|███▎      | 82/250 [01:13<01:07,  2.50it/s]
 33%|███▎      | 83/250 [01:13<01:05,  2.57it/s]
 34%|███▎      | 84/250 [01:13<00:51,  3.20it/s]
 34%|███▍      | 85/250 [01:14<01:31,  1.80it/s]
 34%|███▍      | 86/250 [01:14<01:12,  2.26it/s]
 35%|███▍      | 87/250 [01:15<01:00,  2.70it/s]
 35%|███▌      | 88/250 [01:15<00:48,  3.37it/s]
 36%|███▌      | 89/250 [01:18<02:46,  1.03s/it]
 36%|███▌      | 90/250 [01:18<02:19,  1.15it/s]
 36%|███▋      | 91/250 [01:18<01:45,  1.51it/s]
 37%|███▋      | 93/250 [01:18<01:06,  2.37it/s]
 38%|███▊      | 94/250 [01:19<01:25,  1.83it/s]
 38%|███▊      | 95/250 [01:20<01:14,  2.08it/s]
 38%|███▊      | 96/250 [01:20<01:01,  2.52it/s]
 39%|███▉      | 97/250 [01:21<01:23,  1.82it/s]
 39%|███▉      | 98/250 [01:21<01:20,  1.90it/s]
 40%|███▉      | 99/250 [01:26<04:15,  1.70s/it]
 40%|████      | 100/250 [01:26<03:26,  1.37s/it]
 40%|████      | 101/250 [01:28<03:26,  1.38s/it]
 41%|████      | 102/250 [01:28<02:30,  1.02s/it]
 41%|████      | 103/250 [01:28<02:04,  1.18it/s]
 42%|████▏     | 104/250 [01:29<01:59,  1.22it/s]
 42%|████▏     | 105/250 [01:30<01:41,  1.44it/s]
 42%|████▏     | 106/250 [01:30<01:27,  1.64it/s]
 43%|████▎     | 107/250 [01:31<01:48,  1.32it/s]
 43%|████▎     | 108/250 [01:32<01:50,  1.29it/s]
 44%|████▎     | 109/250 [01:32<01:35,  1.47it/s]
 44%|████▍     | 110/250 [01:33<01:22,  1.69it/s]
 44%|████▍     | 111/250 [01:33<01:21,  1.71it/s]
 45%|████▌     | 113/250 [01:33<00:47,  2.87it/s]
 46%|████▌     | 114/250 [01:34<00:53,  2.53it/s]
 46%|████▌     | 115/250 [01:34<00:43,  3.10it/s]
 46%|████▋     | 116/250 [01:34<00:43,  3.10it/s]
 47%|████▋     | 117/250 [01:35<00:47,  2.78it/s]
 47%|████▋     | 118/250 [01:35<00:40,  3.26it/s]
 48%|████▊     | 119/250 [01:37<01:25,  1.53it/s]
 48%|████▊     | 120/250 [01:37<01:19,  1.63it/s]
 48%|████▊     | 121/250 [01:38<01:28,  1.45it/s]
 49%|████▉     | 122/250 [01:40<02:00,  1.06it/s]
 49%|████▉     | 123/250 [01:40<01:53,  1.12it/s]
 50%|████▉     | 124/250 [01:41<01:36,  1.30it/s]
 50%|█████     | 125/250 [01:41<01:23,  1.49it/s]
 50%|█████     | 126/250 [01:41<01:05,  1.88it/s]
 51%|█████     | 127/250 [01:42<00:53,  2.31it/s]
 51%|█████     | 128/250 [01:42<01:01,  1.97it/s]
 52%|█████▏    | 129/250 [01:43<01:06,  1.81it/s]
 52%|█████▏    | 130/250 [01:44<01:06,  1.80it/s]
 52%|█████▏    | 131/250 [01:45<01:24,  1.40it/s]
 53%|█████▎    | 132/250 [01:45<01:06,  1.77it/s]
 53%|█████▎    | 133/250 [01:45<01:09,  1.69it/s]
 54%|█████▎    | 134/250 [01:46<00:56,  2.06it/s]
 54%|█████▍    | 135/250 [01:46<01:03,  1.81it/s]
 54%|█████▍    | 136/250 [01:47<01:01,  1.85it/s]
 55%|█████▍    | 137/250 [01:47<00:55,  2.05it/s]
 55%|█████▌    | 138/250 [01:47<00:42,  2.61it/s]
 56%|█████▌    | 139/250 [01:48<00:34,  3.23it/s]
 56%|█████▌    | 140/250 [01:48<00:51,  2.14it/s]
 56%|█████▋    | 141/250 [01:49<00:43,  2.48it/s]
 57%|█████▋    | 142/250 [01:49<00:40,  2.64it/s]
 57%|█████▋    | 143/250 [01:49<00:42,  2.54it/s]
 58%|█████▊    | 144/250 [01:50<00:50,  2.08it/s]
 58%|█████▊    | 145/250 [01:50<00:43,  2.41it/s]
 58%|█████▊    | 146/250 [01:51<00:59,  1.74it/s]
 59%|█████▉    | 147/250 [01:51<00:45,  2.27it/s]
 59%|█████▉    | 148/250 [01:52<00:40,  2.52it/s]
 60%|█████▉    | 149/250 [01:53<01:00,  1.66it/s]
 60%|██████    | 150/250 [01:53<00:58,  1.72it/s]
 61%|██████    | 152/250 [01:54<00:55,  1.76it/s]
 61%|██████    | 153/250 [01:55<01:02,  1.55it/s]
 62%|██████▏   | 154/250 [01:56<01:14,  1.28it/s]
 62%|██████▏   | 155/250 [01:57<01:00,  1.56it/s]
 63%|██████▎   | 157/250 [01:57<00:47,  1.98it/s]
 63%|██████▎   | 158/250 [01:58<00:53,  1.74it/s]
 64%|██████▎   | 159/250 [02:00<01:21,  1.12it/s]
 64%|██████▍   | 161/250 [02:02<01:15,  1.17it/s]
 65%|██████▍   | 162/250 [02:04<01:43,  1.18s/it]
 65%|██████▌   | 163/250 [02:07<02:28,  1.71s/it]
 66%|██████▌   | 164/250 [02:08<02:12,  1.54s/it]
 66%|██████▌   | 165/250 [02:09<02:00,  1.42s/it]
 66%|██████▋   | 166/250 [02:12<02:22,  1.70s/it]
 67%|██████▋   | 167/250 [02:14<02:27,  1.78s/it]
 67%|██████▋   | 168/250 [02:14<01:55,  1.41s/it]
 68%|██████▊   | 169/250 [02:15<01:40,  1.24s/it]
 68%|██████▊   | 170/250 [02:17<02:00,  1.50s/it]
 68%|██████▊   | 171/250 [02:19<02:10,  1.65s/it]
 69%|██████▉   | 172/250 [02:20<01:36,  1.24s/it]
 69%|██████▉   | 173/250 [02:20<01:16,  1.01it/s]
 70%|██████▉   | 174/250 [02:21<01:06,  1.14it/s]
 70%|███████   | 175/250 [02:22<01:10,  1.07it/s]
 70%|███████   | 176/250 [02:23<01:12,  1.02it/s]
 71%|███████   | 177/250 [02:23<00:56,  1.30it/s]
 71%|███████   | 178/250 [02:23<00:46,  1.56it/s]
 72%|███████▏  | 179/250 [02:23<00:35,  2.01it/s]
 72%|███████▏  | 180/250 [02:24<00:27,  2.59it/s]
 72%|███████▏  | 181/250 [02:25<00:55,  1.23it/s]
 73%|███████▎  | 182/250 [02:26<00:44,  1.52it/s]
 73%|███████▎  | 183/250 [02:28<01:13,  1.10s/it]
 74%|███████▎  | 184/250 [02:28<00:56,  1.17it/s]
 74%|███████▍  | 185/250 [02:29<00:57,  1.13it/s]
 74%|███████▍  | 186/250 [02:30<00:50,  1.27it/s]
 75%|███████▍  | 187/250 [02:30<00:42,  1.50it/s]
 75%|███████▌  | 188/250 [02:32<00:57,  1.08it/s]
 76%|███████▌  | 189/250 [02:33<01:00,  1.01it/s]
 76%|███████▌  | 190/250 [02:33<00:47,  1.26it/s]
 77%|███████▋  | 192/250 [02:34<00:31,  1.85it/s]
 77%|███████▋  | 193/250 [02:35<00:46,  1.23it/s]
 78%|███████▊  | 194/250 [02:36<00:43,  1.29it/s]
 78%|███████▊  | 195/250 [02:36<00:34,  1.59it/s]
 78%|███████▊  | 196/250 [02:37<00:35,  1.53it/s]
 79%|███████▉  | 197/250 [02:38<00:43,  1.22it/s]
 79%|███████▉  | 198/250 [02:39<00:44,  1.16it/s]
 80%|███████▉  | 199/250 [02:39<00:35,  1.44it/s]
 80%|████████  | 200/250 [02:40<00:32,  1.52it/s]
 80%|████████  | 201/250 [02:41<00:38,  1.26it/s]
 81%|████████  | 202/250 [02:41<00:31,  1.53it/s]
 81%|████████  | 203/250 [02:41<00:23,  2.00it/s]
 82%|████████▏ | 204/250 [02:42<00:17,  2.57it/s]
 82%|████████▏ | 205/250 [02:42<00:22,  1.99it/s]
 82%|████████▏ | 206/250 [02:43<00:22,  1.94it/s]
 83%|████████▎ | 207/250 [02:43<00:19,  2.24it/s]
 83%|████████▎ | 208/250 [02:45<00:35,  1.19it/s]
 84%|████████▎ | 209/250 [02:45<00:28,  1.42it/s]
 84%|████████▍ | 210/250 [02:46<00:31,  1.26it/s]
 84%|████████▍ | 211/250 [02:48<00:38,  1.01it/s]
 85%|████████▌ | 213/250 [02:49<00:29,  1.26it/s]
 86%|████████▌ | 215/250 [02:50<00:26,  1.34it/s]
 87%|████████▋ | 217/250 [02:50<00:16,  2.01it/s]
 87%|████████▋ | 218/250 [02:51<00:18,  1.74it/s]
 88%|████████▊ | 219/250 [02:52<00:17,  1.75it/s]
 88%|████████▊ | 220/250 [02:53<00:19,  1.50it/s]
 88%|████████▊ | 221/250 [02:53<00:18,  1.60it/s]
 89%|████████▉ | 222/250 [02:56<00:29,  1.07s/it]
 89%|████████▉ | 223/250 [02:57<00:29,  1.08s/it]
 90%|████████▉ | 224/250 [02:57<00:21,  1.24it/s]
 90%|█████████ | 225/250 [02:59<00:32,  1.32s/it]
 90%|█████████ | 226/250 [03:00<00:25,  1.06s/it]
 91%|█████████ | 228/250 [03:00<00:15,  1.41it/s]
 92%|█████████▏| 231/250 [03:00<00:07,  2.61it/s]
 93%|█████████▎| 232/250 [03:01<00:06,  2.87it/s]
 93%|█████████▎| 233/250 [03:01<00:07,  2.31it/s]
 94%|█████████▎| 234/250 [03:02<00:05,  2.82it/s]
 94%|█████████▍| 236/250 [03:02<00:04,  2.94it/s]
 95%|█████████▌| 238/250 [03:02<00:03,  3.92it/s]
 96%|█████████▋| 241/250 [03:03<00:01,  6.04it/s]
 97%|█████████▋| 243/250 [03:05<00:03,  2.26it/s]
 98%|█████████▊| 244/250 [03:07<00:04,  1.23it/s]
 98%|█████████▊| 245/250 [03:11<00:07,  1.47s/it]
 98%|█████████▊| 246/250 [03:12<00:04,  1.24s/it]
 99%|█████████▉| 247/250 [03:13<00:03,  1.15s/it]
 99%|█████████▉| 248/250 [03:16<00:03,  1.68s/it]
100%|█████████▉| 249/250 [03:18<00:01,  1.66s/it]
100%|██████████| 250/250 [03:21<00:00,  2.03s/it]
100%|██████████| 250/250 [03:21<00:00,  1.24it/s]
2025-05-16 13:39:04,474 - modnet - INFO - Loss per individual: ind 0: 37.921 	ind 1: 39.347 	ind 2: 40.951 	ind 3: 42.487 	ind 4: 44.708 	ind 5: 42.010 	ind 6: 38.106 	ind 7: 37.003 	ind 8: 42.826 	ind 9: 41.327 	ind 10: 39.567 	ind 11: 40.411 	ind 12: 39.503 	ind 13: 38.464 	ind 14: 36.697 	ind 15: 44.595 	ind 16: 39.918 	ind 17: 51.144 	ind 18: 43.111 	ind 19: 42.846 	ind 20: 40.989 	ind 21: 45.419 	ind 22: 53.645 	ind 23: 39.894 	ind 24: 39.725 	ind 25: 42.799 	ind 26: 40.018 	ind 27: 48.708 	ind 28: 51.694 	ind 29: 37.669 	ind 30: 43.600 	ind 31: 40.761 	ind 32: 37.256 	ind 33: 39.343 	ind 34: 36.561 	ind 35: 44.551 	ind 36: 39.713 	ind 37: 37.937 	ind 38: 40.357 	ind 39: 44.091 	ind 40: 42.386 	ind 41: 41.284 	ind 42: 45.684 	ind 43: 37.898 	ind 44: 43.236 	ind 45: 37.922 	ind 46: 42.327 	ind 47: 43.979 	ind 48: 39.675 	ind 49: 41.760 	
2025-05-16 13:39:04,475 - modnet - INFO - After generation 5:
2025-05-16 13:39:04,476 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 288, 'fraction1': 0.25, 'fraction2': 0.75, 'fraction3': 0.25, 'fraction4': 0.5, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 600, 'n_layers': 5, 'layer_mults': None}
2025-05-16 13:39:04,476 - modnet - INFO - Best validation loss: 36.5611
2025-05-16 13:39:04,476 - modnet - INFO - Generation number 6

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:04<19:29,  4.70s/it]
  1%|          | 2/250 [00:07<13:41,  3.31s/it]
  2%|▏         | 4/250 [00:07<06:10,  1.50s/it]
  2%|▏         | 5/250 [00:08<04:46,  1.17s/it]
  2%|▏         | 6/250 [00:09<05:22,  1.32s/it]
  3%|▎         | 7/250 [00:13<08:09,  2.01s/it]
  4%|▎         | 9/250 [00:14<05:09,  1.28s/it]
  4%|▍         | 10/250 [00:15<05:22,  1.35s/it]
  4%|▍         | 11/250 [00:16<04:31,  1.14s/it]
  5%|▍         | 12/250 [00:16<03:24,  1.17it/s]
  5%|▌         | 13/250 [00:17<03:00,  1.32it/s]
  6%|▌         | 14/250 [00:17<02:29,  1.58it/s]
  6%|▌         | 15/250 [00:18<02:41,  1.45it/s]
  6%|▋         | 16/250 [00:19<02:42,  1.44it/s]
  7%|▋         | 17/250 [00:20<04:01,  1.04s/it]
  7%|▋         | 18/250 [00:21<03:27,  1.12it/s]
  8%|▊         | 19/250 [00:22<03:57,  1.03s/it]
  8%|▊         | 20/250 [00:23<03:32,  1.08it/s]
  8%|▊         | 21/250 [00:28<07:56,  2.08s/it]
  9%|▉         | 22/250 [00:29<06:47,  1.79s/it]
  9%|▉         | 23/250 [00:30<05:43,  1.51s/it]
 10%|▉         | 24/250 [00:34<09:04,  2.41s/it]
 10%|█         | 25/250 [00:35<07:09,  1.91s/it]
 10%|█         | 26/250 [00:35<05:09,  1.38s/it]
 11%|█         | 27/250 [00:35<03:46,  1.02s/it]
 12%|█▏        | 29/250 [00:36<02:14,  1.64it/s]
 12%|█▏        | 31/250 [00:36<01:27,  2.49it/s]
 13%|█▎        | 33/250 [00:36<01:02,  3.49it/s]
 14%|█▎        | 34/250 [00:38<02:10,  1.66it/s]
 14%|█▍        | 36/250 [00:38<01:41,  2.10it/s]
 15%|█▍        | 37/250 [00:39<01:32,  2.30it/s]
 15%|█▌        | 38/250 [00:41<02:54,  1.21it/s]
 16%|█▌        | 39/250 [00:44<04:58,  1.41s/it]
 16%|█▋        | 41/250 [00:44<03:18,  1.05it/s]
 17%|█▋        | 42/250 [00:45<03:07,  1.11it/s]
 17%|█▋        | 43/250 [00:46<02:47,  1.23it/s]
 18%|█▊        | 44/250 [00:46<02:26,  1.41it/s]
 18%|█▊        | 45/250 [00:47<02:27,  1.39it/s]
 18%|█▊        | 46/250 [00:48<02:33,  1.33it/s]
 19%|█▉        | 47/250 [00:49<02:56,  1.15it/s]
 19%|█▉        | 48/250 [00:50<02:50,  1.18it/s]
 20%|██        | 50/250 [00:51<02:31,  1.32it/s]
 20%|██        | 51/250 [00:52<03:08,  1.06it/s]
 21%|██        | 53/250 [00:53<01:58,  1.67it/s]
 22%|██▏       | 54/250 [00:53<01:42,  1.92it/s]
 22%|██▏       | 55/250 [00:54<01:44,  1.87it/s]
 22%|██▏       | 56/250 [00:54<01:46,  1.82it/s]
 23%|██▎       | 57/250 [00:55<01:45,  1.83it/s]
 23%|██▎       | 58/250 [00:55<01:25,  2.24it/s]
 24%|██▎       | 59/250 [00:55<01:31,  2.10it/s]
 24%|██▍       | 60/250 [00:56<01:53,  1.68it/s]
 24%|██▍       | 61/250 [00:57<01:48,  1.74it/s]
 25%|██▍       | 62/250 [00:57<01:50,  1.69it/s]
 25%|██▌       | 63/250 [00:58<01:55,  1.62it/s]
 26%|██▌       | 64/250 [00:59<02:17,  1.36it/s]
 26%|██▌       | 65/250 [01:00<02:11,  1.40it/s]
 26%|██▋       | 66/250 [01:01<02:47,  1.10it/s]
 27%|██▋       | 68/250 [01:01<01:34,  1.94it/s]
 28%|██▊       | 69/250 [01:02<01:36,  1.88it/s]
 28%|██▊       | 70/250 [01:02<01:34,  1.90it/s]
 29%|██▉       | 72/250 [01:03<01:24,  2.11it/s]
 29%|██▉       | 73/250 [01:04<01:35,  1.86it/s]
 30%|██▉       | 74/250 [01:07<03:45,  1.28s/it]
 30%|███       | 75/250 [01:08<02:49,  1.03it/s]
 30%|███       | 76/250 [01:08<02:09,  1.35it/s]
 31%|███       | 77/250 [01:09<02:28,  1.16it/s]
 31%|███       | 78/250 [01:09<02:08,  1.34it/s]
 32%|███▏      | 79/250 [01:11<02:31,  1.13it/s]
 32%|███▏      | 80/250 [01:12<02:45,  1.03it/s]
 32%|███▏      | 81/250 [01:13<02:52,  1.02s/it]
 33%|███▎      | 82/250 [01:13<02:18,  1.21it/s]
 33%|███▎      | 83/250 [01:14<01:58,  1.41it/s]
 34%|███▎      | 84/250 [01:15<02:23,  1.16it/s]
 34%|███▍      | 85/250 [01:15<01:56,  1.42it/s]
 34%|███▍      | 86/250 [01:15<01:28,  1.85it/s]
 35%|███▍      | 87/250 [01:15<01:08,  2.37it/s]
 35%|███▌      | 88/250 [01:16<01:12,  2.23it/s]
 36%|███▌      | 89/250 [01:18<02:10,  1.23it/s]
 36%|███▌      | 90/250 [01:18<01:59,  1.34it/s]
 36%|███▋      | 91/250 [01:19<02:08,  1.24it/s]
 37%|███▋      | 92/250 [01:19<01:38,  1.60it/s]
 37%|███▋      | 93/250 [01:22<02:48,  1.07s/it]
 38%|███▊      | 94/250 [01:22<02:10,  1.20it/s]
 38%|███▊      | 95/250 [01:23<02:02,  1.26it/s]
 38%|███▊      | 96/250 [01:23<01:34,  1.62it/s]
 39%|███▉      | 97/250 [01:25<02:36,  1.02s/it]
 39%|███▉      | 98/250 [01:25<01:57,  1.30it/s]
 40%|███▉      | 99/250 [01:26<01:55,  1.30it/s]
 40%|████      | 100/250 [01:26<01:36,  1.55it/s]
 40%|████      | 101/250 [01:27<01:53,  1.31it/s]
 41%|████      | 102/250 [01:28<01:58,  1.25it/s]
 41%|████      | 103/250 [01:28<01:43,  1.42it/s]
 42%|████▏     | 104/250 [01:29<01:34,  1.54it/s]
 42%|████▏     | 105/250 [01:29<01:16,  1.89it/s]
 43%|████▎     | 107/250 [01:29<00:50,  2.86it/s]
 43%|████▎     | 108/250 [01:31<01:24,  1.68it/s]
 44%|████▎     | 109/250 [01:31<01:10,  2.01it/s]
 44%|████▍     | 111/250 [01:34<02:11,  1.06it/s]
 45%|████▍     | 112/250 [01:35<02:04,  1.11it/s]
 45%|████▌     | 113/250 [01:35<01:41,  1.34it/s]
 46%|████▌     | 115/250 [01:38<02:09,  1.04it/s]
 46%|████▋     | 116/250 [01:38<02:03,  1.08it/s]
 47%|████▋     | 117/250 [01:41<03:02,  1.37s/it]
 47%|████▋     | 118/250 [01:43<03:30,  1.59s/it]
 48%|████▊     | 119/250 [01:45<03:12,  1.47s/it]
 48%|████▊     | 120/250 [01:46<02:58,  1.37s/it]
 49%|████▉     | 122/250 [01:46<01:57,  1.09it/s]
 49%|████▉     | 123/250 [01:47<01:34,  1.34it/s]
 50%|████▉     | 124/250 [01:47<01:26,  1.45it/s]
 50%|█████     | 125/250 [01:48<01:20,  1.55it/s]
 50%|█████     | 126/250 [01:48<01:10,  1.75it/s]
 51%|█████     | 127/250 [01:49<01:33,  1.32it/s]
 52%|█████▏    | 129/250 [01:51<01:25,  1.41it/s]
 52%|█████▏    | 130/250 [01:51<01:22,  1.46it/s]
 52%|█████▏    | 131/250 [01:52<01:38,  1.20it/s]
 53%|█████▎    | 133/250 [01:54<01:43,  1.13it/s]
 54%|█████▎    | 134/250 [01:55<01:43,  1.12it/s]
 54%|█████▍    | 136/250 [01:57<01:41,  1.12it/s]
 55%|█████▍    | 137/250 [02:00<02:28,  1.31s/it]
 55%|█████▌    | 138/250 [02:00<01:59,  1.07s/it]
 56%|█████▌    | 139/250 [02:01<01:47,  1.03it/s]
 56%|█████▌    | 140/250 [02:04<02:52,  1.56s/it]
 56%|█████▋    | 141/250 [02:06<03:16,  1.80s/it]
 57%|█████▋    | 142/250 [02:07<02:45,  1.53s/it]
 57%|█████▋    | 143/250 [02:09<02:44,  1.53s/it]
 58%|█████▊    | 145/250 [02:09<01:36,  1.09it/s]
 58%|█████▊    | 146/250 [02:09<01:15,  1.37it/s]
 59%|█████▉    | 147/250 [02:11<01:31,  1.13it/s]
 59%|█████▉    | 148/250 [02:11<01:09,  1.46it/s]
 60%|█████▉    | 149/250 [02:11<00:53,  1.87it/s]
 60%|██████    | 150/250 [02:11<00:52,  1.90it/s]
 60%|██████    | 151/250 [02:12<01:06,  1.48it/s]
 61%|██████    | 152/250 [02:13<01:05,  1.50it/s]
 62%|██████▏   | 154/250 [02:13<00:41,  2.33it/s]
 62%|██████▏   | 155/250 [02:14<00:52,  1.81it/s]
 62%|██████▏   | 156/250 [02:15<00:55,  1.70it/s]
 63%|██████▎   | 157/250 [02:15<00:49,  1.87it/s]
 63%|██████▎   | 158/250 [02:16<00:45,  2.04it/s]
 64%|██████▎   | 159/250 [02:16<00:42,  2.15it/s]
 64%|██████▍   | 160/250 [02:20<02:00,  1.34s/it]
 64%|██████▍   | 161/250 [02:20<01:37,  1.09s/it]
 65%|██████▌   | 163/250 [02:20<00:55,  1.58it/s]
 66%|██████▌   | 164/250 [02:20<00:45,  1.90it/s]
 66%|██████▌   | 165/250 [02:21<00:42,  1.99it/s]
 66%|██████▋   | 166/250 [02:21<00:41,  2.02it/s]
 67%|██████▋   | 167/250 [02:22<00:38,  2.14it/s]
 68%|██████▊   | 169/250 [02:22<00:24,  3.28it/s]
 68%|██████▊   | 170/250 [02:22<00:23,  3.43it/s]
 68%|██████▊   | 171/250 [02:23<00:29,  2.71it/s]
 69%|██████▉   | 172/250 [02:24<00:42,  1.84it/s]
 69%|██████▉   | 173/250 [02:24<00:38,  2.00it/s]
 70%|██████▉   | 174/250 [02:25<00:52,  1.46it/s]
 70%|███████   | 175/250 [02:26<00:40,  1.86it/s]
 71%|███████   | 177/250 [02:26<00:24,  2.97it/s]
 72%|███████▏  | 179/250 [02:26<00:17,  3.95it/s]
 72%|███████▏  | 180/250 [02:27<00:23,  3.00it/s]
 72%|███████▏  | 181/250 [02:27<00:19,  3.52it/s]
 73%|███████▎  | 182/250 [02:28<00:39,  1.72it/s]
 73%|███████▎  | 183/250 [02:29<00:34,  1.93it/s]
 74%|███████▎  | 184/250 [02:30<00:51,  1.28it/s]
 74%|███████▍  | 186/250 [02:31<00:37,  1.73it/s]
 75%|███████▌  | 188/250 [02:33<00:48,  1.29it/s]
 76%|███████▌  | 189/250 [02:33<00:43,  1.40it/s]
 76%|███████▌  | 190/250 [02:34<00:35,  1.68it/s]
 77%|███████▋  | 192/250 [02:34<00:26,  2.21it/s]
 77%|███████▋  | 193/250 [02:35<00:29,  1.95it/s]
 78%|███████▊  | 194/250 [02:36<00:31,  1.75it/s]
 78%|███████▊  | 195/250 [02:36<00:32,  1.69it/s]
 78%|███████▊  | 196/250 [02:37<00:33,  1.59it/s]
 79%|███████▉  | 197/250 [02:38<00:33,  1.59it/s]
 79%|███████▉  | 198/250 [02:39<00:48,  1.08it/s]
 80%|███████▉  | 199/250 [02:41<01:00,  1.19s/it]
 80%|████████  | 200/250 [02:42<00:52,  1.06s/it]
 80%|████████  | 201/250 [02:42<00:38,  1.29it/s]
 81%|████████  | 202/250 [02:42<00:34,  1.41it/s]
 81%|████████  | 203/250 [02:43<00:26,  1.74it/s]
 82%|████████▏ | 204/250 [02:43<00:24,  1.85it/s]
 82%|████████▏ | 206/250 [02:43<00:14,  2.95it/s]
 83%|████████▎ | 207/250 [02:44<00:15,  2.71it/s]
 83%|████████▎ | 208/250 [02:44<00:15,  2.75it/s]
 84%|████████▎ | 209/250 [02:45<00:19,  2.07it/s]
 84%|████████▍ | 210/250 [02:47<00:35,  1.13it/s]
 84%|████████▍ | 211/250 [02:49<00:51,  1.33s/it]
 85%|████████▍ | 212/250 [02:50<00:46,  1.21s/it]
 85%|████████▌ | 213/250 [02:51<00:38,  1.05s/it]
 86%|████████▌ | 214/250 [02:52<00:34,  1.06it/s]
 86%|████████▌ | 215/250 [02:52<00:27,  1.29it/s]
 86%|████████▋ | 216/250 [02:52<00:20,  1.62it/s]
 87%|████████▋ | 217/250 [02:53<00:17,  1.85it/s]
 87%|████████▋ | 218/250 [02:53<00:14,  2.19it/s]
 88%|████████▊ | 219/250 [02:54<00:21,  1.47it/s]
 88%|████████▊ | 220/250 [02:54<00:17,  1.75it/s]
 89%|████████▉ | 222/250 [02:55<00:12,  2.17it/s]
 89%|████████▉ | 223/250 [02:57<00:21,  1.25it/s]
 90%|████████▉ | 224/250 [02:57<00:17,  1.46it/s]
 90%|█████████ | 225/250 [02:59<00:23,  1.05it/s]
 91%|█████████ | 227/250 [02:59<00:14,  1.53it/s]
 91%|█████████ | 228/250 [03:01<00:16,  1.31it/s]
 92%|█████████▏| 229/250 [03:02<00:17,  1.19it/s]
 92%|█████████▏| 230/250 [03:02<00:14,  1.39it/s]
 92%|█████████▏| 231/250 [03:02<00:11,  1.62it/s]
 93%|█████████▎| 232/250 [03:04<00:14,  1.25it/s]
 93%|█████████▎| 233/250 [03:04<00:12,  1.32it/s]
 94%|█████████▎| 234/250 [03:05<00:11,  1.42it/s]
 94%|█████████▍| 235/250 [03:05<00:08,  1.79it/s]
 95%|█████████▍| 237/250 [03:05<00:04,  2.68it/s]
 96%|█████████▌| 239/250 [03:06<00:02,  3.69it/s]
 96%|█████████▌| 240/250 [03:07<00:05,  1.70it/s]
 96%|█████████▋| 241/250 [03:09<00:08,  1.09it/s]
 97%|█████████▋| 243/250 [03:12<00:07,  1.04s/it]
 98%|█████████▊| 244/250 [03:14<00:07,  1.32s/it]
 98%|█████████▊| 245/250 [03:14<00:05,  1.09s/it]
 98%|█████████▊| 246/250 [03:18<00:07,  1.75s/it]
 99%|█████████▉| 247/250 [03:19<00:04,  1.49s/it]
 99%|█████████▉| 248/250 [03:22<00:03,  1.88s/it]
100%|█████████▉| 249/250 [03:22<00:01,  1.43s/it]
100%|██████████| 250/250 [03:22<00:00,  1.08s/it]
100%|██████████| 250/250 [03:22<00:00,  1.23it/s]
2025-05-16 13:42:27,191 - modnet - INFO - Loss per individual: ind 0: 40.923 	ind 1: 43.783 	ind 2: 42.590 	ind 3: 38.568 	ind 4: 64.258 	ind 5: 39.693 	ind 6: 45.769 	ind 7: 39.510 	ind 8: 40.082 	ind 9: 43.061 	ind 10: 37.523 	ind 11: 44.231 	ind 12: 41.354 	ind 13: 60.823 	ind 14: 47.502 	ind 15: 39.111 	ind 16: 38.231 	ind 17: 43.337 	ind 18: 40.072 	ind 19: 38.134 	ind 20: 37.855 	ind 21: 38.674 	ind 22: 37.902 	ind 23: 38.482 	ind 24: 54.776 	ind 25: 40.237 	ind 26: 41.831 	ind 27: 41.239 	ind 28: 51.058 	ind 29: 38.830 	ind 30: 40.469 	ind 31: 40.335 	ind 32: 39.740 	ind 33: 43.052 	ind 34: 40.933 	ind 35: 41.782 	ind 36: 53.806 	ind 37: 50.312 	ind 38: 39.119 	ind 39: 46.036 	ind 40: 55.324 	ind 41: 40.890 	ind 42: 40.080 	ind 43: 37.627 	ind 44: 37.233 	ind 45: 37.791 	ind 46: 43.840 	ind 47: 38.091 	ind 48: 47.938 	ind 49: 39.581 	
2025-05-16 13:42:27,192 - modnet - INFO - After generation 6:
2025-05-16 13:42:27,192 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 288, 'fraction1': 0.25, 'fraction2': 0.75, 'fraction3': 0.25, 'fraction4': 0.5, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 600, 'n_layers': 5, 'layer_mults': None}
2025-05-16 13:42:27,192 - modnet - INFO - Best validation loss: 36.5611
2025-05-16 13:42:27,192 - modnet - INFO - Generation number 7

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:07<31:02,  7.48s/it]
  1%|          | 2/250 [00:09<16:57,  4.10s/it]
  1%|          | 3/250 [00:09<10:05,  2.45s/it]
  2%|▏         | 4/250 [00:09<06:24,  1.56s/it]
  2%|▏         | 5/250 [00:10<05:17,  1.30s/it]
  3%|▎         | 7/250 [00:11<03:00,  1.35it/s]
  3%|▎         | 8/250 [00:11<02:56,  1.37it/s]
  4%|▎         | 9/250 [00:12<02:46,  1.45it/s]
  4%|▍         | 10/250 [00:13<03:06,  1.29it/s]
  4%|▍         | 11/250 [00:13<02:21,  1.69it/s]
  5%|▍         | 12/250 [00:14<02:27,  1.62it/s]
  5%|▌         | 13/250 [00:15<02:51,  1.38it/s]
  6%|▌         | 14/250 [00:15<02:23,  1.64it/s]
  6%|▌         | 15/250 [00:16<03:04,  1.27it/s]
  6%|▋         | 16/250 [00:17<03:28,  1.12it/s]
  7%|▋         | 17/250 [00:20<05:20,  1.37s/it]
  7%|▋         | 18/250 [00:21<05:01,  1.30s/it]
  8%|▊         | 19/250 [00:22<04:22,  1.14s/it]
  8%|▊         | 20/250 [00:22<03:44,  1.02it/s]
  8%|▊         | 21/250 [00:23<03:02,  1.25it/s]
  9%|▉         | 22/250 [00:23<02:21,  1.62it/s]
  9%|▉         | 23/250 [00:24<02:17,  1.65it/s]
 10%|▉         | 24/250 [00:25<03:32,  1.06it/s]
 10%|█         | 25/250 [00:26<03:48,  1.02s/it]
 10%|█         | 26/250 [00:27<03:16,  1.14it/s]
 11%|█         | 27/250 [00:28<03:36,  1.03it/s]
 11%|█         | 28/250 [00:29<02:59,  1.24it/s]
 12%|█▏        | 29/250 [00:29<02:45,  1.34it/s]
 12%|█▏        | 31/250 [00:29<01:39,  2.20it/s]
 13%|█▎        | 32/250 [00:30<01:39,  2.20it/s]
 13%|█▎        | 33/250 [00:30<01:21,  2.67it/s]
 14%|█▍        | 35/250 [00:32<01:57,  1.84it/s]
 14%|█▍        | 36/250 [00:32<01:58,  1.81it/s]
 15%|█▌        | 38/250 [00:33<02:01,  1.75it/s]
 16%|█▌        | 39/250 [00:33<01:38,  2.14it/s]
 16%|█▌        | 40/250 [00:35<02:16,  1.54it/s]
 16%|█▋        | 41/250 [00:36<02:48,  1.24it/s]
 17%|█▋        | 42/250 [00:36<02:30,  1.38it/s]
 18%|█▊        | 44/250 [00:38<02:23,  1.43it/s]
 18%|█▊        | 46/250 [00:38<01:44,  1.95it/s]
 19%|█▉        | 47/250 [00:39<01:37,  2.09it/s]
 19%|█▉        | 48/250 [00:39<01:25,  2.36it/s]
 20%|█▉        | 49/250 [00:39<01:14,  2.68it/s]
 20%|██        | 50/250 [00:40<01:39,  2.01it/s]
 20%|██        | 51/250 [00:40<01:36,  2.06it/s]
 21%|██        | 52/250 [00:41<02:06,  1.56it/s]
 21%|██        | 53/250 [00:42<01:55,  1.71it/s]
 22%|██▏       | 55/250 [00:42<01:07,  2.87it/s]
 22%|██▏       | 56/250 [00:42<01:02,  3.10it/s]
 23%|██▎       | 57/250 [00:43<01:37,  1.97it/s]
 23%|██▎       | 58/250 [00:44<01:25,  2.24it/s]
 24%|██▎       | 59/250 [00:44<01:48,  1.76it/s]
 24%|██▍       | 61/250 [00:45<01:13,  2.56it/s]
 25%|██▍       | 62/250 [00:45<01:10,  2.66it/s]
 25%|██▌       | 63/250 [00:45<01:00,  3.07it/s]
 26%|██▌       | 64/250 [00:46<01:36,  1.93it/s]
 26%|██▌       | 65/250 [00:47<01:56,  1.59it/s]
 26%|██▋       | 66/250 [00:48<01:54,  1.61it/s]
 27%|██▋       | 67/250 [00:48<01:35,  1.91it/s]
 27%|██▋       | 68/250 [00:49<01:45,  1.72it/s]
 28%|██▊       | 69/250 [00:51<02:47,  1.08it/s]
 28%|██▊       | 70/250 [00:52<02:56,  1.02it/s]
 28%|██▊       | 71/250 [00:52<02:34,  1.16it/s]
 29%|██▉       | 72/250 [00:53<02:01,  1.46it/s]
 29%|██▉       | 73/250 [00:54<02:34,  1.14it/s]
 30%|███       | 75/250 [00:54<01:35,  1.83it/s]
 30%|███       | 76/250 [00:57<02:59,  1.03s/it]
 31%|███       | 77/250 [00:58<02:59,  1.04s/it]
 31%|███       | 78/250 [00:58<02:43,  1.05it/s]
 32%|███▏      | 79/250 [00:59<02:26,  1.17it/s]
 32%|███▏      | 80/250 [01:00<02:11,  1.30it/s]
 32%|███▏      | 81/250 [01:00<01:53,  1.49it/s]
 33%|███▎      | 82/250 [01:01<01:55,  1.45it/s]
 33%|███▎      | 83/250 [01:02<02:12,  1.26it/s]
 34%|███▎      | 84/250 [01:02<01:56,  1.42it/s]
 34%|███▍      | 85/250 [01:02<01:28,  1.85it/s]
 34%|███▍      | 86/250 [01:04<02:20,  1.17it/s]
 35%|███▌      | 88/250 [01:04<01:26,  1.87it/s]
 36%|███▌      | 89/250 [01:05<01:24,  1.91it/s]
 36%|███▌      | 90/250 [01:08<03:17,  1.24s/it]
 36%|███▋      | 91/250 [01:14<06:21,  2.40s/it]
 37%|███▋      | 93/250 [01:15<04:31,  1.73s/it]
 38%|███▊      | 94/250 [01:16<03:33,  1.37s/it]
 38%|███▊      | 95/250 [01:16<03:10,  1.23s/it]
 38%|███▊      | 96/250 [01:17<02:29,  1.03it/s]
 39%|███▉      | 97/250 [01:20<04:14,  1.66s/it]
 39%|███▉      | 98/250 [01:21<03:21,  1.33s/it]
 40%|███▉      | 99/250 [01:22<03:39,  1.45s/it]
 40%|████      | 100/250 [01:23<02:45,  1.11s/it]
 40%|████      | 101/250 [01:24<02:36,  1.05s/it]
 41%|████      | 102/250 [01:24<02:30,  1.02s/it]
 41%|████      | 103/250 [01:25<01:58,  1.25it/s]
 42%|████▏     | 104/250 [01:25<01:40,  1.45it/s]
 42%|████▏     | 106/250 [01:26<01:03,  2.26it/s]
 43%|████▎     | 108/250 [01:26<01:00,  2.36it/s]
 44%|████▎     | 109/250 [01:27<00:57,  2.47it/s]
 44%|████▍     | 110/250 [01:27<00:52,  2.66it/s]
 44%|████▍     | 111/250 [01:28<01:23,  1.66it/s]
 45%|████▍     | 112/250 [01:30<01:50,  1.24it/s]
 45%|████▌     | 113/250 [01:31<02:25,  1.06s/it]
 46%|████▌     | 114/250 [01:32<01:54,  1.19it/s]
 46%|████▌     | 115/250 [01:32<01:34,  1.43it/s]
 46%|████▋     | 116/250 [01:32<01:15,  1.78it/s]
 47%|████▋     | 117/250 [01:33<01:20,  1.66it/s]
 47%|████▋     | 118/250 [01:34<01:32,  1.43it/s]
 48%|████▊     | 119/250 [01:35<01:47,  1.22it/s]
 48%|████▊     | 120/250 [01:37<02:34,  1.19s/it]
 48%|████▊     | 121/250 [01:37<02:06,  1.02it/s]
 49%|████▉     | 122/250 [01:38<01:55,  1.10it/s]
 49%|████▉     | 123/250 [01:39<01:35,  1.33it/s]
 50%|████▉     | 124/250 [01:41<02:37,  1.25s/it]
 50%|█████     | 125/250 [01:41<02:07,  1.02s/it]
 50%|█████     | 126/250 [01:42<01:39,  1.24it/s]
 51%|█████     | 128/250 [01:42<00:58,  2.10it/s]
 52%|█████▏    | 129/250 [01:44<01:57,  1.03it/s]
 52%|█████▏    | 130/250 [01:45<01:58,  1.02it/s]
 52%|█████▏    | 131/250 [01:47<02:10,  1.10s/it]
 53%|█████▎    | 132/250 [01:48<02:13,  1.13s/it]
 53%|█████▎    | 133/250 [01:49<02:19,  1.19s/it]
 54%|█████▎    | 134/250 [01:51<02:35,  1.34s/it]
 54%|█████▍    | 135/250 [01:51<02:01,  1.06s/it]
 54%|█████▍    | 136/250 [01:52<01:39,  1.15it/s]
 55%|█████▍    | 137/250 [01:52<01:29,  1.26it/s]
 55%|█████▌    | 138/250 [01:53<01:22,  1.35it/s]
 56%|█████▌    | 140/250 [01:53<00:48,  2.28it/s]
 57%|█████▋    | 142/250 [01:53<00:32,  3.29it/s]
 58%|█████▊    | 144/250 [01:54<00:22,  4.62it/s]
 58%|█████▊    | 145/250 [01:54<00:28,  3.64it/s]
 58%|█████▊    | 146/250 [01:54<00:27,  3.78it/s]
 59%|█████▉    | 147/250 [01:55<00:30,  3.40it/s]
 59%|█████▉    | 148/250 [01:55<00:30,  3.40it/s]
 60%|█████▉    | 149/250 [01:56<00:45,  2.21it/s]
 60%|██████    | 150/250 [01:59<01:56,  1.17s/it]
 61%|██████    | 152/250 [01:59<01:06,  1.47it/s]
 61%|██████    | 153/250 [02:00<01:16,  1.27it/s]
 62%|██████▏   | 154/250 [02:00<01:01,  1.57it/s]
 62%|██████▏   | 155/250 [02:01<00:51,  1.85it/s]
 62%|██████▏   | 156/250 [02:01<00:45,  2.07it/s]
 63%|██████▎   | 157/250 [02:04<01:51,  1.20s/it]
 63%|██████▎   | 158/250 [02:05<01:32,  1.01s/it]
 64%|██████▎   | 159/250 [02:05<01:09,  1.31it/s]
 64%|██████▍   | 160/250 [02:05<00:58,  1.53it/s]
 64%|██████▍   | 161/250 [02:06<01:02,  1.42it/s]
 65%|██████▍   | 162/250 [02:06<00:54,  1.63it/s]
 65%|██████▌   | 163/250 [02:07<00:49,  1.75it/s]
 66%|██████▌   | 164/250 [02:08<01:09,  1.23it/s]
 66%|██████▌   | 165/250 [02:08<00:56,  1.50it/s]
 66%|██████▋   | 166/250 [02:09<01:02,  1.34it/s]
 67%|██████▋   | 167/250 [02:11<01:12,  1.14it/s]
 67%|██████▋   | 168/250 [02:12<01:29,  1.09s/it]
 68%|██████▊   | 169/250 [02:14<01:36,  1.20s/it]
 68%|██████▊   | 170/250 [02:16<01:56,  1.46s/it]
 68%|██████▊   | 171/250 [02:19<02:37,  2.00s/it]
 69%|██████▉   | 173/250 [02:19<01:31,  1.19s/it]
 70%|██████▉   | 174/250 [02:23<02:08,  1.70s/it]
 70%|███████   | 175/250 [02:25<02:23,  1.91s/it]
 70%|███████   | 176/250 [02:27<02:25,  1.96s/it]
 71%|███████   | 177/250 [02:28<01:51,  1.52s/it]
 71%|███████   | 178/250 [02:28<01:20,  1.12s/it]
 72%|███████▏  | 179/250 [02:32<02:28,  2.10s/it]
 72%|███████▏  | 180/250 [02:34<02:22,  2.03s/it]
 72%|███████▏  | 181/250 [02:35<01:54,  1.65s/it]
 73%|███████▎  | 183/250 [02:35<01:03,  1.05it/s]
 74%|███████▍  | 185/250 [02:35<00:40,  1.59it/s]
 74%|███████▍  | 186/250 [02:36<00:33,  1.91it/s]
 75%|███████▍  | 187/250 [02:36<00:31,  1.98it/s]
 75%|███████▌  | 188/250 [02:37<00:33,  1.84it/s]
 76%|███████▌  | 189/250 [02:37<00:28,  2.17it/s]
 76%|███████▌  | 190/250 [02:37<00:27,  2.17it/s]
 76%|███████▋  | 191/250 [02:38<00:24,  2.36it/s]
 77%|███████▋  | 192/250 [02:38<00:21,  2.65it/s]
 77%|███████▋  | 193/250 [02:39<00:27,  2.08it/s]
 78%|███████▊  | 194/250 [02:40<00:38,  1.47it/s]
 78%|███████▊  | 195/250 [02:41<00:51,  1.06it/s]
 79%|███████▉  | 197/250 [02:42<00:28,  1.85it/s]
 79%|███████▉  | 198/250 [02:42<00:27,  1.91it/s]
 80%|███████▉  | 199/250 [02:42<00:23,  2.16it/s]
 80%|████████  | 201/250 [02:43<00:21,  2.28it/s]
 81%|████████  | 202/250 [02:44<00:24,  1.93it/s]
 81%|████████  | 203/250 [02:44<00:22,  2.08it/s]
 82%|████████▏ | 204/250 [02:45<00:20,  2.27it/s]
 82%|████████▏ | 205/250 [02:45<00:17,  2.52it/s]
 82%|████████▏ | 206/250 [02:46<00:22,  1.98it/s]
 83%|████████▎ | 207/250 [02:46<00:20,  2.06it/s]
 84%|████████▎ | 209/250 [02:47<00:15,  2.57it/s]
 84%|████████▍ | 210/250 [02:47<00:15,  2.61it/s]
 85%|████████▍ | 212/250 [02:48<00:14,  2.54it/s]
 85%|████████▌ | 213/250 [02:48<00:13,  2.76it/s]
 86%|████████▌ | 215/250 [02:49<00:10,  3.24it/s]
 86%|████████▋ | 216/250 [02:49<00:09,  3.55it/s]
 87%|████████▋ | 218/250 [02:49<00:07,  4.21it/s]
 88%|████████▊ | 219/250 [02:49<00:07,  4.17it/s]
 88%|████████▊ | 220/250 [02:52<00:26,  1.15it/s]
 88%|████████▊ | 221/250 [02:53<00:27,  1.05it/s]
 89%|████████▉ | 222/250 [02:54<00:20,  1.37it/s]
 89%|████████▉ | 223/250 [02:54<00:18,  1.45it/s]
 90%|████████▉ | 224/250 [02:55<00:17,  1.47it/s]
 90%|█████████ | 225/250 [02:55<00:15,  1.58it/s]
 90%|█████████ | 226/250 [02:55<00:11,  2.02it/s]
 91%|█████████ | 227/250 [02:57<00:18,  1.24it/s]
 91%|█████████ | 228/250 [03:01<00:36,  1.65s/it]
 92%|█████████▏| 229/250 [03:01<00:26,  1.27s/it]
 92%|█████████▏| 230/250 [03:03<00:27,  1.35s/it]
 93%|█████████▎| 232/250 [03:03<00:14,  1.25it/s]
 93%|█████████▎| 233/250 [03:03<00:10,  1.57it/s]
 94%|█████████▍| 235/250 [03:04<00:07,  1.91it/s]
 94%|█████████▍| 236/250 [03:05<00:10,  1.39it/s]
 95%|█████████▍| 237/250 [03:06<00:08,  1.46it/s]
 95%|█████████▌| 238/250 [03:06<00:08,  1.45it/s]
 96%|█████████▌| 239/250 [03:09<00:12,  1.13s/it]
 96%|█████████▋| 241/250 [03:09<00:06,  1.46it/s]
 97%|█████████▋| 242/250 [03:09<00:05,  1.56it/s]
 97%|█████████▋| 243/250 [03:10<00:04,  1.65it/s]
 98%|█████████▊| 244/250 [03:11<00:04,  1.22it/s]
 98%|█████████▊| 245/250 [03:12<00:03,  1.55it/s]
 98%|█████████▊| 246/250 [03:12<00:02,  1.87it/s]
 99%|█████████▉| 247/250 [03:13<00:01,  1.66it/s]
 99%|█████████▉| 248/250 [03:13<00:01,  1.90it/s]
100%|█████████▉| 249/250 [03:13<00:00,  2.44it/s]
100%|██████████| 250/250 [03:16<00:00,  1.07s/it]
100%|██████████| 250/250 [03:16<00:00,  1.27it/s]
2025-05-16 13:45:43,414 - modnet - INFO - Loss per individual: ind 0: 39.331 	ind 1: 41.528 	ind 2: 46.048 	ind 3: 42.139 	ind 4: 39.628 	ind 5: 40.271 	ind 6: 50.237 	ind 7: 40.802 	ind 8: 37.757 	ind 9: 38.180 	ind 10: 61.779 	ind 11: 37.994 	ind 12: 50.345 	ind 13: 43.403 	ind 14: 40.698 	ind 15: 38.319 	ind 16: 43.410 	ind 17: 45.150 	ind 18: 38.843 	ind 19: 37.556 	ind 20: 48.941 	ind 21: 38.692 	ind 22: 39.350 	ind 23: 40.489 	ind 24: 43.845 	ind 25: 43.134 	ind 26: 37.502 	ind 27: 38.496 	ind 28: 43.088 	ind 29: 43.323 	ind 30: 45.944 	ind 31: 43.053 	ind 32: 44.130 	ind 33: 41.100 	ind 34: 41.157 	ind 35: 42.403 	ind 36: 38.204 	ind 37: 36.848 	ind 38: 37.192 	ind 39: 45.703 	ind 40: 37.703 	ind 41: 42.235 	ind 42: 42.009 	ind 43: 41.334 	ind 44: 37.198 	ind 45: 48.844 	ind 46: 37.989 	ind 47: 45.808 	ind 48: 41.404 	ind 49: 55.781 	
2025-05-16 13:45:43,415 - modnet - INFO - After generation 7:
2025-05-16 13:45:43,415 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 288, 'fraction1': 0.25, 'fraction2': 0.75, 'fraction3': 0.25, 'fraction4': 0.5, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 600, 'n_layers': 5, 'layer_mults': None}
2025-05-16 13:45:43,415 - modnet - INFO - Best validation loss: 36.5611
2025-05-16 13:45:43,416 - modnet - INFO - Generation number 8

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:13<54:55, 13.23s/it]
  1%|          | 2/250 [00:14<26:25,  6.39s/it]
  1%|          | 3/250 [00:15<15:36,  3.79s/it]
  2%|▏         | 4/250 [00:17<11:58,  2.92s/it]
  2%|▏         | 5/250 [00:17<07:54,  1.94s/it]
  2%|▏         | 6/250 [00:18<06:35,  1.62s/it]
  3%|▎         | 7/250 [00:18<05:11,  1.28s/it]
  3%|▎         | 8/250 [00:20<05:50,  1.45s/it]
  4%|▎         | 9/250 [00:21<05:00,  1.25s/it]
  4%|▍         | 10/250 [00:22<04:07,  1.03s/it]
  5%|▍         | 12/250 [00:22<02:44,  1.44it/s]
  5%|▌         | 13/250 [00:23<02:34,  1.54it/s]
  6%|▌         | 15/250 [00:23<01:36,  2.44it/s]
  6%|▋         | 16/250 [00:23<01:36,  2.42it/s]
  7%|▋         | 17/250 [00:24<02:12,  1.76it/s]
  7%|▋         | 18/250 [00:25<02:45,  1.40it/s]
  8%|▊         | 19/250 [00:26<02:10,  1.76it/s]
  8%|▊         | 20/250 [00:26<02:09,  1.78it/s]
  8%|▊         | 21/250 [00:26<01:39,  2.29it/s]
  9%|▉         | 22/250 [00:27<01:32,  2.45it/s]
  9%|▉         | 23/250 [00:27<01:34,  2.41it/s]
 10%|▉         | 24/250 [00:28<02:21,  1.59it/s]
 10%|█         | 25/250 [00:29<02:53,  1.30it/s]
 10%|█         | 26/250 [00:31<03:22,  1.11it/s]
 11%|█         | 27/250 [00:31<02:28,  1.50it/s]
 11%|█         | 28/250 [00:31<02:00,  1.84it/s]
 12%|█▏        | 29/250 [00:33<03:41,  1.00s/it]
 12%|█▏        | 30/250 [00:36<05:35,  1.53s/it]
 12%|█▏        | 31/250 [00:37<04:46,  1.31s/it]
 13%|█▎        | 32/250 [00:37<03:33,  1.02it/s]
 13%|█▎        | 33/250 [00:37<02:54,  1.25it/s]
 14%|█▎        | 34/250 [00:37<02:26,  1.48it/s]
 14%|█▍        | 35/250 [00:38<02:27,  1.45it/s]
 14%|█▍        | 36/250 [00:40<03:20,  1.07it/s]
 15%|█▍        | 37/250 [00:40<02:37,  1.36it/s]
 15%|█▌        | 38/250 [00:40<02:00,  1.77it/s]
 16%|█▌        | 39/250 [00:40<01:44,  2.02it/s]
 16%|█▌        | 40/250 [00:44<04:28,  1.28s/it]
 16%|█▋        | 41/250 [00:44<03:17,  1.06it/s]
 17%|█▋        | 42/250 [00:44<02:32,  1.37it/s]
 17%|█▋        | 43/250 [00:45<02:30,  1.38it/s]
 18%|█▊        | 44/250 [00:45<02:24,  1.43it/s]
 18%|█▊        | 45/250 [00:48<04:13,  1.24s/it]
 18%|█▊        | 46/250 [00:48<03:22,  1.01it/s]
 19%|█▉        | 47/250 [00:50<04:19,  1.28s/it]
 19%|█▉        | 48/250 [00:50<03:07,  1.08it/s]
 20%|█▉        | 49/250 [00:51<02:31,  1.32it/s]
 20%|██        | 50/250 [00:52<02:45,  1.21it/s]
 20%|██        | 51/250 [00:52<02:38,  1.25it/s]
 21%|██        | 52/250 [00:53<02:01,  1.64it/s]
 21%|██        | 53/250 [00:53<01:33,  2.11it/s]
 22%|██▏       | 54/250 [00:54<02:13,  1.47it/s]
 22%|██▏       | 55/250 [00:56<03:42,  1.14s/it]
 22%|██▏       | 56/250 [00:56<02:47,  1.16it/s]
 23%|██▎       | 57/250 [00:57<02:14,  1.43it/s]
 23%|██▎       | 58/250 [00:57<01:46,  1.79it/s]
 24%|██▎       | 59/250 [00:58<01:54,  1.67it/s]
 24%|██▍       | 60/250 [00:58<01:53,  1.67it/s]
 24%|██▍       | 61/250 [01:01<03:53,  1.24s/it]
 25%|██▍       | 62/250 [01:02<03:44,  1.19s/it]
 26%|██▌       | 64/250 [01:02<02:09,  1.44it/s]
 26%|██▌       | 65/250 [01:03<02:29,  1.23it/s]
 26%|██▋       | 66/250 [01:04<02:05,  1.46it/s]
 27%|██▋       | 68/250 [01:04<01:22,  2.21it/s]
 28%|██▊       | 69/250 [01:04<01:22,  2.18it/s]
 28%|██▊       | 70/250 [01:05<01:11,  2.50it/s]
 28%|██▊       | 71/250 [01:05<01:03,  2.82it/s]
 29%|██▉       | 72/250 [01:06<01:56,  1.53it/s]
 29%|██▉       | 73/250 [01:07<01:50,  1.59it/s]
 30%|██▉       | 74/250 [01:07<01:46,  1.65it/s]
 30%|███       | 75/250 [01:08<01:31,  1.90it/s]
 30%|███       | 76/250 [01:10<03:07,  1.08s/it]
 31%|███       | 77/250 [01:11<02:52,  1.00it/s]
 32%|███▏      | 79/250 [01:13<02:56,  1.03s/it]
 32%|███▏      | 80/250 [01:15<03:29,  1.23s/it]
 32%|███▏      | 81/250 [01:18<04:58,  1.77s/it]
 33%|███▎      | 82/250 [01:18<03:41,  1.32s/it]
 33%|███▎      | 83/250 [01:19<03:06,  1.12s/it]
 34%|███▎      | 84/250 [01:19<02:33,  1.08it/s]
 34%|███▍      | 85/250 [01:21<02:50,  1.03s/it]
 34%|███▍      | 86/250 [01:21<02:24,  1.14it/s]
 35%|███▍      | 87/250 [01:22<02:33,  1.06it/s]
 35%|███▌      | 88/250 [01:23<02:20,  1.16it/s]
 36%|███▌      | 89/250 [01:24<02:06,  1.27it/s]
 36%|███▌      | 90/250 [01:25<02:54,  1.09s/it]
 36%|███▋      | 91/250 [01:27<03:34,  1.35s/it]
 37%|███▋      | 92/250 [01:29<03:26,  1.30s/it]
 37%|███▋      | 93/250 [01:29<02:29,  1.05it/s]
 38%|███▊      | 94/250 [01:29<01:54,  1.37it/s]
 38%|███▊      | 95/250 [01:29<01:28,  1.75it/s]
 38%|███▊      | 96/250 [01:30<01:52,  1.37it/s]
 39%|███▉      | 97/250 [01:31<01:32,  1.66it/s]
 39%|███▉      | 98/250 [01:33<03:00,  1.18s/it]
 40%|███▉      | 99/250 [01:33<02:12,  1.14it/s]
 40%|████      | 100/250 [01:34<01:50,  1.36it/s]
 41%|████      | 102/250 [01:34<01:13,  2.02it/s]
 41%|████      | 103/250 [01:34<00:58,  2.50it/s]
 42%|████▏     | 104/250 [01:35<01:08,  2.15it/s]
 42%|████▏     | 105/250 [01:35<01:14,  1.94it/s]
 42%|████▏     | 106/250 [01:36<01:22,  1.74it/s]
 43%|████▎     | 107/250 [01:37<01:18,  1.83it/s]
 43%|████▎     | 108/250 [01:37<01:04,  2.20it/s]
 44%|████▎     | 109/250 [01:37<01:08,  2.07it/s]
 44%|████▍     | 111/250 [01:38<01:03,  2.21it/s]
 45%|████▍     | 112/250 [01:39<01:05,  2.11it/s]
 45%|████▌     | 113/250 [01:39<00:55,  2.46it/s]
 46%|████▋     | 116/250 [01:39<00:32,  4.12it/s]
 47%|████▋     | 118/250 [01:40<00:26,  4.99it/s]
 48%|████▊     | 119/250 [01:41<00:45,  2.86it/s]
 48%|████▊     | 120/250 [01:41<00:54,  2.37it/s]
 48%|████▊     | 121/250 [01:43<01:21,  1.59it/s]
 49%|████▉     | 122/250 [01:43<01:21,  1.57it/s]
 49%|████▉     | 123/250 [01:44<01:22,  1.55it/s]
 50%|████▉     | 124/250 [01:44<01:10,  1.78it/s]
 50%|█████     | 125/250 [01:46<01:47,  1.16it/s]
 51%|█████     | 127/250 [01:46<01:12,  1.70it/s]
 51%|█████     | 128/250 [01:50<02:43,  1.34s/it]
 52%|█████▏    | 129/250 [01:51<02:43,  1.35s/it]
 52%|█████▏    | 130/250 [01:53<02:38,  1.32s/it]
 52%|█████▏    | 131/250 [01:53<02:01,  1.02s/it]
 53%|█████▎    | 132/250 [01:54<01:57,  1.01it/s]
 53%|█████▎    | 133/250 [01:54<01:26,  1.36it/s]
 54%|█████▎    | 134/250 [01:56<02:15,  1.17s/it]
 54%|█████▍    | 135/250 [01:56<01:47,  1.07it/s]
 54%|█████▍    | 136/250 [01:57<01:37,  1.17it/s]
 55%|█████▍    | 137/250 [01:58<01:45,  1.07it/s]
 55%|█████▌    | 138/250 [02:00<02:04,  1.12s/it]
 56%|█████▌    | 139/250 [02:00<01:38,  1.12it/s]
 56%|█████▌    | 140/250 [02:00<01:17,  1.43it/s]
 56%|█████▋    | 141/250 [02:01<01:00,  1.80it/s]
 57%|█████▋    | 142/250 [02:01<00:58,  1.84it/s]
 57%|█████▋    | 143/250 [02:04<02:13,  1.25s/it]
 58%|█████▊    | 144/250 [02:05<01:50,  1.05s/it]
 58%|█████▊    | 145/250 [02:06<02:04,  1.19s/it]
 58%|█████▊    | 146/250 [02:07<01:41,  1.03it/s]
 59%|█████▉    | 147/250 [02:07<01:26,  1.19it/s]
 59%|█████▉    | 148/250 [02:07<01:09,  1.46it/s]
 60%|█████▉    | 149/250 [02:08<00:53,  1.89it/s]
 60%|██████    | 150/250 [02:08<00:52,  1.90it/s]
 60%|██████    | 151/250 [02:10<01:30,  1.09it/s]
 61%|██████    | 152/250 [02:12<02:03,  1.26s/it]
 61%|██████    | 153/250 [02:13<02:02,  1.26s/it]
 62%|██████▏   | 155/250 [02:15<01:45,  1.11s/it]
 63%|██████▎   | 157/250 [02:16<01:07,  1.37it/s]
 63%|██████▎   | 158/250 [02:16<01:08,  1.34it/s]
 64%|██████▎   | 159/250 [02:17<01:01,  1.49it/s]
 64%|██████▍   | 160/250 [02:17<00:50,  1.80it/s]
 65%|██████▍   | 162/250 [02:17<00:31,  2.81it/s]
 65%|██████▌   | 163/250 [02:17<00:27,  3.19it/s]
 66%|██████▌   | 164/250 [02:18<00:27,  3.14it/s]
 66%|██████▌   | 165/250 [02:20<01:12,  1.17it/s]
 66%|██████▋   | 166/250 [02:21<01:08,  1.22it/s]
 67%|██████▋   | 167/250 [02:22<01:22,  1.01it/s]
 67%|██████▋   | 168/250 [02:23<01:20,  1.02it/s]
 68%|██████▊   | 170/250 [02:23<00:49,  1.63it/s]
 68%|██████▊   | 171/250 [02:24<00:52,  1.50it/s]
 69%|██████▉   | 172/250 [02:27<01:41,  1.30s/it]
 69%|██████▉   | 173/250 [02:28<01:31,  1.19s/it]
 70%|██████▉   | 174/250 [02:30<01:46,  1.40s/it]
 70%|███████   | 175/250 [02:32<01:50,  1.48s/it]
 70%|███████   | 176/250 [02:33<01:40,  1.36s/it]
 71%|███████   | 177/250 [02:34<01:37,  1.34s/it]
 71%|███████   | 178/250 [02:35<01:18,  1.09s/it]
 72%|███████▏  | 180/250 [02:35<00:45,  1.53it/s]
 72%|███████▏  | 181/250 [02:36<00:43,  1.59it/s]
 73%|███████▎  | 182/250 [02:38<01:08,  1.00s/it]
 73%|███████▎  | 183/250 [02:39<01:14,  1.11s/it]
 74%|███████▎  | 184/250 [02:39<00:59,  1.11it/s]
 74%|███████▍  | 185/250 [02:40<00:52,  1.23it/s]
 74%|███████▍  | 186/250 [02:42<01:08,  1.07s/it]
 75%|███████▍  | 187/250 [02:42<00:58,  1.07it/s]
 75%|███████▌  | 188/250 [02:44<01:18,  1.27s/it]
 76%|███████▌  | 189/250 [02:44<00:56,  1.08it/s]
 76%|███████▌  | 190/250 [02:45<00:45,  1.31it/s]
 76%|███████▋  | 191/250 [02:46<00:46,  1.28it/s]
 77%|███████▋  | 192/250 [02:48<01:10,  1.22s/it]
 77%|███████▋  | 193/250 [02:48<00:55,  1.04it/s]
 78%|███████▊  | 194/250 [02:48<00:39,  1.41it/s]
 78%|███████▊  | 195/250 [02:49<00:39,  1.41it/s]
 78%|███████▊  | 196/250 [02:50<00:33,  1.61it/s]
 79%|███████▉  | 197/250 [02:51<00:44,  1.20it/s]
 80%|███████▉  | 199/250 [02:51<00:25,  1.97it/s]
 80%|████████  | 200/250 [02:53<00:45,  1.11it/s]
 80%|████████  | 201/250 [02:54<00:48,  1.01it/s]
 81%|████████  | 202/250 [02:58<01:16,  1.59s/it]
 81%|████████  | 203/250 [02:58<01:00,  1.28s/it]
 82%|████████▏ | 205/250 [02:58<00:34,  1.31it/s]
 82%|████████▏ | 206/250 [02:59<00:28,  1.54it/s]
 83%|████████▎ | 207/250 [03:00<00:38,  1.13it/s]
 83%|████████▎ | 208/250 [03:02<00:46,  1.11s/it]
 84%|████████▎ | 209/250 [03:03<00:46,  1.13s/it]
 84%|████████▍ | 210/250 [03:04<00:37,  1.08it/s]
 84%|████████▍ | 211/250 [03:04<00:28,  1.39it/s]
 85%|████████▍ | 212/250 [03:04<00:24,  1.57it/s]
 85%|████████▌ | 213/250 [03:05<00:24,  1.51it/s]
 86%|████████▌ | 214/250 [03:08<00:44,  1.24s/it]
 86%|████████▌ | 215/250 [03:11<01:06,  1.89s/it]
 86%|████████▋ | 216/250 [03:11<00:46,  1.36s/it]
 87%|████████▋ | 217/250 [03:11<00:34,  1.05s/it]
 87%|████████▋ | 218/250 [03:12<00:27,  1.17it/s]
 88%|████████▊ | 219/250 [03:14<00:36,  1.19s/it]
 88%|████████▊ | 220/250 [03:14<00:30,  1.00s/it]
 88%|████████▊ | 221/250 [03:15<00:24,  1.18it/s]
 89%|████████▉ | 222/250 [03:15<00:20,  1.36it/s]
 90%|████████▉ | 224/250 [03:17<00:17,  1.47it/s]
 90%|█████████ | 226/250 [03:19<00:19,  1.24it/s]
 91%|█████████ | 227/250 [03:19<00:16,  1.43it/s]
 91%|█████████ | 228/250 [03:19<00:12,  1.75it/s]
 92%|█████████▏| 229/250 [03:20<00:14,  1.43it/s]
 92%|█████████▏| 230/250 [03:21<00:17,  1.14it/s]
 92%|█████████▏| 231/250 [03:22<00:15,  1.23it/s]
 93%|█████████▎| 232/250 [03:22<00:11,  1.58it/s]
 93%|█████████▎| 233/250 [03:23<00:09,  1.72it/s]
 94%|█████████▎| 234/250 [03:25<00:15,  1.04it/s]
 94%|█████████▍| 235/250 [03:26<00:15,  1.02s/it]
 94%|█████████▍| 236/250 [03:26<00:10,  1.30it/s]
 95%|█████████▍| 237/250 [03:26<00:08,  1.50it/s]
 95%|█████████▌| 238/250 [03:27<00:06,  1.89it/s]
 96%|█████████▌| 239/250 [03:27<00:05,  1.95it/s]
 96%|█████████▋| 241/250 [03:27<00:02,  3.10it/s]
 97%|█████████▋| 242/250 [03:27<00:02,  3.27it/s]
 97%|█████████▋| 243/250 [03:28<00:02,  3.15it/s]
 98%|█████████▊| 244/250 [03:29<00:03,  1.77it/s]
 98%|█████████▊| 245/250 [03:29<00:02,  2.29it/s]
 98%|█████████▊| 246/250 [03:30<00:01,  2.25it/s]
 99%|█████████▉| 247/250 [03:32<00:03,  1.13s/it]
 99%|█████████▉| 248/250 [03:34<00:02,  1.26s/it]
100%|█████████▉| 249/250 [03:34<00:00,  1.07it/s]
100%|██████████| 250/250 [03:34<00:00,  1.38it/s]
100%|██████████| 250/250 [03:34<00:00,  1.16it/s]
2025-05-16 13:49:18,362 - modnet - INFO - Loss per individual: ind 0: 42.953 	ind 1: 39.021 	ind 2: 37.816 	ind 3: 40.208 	ind 4: 39.598 	ind 5: 41.951 	ind 6: 39.141 	ind 7: 39.127 	ind 8: 51.576 	ind 9: 40.304 	ind 10: 38.574 	ind 11: 44.224 	ind 12: 39.027 	ind 13: 38.707 	ind 14: 39.713 	ind 15: 39.286 	ind 16: 47.255 	ind 17: 47.720 	ind 18: 38.105 	ind 19: 37.269 	ind 20: 41.832 	ind 21: 44.086 	ind 22: 42.354 	ind 23: 44.898 	ind 24: 38.695 	ind 25: 56.798 	ind 26: 77.672 	ind 27: 39.198 	ind 28: 36.907 	ind 29: 45.156 	ind 30: 42.792 	ind 31: 40.732 	ind 32: 43.238 	ind 33: 39.311 	ind 34: 39.375 	ind 35: 38.243 	ind 36: 57.171 	ind 37: 38.143 	ind 38: 44.677 	ind 39: 40.227 	ind 40: 39.650 	ind 41: 38.548 	ind 42: 38.054 	ind 43: 301.336 	ind 44: 38.664 	ind 45: 41.229 	ind 46: 40.147 	ind 47: 95.785 	ind 48: 37.717 	ind 49: 39.994 	
2025-05-16 13:49:18,363 - modnet - INFO - After generation 8:
2025-05-16 13:49:18,363 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 288, 'fraction1': 0.25, 'fraction2': 0.75, 'fraction3': 0.25, 'fraction4': 0.5, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 600, 'n_layers': 5, 'layer_mults': None}
2025-05-16 13:49:18,363 - modnet - INFO - Best validation loss: 36.5611
2025-05-16 13:49:18,363 - modnet - INFO - Generation number 9

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:09<39:58,  9.63s/it]
  1%|          | 2/250 [00:11<20:58,  5.07s/it]
  1%|          | 3/250 [00:12<12:38,  3.07s/it]
  2%|▏         | 4/250 [00:12<08:08,  1.99s/it]
  2%|▏         | 5/250 [00:13<06:37,  1.62s/it]
  2%|▏         | 6/250 [00:14<05:24,  1.33s/it]
  3%|▎         | 7/250 [00:14<03:47,  1.07it/s]
  4%|▎         | 9/250 [00:18<05:33,  1.38s/it]
  4%|▍         | 10/250 [00:24<10:15,  2.56s/it]
  4%|▍         | 11/250 [00:25<08:31,  2.14s/it]
  5%|▍         | 12/250 [00:26<07:45,  1.96s/it]
  5%|▌         | 13/250 [00:29<09:14,  2.34s/it]
  6%|▌         | 14/250 [00:30<07:07,  1.81s/it]
  6%|▌         | 15/250 [00:31<05:55,  1.51s/it]
  6%|▋         | 16/250 [00:31<04:52,  1.25s/it]
  7%|▋         | 17/250 [00:32<04:29,  1.16s/it]
  8%|▊         | 19/250 [00:33<03:19,  1.16it/s]
  8%|▊         | 20/250 [00:34<02:45,  1.39it/s]
  8%|▊         | 21/250 [00:34<02:20,  1.63it/s]
  9%|▉         | 22/250 [00:34<01:51,  2.04it/s]
 10%|▉         | 24/250 [00:34<01:16,  2.96it/s]
 10%|█         | 25/250 [00:35<01:28,  2.55it/s]
 10%|█         | 26/250 [00:35<01:37,  2.31it/s]
 11%|█         | 27/250 [00:36<01:21,  2.74it/s]
 11%|█         | 28/250 [00:36<01:11,  3.12it/s]
 12%|█▏        | 29/250 [00:36<01:19,  2.77it/s]
 12%|█▏        | 30/250 [00:36<01:04,  3.41it/s]
 12%|█▏        | 31/250 [00:37<01:16,  2.88it/s]
 13%|█▎        | 32/250 [00:38<01:42,  2.14it/s]
 14%|█▎        | 34/250 [00:41<03:19,  1.08it/s]
 14%|█▍        | 35/250 [00:45<06:02,  1.69s/it]
 14%|█▍        | 36/250 [00:46<05:36,  1.57s/it]
 15%|█▌        | 38/250 [00:47<04:06,  1.16s/it]
 16%|█▌        | 39/250 [00:48<04:19,  1.23s/it]
 16%|█▌        | 40/250 [00:49<03:30,  1.00s/it]
 16%|█▋        | 41/250 [00:49<02:54,  1.20it/s]
 17%|█▋        | 42/250 [00:53<05:51,  1.69s/it]
 17%|█▋        | 43/250 [00:53<04:27,  1.29s/it]
 18%|█▊        | 45/250 [00:54<03:11,  1.07it/s]
 19%|█▉        | 47/250 [00:55<02:05,  1.62it/s]
 19%|█▉        | 48/250 [00:55<02:04,  1.62it/s]
 20%|█▉        | 49/250 [00:55<01:43,  1.94it/s]
 20%|██        | 50/250 [00:56<01:45,  1.90it/s]
 20%|██        | 51/250 [00:56<01:22,  2.41it/s]
 21%|██        | 52/250 [00:57<01:25,  2.32it/s]
 21%|██        | 53/250 [00:59<03:36,  1.10s/it]
 22%|██▏       | 54/250 [01:00<03:07,  1.05it/s]
 22%|██▏       | 55/250 [01:00<02:26,  1.33it/s]
 22%|██▏       | 56/250 [01:02<03:44,  1.16s/it]
 23%|██▎       | 58/250 [01:03<02:08,  1.50it/s]
 24%|██▎       | 59/250 [01:03<01:41,  1.88it/s]
 24%|██▍       | 61/250 [01:04<01:39,  1.90it/s]
 25%|██▍       | 62/250 [01:05<02:24,  1.30it/s]
 26%|██▌       | 64/250 [01:06<01:57,  1.59it/s]
 26%|██▌       | 65/250 [01:07<02:13,  1.39it/s]
 27%|██▋       | 67/250 [01:08<01:33,  1.96it/s]
 27%|██▋       | 68/250 [01:08<01:19,  2.30it/s]
 28%|██▊       | 69/250 [01:08<01:11,  2.52it/s]
 28%|██▊       | 70/250 [01:08<01:16,  2.37it/s]
 29%|██▉       | 72/250 [01:09<00:57,  3.09it/s]
 29%|██▉       | 73/250 [01:09<00:50,  3.54it/s]
 30%|██▉       | 74/250 [01:09<00:45,  3.83it/s]
 30%|███       | 75/250 [01:09<00:43,  4.00it/s]
 30%|███       | 76/250 [01:10<01:02,  2.78it/s]
 31%|███       | 77/250 [01:11<01:21,  2.11it/s]
 31%|███       | 78/250 [01:11<01:06,  2.58it/s]
 32%|███▏      | 79/250 [01:12<01:13,  2.33it/s]
 32%|███▏      | 80/250 [01:16<04:17,  1.52s/it]
 32%|███▏      | 81/250 [01:22<08:32,  3.03s/it]
 33%|███▎      | 82/250 [01:24<07:26,  2.66s/it]
 33%|███▎      | 83/250 [01:25<05:42,  2.05s/it]
 34%|███▎      | 84/250 [01:25<04:13,  1.53s/it]
 34%|███▍      | 85/250 [01:26<03:28,  1.26s/it]
 34%|███▍      | 86/250 [01:27<03:52,  1.42s/it]
 35%|███▍      | 87/250 [01:29<04:17,  1.58s/it]
 35%|███▌      | 88/250 [01:31<04:01,  1.49s/it]
 36%|███▌      | 89/250 [01:31<03:15,  1.21s/it]
 36%|███▌      | 90/250 [01:32<02:30,  1.06it/s]
 36%|███▋      | 91/250 [01:33<02:53,  1.09s/it]
 37%|███▋      | 92/250 [01:34<02:39,  1.01s/it]
 37%|███▋      | 93/250 [01:38<04:57,  1.89s/it]
 38%|███▊      | 94/250 [01:38<03:37,  1.40s/it]
 38%|███▊      | 95/250 [01:39<03:20,  1.29s/it]
 38%|███▊      | 96/250 [01:40<02:54,  1.14s/it]
 39%|███▉      | 97/250 [01:41<02:38,  1.03s/it]
 39%|███▉      | 98/250 [01:41<02:06,  1.20it/s]
 40%|███▉      | 99/250 [01:42<02:12,  1.14it/s]
 40%|████      | 100/250 [01:42<01:38,  1.53it/s]
 40%|████      | 101/250 [01:43<02:01,  1.22it/s]
 41%|████      | 102/250 [01:44<01:43,  1.42it/s]
 41%|████      | 103/250 [01:46<02:35,  1.06s/it]
 42%|████▏     | 104/250 [01:46<02:08,  1.14it/s]
 42%|████▏     | 105/250 [01:46<01:41,  1.43it/s]
 42%|████▏     | 106/250 [01:47<01:21,  1.77it/s]
 43%|████▎     | 107/250 [01:47<01:29,  1.59it/s]
 43%|████▎     | 108/250 [01:48<01:13,  1.92it/s]
 44%|████▎     | 109/250 [01:49<01:50,  1.28it/s]
 44%|████▍     | 110/250 [01:50<01:49,  1.28it/s]
 44%|████▍     | 111/250 [01:50<01:40,  1.38it/s]
 45%|████▍     | 112/250 [01:51<01:18,  1.75it/s]
 45%|████▌     | 113/250 [01:51<01:10,  1.95it/s]
 46%|████▌     | 114/250 [01:53<01:52,  1.21it/s]
 46%|████▋     | 116/250 [01:53<01:06,  2.01it/s]
 47%|████▋     | 118/250 [01:57<02:22,  1.08s/it]
 48%|████▊     | 119/250 [01:58<02:32,  1.16s/it]
 48%|████▊     | 120/250 [01:58<02:00,  1.08it/s]
 48%|████▊     | 121/250 [02:00<02:13,  1.04s/it]
 49%|████▉     | 122/250 [02:00<01:46,  1.20it/s]
 49%|████▉     | 123/250 [02:00<01:30,  1.41it/s]
 50%|████▉     | 124/250 [02:01<01:11,  1.77it/s]
 50%|█████     | 125/250 [02:01<01:05,  1.92it/s]
 50%|█████     | 126/250 [02:01<00:52,  2.36it/s]
 51%|█████     | 127/250 [02:02<00:58,  2.12it/s]
 51%|█████     | 128/250 [02:02<01:03,  1.92it/s]
 52%|█████▏    | 129/250 [02:03<00:56,  2.14it/s]
 52%|█████▏    | 130/250 [02:03<00:50,  2.35it/s]
 52%|█████▏    | 131/250 [02:04<00:52,  2.27it/s]
 53%|█████▎    | 132/250 [02:04<00:43,  2.69it/s]
 53%|█████▎    | 133/250 [02:04<00:52,  2.21it/s]
 54%|█████▎    | 134/250 [02:05<00:41,  2.77it/s]
 54%|█████▍    | 135/250 [02:08<02:37,  1.37s/it]
 54%|█████▍    | 136/250 [02:08<01:54,  1.01s/it]
 55%|█████▍    | 137/250 [02:09<01:29,  1.27it/s]
 55%|█████▌    | 138/250 [02:10<02:01,  1.09s/it]
 56%|█████▌    | 139/250 [02:11<01:36,  1.15it/s]
 56%|█████▌    | 140/250 [02:11<01:12,  1.52it/s]
 56%|█████▋    | 141/250 [02:12<01:31,  1.19it/s]
 57%|█████▋    | 142/250 [02:13<01:20,  1.35it/s]
 57%|█████▋    | 143/250 [02:14<01:24,  1.27it/s]
 58%|█████▊    | 144/250 [02:15<01:41,  1.04it/s]
 58%|█████▊    | 146/250 [02:15<00:57,  1.80it/s]
 59%|█████▉    | 147/250 [02:16<01:07,  1.53it/s]
 59%|█████▉    | 148/250 [02:18<01:33,  1.09it/s]
 60%|█████▉    | 149/250 [02:18<01:13,  1.38it/s]
 60%|██████    | 150/250 [02:18<00:57,  1.75it/s]
 60%|██████    | 151/250 [02:19<01:08,  1.44it/s]
 61%|██████    | 152/250 [02:20<01:00,  1.63it/s]
 61%|██████    | 153/250 [02:20<00:55,  1.76it/s]
 62%|██████▏   | 154/250 [02:21<00:56,  1.71it/s]
 62%|██████▏   | 155/250 [02:21<00:50,  1.89it/s]
 62%|██████▏   | 156/250 [02:22<00:49,  1.90it/s]
 63%|██████▎   | 157/250 [02:22<00:38,  2.43it/s]
 63%|██████▎   | 158/250 [02:22<00:36,  2.51it/s]
 64%|██████▎   | 159/250 [02:24<01:07,  1.35it/s]
 64%|██████▍   | 160/250 [02:27<02:07,  1.42s/it]
 64%|██████▍   | 161/250 [02:28<02:10,  1.46s/it]
 65%|██████▍   | 162/250 [02:28<01:33,  1.07s/it]
 65%|██████▌   | 163/250 [02:29<01:31,  1.05s/it]
 66%|██████▌   | 164/250 [02:30<01:21,  1.06it/s]
 66%|██████▌   | 165/250 [02:30<01:04,  1.31it/s]
 66%|██████▋   | 166/250 [02:31<00:58,  1.43it/s]
 67%|██████▋   | 167/250 [02:32<00:57,  1.45it/s]
 67%|██████▋   | 168/250 [02:32<00:45,  1.81it/s]
 68%|██████▊   | 169/250 [02:32<00:35,  2.28it/s]
 68%|██████▊   | 170/250 [02:32<00:31,  2.57it/s]
 69%|██████▉   | 172/250 [02:33<00:32,  2.40it/s]
 69%|██████▉   | 173/250 [02:34<00:34,  2.20it/s]
 70%|██████▉   | 174/250 [02:34<00:31,  2.41it/s]
 70%|███████   | 176/250 [02:35<00:24,  3.00it/s]
 71%|███████   | 177/250 [02:35<00:24,  2.96it/s]
 71%|███████   | 178/250 [02:35<00:29,  2.46it/s]
 72%|███████▏  | 179/250 [02:36<00:27,  2.59it/s]
 72%|███████▏  | 180/250 [02:36<00:23,  3.03it/s]
 72%|███████▏  | 181/250 [02:36<00:22,  3.09it/s]
 73%|███████▎  | 182/250 [02:40<01:24,  1.24s/it]
 73%|███████▎  | 183/250 [02:40<01:10,  1.06s/it]
 74%|███████▎  | 184/250 [02:41<01:09,  1.06s/it]
 74%|███████▍  | 185/250 [02:45<01:50,  1.70s/it]
 75%|███████▍  | 187/250 [02:46<01:10,  1.13s/it]
 75%|███████▌  | 188/250 [02:46<00:56,  1.09it/s]
 76%|███████▌  | 189/250 [02:46<00:47,  1.27it/s]
 76%|███████▌  | 190/250 [02:47<00:38,  1.58it/s]
 76%|███████▋  | 191/250 [02:47<00:33,  1.78it/s]
 77%|███████▋  | 192/250 [02:47<00:27,  2.10it/s]
 77%|███████▋  | 193/250 [02:47<00:24,  2.33it/s]
 78%|███████▊  | 194/250 [02:48<00:23,  2.36it/s]
 78%|███████▊  | 195/250 [02:48<00:20,  2.74it/s]
 78%|███████▊  | 196/250 [02:49<00:24,  2.18it/s]
 79%|███████▉  | 197/250 [02:50<00:28,  1.85it/s]
 79%|███████▉  | 198/250 [02:50<00:23,  2.20it/s]
 80%|███████▉  | 199/250 [02:51<00:33,  1.54it/s]
 80%|████████  | 200/250 [02:54<01:04,  1.29s/it]
 80%|████████  | 201/250 [02:56<01:12,  1.49s/it]
 81%|████████  | 202/250 [02:57<01:13,  1.53s/it]
 81%|████████  | 203/250 [02:58<01:03,  1.34s/it]
 82%|████████▏ | 204/250 [02:58<00:46,  1.01s/it]
 82%|████████▏ | 205/250 [02:59<00:41,  1.08it/s]
 82%|████████▏ | 206/250 [03:01<00:54,  1.25s/it]
 83%|████████▎ | 207/250 [03:02<00:43,  1.00s/it]
 83%|████████▎ | 208/250 [03:02<00:36,  1.16it/s]
 84%|████████▎ | 209/250 [03:02<00:29,  1.40it/s]
 84%|████████▍ | 210/250 [03:03<00:22,  1.74it/s]
 84%|████████▍ | 211/250 [03:03<00:17,  2.26it/s]
 85%|████████▍ | 212/250 [03:04<00:24,  1.58it/s]
 85%|████████▌ | 213/250 [03:04<00:21,  1.71it/s]
 86%|████████▌ | 214/250 [03:05<00:23,  1.53it/s]
 86%|████████▌ | 215/250 [03:06<00:22,  1.54it/s]
 86%|████████▋ | 216/250 [03:06<00:18,  1.83it/s]
 87%|████████▋ | 217/250 [03:06<00:15,  2.08it/s]
 87%|████████▋ | 218/250 [03:07<00:17,  1.84it/s]
 88%|████████▊ | 220/250 [03:08<00:11,  2.65it/s]
 89%|████████▉ | 222/250 [03:08<00:09,  2.94it/s]
 89%|████████▉ | 223/250 [03:09<00:10,  2.55it/s]
 90%|████████▉ | 224/250 [03:09<00:11,  2.31it/s]
 90%|█████████ | 225/250 [03:09<00:09,  2.63it/s]
 91%|█████████ | 227/250 [03:10<00:07,  3.25it/s]
 91%|█████████ | 228/250 [03:10<00:07,  2.82it/s]
 92%|█████████▏| 229/250 [03:11<00:06,  3.05it/s]
 92%|█████████▏| 230/250 [03:13<00:18,  1.08it/s]
 92%|█████████▏| 231/250 [03:13<00:13,  1.37it/s]
 93%|█████████▎| 232/250 [03:14<00:13,  1.29it/s]
 94%|█████████▎| 234/250 [03:16<00:12,  1.33it/s]
 94%|█████████▍| 235/250 [03:16<00:09,  1.61it/s]
 94%|█████████▍| 236/250 [03:16<00:07,  1.99it/s]
 95%|█████████▍| 237/250 [03:16<00:05,  2.21it/s]
 95%|█████████▌| 238/250 [03:17<00:04,  2.42it/s]
 96%|█████████▌| 239/250 [03:17<00:03,  3.07it/s]
 96%|█████████▌| 240/250 [03:18<00:05,  1.67it/s]
 96%|█████████▋| 241/250 [03:18<00:04,  1.94it/s]
 97%|█████████▋| 243/250 [03:19<00:02,  2.35it/s]
 98%|█████████▊| 245/250 [03:19<00:01,  3.54it/s]
 98%|█████████▊| 246/250 [03:20<00:01,  3.50it/s]
 99%|█████████▉| 247/250 [03:20<00:01,  2.43it/s]
 99%|█████████▉| 248/250 [03:22<00:01,  1.54it/s]
100%|█████████▉| 249/250 [03:26<00:01,  1.49s/it]
100%|██████████| 250/250 [03:28<00:00,  1.68s/it]
100%|██████████| 250/250 [03:28<00:00,  1.20it/s]
2025-05-16 13:52:46,577 - modnet - INFO - Loss per individual: ind 0: 40.291 	ind 1: 44.959 	ind 2: 44.887 	ind 3: 39.177 	ind 4: 41.287 	ind 5: 47.121 	ind 6: 39.166 	ind 7: 39.335 	ind 8: 38.716 	ind 9: 43.118 	ind 10: 43.575 	ind 11: 39.100 	ind 12: 40.337 	ind 13: 38.535 	ind 14: 40.210 	ind 15: 118.213 	ind 16: 41.670 	ind 17: 88.929 	ind 18: 36.631 	ind 19: 38.454 	ind 20: 37.672 	ind 21: 39.572 	ind 22: 42.578 	ind 23: 39.349 	ind 24: 42.730 	ind 25: 38.493 	ind 26: 38.905 	ind 27: 45.271 	ind 28: 41.897 	ind 29: 39.281 	ind 30: 39.210 	ind 31: 38.866 	ind 32: 44.294 	ind 33: 38.004 	ind 34: 43.318 	ind 35: 41.442 	ind 36: 46.038 	ind 37: 57.493 	ind 38: 39.150 	ind 39: 42.991 	ind 40: 44.166 	ind 41: 48.664 	ind 42: 38.005 	ind 43: 44.324 	ind 44: 38.907 	ind 45: 47.015 	ind 46: 40.571 	ind 47: 62.547 	ind 48: 62.854 	ind 49: 37.798 	
2025-05-16 13:52:46,579 - modnet - INFO - After generation 9:
2025-05-16 13:52:46,579 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 288, 'fraction1': 0.25, 'fraction2': 0.75, 'fraction3': 0.25, 'fraction4': 0.5, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 600, 'n_layers': 5, 'layer_mults': None}
2025-05-16 13:52:46,579 - modnet - INFO - Best validation loss: 36.5611
2025-05-16 13:52:46,579 - modnet - INFO - Generation number 10

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:15<1:04:20, 15.50s/it]
  1%|          | 2/250 [00:17<30:22,  7.35s/it]  
  1%|          | 3/250 [00:18<19:12,  4.67s/it]
  2%|▏         | 4/250 [00:19<13:31,  3.30s/it]
  2%|▏         | 5/250 [00:20<10:02,  2.46s/it]
  2%|▏         | 6/250 [00:24<12:24,  3.05s/it]
  3%|▎         | 7/250 [00:27<11:57,  2.95s/it]
  3%|▎         | 8/250 [00:29<10:08,  2.52s/it]
  4%|▎         | 9/250 [00:29<07:08,  1.78s/it]
  4%|▍         | 10/250 [00:30<05:44,  1.43s/it]
  4%|▍         | 11/250 [00:31<05:03,  1.27s/it]
  5%|▍         | 12/250 [00:31<04:02,  1.02s/it]
  5%|▌         | 13/250 [00:31<03:18,  1.19it/s]
  6%|▌         | 14/250 [00:32<03:28,  1.13it/s]
  6%|▌         | 15/250 [00:34<04:00,  1.02s/it]
  6%|▋         | 16/250 [00:34<03:04,  1.27it/s]
  7%|▋         | 18/250 [00:34<01:55,  2.01it/s]
  8%|▊         | 19/250 [00:35<01:54,  2.02it/s]
  8%|▊         | 20/250 [00:38<04:19,  1.13s/it]
  8%|▊         | 21/250 [00:38<03:34,  1.07it/s]
  9%|▉         | 22/250 [00:38<02:42,  1.40it/s]
  9%|▉         | 23/250 [00:39<02:20,  1.61it/s]
 10%|▉         | 24/250 [00:39<01:55,  1.96it/s]
 10%|█         | 25/250 [00:39<01:56,  1.93it/s]
 11%|█         | 27/250 [00:41<02:03,  1.80it/s]
 11%|█         | 28/250 [00:41<01:38,  2.24it/s]
 12%|█▏        | 29/250 [00:44<04:20,  1.18s/it]
 12%|█▏        | 30/250 [00:44<03:36,  1.02it/s]
 13%|█▎        | 32/250 [00:45<02:18,  1.58it/s]
 13%|█▎        | 33/250 [00:45<02:06,  1.72it/s]
 14%|█▎        | 34/250 [00:46<02:00,  1.79it/s]
 14%|█▍        | 35/250 [00:46<01:36,  2.23it/s]
 14%|█▍        | 36/250 [00:46<01:21,  2.64it/s]
 15%|█▍        | 37/250 [00:47<01:29,  2.37it/s]
 15%|█▌        | 38/250 [00:47<01:29,  2.36it/s]
 16%|█▌        | 39/250 [00:47<01:16,  2.75it/s]
 16%|█▋        | 41/250 [00:49<01:46,  1.96it/s]
 17%|█▋        | 42/250 [00:50<02:24,  1.44it/s]
 17%|█▋        | 43/250 [00:51<02:35,  1.33it/s]
 18%|█▊        | 44/250 [00:51<02:06,  1.63it/s]
 18%|█▊        | 45/250 [00:52<02:11,  1.56it/s]
 18%|█▊        | 46/250 [00:53<02:30,  1.36it/s]
 19%|█▉        | 47/250 [00:53<01:54,  1.78it/s]
 19%|█▉        | 48/250 [00:53<01:33,  2.15it/s]
 20%|█▉        | 49/250 [00:53<01:17,  2.61it/s]
 20%|██        | 50/250 [00:54<01:17,  2.59it/s]
 20%|██        | 51/250 [00:55<02:31,  1.32it/s]
 21%|██        | 52/250 [00:56<02:51,  1.16it/s]
 21%|██        | 53/250 [00:58<03:37,  1.11s/it]
 22%|██▏       | 54/250 [01:01<05:08,  1.57s/it]
 22%|██▏       | 55/250 [01:02<04:43,  1.45s/it]
 22%|██▏       | 56/250 [01:09<09:58,  3.09s/it]
 23%|██▎       | 57/250 [01:11<08:36,  2.68s/it]
 23%|██▎       | 58/250 [01:12<07:47,  2.43s/it]
 24%|██▎       | 59/250 [01:13<05:56,  1.86s/it]
 24%|██▍       | 60/250 [01:14<04:57,  1.56s/it]
 24%|██▍       | 61/250 [01:15<04:52,  1.55s/it]
 25%|██▍       | 62/250 [01:16<04:23,  1.40s/it]
 25%|██▌       | 63/250 [01:18<04:29,  1.44s/it]
 26%|██▋       | 66/250 [01:18<02:05,  1.47it/s]
 27%|██▋       | 67/250 [01:18<01:45,  1.73it/s]
 27%|██▋       | 68/250 [01:19<01:27,  2.08it/s]
 28%|██▊       | 69/250 [01:21<03:05,  1.02s/it]
 28%|██▊       | 70/250 [01:24<04:13,  1.41s/it]
 28%|██▊       | 71/250 [01:24<03:41,  1.23s/it]
 29%|██▉       | 72/250 [01:25<02:52,  1.03it/s]
 29%|██▉       | 73/250 [01:25<02:22,  1.24it/s]
 30%|███       | 75/250 [01:26<01:40,  1.75it/s]
 31%|███       | 77/250 [01:26<01:11,  2.43it/s]
 31%|███       | 78/250 [01:26<01:02,  2.75it/s]
 32%|███▏      | 79/250 [01:27<01:30,  1.89it/s]
 32%|███▏      | 80/250 [01:28<01:49,  1.55it/s]
 32%|███▏      | 81/250 [01:29<01:58,  1.42it/s]
 33%|███▎      | 82/250 [01:30<01:44,  1.60it/s]
 33%|███▎      | 83/250 [01:33<03:41,  1.33s/it]
 34%|███▎      | 84/250 [01:34<03:34,  1.29s/it]
 34%|███▍      | 85/250 [01:34<02:58,  1.08s/it]
 34%|███▍      | 86/250 [01:35<02:33,  1.07it/s]
 35%|███▌      | 88/250 [01:36<02:04,  1.30it/s]
 36%|███▌      | 89/250 [01:37<02:25,  1.11it/s]
 36%|███▌      | 90/250 [01:38<02:18,  1.16it/s]
 36%|███▋      | 91/250 [01:40<03:03,  1.16s/it]
 37%|███▋      | 92/250 [01:40<02:21,  1.12it/s]
 37%|███▋      | 93/250 [01:41<02:02,  1.28it/s]
 38%|███▊      | 94/250 [01:43<03:13,  1.24s/it]
 38%|███▊      | 95/250 [01:44<02:36,  1.01s/it]
 38%|███▊      | 96/250 [01:44<01:57,  1.32it/s]
 39%|███▉      | 97/250 [01:47<03:20,  1.31s/it]
 39%|███▉      | 98/250 [01:47<02:31,  1.01it/s]
 40%|███▉      | 99/250 [01:47<02:09,  1.17it/s]
 40%|████      | 101/250 [01:48<01:27,  1.70it/s]
 41%|████      | 102/250 [01:48<01:21,  1.81it/s]
 41%|████      | 103/250 [01:49<01:17,  1.90it/s]
 42%|████▏     | 104/250 [01:50<01:55,  1.26it/s]
 42%|████▏     | 105/250 [01:51<01:52,  1.29it/s]
 42%|████▏     | 106/250 [01:53<02:30,  1.04s/it]
 43%|████▎     | 108/250 [01:53<01:29,  1.58it/s]
 44%|████▍     | 110/250 [01:53<01:01,  2.28it/s]
 45%|████▍     | 112/250 [01:53<00:44,  3.14it/s]
 45%|████▌     | 113/250 [01:54<00:53,  2.54it/s]
 46%|████▌     | 114/250 [01:55<01:10,  1.94it/s]
 47%|████▋     | 117/250 [01:56<00:53,  2.47it/s]
 47%|████▋     | 118/250 [01:56<00:52,  2.51it/s]
 48%|████▊     | 120/250 [01:57<00:41,  3.12it/s]
 48%|████▊     | 121/250 [01:57<00:43,  2.99it/s]
 49%|████▉     | 122/250 [01:58<00:47,  2.67it/s]
 49%|████▉     | 123/250 [01:58<00:55,  2.29it/s]
 50%|████▉     | 124/250 [01:59<01:20,  1.57it/s]
 50%|█████     | 125/250 [02:01<01:43,  1.21it/s]
 50%|█████     | 126/250 [02:01<01:30,  1.37it/s]
 51%|█████     | 127/250 [02:03<01:55,  1.07it/s]
 51%|█████     | 128/250 [02:04<01:52,  1.09it/s]
 52%|█████▏    | 129/250 [02:06<02:27,  1.22s/it]
 52%|█████▏    | 131/250 [02:08<02:16,  1.14s/it]
 53%|█████▎    | 132/250 [02:09<02:25,  1.23s/it]
 53%|█████▎    | 133/250 [02:10<02:01,  1.04s/it]
 54%|█████▎    | 134/250 [02:11<02:07,  1.10s/it]
 54%|█████▍    | 135/250 [02:12<02:11,  1.14s/it]
 54%|█████▍    | 136/250 [02:16<03:53,  2.04s/it]
 55%|█████▍    | 137/250 [02:18<03:29,  1.85s/it]
 55%|█████▌    | 138/250 [02:19<02:50,  1.52s/it]
 56%|█████▌    | 139/250 [02:20<03:02,  1.64s/it]
 56%|█████▌    | 140/250 [02:21<02:19,  1.27s/it]
 56%|█████▋    | 141/250 [02:21<01:42,  1.06it/s]
 57%|█████▋    | 142/250 [02:22<01:40,  1.07it/s]
 57%|█████▋    | 143/250 [02:22<01:17,  1.38it/s]
 58%|█████▊    | 144/250 [02:23<01:10,  1.49it/s]
 58%|█████▊    | 145/250 [02:24<01:34,  1.11it/s]
 58%|█████▊    | 146/250 [02:26<01:53,  1.09s/it]
 59%|█████▉    | 147/250 [02:26<01:29,  1.15it/s]
 59%|█████▉    | 148/250 [02:27<01:21,  1.25it/s]
 60%|█████▉    | 149/250 [02:27<01:01,  1.64it/s]
 60%|██████    | 150/250 [02:27<00:51,  1.94it/s]
 60%|██████    | 151/250 [02:28<01:09,  1.43it/s]
 61%|██████    | 152/250 [02:28<00:51,  1.89it/s]
 61%|██████    | 153/250 [02:28<00:39,  2.49it/s]
 62%|██████▏   | 155/250 [02:30<00:45,  2.08it/s]
 62%|██████▏   | 156/250 [02:30<00:50,  1.87it/s]
 63%|██████▎   | 157/250 [02:31<00:41,  2.24it/s]
 63%|██████▎   | 158/250 [02:31<00:39,  2.34it/s]
 64%|██████▎   | 159/250 [02:32<00:58,  1.57it/s]
 64%|██████▍   | 160/250 [02:32<00:45,  1.97it/s]
 64%|██████▍   | 161/250 [02:33<00:40,  2.18it/s]
 65%|██████▍   | 162/250 [02:33<00:40,  2.16it/s]
 66%|██████▌   | 164/250 [02:33<00:25,  3.40it/s]
 66%|██████▌   | 165/250 [02:34<00:29,  2.88it/s]
 66%|██████▋   | 166/250 [02:35<00:46,  1.81it/s]
 67%|██████▋   | 167/250 [02:35<00:46,  1.80it/s]
 67%|██████▋   | 168/250 [02:36<00:46,  1.78it/s]
 68%|██████▊   | 169/250 [02:41<02:16,  1.68s/it]
 68%|██████▊   | 170/250 [02:41<01:42,  1.28s/it]
 68%|██████▊   | 171/250 [02:42<01:33,  1.18s/it]
 69%|██████▉   | 173/250 [02:43<01:02,  1.23it/s]
 70%|██████▉   | 174/250 [02:49<02:55,  2.31s/it]
 70%|███████   | 175/250 [02:50<02:24,  1.92s/it]
 70%|███████   | 176/250 [02:51<01:54,  1.55s/it]
 71%|███████   | 177/250 [02:51<01:24,  1.16s/it]
 71%|███████   | 178/250 [02:53<01:36,  1.35s/it]
 72%|███████▏  | 179/250 [02:53<01:11,  1.00s/it]
 72%|███████▏  | 180/250 [02:54<01:10,  1.01s/it]
 72%|███████▏  | 181/250 [02:54<00:51,  1.34it/s]
 73%|███████▎  | 182/250 [02:54<00:39,  1.74it/s]
 73%|███████▎  | 183/250 [02:55<00:46,  1.44it/s]
 74%|███████▎  | 184/250 [02:55<00:34,  1.90it/s]
 74%|███████▍  | 185/250 [02:55<00:26,  2.42it/s]
 74%|███████▍  | 186/250 [02:57<00:49,  1.28it/s]
 75%|███████▍  | 187/250 [02:58<00:42,  1.47it/s]
 75%|███████▌  | 188/250 [02:59<00:50,  1.23it/s]
 76%|███████▌  | 189/250 [02:59<00:40,  1.51it/s]
 76%|███████▌  | 190/250 [02:59<00:29,  2.00it/s]
 76%|███████▋  | 191/250 [03:00<00:27,  2.17it/s]
 77%|███████▋  | 192/250 [03:00<00:22,  2.58it/s]
 78%|███████▊  | 194/250 [03:01<00:30,  1.82it/s]
 78%|███████▊  | 195/250 [03:02<00:27,  2.01it/s]
 79%|███████▉  | 197/250 [03:02<00:17,  3.07it/s]
 79%|███████▉  | 198/250 [03:03<00:30,  1.72it/s]
 80%|███████▉  | 199/250 [03:04<00:36,  1.40it/s]
 80%|████████  | 200/250 [03:05<00:31,  1.60it/s]
 80%|████████  | 201/250 [03:05<00:25,  1.91it/s]
 81%|████████  | 202/250 [03:05<00:21,  2.25it/s]
 82%|████████▏ | 204/250 [03:07<00:26,  1.73it/s]
 82%|████████▏ | 205/250 [03:10<00:56,  1.25s/it]
 82%|████████▏ | 206/250 [03:11<00:54,  1.24s/it]
 83%|████████▎ | 208/250 [03:12<00:38,  1.10it/s]
 84%|████████▎ | 209/250 [03:12<00:30,  1.35it/s]
 84%|████████▍ | 210/250 [03:13<00:26,  1.53it/s]
 85%|████████▍ | 212/250 [03:13<00:18,  2.06it/s]
 86%|████████▌ | 214/250 [03:16<00:27,  1.33it/s]
 86%|████████▌ | 215/250 [03:16<00:21,  1.62it/s]
 86%|████████▋ | 216/250 [03:16<00:17,  1.94it/s]
 87%|████████▋ | 217/250 [03:16<00:14,  2.35it/s]
 87%|████████▋ | 218/250 [03:17<00:14,  2.14it/s]
 88%|████████▊ | 219/250 [03:17<00:17,  1.74it/s]
 88%|████████▊ | 220/250 [03:18<00:20,  1.43it/s]
 89%|████████▉ | 222/250 [03:19<00:11,  2.42it/s]
 90%|████████▉ | 224/250 [03:21<00:18,  1.43it/s]
 90%|█████████ | 225/250 [03:22<00:17,  1.44it/s]
 90%|█████████ | 226/250 [03:22<00:16,  1.43it/s]
 91%|█████████ | 227/250 [03:22<00:12,  1.80it/s]
 91%|█████████ | 228/250 [03:23<00:09,  2.25it/s]
 92%|█████████▏| 229/250 [03:23<00:11,  1.81it/s]
 92%|█████████▏| 230/250 [03:25<00:14,  1.37it/s]
 92%|█████████▏| 231/250 [03:25<00:11,  1.69it/s]
 93%|█████████▎| 232/250 [03:27<00:16,  1.08it/s]
 93%|█████████▎| 233/250 [03:27<00:13,  1.27it/s]
 94%|█████████▎| 234/250 [03:28<00:13,  1.23it/s]
 94%|█████████▍| 236/250 [03:29<00:11,  1.25it/s]
 95%|█████████▍| 237/250 [03:30<00:08,  1.54it/s]
 95%|█████████▌| 238/250 [03:30<00:07,  1.63it/s]
 96%|█████████▌| 239/250 [03:31<00:07,  1.53it/s]
 96%|█████████▌| 240/250 [03:35<00:17,  1.72s/it]
 96%|█████████▋| 241/250 [03:38<00:16,  1.86s/it]
 97%|█████████▋| 242/250 [03:40<00:15,  1.88s/it]
 97%|█████████▋| 243/250 [03:41<00:13,  1.88s/it]
 98%|█████████▊| 245/250 [03:42<00:05,  1.14s/it]
 98%|█████████▊| 246/250 [03:43<00:04,  1.12s/it]
 99%|█████████▉| 247/250 [03:44<00:03,  1.07s/it]
 99%|█████████▉| 248/250 [03:47<00:03,  1.55s/it]
100%|█████████▉| 249/250 [03:50<00:02,  2.05s/it]
100%|██████████| 250/250 [03:53<00:00,  2.27s/it]
100%|██████████| 250/250 [03:53<00:00,  1.07it/s]
2025-05-16 13:56:40,032 - modnet - INFO - Loss per individual: ind 0: 41.097 	ind 1: 40.904 	ind 2: 41.484 	ind 3: 40.723 	ind 4: 38.151 	ind 5: 39.646 	ind 6: 40.414 	ind 7: 41.699 	ind 8: 40.715 	ind 9: 41.468 	ind 10: 44.796 	ind 11: 42.438 	ind 12: 38.812 	ind 13: 40.686 	ind 14: 39.583 	ind 15: 38.510 	ind 16: 39.568 	ind 17: 38.601 	ind 18: 37.680 	ind 19: 40.146 	ind 20: 39.093 	ind 21: 40.088 	ind 22: 41.716 	ind 23: 38.740 	ind 24: 47.388 	ind 25: 52.267 	ind 26: 39.658 	ind 27: 37.822 	ind 28: 39.753 	ind 29: 44.492 	ind 30: 38.260 	ind 31: 38.839 	ind 32: 47.669 	ind 33: 39.008 	ind 34: 36.648 	ind 35: 40.044 	ind 36: 41.540 	ind 37: 38.406 	ind 38: 38.675 	ind 39: 141.635 	ind 40: 49.080 	ind 41: 123.537 	ind 42: 64.061 	ind 43: 41.545 	ind 44: 45.950 	ind 45: 47.129 	ind 46: 43.453 	ind 47: 57.924 	ind 48: 85.857 	ind 49: 39.651 	
2025-05-16 13:56:40,034 - modnet - INFO - After generation 10:
2025-05-16 13:56:40,034 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 288, 'fraction1': 0.25, 'fraction2': 0.75, 'fraction3': 0.25, 'fraction4': 0.5, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 600, 'n_layers': 5, 'layer_mults': None}
2025-05-16 13:56:40,034 - modnet - INFO - Best validation loss: 36.5611
2025-05-16 13:56:40,034 - modnet - INFO - Generation number 11

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:12<50:20, 12.13s/it]
  1%|          | 2/250 [00:12<22:00,  5.33s/it]
  1%|          | 3/250 [00:13<14:04,  3.42s/it]
  2%|▏         | 4/250 [00:14<08:50,  2.16s/it]
  2%|▏         | 5/250 [00:14<06:00,  1.47s/it]
  3%|▎         | 7/250 [00:14<03:04,  1.31it/s]
  3%|▎         | 8/250 [00:15<03:13,  1.25it/s]
  4%|▎         | 9/250 [00:16<03:19,  1.21it/s]
  4%|▍         | 10/250 [00:16<03:00,  1.33it/s]
  4%|▍         | 11/250 [00:17<02:57,  1.35it/s]
  5%|▍         | 12/250 [00:18<03:16,  1.21it/s]
  5%|▌         | 13/250 [00:19<03:17,  1.20it/s]
  6%|▌         | 14/250 [00:21<04:42,  1.20s/it]
  6%|▌         | 15/250 [00:21<03:48,  1.03it/s]
  6%|▋         | 16/250 [00:23<04:18,  1.11s/it]
  7%|▋         | 17/250 [00:24<04:34,  1.18s/it]
  7%|▋         | 18/250 [00:24<03:24,  1.13it/s]
  8%|▊         | 19/250 [00:25<03:19,  1.16it/s]
  8%|▊         | 20/250 [00:26<03:06,  1.24it/s]
  8%|▊         | 21/250 [00:27<03:17,  1.16it/s]
  9%|▉         | 22/250 [00:29<04:59,  1.31s/it]
  9%|▉         | 23/250 [00:31<05:26,  1.44s/it]
 10%|▉         | 24/250 [00:31<03:57,  1.05s/it]
 10%|█         | 25/250 [00:32<03:40,  1.02it/s]
 10%|█         | 26/250 [00:34<04:35,  1.23s/it]
 11%|█         | 27/250 [00:34<03:24,  1.09it/s]
 11%|█         | 28/250 [00:35<03:23,  1.09it/s]
 12%|█▏        | 29/250 [00:35<02:39,  1.38it/s]
 12%|█▏        | 30/250 [00:35<02:14,  1.63it/s]
 13%|█▎        | 32/250 [00:36<01:33,  2.32it/s]
 13%|█▎        | 33/250 [00:36<01:23,  2.61it/s]
 14%|█▎        | 34/250 [00:37<02:04,  1.74it/s]
 14%|█▍        | 35/250 [00:38<02:03,  1.75it/s]
 15%|█▍        | 37/250 [00:38<01:25,  2.49it/s]
 15%|█▌        | 38/250 [00:39<01:26,  2.45it/s]
 16%|█▌        | 39/250 [00:42<04:03,  1.16s/it]
 16%|█▌        | 40/250 [00:43<03:27,  1.01it/s]
 16%|█▋        | 41/250 [00:43<02:44,  1.27it/s]
 17%|█▋        | 43/250 [00:44<02:09,  1.59it/s]
 18%|█▊        | 44/250 [00:44<01:45,  1.94it/s]
 18%|█▊        | 45/250 [00:44<01:45,  1.94it/s]
 19%|█▉        | 47/250 [00:44<01:09,  2.92it/s]
 19%|█▉        | 48/250 [00:45<01:01,  3.31it/s]
 20%|█▉        | 49/250 [00:45<01:06,  3.03it/s]
 20%|██        | 50/250 [00:46<01:38,  2.03it/s]
 20%|██        | 51/250 [00:47<01:43,  1.92it/s]
 21%|██        | 52/250 [00:47<01:39,  1.99it/s]
 21%|██        | 53/250 [00:48<02:01,  1.62it/s]
 22%|██▏       | 54/250 [00:48<01:44,  1.88it/s]
 22%|██▏       | 55/250 [00:50<02:28,  1.32it/s]
 23%|██▎       | 57/250 [00:50<01:35,  2.01it/s]
 23%|██▎       | 58/250 [00:51<02:01,  1.59it/s]
 24%|██▎       | 59/250 [00:51<01:49,  1.75it/s]
 24%|██▍       | 60/250 [00:53<02:29,  1.28it/s]
 24%|██▍       | 61/250 [00:53<02:22,  1.33it/s]
 25%|██▍       | 62/250 [00:55<03:07,  1.00it/s]
 25%|██▌       | 63/250 [00:55<02:30,  1.25it/s]
 26%|██▌       | 64/250 [00:56<02:09,  1.43it/s]
 26%|██▌       | 65/250 [00:57<02:35,  1.19it/s]
 26%|██▋       | 66/250 [00:58<02:27,  1.25it/s]
 27%|██▋       | 67/250 [00:58<02:18,  1.32it/s]
 27%|██▋       | 68/250 [01:00<03:03,  1.01s/it]
 28%|██▊       | 69/250 [01:00<02:27,  1.23it/s]
 28%|██▊       | 70/250 [01:01<02:01,  1.48it/s]
 28%|██▊       | 71/250 [01:02<02:55,  1.02it/s]
 29%|██▉       | 72/250 [01:03<02:18,  1.29it/s]
 29%|██▉       | 73/250 [01:04<03:09,  1.07s/it]
 30%|██▉       | 74/250 [01:05<02:27,  1.19it/s]
 30%|███       | 75/250 [01:05<02:10,  1.34it/s]
 30%|███       | 76/250 [01:06<01:49,  1.59it/s]
 31%|███       | 77/250 [01:08<03:02,  1.05s/it]
 31%|███       | 78/250 [01:10<04:15,  1.49s/it]
 32%|███▏      | 79/250 [01:11<03:25,  1.20s/it]
 32%|███▏      | 80/250 [01:12<03:19,  1.17s/it]
 32%|███▏      | 81/250 [01:12<02:46,  1.01it/s]
 33%|███▎      | 82/250 [01:14<03:04,  1.10s/it]
 33%|███▎      | 83/250 [01:14<02:27,  1.13it/s]
 34%|███▎      | 84/250 [01:17<04:05,  1.48s/it]
 34%|███▍      | 85/250 [01:18<03:25,  1.24s/it]
 35%|███▍      | 87/250 [01:18<02:03,  1.33it/s]
 35%|███▌      | 88/250 [01:18<01:36,  1.68it/s]
 36%|███▌      | 89/250 [01:18<01:16,  2.10it/s]
 36%|███▌      | 90/250 [01:19<01:05,  2.43it/s]
 36%|███▋      | 91/250 [01:20<01:31,  1.75it/s]
 37%|███▋      | 92/250 [01:21<02:05,  1.26it/s]
 37%|███▋      | 93/250 [01:22<02:31,  1.03it/s]
 38%|███▊      | 94/250 [01:25<03:35,  1.38s/it]
 38%|███▊      | 95/250 [01:26<03:52,  1.50s/it]
 38%|███▊      | 96/250 [01:27<03:07,  1.22s/it]
 39%|███▉      | 97/250 [01:28<03:19,  1.31s/it]
 39%|███▉      | 98/250 [01:29<02:36,  1.03s/it]
 40%|███▉      | 99/250 [01:29<01:56,  1.29it/s]
 40%|████      | 100/250 [01:31<02:37,  1.05s/it]
 40%|████      | 101/250 [01:31<02:16,  1.09it/s]
 41%|████      | 102/250 [01:32<01:51,  1.33it/s]
 41%|████      | 103/250 [01:32<01:48,  1.36it/s]
 42%|████▏     | 105/250 [01:33<01:02,  2.31it/s]
 42%|████▏     | 106/250 [01:33<01:03,  2.28it/s]
 43%|████▎     | 107/250 [01:33<00:53,  2.68it/s]
 43%|████▎     | 108/250 [01:34<00:52,  2.70it/s]
 44%|████▍     | 110/250 [01:34<00:55,  2.52it/s]
 44%|████▍     | 111/250 [01:35<01:01,  2.26it/s]
 45%|████▍     | 112/250 [01:36<01:09,  1.97it/s]
 45%|████▌     | 113/250 [01:37<01:28,  1.55it/s]
 46%|████▌     | 114/250 [01:37<01:29,  1.53it/s]
 46%|████▌     | 115/250 [01:38<01:36,  1.41it/s]
 46%|████▋     | 116/250 [01:39<01:19,  1.69it/s]
 47%|████▋     | 117/250 [01:42<03:12,  1.45s/it]
 47%|████▋     | 118/250 [01:42<02:26,  1.11s/it]
 48%|████▊     | 119/250 [01:43<01:52,  1.16it/s]
 48%|████▊     | 120/250 [01:43<01:44,  1.25it/s]
 48%|████▊     | 121/250 [01:46<02:42,  1.26s/it]
 49%|████▉     | 122/250 [01:47<02:30,  1.17s/it]
 49%|████▉     | 123/250 [01:48<02:25,  1.14s/it]
 50%|████▉     | 124/250 [01:48<01:55,  1.10it/s]
 50%|█████     | 125/250 [01:48<01:29,  1.39it/s]
 50%|█████     | 126/250 [01:49<01:26,  1.43it/s]
 51%|█████     | 127/250 [01:51<02:12,  1.07s/it]
 51%|█████     | 128/250 [01:54<03:26,  1.69s/it]
 52%|█████▏    | 129/250 [01:55<02:53,  1.43s/it]
 52%|█████▏    | 130/250 [01:56<02:47,  1.40s/it]
 52%|█████▏    | 131/250 [01:57<02:36,  1.32s/it]
 53%|█████▎    | 132/250 [01:58<02:03,  1.05s/it]
 53%|█████▎    | 133/250 [01:59<01:51,  1.05it/s]
 54%|█████▎    | 134/250 [02:00<02:08,  1.10s/it]
 54%|█████▍    | 135/250 [02:00<01:45,  1.09it/s]
 54%|█████▍    | 136/250 [02:01<01:29,  1.28it/s]
 55%|█████▍    | 137/250 [02:03<02:01,  1.08s/it]
 55%|█████▌    | 138/250 [02:03<01:28,  1.26it/s]
 56%|█████▌    | 139/250 [02:03<01:19,  1.39it/s]
 56%|█████▌    | 140/250 [02:04<01:02,  1.76it/s]
 56%|█████▋    | 141/250 [02:04<00:58,  1.87it/s]
 57%|█████▋    | 142/250 [02:05<01:13,  1.46it/s]
 57%|█████▋    | 143/250 [02:06<01:06,  1.60it/s]
 58%|█████▊    | 144/250 [02:07<01:39,  1.07it/s]
 59%|█████▉    | 147/250 [02:07<00:45,  2.27it/s]
 60%|██████    | 150/250 [02:07<00:26,  3.82it/s]
 61%|██████    | 152/250 [02:08<00:22,  4.36it/s]
 62%|██████▏   | 154/250 [02:14<01:40,  1.05s/it]
 62%|██████▏   | 155/250 [02:15<01:37,  1.03s/it]
 62%|██████▏   | 156/250 [02:15<01:19,  1.19it/s]
 63%|██████▎   | 157/250 [02:16<01:18,  1.18it/s]
 63%|██████▎   | 158/250 [02:16<01:09,  1.32it/s]
 64%|██████▎   | 159/250 [02:21<02:41,  1.77s/it]
 64%|██████▍   | 160/250 [02:26<03:55,  2.61s/it]
 64%|██████▍   | 161/250 [02:26<02:53,  1.95s/it]
 65%|██████▍   | 162/250 [02:26<02:15,  1.54s/it]
 65%|██████▌   | 163/250 [02:27<01:40,  1.16s/it]
 66%|██████▌   | 164/250 [02:29<02:20,  1.64s/it]
 66%|██████▋   | 166/250 [02:30<01:22,  1.02it/s]
 67%|██████▋   | 167/250 [02:31<01:17,  1.07it/s]
 67%|██████▋   | 168/250 [02:31<01:11,  1.15it/s]
 68%|██████▊   | 169/250 [02:32<01:11,  1.14it/s]
 68%|██████▊   | 170/250 [02:33<01:07,  1.19it/s]
 68%|██████▊   | 171/250 [02:33<00:50,  1.57it/s]
 69%|██████▉   | 172/250 [02:33<00:38,  2.00it/s]
 69%|██████▉   | 173/250 [02:34<00:42,  1.83it/s]
 70%|██████▉   | 174/250 [02:36<01:05,  1.16it/s]
 70%|███████   | 175/250 [02:36<00:48,  1.56it/s]
 71%|███████   | 177/250 [02:36<00:31,  2.35it/s]
 71%|███████   | 178/250 [02:37<00:37,  1.91it/s]
 72%|███████▏  | 179/250 [02:39<01:04,  1.10it/s]
 72%|███████▏  | 180/250 [02:39<00:48,  1.43it/s]
 72%|███████▏  | 181/250 [02:39<00:39,  1.74it/s]
 73%|███████▎  | 182/250 [02:40<00:42,  1.59it/s]
 73%|███████▎  | 183/250 [02:40<00:32,  2.07it/s]
 74%|███████▎  | 184/250 [02:40<00:25,  2.57it/s]
 74%|███████▍  | 185/250 [02:40<00:20,  3.22it/s]
 74%|███████▍  | 186/250 [02:40<00:16,  4.00it/s]
 75%|███████▍  | 187/250 [02:41<00:13,  4.84it/s]
 75%|███████▌  | 188/250 [02:41<00:13,  4.57it/s]
 76%|███████▌  | 189/250 [02:42<00:34,  1.79it/s]
 76%|███████▌  | 190/250 [02:42<00:26,  2.28it/s]
 76%|███████▋  | 191/250 [02:43<00:35,  1.66it/s]
 77%|███████▋  | 192/250 [02:44<00:30,  1.91it/s]
 77%|███████▋  | 193/250 [02:45<00:45,  1.26it/s]
 78%|███████▊  | 194/250 [02:46<00:39,  1.43it/s]
 78%|███████▊  | 195/250 [02:46<00:34,  1.61it/s]
 78%|███████▊  | 196/250 [02:47<00:40,  1.33it/s]
 79%|███████▉  | 197/250 [02:48<00:43,  1.23it/s]
 79%|███████▉  | 198/250 [02:49<00:37,  1.38it/s]
 80%|███████▉  | 199/250 [02:49<00:28,  1.78it/s]
 80%|████████  | 200/250 [02:49<00:29,  1.71it/s]
 80%|████████  | 201/250 [02:50<00:31,  1.55it/s]
 81%|████████  | 203/250 [02:53<00:42,  1.10it/s]
 82%|████████▏ | 205/250 [02:54<00:35,  1.26it/s]
 82%|████████▏ | 206/250 [02:55<00:41,  1.07it/s]
 83%|████████▎ | 207/250 [02:56<00:38,  1.13it/s]
 83%|████████▎ | 208/250 [02:59<00:54,  1.30s/it]
 84%|████████▍ | 210/250 [03:00<00:46,  1.16s/it]
 84%|████████▍ | 211/250 [03:04<01:07,  1.74s/it]
 85%|████████▍ | 212/250 [03:06<01:03,  1.67s/it]
 86%|████████▌ | 214/250 [03:09<01:01,  1.70s/it]
 86%|████████▌ | 215/250 [03:09<00:48,  1.39s/it]
 86%|████████▋ | 216/250 [03:10<00:36,  1.07s/it]
 87%|████████▋ | 217/250 [03:10<00:33,  1.03s/it]
 87%|████████▋ | 218/250 [03:11<00:27,  1.15it/s]
 88%|████████▊ | 219/250 [03:12<00:25,  1.23it/s]
 88%|████████▊ | 220/250 [03:12<00:19,  1.56it/s]
 88%|████████▊ | 221/250 [03:13<00:25,  1.16it/s]
 89%|████████▉ | 222/250 [03:14<00:26,  1.04it/s]
 89%|████████▉ | 223/250 [03:15<00:20,  1.34it/s]
 90%|████████▉ | 224/250 [03:15<00:15,  1.73it/s]
 90%|█████████ | 225/250 [03:15<00:12,  2.01it/s]
 90%|█████████ | 226/250 [03:16<00:12,  1.93it/s]
 91%|█████████ | 228/250 [03:16<00:07,  2.78it/s]
 92%|█████████▏| 229/250 [03:17<00:09,  2.32it/s]
 92%|█████████▏| 230/250 [03:17<00:08,  2.46it/s]
 92%|█████████▏| 231/250 [03:17<00:06,  2.93it/s]
 93%|█████████▎| 232/250 [03:18<00:10,  1.74it/s]
 93%|█████████▎| 233/250 [03:19<00:09,  1.80it/s]
 94%|█████████▎| 234/250 [03:19<00:09,  1.75it/s]
 94%|█████████▍| 235/250 [03:20<00:08,  1.76it/s]
 94%|█████████▍| 236/250 [03:21<00:08,  1.70it/s]
 95%|█████████▍| 237/250 [03:21<00:05,  2.24it/s]
 95%|█████████▌| 238/250 [03:22<00:07,  1.65it/s]
 96%|█████████▌| 239/250 [03:22<00:06,  1.82it/s]
 96%|█████████▌| 240/250 [03:23<00:05,  1.77it/s]
 96%|█████████▋| 241/250 [03:23<00:03,  2.30it/s]
 97%|█████████▋| 242/250 [03:23<00:03,  2.33it/s]
 97%|█████████▋| 243/250 [03:24<00:03,  1.78it/s]
 98%|█████████▊| 245/250 [03:25<00:02,  2.03it/s]
 98%|█████████▊| 246/250 [03:25<00:01,  2.42it/s]
 99%|█████████▉| 247/250 [03:26<00:01,  2.02it/s]
 99%|█████████▉| 248/250 [03:26<00:00,  2.12it/s]
100%|█████████▉| 249/250 [03:27<00:00,  2.35it/s]
100%|██████████| 250/250 [03:31<00:00,  1.44s/it]
100%|██████████| 250/250 [03:31<00:00,  1.18it/s]
2025-05-16 14:00:11,139 - modnet - INFO - Loss per individual: ind 0: 45.031 	ind 1: 48.977 	ind 2: 38.225 	ind 3: 54.199 	ind 4: 38.681 	ind 5: 41.511 	ind 6: 38.708 	ind 7: 38.211 	ind 8: 38.332 	ind 9: 39.030 	ind 10: 64.019 	ind 11: 39.624 	ind 12: 45.574 	ind 13: 40.221 	ind 14: 40.336 	ind 15: 38.634 	ind 16: 37.602 	ind 17: 36.108 	ind 18: 38.584 	ind 19: 120.480 	ind 20: 38.384 	ind 21: 39.964 	ind 22: 37.779 	ind 23: 41.533 	ind 24: 51.015 	ind 25: 44.397 	ind 26: 48.709 	ind 27: 44.217 	ind 28: 42.211 	ind 29: 42.721 	ind 30: 44.797 	ind 31: 47.835 	ind 32: 37.156 	ind 33: 37.800 	ind 34: 37.362 	ind 35: 38.547 	ind 36: 40.606 	ind 37: 40.040 	ind 38: 48.006 	ind 39: 38.701 	ind 40: 39.177 	ind 41: 43.640 	ind 42: 43.586 	ind 43: 41.940 	ind 44: 38.281 	ind 45: 39.988 	ind 46: 36.196 	ind 47: 39.417 	ind 48: 39.450 	ind 49: 37.773 	
2025-05-16 14:00:11,149 - modnet - INFO - After generation 11:
2025-05-16 14:00:11,149 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 224, 'fraction1': 0.25, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 0.25, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 490, 'n_layers': 5, 'layer_mults': None}
2025-05-16 14:00:11,149 - modnet - INFO - Best validation loss: 36.1079
2025-05-16 14:00:11,149 - modnet - INFO - Generation number 12

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:06<28:12,  6.80s/it]
  1%|          | 2/250 [00:07<13:59,  3.39s/it]
  1%|          | 3/250 [00:10<11:50,  2.87s/it]
  2%|▏         | 4/250 [00:11<10:06,  2.47s/it]
  2%|▏         | 5/250 [00:13<08:33,  2.09s/it]
  2%|▏         | 6/250 [00:21<16:54,  4.16s/it]
  3%|▎         | 7/250 [00:22<12:31,  3.09s/it]
  3%|▎         | 8/250 [00:24<10:52,  2.70s/it]
  4%|▎         | 9/250 [00:24<07:50,  1.95s/it]
  4%|▍         | 10/250 [00:25<06:41,  1.67s/it]
  5%|▍         | 12/250 [00:25<03:46,  1.05it/s]
  5%|▌         | 13/250 [00:26<03:15,  1.21it/s]
  6%|▌         | 14/250 [00:27<03:43,  1.05it/s]
  6%|▌         | 15/250 [00:28<03:10,  1.23it/s]
  6%|▋         | 16/250 [00:28<02:25,  1.61it/s]
  7%|▋         | 17/250 [00:28<02:02,  1.90it/s]
  7%|▋         | 18/250 [00:29<02:24,  1.60it/s]
  8%|▊         | 19/250 [00:29<02:16,  1.69it/s]
  8%|▊         | 20/250 [00:31<03:50,  1.00s/it]
  9%|▉         | 22/250 [00:33<03:09,  1.21it/s]
  9%|▉         | 23/250 [00:35<04:32,  1.20s/it]
 10%|▉         | 24/250 [00:35<03:27,  1.09it/s]
 10%|█         | 25/250 [00:37<04:08,  1.10s/it]
 10%|█         | 26/250 [00:38<04:17,  1.15s/it]
 11%|█         | 28/250 [00:39<02:51,  1.30it/s]
 12%|█▏        | 29/250 [00:40<03:33,  1.03it/s]
 12%|█▏        | 30/250 [00:40<02:47,  1.31it/s]
 12%|█▏        | 31/250 [00:42<03:38,  1.00it/s]
 13%|█▎        | 32/250 [00:43<03:19,  1.09it/s]
 13%|█▎        | 33/250 [00:44<03:26,  1.05it/s]
 14%|█▎        | 34/250 [00:45<03:24,  1.06it/s]
 14%|█▍        | 35/250 [00:46<04:05,  1.14s/it]
 14%|█▍        | 36/250 [00:51<08:00,  2.25s/it]
 15%|█▍        | 37/250 [00:52<06:16,  1.77s/it]
 15%|█▌        | 38/250 [00:52<04:44,  1.34s/it]
 16%|█▌        | 39/250 [00:53<04:11,  1.19s/it]
 16%|█▌        | 40/250 [00:54<03:53,  1.11s/it]
 17%|█▋        | 42/250 [00:54<02:10,  1.60it/s]
 17%|█▋        | 43/250 [00:54<01:49,  1.90it/s]
 18%|█▊        | 45/250 [00:54<01:09,  2.95it/s]
 18%|█▊        | 46/250 [00:55<01:42,  1.99it/s]
 19%|█▉        | 47/250 [00:56<01:40,  2.02it/s]
 19%|█▉        | 48/250 [00:59<04:07,  1.22s/it]
 20%|█▉        | 49/250 [01:00<03:21,  1.00s/it]
 20%|██        | 50/250 [01:00<02:50,  1.18it/s]
 20%|██        | 51/250 [01:01<02:51,  1.16it/s]
 21%|██        | 52/250 [01:02<02:40,  1.23it/s]
 22%|██▏       | 54/250 [01:02<01:44,  1.87it/s]
 22%|██▏       | 55/250 [01:03<02:01,  1.60it/s]
 22%|██▏       | 56/250 [01:03<01:39,  1.96it/s]
 23%|██▎       | 57/250 [01:04<01:52,  1.71it/s]
 23%|██▎       | 58/250 [01:04<01:52,  1.71it/s]
 24%|██▎       | 59/250 [01:05<01:56,  1.64it/s]
 24%|██▍       | 60/250 [01:06<02:01,  1.57it/s]
 24%|██▍       | 61/250 [01:06<01:57,  1.61it/s]
 25%|██▍       | 62/250 [01:09<03:21,  1.07s/it]
 25%|██▌       | 63/250 [01:11<04:39,  1.49s/it]
 26%|██▌       | 64/250 [01:11<03:39,  1.18s/it]
 26%|██▋       | 66/250 [01:12<02:12,  1.39it/s]
 27%|██▋       | 67/250 [01:12<01:51,  1.64it/s]
 27%|██▋       | 68/250 [01:14<03:07,  1.03s/it]
 28%|██▊       | 69/250 [01:14<02:22,  1.27it/s]
 28%|██▊       | 70/250 [01:15<02:01,  1.48it/s]
 28%|██▊       | 71/250 [01:16<02:08,  1.39it/s]
 29%|██▉       | 72/250 [01:17<02:53,  1.02it/s]
 29%|██▉       | 73/250 [01:18<02:15,  1.30it/s]
 30%|██▉       | 74/250 [01:18<02:06,  1.39it/s]
 30%|███       | 75/250 [01:19<01:53,  1.54it/s]
 30%|███       | 76/250 [01:19<01:25,  2.04it/s]
 31%|███       | 77/250 [01:19<01:05,  2.63it/s]
 31%|███       | 78/250 [01:21<02:09,  1.33it/s]
 32%|███▏      | 79/250 [01:23<03:13,  1.13s/it]
 32%|███▏      | 80/250 [01:23<02:39,  1.07it/s]
 32%|███▏      | 81/250 [01:25<03:39,  1.30s/it]
 33%|███▎      | 83/250 [01:25<02:01,  1.37it/s]
 34%|███▎      | 84/250 [01:26<02:18,  1.20it/s]
 34%|███▍      | 85/250 [01:27<02:17,  1.20it/s]
 34%|███▍      | 86/250 [01:30<03:37,  1.33s/it]
 35%|███▍      | 87/250 [01:31<03:18,  1.22s/it]
 35%|███▌      | 88/250 [01:32<03:12,  1.19s/it]
 36%|███▌      | 90/250 [01:32<01:51,  1.44it/s]
 36%|███▋      | 91/250 [01:32<01:35,  1.66it/s]
 37%|███▋      | 92/250 [01:33<01:49,  1.44it/s]
 37%|███▋      | 93/250 [01:35<02:13,  1.17it/s]
 38%|███▊      | 94/250 [01:36<02:44,  1.05s/it]
 38%|███▊      | 95/250 [01:37<02:20,  1.10it/s]
 38%|███▊      | 96/250 [01:37<01:58,  1.30it/s]
 39%|███▉      | 97/250 [01:38<01:52,  1.37it/s]
 39%|███▉      | 98/250 [01:39<01:55,  1.32it/s]
 40%|███▉      | 99/250 [01:39<01:51,  1.36it/s]
 40%|████      | 100/250 [01:41<02:20,  1.07it/s]
 41%|████      | 102/250 [01:43<02:25,  1.02it/s]
 41%|████      | 103/250 [01:43<01:56,  1.26it/s]
 42%|████▏     | 104/250 [01:44<01:59,  1.22it/s]
 42%|████▏     | 105/250 [01:45<01:53,  1.28it/s]
 42%|████▏     | 106/250 [01:45<01:30,  1.59it/s]
 43%|████▎     | 108/250 [01:48<02:36,  1.10s/it]
 44%|████▎     | 109/250 [01:53<04:23,  1.87s/it]
 44%|████▍     | 110/250 [01:53<03:40,  1.58s/it]
 44%|████▍     | 111/250 [01:58<05:25,  2.34s/it]
 45%|████▍     | 112/250 [01:58<04:16,  1.86s/it]
 45%|████▌     | 113/250 [02:00<03:57,  1.74s/it]
 46%|████▌     | 114/250 [02:01<03:17,  1.45s/it]
 46%|████▌     | 115/250 [02:06<05:52,  2.61s/it]
 46%|████▋     | 116/250 [02:06<04:14,  1.90s/it]
 47%|████▋     | 117/250 [02:07<03:25,  1.54s/it]
 47%|████▋     | 118/250 [02:09<03:43,  1.69s/it]
 48%|████▊     | 119/250 [02:10<03:21,  1.54s/it]
 48%|████▊     | 120/250 [02:11<02:52,  1.33s/it]
 48%|████▊     | 121/250 [02:12<02:29,  1.16s/it]
 49%|████▉     | 122/250 [02:12<01:48,  1.18it/s]
 50%|████▉     | 124/250 [02:13<01:25,  1.48it/s]
 50%|█████     | 125/250 [02:13<01:11,  1.74it/s]
 50%|█████     | 126/250 [02:13<01:06,  1.86it/s]
 51%|█████     | 127/250 [02:14<01:08,  1.80it/s]
 51%|█████     | 128/250 [02:14<00:52,  2.33it/s]
 52%|█████▏    | 129/250 [02:15<00:55,  2.16it/s]
 52%|█████▏    | 130/250 [02:16<01:09,  1.72it/s]
 53%|█████▎    | 132/250 [02:16<00:46,  2.56it/s]
 53%|█████▎    | 133/250 [02:16<00:44,  2.63it/s]
 54%|█████▍    | 135/250 [02:18<00:59,  1.94it/s]
 54%|█████▍    | 136/250 [02:18<00:55,  2.05it/s]
 55%|█████▍    | 137/250 [02:20<01:25,  1.32it/s]
 55%|█████▌    | 138/250 [02:20<01:17,  1.44it/s]
 56%|█████▌    | 139/250 [02:21<01:29,  1.25it/s]
 56%|█████▌    | 140/250 [02:21<01:09,  1.59it/s]
 57%|█████▋    | 142/250 [02:22<00:52,  2.06it/s]
 57%|█████▋    | 143/250 [02:22<00:49,  2.18it/s]
 58%|█████▊    | 144/250 [02:22<00:39,  2.70it/s]
 58%|█████▊    | 145/250 [02:23<00:39,  2.63it/s]
 59%|█████▉    | 147/250 [02:24<00:36,  2.84it/s]
 59%|█████▉    | 148/250 [02:24<00:44,  2.28it/s]
 60%|█████▉    | 149/250 [02:26<01:13,  1.38it/s]
 60%|██████    | 150/250 [02:28<01:55,  1.16s/it]
 60%|██████    | 151/250 [02:30<02:03,  1.25s/it]
 61%|██████    | 152/250 [02:31<02:01,  1.24s/it]
 61%|██████    | 153/250 [02:34<02:49,  1.74s/it]
 62%|██████▏   | 154/250 [02:34<02:12,  1.38s/it]
 62%|██████▏   | 155/250 [02:36<02:25,  1.54s/it]
 62%|██████▏   | 156/250 [02:37<02:12,  1.41s/it]
 63%|██████▎   | 158/250 [02:38<01:23,  1.10it/s]
 64%|██████▎   | 159/250 [02:40<01:57,  1.29s/it]
 64%|██████▍   | 160/250 [02:42<02:04,  1.38s/it]
 64%|██████▍   | 161/250 [02:43<01:44,  1.18s/it]
 65%|██████▍   | 162/250 [02:44<01:44,  1.19s/it]
 65%|██████▌   | 163/250 [02:44<01:22,  1.05it/s]
 66%|██████▌   | 164/250 [02:46<01:35,  1.11s/it]
 66%|██████▋   | 166/250 [02:46<01:02,  1.35it/s]
 67%|██████▋   | 167/250 [02:48<01:20,  1.03it/s]
 67%|██████▋   | 168/250 [02:49<01:08,  1.19it/s]
 68%|██████▊   | 169/250 [02:49<00:54,  1.48it/s]
 68%|██████▊   | 170/250 [02:49<00:48,  1.64it/s]
 68%|██████▊   | 171/250 [02:50<00:46,  1.71it/s]
 69%|██████▉   | 172/250 [02:51<00:55,  1.40it/s]
 69%|██████▉   | 173/250 [02:53<01:37,  1.27s/it]
 70%|██████▉   | 174/250 [02:56<02:05,  1.65s/it]
 70%|███████   | 175/250 [02:57<01:46,  1.41s/it]
 70%|███████   | 176/250 [02:58<01:45,  1.43s/it]
 71%|███████   | 178/250 [02:59<01:09,  1.04it/s]
 72%|███████▏  | 179/250 [03:00<01:10,  1.01it/s]
 72%|███████▏  | 180/250 [03:01<01:01,  1.14it/s]
 73%|███████▎  | 182/250 [03:02<00:59,  1.15it/s]
 73%|███████▎  | 183/250 [03:03<00:51,  1.30it/s]
 74%|███████▎  | 184/250 [03:04<00:58,  1.13it/s]
 74%|███████▍  | 185/250 [03:05<00:50,  1.30it/s]
 74%|███████▍  | 186/250 [03:06<01:02,  1.02it/s]
 75%|███████▍  | 187/250 [03:07<00:55,  1.13it/s]
 76%|███████▌  | 189/250 [03:07<00:35,  1.74it/s]
 76%|███████▌  | 190/250 [03:08<00:42,  1.41it/s]
 76%|███████▋  | 191/250 [03:12<01:28,  1.50s/it]
 77%|███████▋  | 192/250 [03:12<01:06,  1.14s/it]
 77%|███████▋  | 193/250 [03:13<00:52,  1.08it/s]
 78%|███████▊  | 194/250 [03:15<01:14,  1.33s/it]
 78%|███████▊  | 195/250 [03:15<01:00,  1.11s/it]
 78%|███████▊  | 196/250 [03:17<01:07,  1.25s/it]
 79%|███████▉  | 197/250 [03:19<01:10,  1.33s/it]
 79%|███████▉  | 198/250 [03:19<00:57,  1.10s/it]
 80%|███████▉  | 199/250 [03:22<01:18,  1.53s/it]
 80%|████████  | 200/250 [03:22<00:58,  1.17s/it]
 80%|████████  | 201/250 [03:23<00:54,  1.11s/it]
 81%|████████  | 202/250 [03:24<00:45,  1.06it/s]
 81%|████████  | 203/250 [03:24<00:43,  1.07it/s]
 82%|████████▏ | 204/250 [03:25<00:32,  1.43it/s]
 82%|████████▏ | 205/250 [03:25<00:32,  1.39it/s]
 83%|████████▎ | 207/250 [03:27<00:37,  1.15it/s]
 83%|████████▎ | 208/250 [03:28<00:29,  1.41it/s]
 84%|████████▎ | 209/250 [03:29<00:39,  1.03it/s]
 84%|████████▍ | 210/250 [03:30<00:32,  1.24it/s]
 84%|████████▍ | 211/250 [03:31<00:33,  1.16it/s]
 85%|████████▌ | 213/250 [03:32<00:24,  1.49it/s]
 86%|████████▌ | 214/250 [03:33<00:26,  1.36it/s]
 86%|████████▌ | 215/250 [03:33<00:25,  1.39it/s]
 86%|████████▋ | 216/250 [03:34<00:23,  1.45it/s]
 87%|████████▋ | 217/250 [03:34<00:19,  1.68it/s]
 87%|████████▋ | 218/250 [03:35<00:19,  1.63it/s]
 88%|████████▊ | 219/250 [03:35<00:15,  1.98it/s]
 88%|████████▊ | 220/250 [03:36<00:15,  1.92it/s]
 88%|████████▊ | 221/250 [03:36<00:12,  2.40it/s]
 89%|████████▉ | 222/250 [03:37<00:16,  1.73it/s]
 90%|████████▉ | 224/250 [03:38<00:15,  1.67it/s]
 90%|█████████ | 226/250 [03:38<00:10,  2.21it/s]
 91%|█████████ | 227/250 [03:40<00:13,  1.70it/s]
 91%|█████████ | 228/250 [03:42<00:21,  1.01it/s]
 92%|█████████▏| 229/250 [03:43<00:20,  1.02it/s]
 92%|█████████▏| 230/250 [03:43<00:16,  1.22it/s]
 92%|█████████▏| 231/250 [03:44<00:14,  1.29it/s]
 93%|█████████▎| 233/250 [03:46<00:15,  1.09it/s]
 94%|█████████▎| 234/250 [03:48<00:18,  1.15s/it]
 94%|█████████▍| 235/250 [03:49<00:15,  1.04s/it]
 94%|█████████▍| 236/250 [03:51<00:20,  1.49s/it]
 95%|█████████▍| 237/250 [03:55<00:27,  2.12s/it]
 95%|█████████▌| 238/250 [03:57<00:23,  1.96s/it]
 96%|█████████▌| 239/250 [04:01<00:30,  2.77s/it]
 96%|█████████▌| 240/250 [04:03<00:22,  2.27s/it]
 96%|█████████▋| 241/250 [04:03<00:16,  1.83s/it]
 97%|█████████▋| 243/250 [04:04<00:07,  1.12s/it]
 98%|█████████▊| 244/250 [04:04<00:05,  1.11it/s]
 98%|█████████▊| 245/250 [04:04<00:03,  1.34it/s]
 98%|█████████▊| 246/250 [04:06<00:04,  1.02s/it]
 99%|█████████▉| 248/250 [04:07<00:01,  1.24it/s]
100%|██████████| 250/250 [04:09<00:00,  1.17it/s]
100%|██████████| 250/250 [04:09<00:00,  1.00it/s]
2025-05-16 14:04:20,752 - modnet - INFO - Loss per individual: ind 0: 49.406 	ind 1: 47.035 	ind 2: 38.787 	ind 3: 42.636 	ind 4: 38.959 	ind 5: 37.278 	ind 6: 38.091 	ind 7: 38.517 	ind 8: 47.329 	ind 9: 38.212 	ind 10: 37.994 	ind 11: 46.830 	ind 12: 38.049 	ind 13: 42.905 	ind 14: 40.765 	ind 15: 42.789 	ind 16: 39.178 	ind 17: 44.626 	ind 18: 41.658 	ind 19: 41.413 	ind 20: 40.185 	ind 21: 41.476 	ind 22: 39.744 	ind 23: 37.853 	ind 24: 38.472 	ind 25: 40.172 	ind 26: 39.499 	ind 27: 39.017 	ind 28: 39.189 	ind 29: 65.438 	ind 30: 38.279 	ind 31: 39.722 	ind 32: 36.906 	ind 33: 39.771 	ind 34: 38.367 	ind 35: 41.380 	ind 36: 40.675 	ind 37: 38.343 	ind 38: 40.419 	ind 39: 40.487 	ind 40: 39.773 	ind 41: 40.276 	ind 42: 38.565 	ind 43: 40.765 	ind 44: 41.223 	ind 45: 43.150 	ind 46: 41.625 	ind 47: 48.516 	ind 48: 41.006 	ind 49: 41.165 	
2025-05-16 14:04:20,754 - modnet - INFO - After generation 12:
2025-05-16 14:04:20,754 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 224, 'fraction1': 0.25, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 0.25, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 490, 'n_layers': 5, 'layer_mults': None}
2025-05-16 14:04:20,754 - modnet - INFO - Best validation loss: 36.1079
2025-05-16 14:04:20,754 - modnet - INFO - Generation number 13

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:18<1:15:43, 18.25s/it]
  1%|          | 2/250 [00:19<33:22,  8.07s/it]  
  1%|          | 3/250 [00:19<18:16,  4.44s/it]
  2%|▏         | 4/250 [00:22<16:34,  4.04s/it]
  2%|▏         | 6/250 [00:24<09:50,  2.42s/it]
  3%|▎         | 7/250 [00:27<10:10,  2.51s/it]
  3%|▎         | 8/250 [00:29<09:05,  2.26s/it]
  4%|▎         | 9/250 [00:30<08:32,  2.12s/it]
  4%|▍         | 10/250 [00:32<07:31,  1.88s/it]
  4%|▍         | 11/250 [00:32<05:56,  1.49s/it]
  5%|▍         | 12/250 [00:32<04:21,  1.10s/it]
  5%|▌         | 13/250 [00:33<04:18,  1.09s/it]
  6%|▌         | 14/250 [00:35<05:01,  1.28s/it]
  6%|▌         | 15/250 [00:37<05:09,  1.32s/it]
  6%|▋         | 16/250 [00:38<04:57,  1.27s/it]
  7%|▋         | 17/250 [00:39<04:59,  1.29s/it]
  7%|▋         | 18/250 [00:39<03:50,  1.00it/s]
  8%|▊         | 19/250 [00:40<03:03,  1.26it/s]
  8%|▊         | 20/250 [00:40<02:19,  1.65it/s]
  8%|▊         | 21/250 [00:41<02:29,  1.53it/s]
  9%|▉         | 22/250 [00:41<02:17,  1.66it/s]
  9%|▉         | 23/250 [00:41<02:01,  1.86it/s]
 10%|▉         | 24/250 [00:42<01:34,  2.39it/s]
 10%|█         | 25/250 [00:42<01:13,  3.05it/s]
 10%|█         | 26/250 [00:42<01:35,  2.36it/s]
 11%|█         | 27/250 [00:45<03:34,  1.04it/s]
 12%|█▏        | 29/250 [00:45<02:02,  1.81it/s]
 12%|█▏        | 31/250 [00:45<01:17,  2.83it/s]
 13%|█▎        | 33/250 [00:46<01:51,  1.94it/s]
 14%|█▎        | 34/250 [00:47<01:51,  1.93it/s]
 14%|█▍        | 35/250 [00:48<02:18,  1.55it/s]
 14%|█▍        | 36/250 [00:50<03:24,  1.05it/s]
 15%|█▍        | 37/250 [00:50<02:42,  1.31it/s]
 15%|█▌        | 38/250 [00:51<02:22,  1.49it/s]
 16%|█▌        | 39/250 [00:51<01:57,  1.79it/s]
 16%|█▌        | 40/250 [00:51<01:35,  2.20it/s]
 17%|█▋        | 42/250 [00:51<01:04,  3.25it/s]
 17%|█▋        | 43/250 [00:52<01:06,  3.13it/s]
 18%|█▊        | 44/250 [00:58<06:04,  1.77s/it]
 18%|█▊        | 45/250 [00:58<04:56,  1.45s/it]
 18%|█▊        | 46/250 [00:59<04:17,  1.26s/it]
 19%|█▉        | 47/250 [01:00<03:49,  1.13s/it]
 19%|█▉        | 48/250 [01:00<03:07,  1.08it/s]
 20%|█▉        | 49/250 [01:01<03:25,  1.02s/it]
 20%|██        | 50/250 [01:02<02:58,  1.12it/s]
 20%|██        | 51/250 [01:03<03:06,  1.06it/s]
 21%|██        | 52/250 [01:04<02:50,  1.16it/s]
 21%|██        | 53/250 [01:05<03:03,  1.08it/s]
 22%|██▏       | 55/250 [01:05<01:59,  1.63it/s]
 22%|██▏       | 56/250 [01:06<01:36,  2.01it/s]
 24%|██▎       | 59/250 [01:06<00:55,  3.44it/s]
 24%|██▍       | 60/250 [01:08<01:54,  1.65it/s]
 24%|██▍       | 61/250 [01:08<01:46,  1.77it/s]
 25%|██▍       | 62/250 [01:08<01:28,  2.14it/s]
 25%|██▌       | 63/250 [01:09<01:51,  1.67it/s]
 26%|██▌       | 64/250 [01:12<03:21,  1.08s/it]
 26%|██▌       | 65/250 [01:13<03:33,  1.16s/it]
 27%|██▋       | 67/250 [01:13<02:04,  1.48it/s]
 27%|██▋       | 68/250 [01:14<02:23,  1.27it/s]
 28%|██▊       | 69/250 [01:15<02:23,  1.26it/s]
 28%|██▊       | 70/250 [01:18<03:46,  1.26s/it]
 29%|██▉       | 72/250 [01:18<02:30,  1.18it/s]
 29%|██▉       | 73/250 [01:19<02:13,  1.33it/s]
 30%|██▉       | 74/250 [01:19<01:45,  1.66it/s]
 30%|███       | 75/250 [01:19<01:34,  1.86it/s]
 31%|███       | 77/250 [01:20<01:25,  2.02it/s]
 31%|███       | 78/250 [01:22<02:07,  1.34it/s]
 32%|███▏      | 79/250 [01:22<01:44,  1.64it/s]
 32%|███▏      | 80/250 [01:22<01:24,  2.02it/s]
 32%|███▏      | 81/250 [01:23<01:46,  1.59it/s]
 33%|███▎      | 82/250 [01:23<01:36,  1.74it/s]
 34%|███▎      | 84/250 [01:25<01:32,  1.80it/s]
 34%|███▍      | 85/250 [01:25<01:46,  1.54it/s]
 34%|███▍      | 86/250 [01:27<02:26,  1.12it/s]
 35%|███▍      | 87/250 [01:27<01:58,  1.37it/s]
 35%|███▌      | 88/250 [01:27<01:31,  1.76it/s]
 36%|███▌      | 89/250 [01:28<01:28,  1.81it/s]
 36%|███▌      | 90/250 [01:28<01:08,  2.33it/s]
 36%|███▋      | 91/250 [01:28<00:54,  2.93it/s]
 37%|███▋      | 92/250 [01:30<01:46,  1.48it/s]
 37%|███▋      | 93/250 [01:30<01:26,  1.81it/s]
 38%|███▊      | 94/250 [01:31<01:32,  1.69it/s]
 38%|███▊      | 95/250 [01:32<01:46,  1.45it/s]
 38%|███▊      | 96/250 [01:33<02:22,  1.08it/s]
 39%|███▉      | 98/250 [01:33<01:23,  1.82it/s]
 40%|███▉      | 99/250 [01:34<01:18,  1.93it/s]
 40%|████      | 100/250 [01:34<01:16,  1.96it/s]
 41%|████      | 102/250 [01:35<00:58,  2.53it/s]
 41%|████      | 103/250 [01:36<01:26,  1.70it/s]
 42%|████▏     | 104/250 [01:36<01:21,  1.79it/s]
 42%|████▏     | 106/250 [01:37<01:15,  1.92it/s]
 43%|████▎     | 107/250 [01:38<01:33,  1.52it/s]
 43%|████▎     | 108/250 [01:39<01:31,  1.55it/s]
 44%|████▍     | 110/250 [01:41<01:44,  1.34it/s]
 44%|████▍     | 111/250 [01:42<02:00,  1.15it/s]
 45%|████▍     | 112/250 [01:42<01:42,  1.34it/s]
 45%|████▌     | 113/250 [01:46<03:12,  1.40s/it]
 46%|████▌     | 114/250 [01:47<02:58,  1.31s/it]
 46%|████▌     | 115/250 [01:48<02:57,  1.32s/it]
 47%|████▋     | 117/250 [01:48<01:47,  1.24it/s]
 47%|████▋     | 118/250 [01:49<01:26,  1.53it/s]
 48%|████▊     | 119/250 [01:50<01:45,  1.24it/s]
 48%|████▊     | 120/250 [01:50<01:20,  1.61it/s]
 48%|████▊     | 121/250 [01:52<02:03,  1.05it/s]
 49%|████▉     | 122/250 [01:52<01:44,  1.22it/s]
 49%|████▉     | 123/250 [01:53<01:35,  1.32it/s]
 50%|████▉     | 124/250 [01:53<01:27,  1.44it/s]
 50%|█████     | 125/250 [01:54<01:21,  1.53it/s]
 50%|█████     | 126/250 [01:55<01:47,  1.16it/s]
 51%|█████     | 127/250 [01:56<01:23,  1.47it/s]
 51%|█████     | 128/250 [01:56<01:07,  1.80it/s]
 52%|█████▏    | 129/250 [01:56<00:51,  2.35it/s]
 52%|█████▏    | 130/250 [01:56<00:42,  2.81it/s]
 52%|█████▏    | 131/250 [01:57<00:41,  2.89it/s]
 53%|█████▎    | 133/250 [01:57<00:31,  3.66it/s]
 54%|█████▎    | 134/250 [01:58<00:53,  2.15it/s]
 54%|█████▍    | 135/250 [01:58<00:52,  2.17it/s]
 54%|█████▍    | 136/250 [01:59<00:51,  2.20it/s]
 55%|█████▍    | 137/250 [02:06<04:15,  2.26s/it]
 55%|█████▌    | 138/250 [02:06<03:15,  1.74s/it]
 56%|█████▌    | 139/250 [02:06<02:23,  1.29s/it]
 56%|█████▌    | 140/250 [02:07<01:49,  1.01it/s]
 56%|█████▋    | 141/250 [02:07<01:21,  1.33it/s]
 57%|█████▋    | 142/250 [02:07<01:09,  1.55it/s]
 57%|█████▋    | 143/250 [02:08<01:06,  1.60it/s]
 58%|█████▊    | 144/250 [02:09<01:12,  1.45it/s]
 58%|█████▊    | 145/250 [02:09<01:10,  1.49it/s]
 58%|█████▊    | 146/250 [02:11<01:46,  1.03s/it]
 59%|█████▉    | 147/250 [02:13<02:25,  1.41s/it]
 59%|█████▉    | 148/250 [02:15<02:15,  1.33s/it]
 60%|█████▉    | 149/250 [02:15<02:00,  1.19s/it]
 60%|██████    | 150/250 [02:16<01:35,  1.05it/s]
 60%|██████    | 151/250 [02:17<01:26,  1.14it/s]
 61%|██████    | 152/250 [02:17<01:06,  1.47it/s]
 61%|██████    | 153/250 [02:17<00:53,  1.80it/s]
 62%|██████▏   | 154/250 [02:19<01:31,  1.05it/s]
 62%|██████▏   | 155/250 [02:21<02:06,  1.33s/it]
 62%|██████▏   | 156/250 [02:22<01:42,  1.09s/it]
 63%|██████▎   | 158/250 [02:23<01:25,  1.08it/s]
 64%|██████▎   | 159/250 [02:23<01:11,  1.27it/s]
 65%|██████▍   | 162/250 [02:25<00:50,  1.74it/s]
 65%|██████▌   | 163/250 [02:25<00:42,  2.03it/s]
 66%|██████▌   | 164/250 [02:27<01:19,  1.08it/s]
 66%|██████▌   | 165/250 [02:29<01:27,  1.03s/it]
 66%|██████▋   | 166/250 [02:31<02:06,  1.51s/it]
 67%|██████▋   | 167/250 [02:32<01:34,  1.14s/it]
 67%|██████▋   | 168/250 [02:32<01:20,  1.02it/s]
 68%|██████▊   | 169/250 [02:33<01:04,  1.25it/s]
 68%|██████▊   | 170/250 [02:34<01:13,  1.08it/s]
 68%|██████▊   | 171/250 [02:37<02:14,  1.70s/it]
 69%|██████▉   | 172/250 [02:39<02:06,  1.62s/it]
 69%|██████▉   | 173/250 [02:39<01:36,  1.25s/it]
 70%|██████▉   | 174/250 [02:39<01:14,  1.01it/s]
 70%|███████   | 175/250 [02:41<01:25,  1.15s/it]
 70%|███████   | 176/250 [02:42<01:12,  1.02it/s]
 71%|███████   | 177/250 [02:42<01:01,  1.18it/s]
 71%|███████   | 178/250 [02:43<00:52,  1.38it/s]
 72%|███████▏  | 179/250 [02:43<00:39,  1.79it/s]
 72%|███████▏  | 181/250 [02:45<00:53,  1.28it/s]
 73%|███████▎  | 182/250 [02:45<00:49,  1.38it/s]
 73%|███████▎  | 183/250 [02:46<00:48,  1.37it/s]
 74%|███████▍  | 185/250 [02:50<01:16,  1.18s/it]
 74%|███████▍  | 186/250 [02:52<01:29,  1.39s/it]
 75%|███████▍  | 187/250 [02:53<01:26,  1.37s/it]
 76%|███████▌  | 189/250 [02:54<01:03,  1.04s/it]
 76%|███████▌  | 190/250 [02:55<00:53,  1.12it/s]
 76%|███████▋  | 191/250 [02:55<00:42,  1.40it/s]
 77%|███████▋  | 192/250 [02:59<01:26,  1.50s/it]
 77%|███████▋  | 193/250 [03:02<01:48,  1.91s/it]
 78%|███████▊  | 194/250 [03:02<01:31,  1.63s/it]
 78%|███████▊  | 195/250 [03:03<01:08,  1.24s/it]
 78%|███████▊  | 196/250 [03:03<00:58,  1.08s/it]
 79%|███████▉  | 197/250 [03:04<00:44,  1.20it/s]
 79%|███████▉  | 198/250 [03:04<00:35,  1.47it/s]
 80%|███████▉  | 199/250 [03:04<00:25,  1.97it/s]
 80%|████████  | 200/250 [03:05<00:29,  1.72it/s]
 80%|████████  | 201/250 [03:05<00:25,  1.89it/s]
 81%|████████  | 203/250 [03:05<00:15,  3.04it/s]
 82%|████████▏ | 204/250 [03:06<00:21,  2.11it/s]
 82%|████████▏ | 205/250 [03:06<00:16,  2.65it/s]
 82%|████████▏ | 206/250 [03:07<00:18,  2.36it/s]
 83%|████████▎ | 207/250 [03:08<00:23,  1.87it/s]
 84%|████████▎ | 209/250 [03:09<00:19,  2.11it/s]
 84%|████████▍ | 210/250 [03:11<00:33,  1.21it/s]
 84%|████████▍ | 211/250 [03:12<00:34,  1.12it/s]
 85%|████████▍ | 212/250 [03:12<00:30,  1.26it/s]
 85%|████████▌ | 213/250 [03:12<00:24,  1.51it/s]
 86%|████████▌ | 214/250 [03:13<00:19,  1.84it/s]
 86%|████████▌ | 215/250 [03:14<00:21,  1.60it/s]
 86%|████████▋ | 216/250 [03:14<00:23,  1.43it/s]
 87%|████████▋ | 217/250 [03:15<00:18,  1.79it/s]
 87%|████████▋ | 218/250 [03:15<00:19,  1.66it/s]
 88%|████████▊ | 219/250 [03:16<00:19,  1.57it/s]
 88%|████████▊ | 220/250 [03:17<00:18,  1.60it/s]
 88%|████████▊ | 221/250 [03:17<00:13,  2.10it/s]
 89%|████████▉ | 222/250 [03:17<00:13,  2.11it/s]
 89%|████████▉ | 223/250 [03:18<00:11,  2.43it/s]
 90%|████████▉ | 224/250 [03:18<00:08,  3.01it/s]
 90%|█████████ | 226/250 [03:18<00:08,  2.77it/s]
 91%|█████████ | 228/250 [03:19<00:05,  3.92it/s]
 92%|█████████▏| 229/250 [03:21<00:13,  1.58it/s]
 92%|█████████▏| 230/250 [03:23<00:20,  1.04s/it]
 92%|█████████▏| 231/250 [03:24<00:18,  1.03it/s]
 93%|█████████▎| 232/250 [03:24<00:15,  1.16it/s]
 93%|█████████▎| 233/250 [03:25<00:13,  1.22it/s]
 94%|█████████▎| 234/250 [03:25<00:11,  1.39it/s]
 94%|█████████▍| 235/250 [03:27<00:12,  1.20it/s]
 94%|█████████▍| 236/250 [03:27<00:09,  1.48it/s]
 95%|█████████▍| 237/250 [03:27<00:07,  1.78it/s]
 95%|█████████▌| 238/250 [03:27<00:05,  2.09it/s]
 96%|█████████▌| 239/250 [03:29<00:07,  1.43it/s]
 96%|█████████▋| 241/250 [03:29<00:03,  2.25it/s]
 97%|█████████▋| 243/250 [03:29<00:02,  2.66it/s]
 98%|█████████▊| 244/250 [03:33<00:06,  1.02s/it]
 98%|█████████▊| 245/250 [03:34<00:04,  1.04it/s]
 99%|█████████▉| 247/250 [03:34<00:02,  1.38it/s]
 99%|█████████▉| 248/250 [03:35<00:01,  1.55it/s]
100%|█████████▉| 249/250 [03:35<00:00,  1.56it/s]
100%|██████████| 250/250 [03:36<00:00,  1.35it/s]
100%|██████████| 250/250 [03:36<00:00,  1.15it/s]
2025-05-16 14:07:57,647 - modnet - INFO - Loss per individual: ind 0: 40.734 	ind 1: 38.963 	ind 2: 39.188 	ind 3: 39.618 	ind 4: 42.408 	ind 5: 37.712 	ind 6: 36.401 	ind 7: 40.881 	ind 8: 39.352 	ind 9: 41.930 	ind 10: 60.022 	ind 11: 45.414 	ind 12: 41.380 	ind 13: 37.967 	ind 14: 38.408 	ind 15: 39.355 	ind 16: 42.532 	ind 17: 38.862 	ind 18: 42.613 	ind 19: 38.410 	ind 20: 43.676 	ind 21: 40.459 	ind 22: 38.688 	ind 23: 38.601 	ind 24: 37.742 	ind 25: 38.626 	ind 26: 40.218 	ind 27: 37.449 	ind 28: 40.952 	ind 29: 42.115 	ind 30: 36.792 	ind 31: 38.316 	ind 32: 38.068 	ind 33: 56.716 	ind 34: 37.695 	ind 35: 44.215 	ind 36: 38.525 	ind 37: 38.431 	ind 38: 41.914 	ind 39: 38.991 	ind 40: 47.464 	ind 41: 39.655 	ind 42: 45.595 	ind 43: 40.192 	ind 44: 39.020 	ind 45: 38.471 	ind 46: 50.782 	ind 47: 48.494 	ind 48: 41.995 	ind 49: 40.763 	
2025-05-16 14:07:57,648 - modnet - INFO - After generation 13:
2025-05-16 14:07:57,648 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 224, 'fraction1': 0.25, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 0.25, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 490, 'n_layers': 5, 'layer_mults': None}
2025-05-16 14:07:57,649 - modnet - INFO - Best validation loss: 36.1079
2025-05-16 14:07:57,649 - modnet - INFO - Generation number 14

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:13<54:04, 13.03s/it]
  1%|          | 2/250 [00:14<25:07,  6.08s/it]
  1%|          | 3/250 [00:15<15:37,  3.80s/it]
  2%|▏         | 4/250 [00:17<12:21,  3.02s/it]
  2%|▏         | 5/250 [00:19<10:38,  2.61s/it]
  2%|▏         | 6/250 [00:19<08:04,  1.98s/it]
  3%|▎         | 8/250 [00:20<04:17,  1.06s/it]
  4%|▎         | 9/250 [00:20<03:23,  1.18it/s]
  4%|▍         | 10/250 [00:21<03:29,  1.15it/s]
  4%|▍         | 11/250 [00:22<04:07,  1.03s/it]
  5%|▍         | 12/250 [00:24<04:47,  1.21s/it]
  5%|▌         | 13/250 [00:25<05:15,  1.33s/it]
  6%|▌         | 14/250 [00:27<05:17,  1.35s/it]
  6%|▌         | 15/250 [00:28<04:45,  1.21s/it]
  6%|▋         | 16/250 [00:28<03:52,  1.01it/s]
  7%|▋         | 17/250 [00:29<03:52,  1.00it/s]
  7%|▋         | 18/250 [00:31<04:37,  1.20s/it]
  8%|▊         | 19/250 [00:31<03:48,  1.01it/s]
  8%|▊         | 21/250 [00:32<02:19,  1.64it/s]
  9%|▉         | 22/250 [00:32<02:13,  1.71it/s]
 10%|▉         | 24/250 [00:32<01:22,  2.73it/s]
 10%|█         | 25/250 [00:33<01:35,  2.36it/s]
 10%|█         | 26/250 [00:34<02:19,  1.61it/s]
 11%|█         | 27/250 [00:37<04:25,  1.19s/it]
 11%|█         | 28/250 [00:37<03:20,  1.11it/s]
 12%|█▏        | 29/250 [00:39<04:29,  1.22s/it]
 12%|█▏        | 31/250 [00:39<02:35,  1.41it/s]
 13%|█▎        | 32/250 [00:42<04:08,  1.14s/it]
 13%|█▎        | 33/250 [00:42<03:10,  1.14it/s]
 14%|█▎        | 34/250 [00:44<03:57,  1.10s/it]
 14%|█▍        | 35/250 [00:44<03:05,  1.16it/s]
 14%|█▍        | 36/250 [00:44<02:28,  1.44it/s]
 15%|█▍        | 37/250 [00:45<02:38,  1.35it/s]
 16%|█▌        | 39/250 [00:45<01:33,  2.27it/s]
 16%|█▌        | 40/250 [00:45<01:28,  2.38it/s]
 16%|█▋        | 41/250 [00:47<02:45,  1.27it/s]
 17%|█▋        | 42/250 [00:48<02:24,  1.44it/s]
 18%|█▊        | 44/250 [00:49<02:23,  1.43it/s]
 18%|█▊        | 45/250 [00:50<02:23,  1.43it/s]
 18%|█▊        | 46/250 [00:50<02:13,  1.53it/s]
 19%|█▉        | 47/250 [00:51<01:52,  1.80it/s]
 19%|█▉        | 48/250 [00:52<02:34,  1.30it/s]
 20%|█▉        | 49/250 [00:52<02:04,  1.62it/s]
 20%|██        | 51/250 [00:53<01:34,  2.11it/s]
 21%|██        | 52/250 [00:54<01:50,  1.79it/s]
 21%|██        | 53/250 [00:55<02:06,  1.56it/s]
 22%|██▏       | 54/250 [00:55<02:16,  1.43it/s]
 22%|██▏       | 55/250 [00:57<03:18,  1.02s/it]
 22%|██▏       | 56/250 [00:57<02:26,  1.32it/s]
 23%|██▎       | 57/250 [01:00<03:50,  1.20s/it]
 24%|██▎       | 59/250 [01:00<02:16,  1.40it/s]
 24%|██▍       | 60/250 [01:01<02:13,  1.42it/s]
 24%|██▍       | 61/250 [01:03<03:58,  1.26s/it]
 25%|██▍       | 62/250 [01:05<04:08,  1.32s/it]
 25%|██▌       | 63/250 [01:06<04:22,  1.40s/it]
 26%|██▌       | 64/250 [01:09<05:41,  1.84s/it]
 26%|██▌       | 65/250 [01:10<04:37,  1.50s/it]
 26%|██▋       | 66/250 [01:10<03:26,  1.12s/it]
 27%|██▋       | 68/250 [01:11<02:26,  1.24it/s]
 28%|██▊       | 69/250 [01:12<02:28,  1.22it/s]
 29%|██▉       | 72/250 [01:13<01:45,  1.69it/s]
 30%|██▉       | 74/250 [01:14<01:49,  1.61it/s]
 30%|███       | 75/250 [01:17<02:39,  1.10it/s]
 30%|███       | 76/250 [01:19<03:18,  1.14s/it]
 31%|███       | 77/250 [01:19<02:58,  1.03s/it]
 31%|███       | 78/250 [01:21<03:07,  1.09s/it]
 32%|███▏      | 80/250 [01:21<02:02,  1.39it/s]
 33%|███▎      | 82/250 [01:21<01:20,  2.09it/s]
 34%|███▎      | 84/250 [01:22<01:03,  2.61it/s]
 34%|███▍      | 86/250 [01:25<02:03,  1.33it/s]
 35%|███▍      | 87/250 [01:25<01:56,  1.39it/s]
 36%|███▌      | 89/250 [01:26<01:39,  1.62it/s]
 36%|███▋      | 91/250 [01:27<01:27,  1.82it/s]
 37%|███▋      | 93/250 [01:28<01:31,  1.72it/s]
 38%|███▊      | 95/250 [01:28<01:05,  2.35it/s]
 39%|███▉      | 97/250 [01:29<00:56,  2.73it/s]
 39%|███▉      | 98/250 [01:29<00:48,  3.10it/s]
 40%|███▉      | 99/250 [01:30<01:07,  2.23it/s]
 40%|████      | 100/250 [01:30<00:58,  2.54it/s]
 40%|████      | 101/250 [01:31<01:12,  2.05it/s]
 41%|████      | 102/250 [01:31<01:08,  2.15it/s]
 41%|████      | 103/250 [01:32<01:29,  1.65it/s]
 42%|████▏     | 104/250 [01:32<01:16,  1.90it/s]
 42%|████▏     | 105/250 [01:35<02:47,  1.15s/it]
 42%|████▏     | 106/250 [01:36<02:46,  1.16s/it]
 43%|████▎     | 108/250 [01:37<01:43,  1.37it/s]
 44%|████▎     | 109/250 [01:37<01:28,  1.59it/s]
 44%|████▍     | 110/250 [01:38<01:34,  1.48it/s]
 44%|████▍     | 111/250 [01:39<01:34,  1.47it/s]
 45%|████▍     | 112/250 [01:40<01:58,  1.16it/s]
 45%|████▌     | 113/250 [01:40<01:43,  1.32it/s]
 46%|████▌     | 114/250 [01:41<01:35,  1.43it/s]
 46%|████▌     | 115/250 [01:42<01:40,  1.34it/s]
 46%|████▋     | 116/250 [01:44<02:24,  1.08s/it]
 47%|████▋     | 117/250 [01:44<01:53,  1.17it/s]
 47%|████▋     | 118/250 [01:45<01:51,  1.19it/s]
 48%|████▊     | 119/250 [01:48<03:06,  1.43s/it]
 48%|████▊     | 120/250 [01:48<02:35,  1.20s/it]
 48%|████▊     | 121/250 [01:49<02:09,  1.00s/it]
 49%|████▉     | 122/250 [01:49<01:44,  1.23it/s]
 49%|████▉     | 123/250 [01:51<02:12,  1.04s/it]
 50%|████▉     | 124/250 [01:53<02:53,  1.38s/it]
 50%|█████     | 125/250 [01:54<02:43,  1.31s/it]
 50%|█████     | 126/250 [01:54<02:02,  1.01it/s]
 51%|█████     | 127/250 [01:56<02:24,  1.17s/it]
 51%|█████     | 128/250 [01:56<01:44,  1.17it/s]
 52%|█████▏    | 129/250 [01:57<01:41,  1.20it/s]
 52%|█████▏    | 130/250 [01:58<01:59,  1.00it/s]
 52%|█████▏    | 131/250 [01:59<01:56,  1.02it/s]
 53%|█████▎    | 132/250 [02:00<01:53,  1.04it/s]
 53%|█████▎    | 133/250 [02:00<01:26,  1.36it/s]
 54%|█████▎    | 134/250 [02:02<02:03,  1.06s/it]
 54%|█████▍    | 135/250 [02:03<01:51,  1.03it/s]
 54%|█████▍    | 136/250 [02:03<01:22,  1.38it/s]
 55%|█████▍    | 137/250 [02:03<01:05,  1.73it/s]
 55%|█████▌    | 138/250 [02:04<00:57,  1.93it/s]
 56%|█████▌    | 139/250 [02:05<01:12,  1.54it/s]
 56%|█████▌    | 140/250 [02:05<01:03,  1.74it/s]
 56%|█████▋    | 141/250 [02:06<01:12,  1.51it/s]
 57%|█████▋    | 142/250 [02:06<01:04,  1.67it/s]
 57%|█████▋    | 143/250 [02:08<01:28,  1.21it/s]
 58%|█████▊    | 144/250 [02:09<01:49,  1.04s/it]
 58%|█████▊    | 145/250 [02:10<01:49,  1.04s/it]
 58%|█████▊    | 146/250 [02:11<01:46,  1.03s/it]
 59%|█████▉    | 148/250 [02:12<01:14,  1.37it/s]
 60%|██████    | 150/250 [02:12<00:47,  2.12it/s]
 61%|██████    | 152/250 [02:13<00:35,  2.74it/s]
 61%|██████    | 153/250 [02:13<00:31,  3.09it/s]
 62%|██████▏   | 154/250 [02:13<00:27,  3.54it/s]
 62%|██████▏   | 156/250 [02:13<00:19,  4.82it/s]
 63%|██████▎   | 157/250 [02:13<00:22,  4.10it/s]
 63%|██████▎   | 158/250 [02:14<00:33,  2.73it/s]
 64%|██████▎   | 159/250 [02:15<00:40,  2.24it/s]
 64%|██████▍   | 161/250 [02:15<00:26,  3.42it/s]
 65%|██████▍   | 162/250 [02:15<00:23,  3.80it/s]
 65%|██████▌   | 163/250 [02:19<01:30,  1.04s/it]
 66%|██████▌   | 164/250 [02:19<01:13,  1.17it/s]
 66%|██████▌   | 165/250 [02:22<01:59,  1.41s/it]
 66%|██████▋   | 166/250 [02:22<01:28,  1.06s/it]
 67%|██████▋   | 167/250 [02:23<01:30,  1.10s/it]
 67%|██████▋   | 168/250 [02:24<01:26,  1.05s/it]
 68%|██████▊   | 169/250 [02:24<01:08,  1.18it/s]
 68%|██████▊   | 171/250 [02:25<00:40,  1.94it/s]
 69%|██████▉   | 172/250 [02:26<00:53,  1.46it/s]
 69%|██████▉   | 173/250 [02:29<01:49,  1.43s/it]
 70%|██████▉   | 174/250 [02:30<01:27,  1.15s/it]
 70%|███████   | 175/250 [02:30<01:10,  1.07it/s]
 71%|███████   | 177/250 [02:30<00:41,  1.76it/s]
 71%|███████   | 178/250 [02:31<00:33,  2.16it/s]
 72%|███████▏  | 179/250 [02:32<00:42,  1.67it/s]
 72%|███████▏  | 180/250 [02:32<00:43,  1.62it/s]
 72%|███████▏  | 181/250 [02:32<00:33,  2.06it/s]
 73%|███████▎  | 182/250 [02:33<00:36,  1.89it/s]
 73%|███████▎  | 183/250 [02:36<01:27,  1.31s/it]
 74%|███████▎  | 184/250 [02:37<01:06,  1.00s/it]
 74%|███████▍  | 186/250 [02:37<00:36,  1.74it/s]
 75%|███████▍  | 187/250 [02:38<00:46,  1.36it/s]
 75%|███████▌  | 188/250 [02:38<00:42,  1.45it/s]
 76%|███████▌  | 189/250 [02:39<00:35,  1.73it/s]
 76%|███████▌  | 190/250 [02:40<00:41,  1.44it/s]
 76%|███████▋  | 191/250 [02:40<00:40,  1.45it/s]
 77%|███████▋  | 192/250 [02:43<01:06,  1.14s/it]
 77%|███████▋  | 193/250 [02:44<01:12,  1.26s/it]
 78%|███████▊  | 194/250 [02:45<01:07,  1.21s/it]
 78%|███████▊  | 195/250 [02:46<00:52,  1.05it/s]
 78%|███████▊  | 196/250 [02:49<01:23,  1.55s/it]
 79%|███████▉  | 198/250 [02:49<00:46,  1.12it/s]
 80%|███████▉  | 199/250 [02:49<00:40,  1.25it/s]
 80%|████████  | 201/250 [02:50<00:26,  1.84it/s]
 81%|████████  | 202/250 [02:51<00:32,  1.48it/s]
 81%|████████  | 203/250 [02:51<00:31,  1.49it/s]
 82%|████████▏ | 204/250 [02:52<00:34,  1.34it/s]
 82%|████████▏ | 205/250 [02:53<00:28,  1.57it/s]
 82%|████████▏ | 206/250 [02:53<00:25,  1.73it/s]
 83%|████████▎ | 207/250 [02:54<00:29,  1.46it/s]
 83%|████████▎ | 208/250 [02:55<00:29,  1.41it/s]
 84%|████████▎ | 209/250 [02:55<00:27,  1.52it/s]
 84%|████████▍ | 210/250 [02:57<00:37,  1.06it/s]
 84%|████████▍ | 211/250 [02:57<00:29,  1.33it/s]
 85%|████████▍ | 212/250 [03:00<00:46,  1.21s/it]
 85%|████████▌ | 213/250 [03:00<00:33,  1.11it/s]
 86%|████████▌ | 214/250 [03:02<00:47,  1.31s/it]
 86%|████████▌ | 215/250 [03:03<00:38,  1.10s/it]
 86%|████████▋ | 216/250 [03:04<00:43,  1.27s/it]
 87%|████████▋ | 217/250 [03:05<00:30,  1.08it/s]
 87%|████████▋ | 218/250 [03:05<00:22,  1.43it/s]
 88%|████████▊ | 219/250 [03:07<00:36,  1.18s/it]
 88%|████████▊ | 220/250 [03:09<00:45,  1.51s/it]
 88%|████████▊ | 221/250 [03:10<00:36,  1.26s/it]
 89%|████████▉ | 222/250 [03:10<00:26,  1.06it/s]
 90%|████████▉ | 224/250 [03:11<00:17,  1.45it/s]
 90%|█████████ | 225/250 [03:11<00:13,  1.80it/s]
 90%|█████████ | 226/250 [03:11<00:12,  1.99it/s]
 91%|█████████ | 227/250 [03:12<00:14,  1.63it/s]
 91%|█████████ | 228/250 [03:13<00:13,  1.67it/s]
 92%|█████████▏| 229/250 [03:13<00:11,  1.77it/s]
 92%|█████████▏| 230/250 [03:14<00:10,  1.88it/s]
 92%|█████████▏| 231/250 [03:14<00:08,  2.12it/s]
 93%|█████████▎| 233/250 [03:14<00:04,  3.53it/s]
 94%|█████████▍| 235/250 [03:15<00:03,  4.63it/s]
 94%|█████████▍| 236/250 [03:15<00:02,  4.80it/s]
 95%|█████████▍| 237/250 [03:15<00:03,  4.32it/s]
 96%|█████████▌| 239/250 [03:15<00:01,  5.82it/s]
 96%|█████████▌| 240/250 [03:16<00:02,  3.85it/s]
 96%|█████████▋| 241/250 [03:16<00:02,  3.75it/s]
 97%|█████████▋| 242/250 [03:16<00:02,  3.86it/s]
 97%|█████████▋| 243/250 [03:17<00:02,  2.63it/s]
 98%|█████████▊| 245/250 [03:17<00:01,  3.90it/s]
 98%|█████████▊| 246/250 [03:21<00:03,  1.00it/s]
 99%|█████████▉| 248/250 [03:21<00:01,  1.29it/s]
100%|█████████▉| 249/250 [03:25<00:01,  1.50s/it]
100%|██████████| 250/250 [03:26<00:00,  1.24s/it]
100%|██████████| 250/250 [03:26<00:00,  1.21it/s]
2025-05-16 14:11:24,086 - modnet - INFO - Loss per individual: ind 0: 43.051 	ind 1: 44.175 	ind 2: 39.832 	ind 3: 37.399 	ind 4: 39.357 	ind 5: 39.581 	ind 6: 40.755 	ind 7: 40.407 	ind 8: 44.092 	ind 9: 38.219 	ind 10: 41.304 	ind 11: 38.577 	ind 12: 47.825 	ind 13: 43.638 	ind 14: 56.637 	ind 15: 43.144 	ind 16: 40.483 	ind 17: 42.970 	ind 18: 38.059 	ind 19: 41.209 	ind 20: 48.889 	ind 21: 41.711 	ind 22: 37.000 	ind 23: 48.242 	ind 24: 141.089 	ind 25: 44.955 	ind 26: 47.016 	ind 27: 40.620 	ind 28: 42.909 	ind 29: 41.362 	ind 30: 37.097 	ind 31: 41.347 	ind 32: 41.287 	ind 33: 70.370 	ind 34: 44.797 	ind 35: 41.042 	ind 36: 39.004 	ind 37: 40.919 	ind 38: 38.903 	ind 39: 40.692 	ind 40: 39.051 	ind 41: 42.185 	ind 42: 41.508 	ind 43: 43.348 	ind 44: 38.519 	ind 45: 68.807 	ind 46: 45.736 	ind 47: 38.032 	ind 48: 41.158 	ind 49: 36.707 	
2025-05-16 14:11:24,088 - modnet - INFO - After generation 14:
2025-05-16 14:11:24,088 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 224, 'fraction1': 0.25, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 0.25, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 490, 'n_layers': 5, 'layer_mults': None}
2025-05-16 14:11:24,088 - modnet - INFO - Best validation loss: 36.1079
2025-05-16 14:11:24,088 - modnet - INFO - Generation number 15

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:09<39:07,  9.43s/it]
  1%|          | 2/250 [00:11<19:59,  4.84s/it]
  1%|          | 3/250 [00:11<12:10,  2.96s/it]
  2%|▏         | 4/250 [00:12<08:49,  2.15s/it]
  2%|▏         | 5/250 [00:13<07:07,  1.74s/it]
  2%|▏         | 6/250 [00:14<06:06,  1.50s/it]
  3%|▎         | 8/250 [00:15<03:27,  1.17it/s]
  4%|▎         | 9/250 [00:15<02:42,  1.48it/s]
  4%|▍         | 10/250 [00:16<02:59,  1.34it/s]
  4%|▍         | 11/250 [00:16<02:51,  1.40it/s]
  5%|▍         | 12/250 [00:17<02:23,  1.66it/s]
  5%|▌         | 13/250 [00:17<02:09,  1.83it/s]
  6%|▌         | 14/250 [00:18<02:31,  1.56it/s]
  6%|▌         | 15/250 [00:18<02:18,  1.69it/s]
  7%|▋         | 17/250 [00:19<01:27,  2.65it/s]
  7%|▋         | 18/250 [00:19<01:24,  2.76it/s]
  8%|▊         | 19/250 [00:20<02:04,  1.86it/s]
  8%|▊         | 20/250 [00:20<01:41,  2.27it/s]
  8%|▊         | 21/250 [00:21<01:46,  2.16it/s]
  9%|▉         | 22/250 [00:23<03:54,  1.03s/it]
  9%|▉         | 23/250 [00:24<03:44,  1.01it/s]
 10%|▉         | 24/250 [00:25<04:03,  1.08s/it]
 10%|█         | 26/250 [00:26<02:44,  1.36it/s]
 11%|█         | 27/250 [00:26<02:22,  1.57it/s]
 11%|█         | 28/250 [00:28<03:17,  1.12it/s]
 12%|█▏        | 29/250 [00:28<02:42,  1.36it/s]
 12%|█▏        | 31/250 [00:29<02:00,  1.82it/s]
 13%|█▎        | 32/250 [00:30<02:16,  1.59it/s]
 13%|█▎        | 33/250 [00:31<02:31,  1.43it/s]
 14%|█▎        | 34/250 [00:31<02:16,  1.58it/s]
 14%|█▍        | 35/250 [00:32<02:48,  1.28it/s]
 14%|█▍        | 36/250 [00:33<02:32,  1.41it/s]
 15%|█▍        | 37/250 [00:34<03:25,  1.04it/s]
 15%|█▌        | 38/250 [00:35<02:36,  1.35it/s]
 16%|█▌        | 39/250 [00:35<02:44,  1.29it/s]
 16%|█▌        | 40/250 [00:36<02:13,  1.57it/s]
 16%|█▋        | 41/250 [00:36<02:12,  1.57it/s]
 17%|█▋        | 43/250 [00:37<01:26,  2.40it/s]
 18%|█▊        | 44/250 [00:37<01:16,  2.68it/s]
 18%|█▊        | 45/250 [00:42<05:02,  1.48s/it]
 18%|█▊        | 46/250 [00:48<09:23,  2.76s/it]
 19%|█▉        | 47/250 [00:51<09:54,  2.93s/it]
 19%|█▉        | 48/250 [00:53<08:19,  2.48s/it]
 20%|█▉        | 49/250 [00:55<08:17,  2.47s/it]
 20%|██        | 50/250 [00:56<06:44,  2.02s/it]
 20%|██        | 51/250 [00:56<05:13,  1.58s/it]
 21%|██        | 52/250 [00:57<03:48,  1.15s/it]
 21%|██        | 53/250 [00:57<03:03,  1.07it/s]
 22%|██▏       | 54/250 [00:58<02:48,  1.16it/s]
 22%|██▏       | 55/250 [00:58<02:06,  1.55it/s]
 22%|██▏       | 56/250 [00:59<02:16,  1.42it/s]
 23%|██▎       | 57/250 [00:59<02:05,  1.54it/s]
 23%|██▎       | 58/250 [01:00<01:47,  1.78it/s]
 24%|██▎       | 59/250 [01:01<02:56,  1.08it/s]
 24%|██▍       | 60/250 [01:02<02:39,  1.19it/s]
 24%|██▍       | 61/250 [01:03<02:39,  1.18it/s]
 25%|██▍       | 62/250 [01:04<02:36,  1.21it/s]
 25%|██▌       | 63/250 [01:04<02:38,  1.18it/s]
 26%|██▌       | 64/250 [01:05<02:19,  1.33it/s]
 26%|██▌       | 65/250 [01:05<01:45,  1.75it/s]
 26%|██▋       | 66/250 [01:06<02:08,  1.43it/s]
 27%|██▋       | 67/250 [01:07<02:27,  1.24it/s]
 27%|██▋       | 68/250 [01:10<03:49,  1.26s/it]
 28%|██▊       | 69/250 [01:10<03:22,  1.12s/it]
 28%|██▊       | 70/250 [01:12<03:42,  1.24s/it]
 28%|██▊       | 71/250 [01:12<02:46,  1.07it/s]
 29%|██▉       | 72/250 [01:13<02:33,  1.16it/s]
 29%|██▉       | 73/250 [01:13<01:58,  1.49it/s]
 30%|███       | 75/250 [01:13<01:11,  2.46it/s]
 30%|███       | 76/250 [01:13<01:04,  2.71it/s]
 31%|███       | 77/250 [01:14<00:52,  3.27it/s]
 31%|███       | 78/250 [01:14<00:59,  2.87it/s]
 32%|███▏      | 79/250 [01:14<00:52,  3.28it/s]
 32%|███▏      | 80/250 [01:14<00:48,  3.51it/s]
 32%|███▏      | 81/250 [01:15<00:50,  3.36it/s]
 33%|███▎      | 82/250 [01:16<01:17,  2.16it/s]
 33%|███▎      | 83/250 [01:16<01:06,  2.51it/s]
 34%|███▎      | 84/250 [01:16<00:55,  2.99it/s]
 34%|███▍      | 85/250 [01:17<01:15,  2.19it/s]
 35%|███▍      | 87/250 [01:18<01:24,  1.93it/s]
 35%|███▌      | 88/250 [01:19<01:24,  1.92it/s]
 36%|███▌      | 89/250 [01:20<01:43,  1.55it/s]
 36%|███▌      | 90/250 [01:22<03:01,  1.13s/it]
 36%|███▋      | 91/250 [01:22<02:20,  1.13it/s]
 37%|███▋      | 92/250 [01:25<03:45,  1.43s/it]
 37%|███▋      | 93/250 [01:25<02:56,  1.12s/it]
 38%|███▊      | 94/250 [01:26<02:27,  1.06it/s]
 38%|███▊      | 95/250 [01:26<01:52,  1.38it/s]
 38%|███▊      | 96/250 [01:26<01:23,  1.83it/s]
 39%|███▉      | 97/250 [01:27<01:55,  1.32it/s]
 40%|███▉      | 99/250 [01:29<02:02,  1.24it/s]
 40%|████      | 100/250 [01:30<02:15,  1.11it/s]
 40%|████      | 101/250 [01:31<01:47,  1.39it/s]
 41%|████      | 102/250 [01:31<01:52,  1.31it/s]
 41%|████      | 103/250 [01:32<01:59,  1.23it/s]
 42%|████▏     | 104/250 [01:33<01:30,  1.61it/s]
 42%|████▏     | 105/250 [01:33<01:11,  2.03it/s]
 42%|████▏     | 106/250 [01:34<01:29,  1.60it/s]
 43%|████▎     | 107/250 [01:34<01:24,  1.68it/s]
 44%|████▎     | 109/250 [01:35<00:58,  2.40it/s]
 44%|████▍     | 110/250 [01:35<00:49,  2.80it/s]
 44%|████▍     | 111/250 [01:35<00:48,  2.85it/s]
 45%|████▍     | 112/250 [01:36<00:57,  2.39it/s]
 46%|████▌     | 114/250 [01:36<00:41,  3.31it/s]
 46%|████▌     | 115/250 [01:38<01:45,  1.28it/s]
 46%|████▋     | 116/250 [01:39<01:29,  1.50it/s]
 47%|████▋     | 117/250 [01:40<01:39,  1.34it/s]
 47%|████▋     | 118/250 [01:41<02:05,  1.05it/s]
 48%|████▊     | 119/250 [01:42<01:58,  1.10it/s]
 48%|████▊     | 120/250 [01:43<01:49,  1.19it/s]
 48%|████▊     | 121/250 [01:43<01:31,  1.42it/s]
 49%|████▉     | 122/250 [01:43<01:09,  1.85it/s]
 49%|████▉     | 123/250 [01:43<00:57,  2.20it/s]
 50%|████▉     | 124/250 [01:44<01:08,  1.85it/s]
 50%|█████     | 125/250 [01:45<01:03,  1.96it/s]
 50%|█████     | 126/250 [01:45<01:08,  1.82it/s]
 51%|█████     | 128/250 [01:48<01:59,  1.02it/s]
 52%|█████▏    | 129/250 [01:49<01:42,  1.18it/s]
 52%|█████▏    | 130/250 [01:50<01:57,  1.02it/s]
 52%|█████▏    | 131/250 [01:58<05:39,  2.85s/it]
 53%|█████▎    | 132/250 [01:58<04:06,  2.09s/it]
 53%|█████▎    | 133/250 [01:58<03:02,  1.56s/it]
 54%|█████▎    | 134/250 [01:58<02:14,  1.16s/it]
 54%|█████▍    | 135/250 [02:03<03:56,  2.05s/it]
 54%|█████▍    | 136/250 [02:03<02:50,  1.50s/it]
 55%|█████▍    | 137/250 [02:04<02:56,  1.56s/it]
 55%|█████▌    | 138/250 [02:05<02:23,  1.28s/it]
 56%|█████▌    | 139/250 [02:06<02:24,  1.30s/it]
 56%|█████▋    | 141/250 [02:07<01:22,  1.33it/s]
 57%|█████▋    | 142/250 [02:07<01:08,  1.58it/s]
 57%|█████▋    | 143/250 [02:09<01:40,  1.06it/s]
 58%|█████▊    | 144/250 [02:09<01:21,  1.30it/s]
 58%|█████▊    | 145/250 [02:11<01:54,  1.09s/it]
 58%|█████▊    | 146/250 [02:11<01:24,  1.23it/s]
 59%|█████▉    | 147/250 [02:11<01:12,  1.42it/s]
 59%|█████▉    | 148/250 [02:12<00:53,  1.89it/s]
 60%|█████▉    | 149/250 [02:12<00:42,  2.36it/s]
 60%|██████    | 150/250 [02:12<00:50,  1.99it/s]
 61%|██████    | 152/250 [02:13<00:30,  3.20it/s]
 62%|██████▏   | 154/250 [02:13<00:20,  4.72it/s]
 62%|██████▏   | 155/250 [02:13<00:30,  3.12it/s]
 62%|██████▏   | 156/250 [02:15<00:59,  1.58it/s]
 63%|██████▎   | 157/250 [02:20<02:29,  1.61s/it]
 63%|██████▎   | 158/250 [02:20<01:56,  1.27s/it]
 64%|██████▎   | 159/250 [02:22<02:16,  1.50s/it]
 64%|██████▍   | 160/250 [02:23<02:02,  1.37s/it]
 64%|██████▍   | 161/250 [02:23<01:29,  1.01s/it]
 65%|██████▍   | 162/250 [02:25<01:43,  1.18s/it]
 65%|██████▌   | 163/250 [02:28<02:30,  1.73s/it]
 66%|██████▌   | 164/250 [02:30<02:44,  1.91s/it]
 66%|██████▌   | 165/250 [02:31<02:23,  1.69s/it]
 67%|██████▋   | 167/250 [02:34<02:00,  1.45s/it]
 67%|██████▋   | 168/250 [02:35<01:59,  1.46s/it]
 68%|██████▊   | 169/250 [02:38<02:28,  1.84s/it]
 68%|██████▊   | 170/250 [02:41<02:57,  2.22s/it]
 68%|██████▊   | 171/250 [02:41<02:08,  1.63s/it]
 69%|██████▉   | 172/250 [02:42<01:42,  1.32s/it]
 69%|██████▉   | 173/250 [02:43<01:29,  1.16s/it]
 70%|███████   | 175/250 [02:44<01:07,  1.12it/s]
 71%|███████   | 177/250 [02:45<00:51,  1.41it/s]
 71%|███████   | 178/250 [02:45<00:41,  1.73it/s]
 72%|███████▏  | 179/250 [02:45<00:40,  1.77it/s]
 72%|███████▏  | 180/250 [02:46<00:35,  1.99it/s]
 72%|███████▏  | 181/250 [02:47<00:46,  1.50it/s]
 73%|███████▎  | 182/250 [02:49<01:08,  1.01s/it]
 73%|███████▎  | 183/250 [02:49<00:53,  1.26it/s]
 74%|███████▎  | 184/250 [02:51<01:15,  1.15s/it]
 74%|███████▍  | 185/250 [02:51<00:58,  1.10it/s]
 74%|███████▍  | 186/250 [02:52<00:59,  1.08it/s]
 75%|███████▍  | 187/250 [02:53<00:46,  1.35it/s]
 75%|███████▌  | 188/250 [02:54<00:51,  1.21it/s]
 76%|███████▌  | 190/250 [02:54<00:31,  1.89it/s]
 76%|███████▋  | 191/250 [02:55<00:40,  1.44it/s]
 77%|███████▋  | 192/250 [02:56<00:35,  1.62it/s]
 77%|███████▋  | 193/250 [02:56<00:36,  1.54it/s]
 78%|███████▊  | 194/250 [02:57<00:36,  1.55it/s]
 78%|███████▊  | 195/250 [02:59<00:56,  1.03s/it]
 78%|███████▊  | 196/250 [02:59<00:49,  1.10it/s]
 79%|███████▉  | 197/250 [03:00<00:48,  1.09it/s]
 79%|███████▉  | 198/250 [03:02<00:50,  1.04it/s]
 80%|███████▉  | 199/250 [03:03<00:58,  1.16s/it]
 80%|████████  | 200/250 [03:04<00:51,  1.02s/it]
 80%|████████  | 201/250 [03:06<01:00,  1.23s/it]
 81%|████████  | 202/250 [03:08<01:13,  1.53s/it]
 81%|████████  | 203/250 [03:10<01:16,  1.62s/it]
 82%|████████▏ | 204/250 [03:10<00:56,  1.23s/it]
 82%|████████▏ | 205/250 [03:10<00:44,  1.02it/s]
 82%|████████▏ | 206/250 [03:11<00:32,  1.34it/s]
 83%|████████▎ | 207/250 [03:11<00:26,  1.63it/s]
 83%|████████▎ | 208/250 [03:13<00:44,  1.07s/it]
 84%|████████▎ | 209/250 [03:13<00:32,  1.26it/s]
 84%|████████▍ | 211/250 [03:17<00:52,  1.34s/it]
 85%|████████▍ | 212/250 [03:18<00:44,  1.17s/it]
 85%|████████▌ | 213/250 [03:18<00:36,  1.03it/s]
 86%|████████▌ | 214/250 [03:20<00:44,  1.24s/it]
 86%|████████▋ | 216/250 [03:21<00:33,  1.02it/s]
 87%|████████▋ | 217/250 [03:24<00:46,  1.42s/it]
 87%|████████▋ | 218/250 [03:24<00:35,  1.12s/it]
 88%|████████▊ | 219/250 [03:26<00:39,  1.28s/it]
 88%|████████▊ | 220/250 [03:26<00:29,  1.00it/s]
 88%|████████▊ | 221/250 [03:27<00:24,  1.18it/s]
 89%|████████▉ | 223/250 [03:27<00:13,  1.98it/s]
 90%|████████▉ | 224/250 [03:28<00:15,  1.65it/s]
 90%|█████████ | 225/250 [03:28<00:12,  1.98it/s]
 90%|█████████ | 226/250 [03:29<00:10,  2.19it/s]
 91%|█████████ | 227/250 [03:29<00:13,  1.71it/s]
 91%|█████████ | 228/250 [03:30<00:11,  1.98it/s]
 92%|█████████▏| 229/250 [03:30<00:11,  1.75it/s]
 92%|█████████▏| 230/250 [03:31<00:08,  2.30it/s]
 92%|█████████▏| 231/250 [03:31<00:06,  2.81it/s]
 93%|█████████▎| 232/250 [03:31<00:05,  3.23it/s]
 93%|█████████▎| 233/250 [03:31<00:04,  3.97it/s]
 94%|█████████▎| 234/250 [03:31<00:03,  4.47it/s]
 94%|█████████▍| 235/250 [03:32<00:06,  2.16it/s]
 95%|█████████▍| 237/250 [03:33<00:04,  3.12it/s]
 95%|█████████▌| 238/250 [03:33<00:03,  3.00it/s]
 96%|█████████▌| 239/250 [03:33<00:03,  2.81it/s]
 96%|█████████▌| 240/250 [03:34<00:03,  2.71it/s]
 97%|█████████▋| 242/250 [03:34<00:01,  4.20it/s]
 98%|█████████▊| 244/250 [03:35<00:01,  3.42it/s]
 98%|█████████▊| 245/250 [03:36<00:02,  1.70it/s]
 98%|█████████▊| 246/250 [03:37<00:02,  1.81it/s]
 99%|█████████▉| 247/250 [03:39<00:03,  1.04s/it]
 99%|█████████▉| 248/250 [03:43<00:03,  1.78s/it]
100%|█████████▉| 249/250 [03:45<00:01,  1.73s/it]
100%|██████████| 250/250 [03:48<00:00,  2.15s/it]
100%|██████████| 250/250 [03:48<00:00,  1.09it/s]
2025-05-16 14:15:12,568 - modnet - INFO - Loss per individual: ind 0: 40.361 	ind 1: 43.027 	ind 2: 40.834 	ind 3: 40.780 	ind 4: 40.237 	ind 5: 52.478 	ind 6: 89.601 	ind 7: 39.966 	ind 8: 38.409 	ind 9: 51.230 	ind 10: 50.582 	ind 11: 41.121 	ind 12: 38.336 	ind 13: 42.504 	ind 14: 38.249 	ind 15: 40.972 	ind 16: 39.262 	ind 17: 37.099 	ind 18: 51.167 	ind 19: 56.645 	ind 20: 37.881 	ind 21: 42.752 	ind 22: 42.879 	ind 23: 41.730 	ind 24: 38.914 	ind 25: 51.375 	ind 26: 38.726 	ind 27: 38.827 	ind 28: 39.408 	ind 29: 43.563 	ind 30: 37.280 	ind 31: 37.591 	ind 32: 51.954 	ind 33: 39.694 	ind 34: 40.591 	ind 35: 37.054 	ind 36: 38.404 	ind 37: 37.103 	ind 38: 37.528 	ind 39: 65.299 	ind 40: 38.777 	ind 41: 39.985 	ind 42: 40.828 	ind 43: 38.075 	ind 44: 42.433 	ind 45: 40.818 	ind 46: 39.352 	ind 47: 38.155 	ind 48: 38.941 	ind 49: 48.706 	
2025-05-16 14:15:12,572 - modnet - INFO - After generation 15:
2025-05-16 14:15:12,572 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 224, 'fraction1': 0.25, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 0.25, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 490, 'n_layers': 5, 'layer_mults': None}
2025-05-16 14:15:12,572 - modnet - INFO - Best validation loss: 36.1079
2025-05-16 14:15:12,572 - modnet - INFO - Generation number 16

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:06<26:11,  6.31s/it]
  1%|          | 2/250 [00:06<11:40,  2.83s/it]
  1%|          | 3/250 [00:06<06:50,  1.66s/it]
  2%|▏         | 4/250 [00:09<08:02,  1.96s/it]
  2%|▏         | 6/250 [00:09<03:58,  1.02it/s]
  3%|▎         | 8/250 [00:10<02:35,  1.56it/s]
  4%|▍         | 10/250 [00:10<02:01,  1.98it/s]
  4%|▍         | 11/250 [00:11<01:58,  2.01it/s]
  5%|▍         | 12/250 [00:11<01:51,  2.13it/s]
  5%|▌         | 13/250 [00:13<03:38,  1.08it/s]
  6%|▌         | 14/250 [00:13<02:50,  1.39it/s]
  6%|▋         | 16/250 [00:14<02:15,  1.73it/s]
  7%|▋         | 17/250 [00:15<02:11,  1.77it/s]
  7%|▋         | 18/250 [00:15<01:57,  1.98it/s]
  8%|▊         | 19/250 [00:15<01:45,  2.20it/s]
  8%|▊         | 20/250 [00:16<01:56,  1.98it/s]
  8%|▊         | 21/250 [00:18<03:05,  1.23it/s]
  9%|▉         | 23/250 [00:19<02:54,  1.30it/s]
 10%|▉         | 24/250 [00:23<05:47,  1.54s/it]
 10%|█         | 25/250 [00:24<05:33,  1.48s/it]
 10%|█         | 26/250 [00:25<04:34,  1.22s/it]
 11%|█         | 27/250 [00:31<09:33,  2.57s/it]
 11%|█         | 28/250 [00:31<07:19,  1.98s/it]
 12%|█▏        | 29/250 [00:32<05:48,  1.58s/it]
 12%|█▏        | 30/250 [00:32<04:15,  1.16s/it]
 12%|█▏        | 31/250 [00:33<03:35,  1.02it/s]
 13%|█▎        | 32/250 [00:33<03:20,  1.09it/s]
 13%|█▎        | 33/250 [00:34<03:22,  1.07it/s]
 14%|█▎        | 34/250 [00:36<03:38,  1.01s/it]
 14%|█▍        | 35/250 [00:37<03:55,  1.10s/it]
 14%|█▍        | 36/250 [00:37<03:16,  1.09it/s]
 15%|█▍        | 37/250 [00:38<03:27,  1.03it/s]
 15%|█▌        | 38/250 [00:40<04:01,  1.14s/it]
 16%|█▌        | 39/250 [00:42<04:57,  1.41s/it]
 16%|█▌        | 40/250 [00:46<07:16,  2.08s/it]
 16%|█▋        | 41/250 [00:46<05:11,  1.49s/it]
 17%|█▋        | 42/250 [00:46<04:18,  1.24s/it]
 18%|█▊        | 44/250 [00:47<02:29,  1.37it/s]
 18%|█▊        | 45/250 [00:47<02:16,  1.50it/s]
 18%|█▊        | 46/250 [00:48<02:13,  1.53it/s]
 19%|█▉        | 47/250 [00:48<02:04,  1.63it/s]
 19%|█▉        | 48/250 [00:49<01:41,  1.99it/s]
 20%|█▉        | 49/250 [00:49<01:44,  1.92it/s]
 20%|██        | 50/250 [00:49<01:21,  2.45it/s]
 20%|██        | 51/250 [00:50<02:02,  1.62it/s]
 21%|██        | 52/250 [00:50<01:33,  2.11it/s]
 21%|██        | 53/250 [00:52<02:47,  1.18it/s]
 22%|██▏       | 54/250 [00:53<02:24,  1.36it/s]
 22%|██▏       | 55/250 [00:53<02:12,  1.47it/s]
 22%|██▏       | 56/250 [00:54<02:07,  1.52it/s]
 23%|██▎       | 57/250 [00:55<02:18,  1.40it/s]
 23%|██▎       | 58/250 [00:56<03:17,  1.03s/it]
 24%|██▎       | 59/250 [00:57<02:59,  1.06it/s]
 24%|██▍       | 60/250 [00:57<02:20,  1.35it/s]
 24%|██▍       | 61/250 [00:58<01:55,  1.64it/s]
 25%|██▍       | 62/250 [00:59<02:09,  1.45it/s]
 25%|██▌       | 63/250 [00:59<02:05,  1.49it/s]
 26%|██▌       | 64/250 [00:59<01:34,  1.98it/s]
 26%|██▌       | 65/250 [01:00<01:38,  1.88it/s]
 26%|██▋       | 66/250 [01:01<02:03,  1.50it/s]
 27%|██▋       | 67/250 [01:02<02:25,  1.26it/s]
 27%|██▋       | 68/250 [01:04<03:53,  1.28s/it]
 28%|██▊       | 69/250 [01:05<03:26,  1.14s/it]
 28%|██▊       | 70/250 [01:06<03:18,  1.11s/it]
 28%|██▊       | 71/250 [01:07<03:10,  1.06s/it]
 29%|██▉       | 72/250 [01:09<03:29,  1.18s/it]
 29%|██▉       | 73/250 [01:09<02:48,  1.05it/s]
 30%|██▉       | 74/250 [01:10<02:46,  1.06it/s]
 30%|███       | 75/250 [01:11<02:41,  1.08it/s]
 30%|███       | 76/250 [01:12<02:40,  1.08it/s]
 31%|███       | 77/250 [01:13<02:39,  1.09it/s]
 31%|███       | 78/250 [01:13<02:20,  1.22it/s]
 32%|███▏      | 79/250 [01:14<01:59,  1.43it/s]
 32%|███▏      | 80/250 [01:14<01:30,  1.87it/s]
 32%|███▏      | 81/250 [01:14<01:12,  2.32it/s]
 33%|███▎      | 82/250 [01:15<01:15,  2.23it/s]
 33%|███▎      | 83/250 [01:15<01:10,  2.37it/s]
 34%|███▎      | 84/250 [01:16<01:29,  1.86it/s]
 34%|███▍      | 85/250 [01:17<02:09,  1.27it/s]
 34%|███▍      | 86/250 [01:18<02:12,  1.24it/s]
 35%|███▍      | 87/250 [01:19<01:56,  1.41it/s]
 35%|███▌      | 88/250 [01:19<01:30,  1.79it/s]
 36%|███▌      | 89/250 [01:20<01:46,  1.51it/s]
 36%|███▌      | 90/250 [01:22<02:55,  1.10s/it]
 36%|███▋      | 91/250 [01:23<03:26,  1.30s/it]
 37%|███▋      | 93/250 [01:24<02:20,  1.12it/s]
 38%|███▊      | 94/250 [01:25<02:17,  1.14it/s]
 38%|███▊      | 95/250 [01:26<02:04,  1.24it/s]
 38%|███▊      | 96/250 [01:26<01:52,  1.37it/s]
 39%|███▉      | 97/250 [01:28<02:16,  1.12it/s]
 39%|███▉      | 98/250 [01:30<03:30,  1.39s/it]
 40%|███▉      | 99/250 [01:30<02:33,  1.01s/it]
 40%|████      | 100/250 [01:33<03:56,  1.57s/it]
 40%|████      | 101/250 [01:34<03:03,  1.23s/it]
 41%|████      | 102/250 [01:34<02:39,  1.08s/it]
 41%|████      | 103/250 [01:36<02:41,  1.10s/it]
 42%|████▏     | 104/250 [01:36<02:23,  1.02it/s]
 42%|████▏     | 105/250 [01:37<02:34,  1.06s/it]
 42%|████▏     | 106/250 [01:38<01:52,  1.28it/s]
 43%|████▎     | 107/250 [01:39<02:04,  1.15it/s]
 43%|████▎     | 108/250 [01:40<02:31,  1.07s/it]
 44%|████▎     | 109/250 [01:42<02:44,  1.17s/it]
 44%|████▍     | 110/250 [01:45<04:06,  1.76s/it]
 44%|████▍     | 111/250 [01:46<03:23,  1.46s/it]
 45%|████▍     | 112/250 [01:48<04:07,  1.80s/it]
 45%|████▌     | 113/250 [01:49<03:15,  1.42s/it]
 46%|████▌     | 114/250 [01:50<03:25,  1.51s/it]
 46%|████▌     | 115/250 [01:51<02:44,  1.22s/it]
 46%|████▋     | 116/250 [01:51<01:59,  1.12it/s]
 47%|████▋     | 118/250 [01:53<02:13,  1.01s/it]
 48%|████▊     | 119/250 [01:54<01:55,  1.14it/s]
 48%|████▊     | 120/250 [01:54<01:43,  1.25it/s]
 48%|████▊     | 121/250 [01:55<01:44,  1.23it/s]
 49%|████▉     | 123/250 [01:55<01:02,  2.03it/s]
 50%|████▉     | 124/250 [01:56<00:50,  2.48it/s]
 50%|█████     | 125/250 [01:57<01:11,  1.74it/s]
 50%|█████     | 126/250 [01:57<00:57,  2.17it/s]
 51%|█████     | 127/250 [01:58<01:13,  1.67it/s]
 51%|█████     | 128/250 [01:59<01:19,  1.53it/s]
 52%|█████▏    | 129/250 [02:00<01:43,  1.17it/s]
 52%|█████▏    | 130/250 [02:02<02:11,  1.09s/it]
 52%|█████▏    | 131/250 [02:02<01:43,  1.15it/s]
 53%|█████▎    | 132/250 [02:02<01:21,  1.46it/s]
 53%|█████▎    | 133/250 [02:03<01:22,  1.41it/s]
 54%|█████▎    | 134/250 [02:04<01:30,  1.29it/s]
 54%|█████▍    | 135/250 [02:05<01:54,  1.01it/s]
 54%|█████▍    | 136/250 [02:06<01:41,  1.12it/s]
 55%|█████▍    | 137/250 [02:07<01:40,  1.12it/s]
 55%|█████▌    | 138/250 [02:08<02:01,  1.08s/it]
 56%|█████▌    | 139/250 [02:09<01:50,  1.01it/s]
 56%|█████▌    | 140/250 [02:13<03:05,  1.69s/it]
 57%|█████▋    | 142/250 [02:15<02:35,  1.44s/it]
 57%|█████▋    | 143/250 [02:15<02:04,  1.16s/it]
 58%|█████▊    | 144/250 [02:15<01:36,  1.10it/s]
 58%|█████▊    | 145/250 [02:16<01:40,  1.04it/s]
 58%|█████▊    | 146/250 [02:17<01:33,  1.11it/s]
 59%|█████▉    | 147/250 [02:18<01:44,  1.02s/it]
 59%|█████▉    | 148/250 [02:19<01:36,  1.06it/s]
 60%|█████▉    | 149/250 [02:20<01:40,  1.00it/s]
 60%|██████    | 150/250 [02:21<01:37,  1.02it/s]
 60%|██████    | 151/250 [02:22<01:23,  1.19it/s]
 61%|██████    | 152/250 [02:23<01:28,  1.10it/s]
 61%|██████    | 153/250 [02:23<01:16,  1.27it/s]
 62%|██████▏   | 154/250 [02:24<00:57,  1.67it/s]
 62%|██████▏   | 155/250 [02:25<01:25,  1.11it/s]
 62%|██████▏   | 156/250 [02:25<01:03,  1.48it/s]
 63%|██████▎   | 157/250 [02:26<01:10,  1.32it/s]
 63%|██████▎   | 158/250 [02:27<01:08,  1.34it/s]
 64%|██████▎   | 159/250 [02:28<01:11,  1.27it/s]
 64%|██████▍   | 160/250 [02:30<01:45,  1.17s/it]
 64%|██████▍   | 161/250 [02:30<01:16,  1.17it/s]
 65%|██████▍   | 162/250 [02:31<01:11,  1.22it/s]
 65%|██████▌   | 163/250 [02:32<01:23,  1.04it/s]
 66%|██████▌   | 164/250 [02:34<01:49,  1.28s/it]
 66%|██████▌   | 165/250 [02:35<01:30,  1.06s/it]
 66%|██████▋   | 166/250 [02:40<03:17,  2.35s/it]
 67%|██████▋   | 167/250 [02:41<02:30,  1.81s/it]
 68%|██████▊   | 169/250 [02:42<01:37,  1.21s/it]
 68%|██████▊   | 171/250 [02:43<01:13,  1.07it/s]
 69%|██████▉   | 172/250 [02:43<01:05,  1.19it/s]
 69%|██████▉   | 173/250 [02:44<01:08,  1.13it/s]
 70%|██████▉   | 174/250 [02:45<01:15,  1.01it/s]
 70%|███████   | 175/250 [02:49<02:05,  1.67s/it]
 70%|███████   | 176/250 [02:51<02:11,  1.78s/it]
 71%|███████   | 177/250 [02:51<01:36,  1.32s/it]
 71%|███████   | 178/250 [02:52<01:17,  1.07s/it]
 72%|███████▏  | 179/250 [02:53<01:29,  1.26s/it]
 72%|███████▏  | 180/250 [02:54<01:20,  1.15s/it]
 72%|███████▏  | 181/250 [02:54<00:59,  1.16it/s]
 73%|███████▎  | 182/250 [02:56<01:17,  1.14s/it]
 74%|███████▎  | 184/250 [02:58<01:02,  1.06it/s]
 74%|███████▍  | 185/250 [02:59<01:10,  1.09s/it]
 74%|███████▍  | 186/250 [03:02<01:31,  1.43s/it]
 75%|███████▍  | 187/250 [03:02<01:13,  1.16s/it]
 75%|███████▌  | 188/250 [03:03<01:17,  1.25s/it]
 76%|███████▌  | 189/250 [03:04<01:11,  1.17s/it]
 76%|███████▌  | 190/250 [03:07<01:37,  1.63s/it]
 76%|███████▋  | 191/250 [03:07<01:12,  1.23s/it]
 77%|███████▋  | 192/250 [03:08<01:01,  1.07s/it]
 77%|███████▋  | 193/250 [03:09<01:00,  1.06s/it]
 78%|███████▊  | 194/250 [03:10<00:51,  1.10it/s]
 78%|███████▊  | 195/250 [03:10<00:36,  1.49it/s]
 78%|███████▊  | 196/250 [03:12<00:57,  1.07s/it]
 79%|███████▉  | 197/250 [03:12<00:45,  1.15it/s]
 80%|███████▉  | 199/250 [03:13<00:28,  1.78it/s]
 80%|████████  | 200/250 [03:13<00:26,  1.86it/s]
 80%|████████  | 201/250 [03:14<00:31,  1.56it/s]
 81%|████████  | 202/250 [03:15<00:28,  1.69it/s]
 81%|████████  | 203/250 [03:15<00:24,  1.91it/s]
 82%|████████▏ | 204/250 [03:18<00:54,  1.18s/it]
 82%|████████▏ | 205/250 [03:18<00:42,  1.06it/s]
 82%|████████▏ | 206/250 [03:18<00:34,  1.28it/s]
 83%|████████▎ | 207/250 [03:19<00:35,  1.23it/s]
 83%|████████▎ | 208/250 [03:22<00:51,  1.22s/it]
 84%|████████▎ | 209/250 [03:23<00:51,  1.24s/it]
 84%|████████▍ | 210/250 [03:23<00:37,  1.06it/s]
 84%|████████▍ | 211/250 [03:23<00:27,  1.42it/s]
 85%|████████▍ | 212/250 [03:29<01:27,  2.31s/it]
 85%|████████▌ | 213/250 [03:30<01:03,  1.71s/it]
 86%|████████▌ | 214/250 [03:30<00:47,  1.33s/it]
 86%|████████▌ | 215/250 [03:31<00:41,  1.19s/it]
 86%|████████▋ | 216/250 [03:32<00:43,  1.28s/it]
 87%|████████▋ | 217/250 [03:35<00:53,  1.61s/it]
 87%|████████▋ | 218/250 [03:36<00:45,  1.43s/it]
 88%|████████▊ | 219/250 [03:39<01:00,  1.96s/it]
 88%|████████▊ | 220/250 [03:40<00:48,  1.62s/it]
 88%|████████▊ | 221/250 [03:40<00:34,  1.18s/it]
 89%|████████▉ | 222/250 [03:41<00:30,  1.08s/it]
 89%|████████▉ | 223/250 [03:42<00:27,  1.02s/it]
 90%|████████▉ | 224/250 [03:42<00:20,  1.25it/s]
 90%|█████████ | 225/250 [03:43<00:19,  1.31it/s]
 90%|█████████ | 226/250 [03:43<00:16,  1.43it/s]
 91%|█████████ | 227/250 [03:43<00:12,  1.89it/s]
 91%|█████████ | 228/250 [03:43<00:08,  2.45it/s]
 92%|█████████▏| 229/250 [03:44<00:07,  2.63it/s]
 92%|█████████▏| 230/250 [03:45<00:15,  1.28it/s]
 92%|█████████▏| 231/250 [03:46<00:10,  1.73it/s]
 93%|█████████▎| 232/250 [03:46<00:09,  1.83it/s]
 93%|█████████▎| 233/250 [03:46<00:08,  2.05it/s]
 94%|█████████▎| 234/250 [03:48<00:11,  1.38it/s]
 94%|█████████▍| 235/250 [03:48<00:10,  1.41it/s]
 95%|█████████▍| 237/250 [03:49<00:05,  2.30it/s]
 95%|█████████▌| 238/250 [03:49<00:05,  2.34it/s]
 96%|█████████▌| 239/250 [03:50<00:06,  1.78it/s]
 96%|█████████▌| 240/250 [03:50<00:04,  2.10it/s]
 97%|█████████▋| 242/250 [03:50<00:02,  3.18it/s]
 97%|█████████▋| 243/250 [03:51<00:02,  3.21it/s]
 98%|█████████▊| 244/250 [03:52<00:03,  1.89it/s]
 98%|█████████▊| 245/250 [03:52<00:02,  1.83it/s]
 98%|█████████▊| 246/250 [03:54<00:02,  1.34it/s]
 99%|█████████▉| 247/250 [03:56<00:03,  1.24s/it]
 99%|█████████▉| 248/250 [03:57<00:02,  1.04s/it]
100%|█████████▉| 249/250 [03:57<00:00,  1.13it/s]
100%|██████████| 250/250 [03:58<00:00,  1.24it/s]
100%|██████████| 250/250 [03:58<00:00,  1.05it/s]
2025-05-16 14:19:11,030 - modnet - INFO - Loss per individual: ind 0: 42.382 	ind 1: 42.776 	ind 2: 38.797 	ind 3: 40.543 	ind 4: 122.408 	ind 5: 40.447 	ind 6: 42.758 	ind 7: 38.202 	ind 8: 36.864 	ind 9: 37.134 	ind 10: 38.306 	ind 11: 40.797 	ind 12: 38.297 	ind 13: 40.593 	ind 14: 44.013 	ind 15: 39.593 	ind 16: 39.200 	ind 17: 38.754 	ind 18: 37.763 	ind 19: 39.099 	ind 20: 39.209 	ind 21: 41.863 	ind 22: 39.676 	ind 23: 43.892 	ind 24: 40.379 	ind 25: 42.244 	ind 26: 41.192 	ind 27: 38.545 	ind 28: 36.049 	ind 29: 36.906 	ind 30: 38.053 	ind 31: 43.335 	ind 32: 40.446 	ind 33: 39.817 	ind 34: 41.441 	ind 35: 38.608 	ind 36: 40.118 	ind 37: 39.244 	ind 38: 38.962 	ind 39: 37.927 	ind 40: 40.920 	ind 41: 38.180 	ind 42: 37.551 	ind 43: 37.498 	ind 44: 40.281 	ind 45: 38.962 	ind 46: 53.234 	ind 47: 38.706 	ind 48: 54.816 	ind 49: 37.479 	
2025-05-16 14:19:11,032 - modnet - INFO - After generation 16:
2025-05-16 14:19:11,032 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 256, 'fraction1': 0.25, 'fraction2': 1, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 360, 'n_layers': 3, 'layer_mults': None}
2025-05-16 14:19:11,032 - modnet - INFO - Best validation loss: 36.0490
2025-05-16 14:19:11,032 - modnet - INFO - Generation number 17

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:10<43:21, 10.45s/it]
  1%|          | 2/250 [00:13<25:44,  6.23s/it]
  1%|          | 3/250 [00:14<14:43,  3.58s/it]
  2%|▏         | 4/250 [00:17<14:34,  3.55s/it]
  2%|▏         | 5/250 [00:17<09:43,  2.38s/it]
  2%|▏         | 6/250 [00:18<07:45,  1.91s/it]
  3%|▎         | 7/250 [00:19<05:25,  1.34s/it]
  4%|▎         | 9/250 [00:20<04:05,  1.02s/it]
  4%|▍         | 11/250 [00:21<03:28,  1.15it/s]
  5%|▍         | 12/250 [00:22<03:16,  1.21it/s]
  6%|▌         | 14/250 [00:22<02:17,  1.72it/s]
  6%|▋         | 16/250 [00:23<02:08,  1.82it/s]
  7%|▋         | 17/250 [00:26<03:39,  1.06it/s]
  7%|▋         | 18/250 [00:27<03:26,  1.12it/s]
  8%|▊         | 19/250 [00:28<04:15,  1.10s/it]
  8%|▊         | 20/250 [00:31<05:32,  1.45s/it]
  8%|▊         | 21/250 [00:31<04:27,  1.17s/it]
  9%|▉         | 23/250 [00:31<02:36,  1.45it/s]
 10%|▉         | 24/250 [00:32<02:33,  1.47it/s]
 10%|█         | 25/250 [00:33<03:10,  1.18it/s]
 10%|█         | 26/250 [00:34<02:43,  1.37it/s]
 11%|█         | 27/250 [00:34<02:15,  1.65it/s]
 11%|█         | 28/250 [00:34<02:09,  1.72it/s]
 12%|█▏        | 29/250 [00:35<02:26,  1.50it/s]
 12%|█▏        | 31/250 [00:36<01:53,  1.93it/s]
 13%|█▎        | 32/250 [00:36<01:40,  2.17it/s]
 14%|█▎        | 34/250 [00:37<01:19,  2.71it/s]
 14%|█▍        | 35/250 [00:37<01:18,  2.73it/s]
 14%|█▍        | 36/250 [00:37<01:05,  3.25it/s]
 15%|█▌        | 38/250 [00:38<00:49,  4.30it/s]
 16%|█▌        | 39/250 [00:38<00:43,  4.82it/s]
 16%|█▌        | 40/250 [00:38<00:48,  4.33it/s]
 17%|█▋        | 42/250 [00:40<01:51,  1.87it/s]
 17%|█▋        | 43/250 [00:41<02:04,  1.66it/s]
 18%|█▊        | 44/250 [00:41<02:07,  1.62it/s]
 18%|█▊        | 45/250 [00:43<02:44,  1.25it/s]
 18%|█▊        | 46/250 [00:43<02:34,  1.32it/s]
 19%|█▉        | 47/250 [00:44<02:48,  1.20it/s]
 19%|█▉        | 48/250 [00:45<02:56,  1.15it/s]
 20%|█▉        | 49/250 [00:46<02:38,  1.27it/s]
 20%|██        | 50/250 [00:47<02:59,  1.11it/s]
 20%|██        | 51/250 [00:50<05:05,  1.53s/it]
 21%|██        | 52/250 [00:52<05:48,  1.76s/it]
 21%|██        | 53/250 [00:53<04:46,  1.45s/it]
 22%|██▏       | 54/250 [00:54<03:46,  1.16s/it]
 22%|██▏       | 55/250 [00:54<03:20,  1.03s/it]
 22%|██▏       | 56/250 [00:55<02:36,  1.24it/s]
 23%|██▎       | 57/250 [00:57<04:31,  1.41s/it]
 24%|██▎       | 59/250 [00:58<02:30,  1.27it/s]
 24%|██▍       | 61/250 [00:58<01:47,  1.76it/s]
 25%|██▍       | 62/250 [00:59<02:08,  1.46it/s]
 25%|██▌       | 63/250 [00:59<01:42,  1.82it/s]
 26%|██▌       | 64/250 [01:00<01:30,  2.05it/s]
 26%|██▌       | 65/250 [01:00<01:11,  2.60it/s]
 26%|██▋       | 66/250 [01:01<02:00,  1.53it/s]
 27%|██▋       | 67/250 [01:01<01:41,  1.81it/s]
 28%|██▊       | 69/250 [01:02<01:32,  1.95it/s]
 28%|██▊       | 70/250 [01:03<01:57,  1.53it/s]
 28%|██▊       | 71/250 [01:04<01:38,  1.82it/s]
 29%|██▉       | 72/250 [01:04<01:21,  2.18it/s]
 29%|██▉       | 73/250 [01:06<02:31,  1.17it/s]
 30%|██▉       | 74/250 [01:06<02:00,  1.46it/s]
 30%|███       | 75/250 [01:07<02:28,  1.18it/s]
 31%|███       | 77/250 [01:07<01:27,  1.98it/s]
 31%|███       | 78/250 [01:08<01:12,  2.38it/s]
 32%|███▏      | 79/250 [01:08<01:22,  2.08it/s]
 32%|███▏      | 80/250 [01:10<02:31,  1.12it/s]
 32%|███▏      | 81/250 [01:11<02:31,  1.11it/s]
 33%|███▎      | 82/250 [01:13<03:21,  1.20s/it]
 33%|███▎      | 83/250 [01:14<03:03,  1.10s/it]
 34%|███▍      | 85/250 [01:15<02:28,  1.11it/s]
 34%|███▍      | 86/250 [01:18<03:56,  1.44s/it]
 35%|███▍      | 87/250 [01:20<03:58,  1.46s/it]
 35%|███▌      | 88/250 [01:21<03:42,  1.37s/it]
 36%|███▌      | 89/250 [01:22<03:12,  1.19s/it]
 36%|███▌      | 90/250 [01:23<03:03,  1.14s/it]
 36%|███▋      | 91/250 [01:25<03:44,  1.41s/it]
 37%|███▋      | 92/250 [01:26<03:46,  1.43s/it]
 37%|███▋      | 93/250 [01:26<02:44,  1.05s/it]
 38%|███▊      | 94/250 [01:27<02:12,  1.17it/s]
 38%|███▊      | 95/250 [01:27<01:38,  1.57it/s]
 38%|███▊      | 96/250 [01:28<02:05,  1.23it/s]
 39%|███▉      | 97/250 [01:30<02:33,  1.00s/it]
 39%|███▉      | 98/250 [01:30<02:04,  1.22it/s]
 40%|███▉      | 99/250 [01:31<01:50,  1.37it/s]
 40%|████      | 100/250 [01:31<01:34,  1.59it/s]
 40%|████      | 101/250 [01:32<01:38,  1.52it/s]
 41%|████      | 103/250 [01:33<01:30,  1.63it/s]
 42%|████▏     | 105/250 [01:33<00:58,  2.46it/s]
 42%|████▏     | 106/250 [01:34<01:03,  2.26it/s]
 43%|████▎     | 107/250 [01:34<01:06,  2.14it/s]
 43%|████▎     | 108/250 [01:35<01:26,  1.63it/s]
 44%|████▎     | 109/250 [01:36<01:36,  1.47it/s]
 44%|████▍     | 110/250 [01:37<01:39,  1.40it/s]
 44%|████▍     | 111/250 [01:39<02:33,  1.11s/it]
 45%|████▌     | 113/250 [01:39<01:41,  1.35it/s]
 46%|████▌     | 114/250 [01:40<01:33,  1.45it/s]
 46%|████▌     | 115/250 [01:41<01:41,  1.33it/s]
 46%|████▋     | 116/250 [01:41<01:21,  1.64it/s]
 47%|████▋     | 117/250 [01:41<01:10,  1.90it/s]
 47%|████▋     | 118/250 [01:42<01:02,  2.11it/s]
 48%|████▊     | 119/250 [01:42<00:50,  2.60it/s]
 48%|████▊     | 120/250 [01:42<00:40,  3.22it/s]
 48%|████▊     | 121/250 [01:43<01:17,  1.67it/s]
 49%|████▉     | 122/250 [01:44<00:59,  2.16it/s]
 49%|████▉     | 123/250 [01:44<00:47,  2.67it/s]
 50%|████▉     | 124/250 [01:44<01:03,  1.98it/s]
 50%|█████     | 125/250 [01:45<01:12,  1.73it/s]
 50%|█████     | 126/250 [01:46<01:36,  1.28it/s]
 51%|█████     | 127/250 [01:47<01:25,  1.44it/s]
 51%|█████     | 128/250 [01:48<01:22,  1.48it/s]
 52%|█████▏    | 129/250 [01:48<01:02,  1.94it/s]
 52%|█████▏    | 131/250 [01:49<01:15,  1.58it/s]
 53%|█████▎    | 132/250 [01:50<01:08,  1.72it/s]
 53%|█████▎    | 133/250 [01:50<01:02,  1.86it/s]
 54%|█████▎    | 134/250 [01:52<01:30,  1.28it/s]
 54%|█████▍    | 135/250 [01:52<01:17,  1.48it/s]
 54%|█████▍    | 136/250 [01:54<02:11,  1.15s/it]
 55%|█████▍    | 137/250 [01:55<01:52,  1.01it/s]
 55%|█████▌    | 138/250 [01:56<02:00,  1.08s/it]
 56%|█████▌    | 139/250 [01:56<01:29,  1.23it/s]
 56%|█████▌    | 140/250 [01:58<01:57,  1.07s/it]
 56%|█████▋    | 141/250 [01:58<01:34,  1.16it/s]
 57%|█████▋    | 142/250 [01:59<01:23,  1.30it/s]
 57%|█████▋    | 143/250 [01:59<01:09,  1.55it/s]
 58%|█████▊    | 144/250 [02:00<01:02,  1.71it/s]
 58%|█████▊    | 145/250 [02:01<01:09,  1.52it/s]
 58%|█████▊    | 146/250 [02:01<01:15,  1.38it/s]
 59%|█████▉    | 147/250 [02:02<01:13,  1.40it/s]
 59%|█████▉    | 148/250 [02:03<01:05,  1.55it/s]
 60%|██████    | 150/250 [02:04<00:59,  1.67it/s]
 60%|██████    | 151/250 [02:04<00:51,  1.93it/s]
 61%|██████    | 152/250 [02:04<00:42,  2.28it/s]
 61%|██████    | 153/250 [02:05<00:40,  2.41it/s]
 62%|██████▏   | 154/250 [02:05<00:49,  1.95it/s]
 62%|██████▏   | 155/250 [02:06<00:52,  1.80it/s]
 62%|██████▏   | 156/250 [02:09<01:56,  1.24s/it]
 63%|██████▎   | 157/250 [02:11<02:13,  1.43s/it]
 63%|██████▎   | 158/250 [02:12<02:06,  1.37s/it]
 64%|██████▎   | 159/250 [02:15<02:34,  1.70s/it]
 64%|██████▍   | 160/250 [02:16<02:24,  1.61s/it]
 64%|██████▍   | 161/250 [02:16<01:45,  1.19s/it]
 65%|██████▍   | 162/250 [02:18<01:54,  1.30s/it]
 65%|██████▌   | 163/250 [02:18<01:30,  1.04s/it]
 66%|██████▌   | 164/250 [02:19<01:21,  1.05it/s]
 66%|██████▌   | 165/250 [02:19<01:09,  1.23it/s]
 66%|██████▋   | 166/250 [02:21<01:33,  1.11s/it]
 67%|██████▋   | 167/250 [02:22<01:19,  1.04it/s]
 67%|██████▋   | 168/250 [02:24<01:57,  1.44s/it]
 68%|██████▊   | 169/250 [02:26<01:54,  1.41s/it]
 68%|██████▊   | 170/250 [02:26<01:22,  1.03s/it]
 68%|██████▊   | 171/250 [02:26<00:59,  1.33it/s]
 69%|██████▉   | 172/250 [02:27<00:58,  1.33it/s]
 69%|██████▉   | 173/250 [02:28<01:06,  1.15it/s]
 70%|██████▉   | 174/250 [02:28<00:48,  1.56it/s]
 70%|███████   | 176/250 [02:29<00:37,  1.98it/s]
 71%|███████   | 177/250 [02:29<00:32,  2.22it/s]
 72%|███████▏  | 179/250 [02:31<00:47,  1.49it/s]
 72%|███████▏  | 180/250 [02:33<01:06,  1.06it/s]
 72%|███████▏  | 181/250 [02:36<01:40,  1.46s/it]
 73%|███████▎  | 182/250 [02:37<01:39,  1.46s/it]
 73%|███████▎  | 183/250 [02:37<01:14,  1.12s/it]
 74%|███████▎  | 184/250 [02:38<00:55,  1.20it/s]
 74%|███████▍  | 185/250 [02:39<01:12,  1.11s/it]
 74%|███████▍  | 186/250 [02:40<00:56,  1.13it/s]
 75%|███████▍  | 187/250 [02:41<01:00,  1.04it/s]
 76%|███████▌  | 189/250 [02:41<00:36,  1.68it/s]
 76%|███████▌  | 190/250 [02:41<00:29,  2.01it/s]
 76%|███████▋  | 191/250 [02:43<00:52,  1.12it/s]
 77%|███████▋  | 192/250 [02:44<00:44,  1.29it/s]
 77%|███████▋  | 193/250 [02:45<00:46,  1.22it/s]
 78%|███████▊  | 194/250 [02:46<00:45,  1.23it/s]
 78%|███████▊  | 195/250 [02:46<00:46,  1.18it/s]
 78%|███████▊  | 196/250 [02:47<00:37,  1.43it/s]
 79%|███████▉  | 197/250 [02:48<00:50,  1.06it/s]
 79%|███████▉  | 198/250 [02:49<00:37,  1.38it/s]
 80%|███████▉  | 199/250 [02:49<00:37,  1.35it/s]
 80%|████████  | 200/250 [02:50<00:33,  1.49it/s]
 80%|████████  | 201/250 [02:52<00:51,  1.05s/it]
 81%|████████  | 202/250 [02:53<00:47,  1.01it/s]
 81%|████████  | 203/250 [02:53<00:44,  1.07it/s]
 82%|████████▏ | 204/250 [02:55<00:51,  1.12s/it]
 82%|████████▏ | 205/250 [02:55<00:41,  1.09it/s]
 82%|████████▏ | 206/250 [02:56<00:31,  1.39it/s]
 83%|████████▎ | 207/250 [02:56<00:30,  1.39it/s]
 84%|████████▎ | 209/250 [02:57<00:22,  1.82it/s]
 84%|████████▍ | 210/250 [02:58<00:22,  1.78it/s]
 84%|████████▍ | 211/250 [02:59<00:25,  1.52it/s]
 85%|████████▍ | 212/250 [03:00<00:35,  1.06it/s]
 85%|████████▌ | 213/250 [03:01<00:34,  1.08it/s]
 86%|████████▌ | 215/250 [03:04<00:41,  1.19s/it]
 86%|████████▋ | 216/250 [03:05<00:34,  1.03s/it]
 87%|████████▋ | 217/250 [03:05<00:28,  1.16it/s]
 87%|████████▋ | 218/250 [03:05<00:22,  1.45it/s]
 88%|████████▊ | 219/250 [03:06<00:21,  1.45it/s]
 88%|████████▊ | 220/250 [03:06<00:17,  1.74it/s]
 89%|████████▉ | 222/250 [03:06<00:09,  2.92it/s]
 89%|████████▉ | 223/250 [03:07<00:08,  3.13it/s]
 90%|████████▉ | 224/250 [03:08<00:17,  1.46it/s]
 90%|█████████ | 225/250 [03:09<00:14,  1.69it/s]
 90%|█████████ | 226/250 [03:10<00:17,  1.34it/s]
 91%|█████████ | 227/250 [03:10<00:14,  1.60it/s]
 91%|█████████ | 228/250 [03:11<00:16,  1.34it/s]
 92%|█████████▏| 229/250 [03:12<00:16,  1.25it/s]
 92%|█████████▏| 231/250 [03:12<00:09,  2.08it/s]
 93%|█████████▎| 232/250 [03:13<00:11,  1.63it/s]
 93%|█████████▎| 233/250 [03:15<00:15,  1.11it/s]
 94%|█████████▍| 235/250 [03:17<00:12,  1.22it/s]
 94%|█████████▍| 236/250 [03:18<00:11,  1.17it/s]
 96%|█████████▌| 239/250 [03:18<00:05,  2.05it/s]
 96%|█████████▌| 240/250 [03:19<00:06,  1.59it/s]
 96%|█████████▋| 241/250 [03:20<00:06,  1.43it/s]
 97%|█████████▋| 242/250 [03:21<00:05,  1.35it/s]
 97%|█████████▋| 243/250 [03:21<00:04,  1.64it/s]
 98%|█████████▊| 244/250 [03:22<00:03,  1.63it/s]
 98%|█████████▊| 245/250 [03:22<00:03,  1.63it/s]
 98%|█████████▊| 246/250 [03:23<00:02,  1.58it/s]
 99%|█████████▉| 247/250 [03:25<00:02,  1.12it/s]
100%|█████████▉| 249/250 [03:28<00:01,  1.33s/it]
100%|██████████| 250/250 [03:29<00:00,  1.21s/it]
100%|██████████| 250/250 [03:29<00:00,  1.19it/s]
2025-05-16 14:22:40,808 - modnet - INFO - Loss per individual: ind 0: 38.719 	ind 1: 41.683 	ind 2: 40.654 	ind 3: 40.391 	ind 4: 41.313 	ind 5: 38.599 	ind 6: 37.028 	ind 7: 38.148 	ind 8: 43.323 	ind 9: 49.354 	ind 10: 71.371 	ind 11: 41.391 	ind 12: 40.745 	ind 13: 37.551 	ind 14: 38.789 	ind 15: 39.422 	ind 16: 45.818 	ind 17: 37.473 	ind 18: 40.240 	ind 19: 39.624 	ind 20: 40.109 	ind 21: 47.466 	ind 22: 39.220 	ind 23: 46.936 	ind 24: 48.767 	ind 25: 41.345 	ind 26: 38.574 	ind 27: 98.561 	ind 28: 37.379 	ind 29: 43.817 	ind 30: 37.011 	ind 31: 45.220 	ind 32: 37.978 	ind 33: 39.664 	ind 34: 39.214 	ind 35: 39.881 	ind 36: 37.382 	ind 37: 40.910 	ind 38: 36.894 	ind 39: 40.361 	ind 40: 38.688 	ind 41: 39.967 	ind 42: 39.325 	ind 43: 38.244 	ind 44: 46.411 	ind 45: 37.258 	ind 46: 44.584 	ind 47: 38.690 	ind 48: 38.905 	ind 49: 40.176 	
2025-05-16 14:22:40,810 - modnet - INFO - After generation 17:
2025-05-16 14:22:40,810 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 256, 'fraction1': 0.25, 'fraction2': 1, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 360, 'n_layers': 3, 'layer_mults': None}
2025-05-16 14:22:40,810 - modnet - INFO - Best validation loss: 36.0490
2025-05-16 14:22:40,810 - modnet - INFO - Generation number 18

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:08<37:12,  8.96s/it]
  1%|          | 2/250 [00:11<22:08,  5.36s/it]
  1%|          | 3/250 [00:12<13:34,  3.30s/it]
  2%|▏         | 4/250 [00:13<09:57,  2.43s/it]
  2%|▏         | 5/250 [00:14<06:56,  1.70s/it]
  2%|▏         | 6/250 [00:14<05:09,  1.27s/it]
  3%|▎         | 7/250 [00:15<04:11,  1.03s/it]
  3%|▎         | 8/250 [00:15<03:35,  1.12it/s]
  4%|▎         | 9/250 [00:15<02:37,  1.53it/s]
  4%|▍         | 10/250 [00:15<01:56,  2.06it/s]
  4%|▍         | 11/250 [00:16<02:03,  1.93it/s]
  5%|▍         | 12/250 [00:17<02:10,  1.82it/s]
  5%|▌         | 13/250 [00:17<02:22,  1.66it/s]
  6%|▌         | 14/250 [00:18<01:47,  2.19it/s]
  6%|▌         | 15/250 [00:18<02:20,  1.68it/s]
  7%|▋         | 17/250 [00:19<02:06,  1.84it/s]
  7%|▋         | 18/250 [00:21<02:59,  1.29it/s]
  8%|▊         | 19/250 [00:22<03:02,  1.26it/s]
  8%|▊         | 20/250 [00:22<02:23,  1.60it/s]
  9%|▉         | 22/250 [00:23<02:05,  1.82it/s]
  9%|▉         | 23/250 [00:23<01:59,  1.91it/s]
 10%|▉         | 24/250 [00:24<02:34,  1.46it/s]
 10%|█         | 25/250 [00:25<02:03,  1.82it/s]
 10%|█         | 26/250 [00:30<07:02,  1.89s/it]
 11%|█         | 27/250 [00:33<07:47,  2.10s/it]
 11%|█         | 28/250 [00:33<05:57,  1.61s/it]
 12%|█▏        | 29/250 [00:34<05:11,  1.41s/it]
 12%|█▏        | 30/250 [00:35<04:27,  1.21s/it]
 12%|█▏        | 31/250 [00:35<03:19,  1.10it/s]
 13%|█▎        | 32/250 [00:35<02:55,  1.24it/s]
 14%|█▎        | 34/250 [00:36<02:09,  1.67it/s]
 14%|█▍        | 35/250 [00:37<02:03,  1.75it/s]
 14%|█▍        | 36/250 [00:38<02:43,  1.31it/s]
 15%|█▍        | 37/250 [00:38<02:12,  1.61it/s]
 15%|█▌        | 38/250 [00:39<02:03,  1.72it/s]
 16%|█▌        | 39/250 [00:40<02:36,  1.35it/s]
 16%|█▌        | 40/250 [00:40<01:57,  1.78it/s]
 16%|█▋        | 41/250 [00:41<02:02,  1.70it/s]
 17%|█▋        | 42/250 [00:41<01:36,  2.15it/s]
 17%|█▋        | 43/250 [00:44<04:30,  1.31s/it]
 18%|█▊        | 44/250 [00:44<03:18,  1.04it/s]
 18%|█▊        | 45/250 [00:45<02:48,  1.22it/s]
 18%|█▊        | 46/250 [00:45<02:16,  1.50it/s]
 19%|█▉        | 47/250 [00:46<03:01,  1.12it/s]
 19%|█▉        | 48/250 [00:47<02:21,  1.42it/s]
 20%|█▉        | 49/250 [00:47<02:00,  1.67it/s]
 20%|██        | 50/250 [00:48<02:14,  1.49it/s]
 21%|██        | 52/250 [00:49<02:23,  1.38it/s]
 22%|██▏       | 54/250 [00:50<01:37,  2.00it/s]
 22%|██▏       | 55/250 [00:50<01:47,  1.82it/s]
 22%|██▏       | 56/250 [00:51<02:03,  1.57it/s]
 23%|██▎       | 57/250 [00:52<02:23,  1.35it/s]
 23%|██▎       | 58/250 [00:53<02:03,  1.55it/s]
 24%|██▎       | 59/250 [00:55<02:59,  1.06it/s]
 24%|██▍       | 61/250 [00:55<01:55,  1.64it/s]
 25%|██▍       | 62/250 [00:56<02:01,  1.54it/s]
 25%|██▌       | 63/250 [00:57<02:11,  1.43it/s]
 26%|██▌       | 64/250 [00:58<02:51,  1.08it/s]
 26%|██▌       | 65/250 [00:59<02:31,  1.22it/s]
 26%|██▋       | 66/250 [00:59<01:59,  1.55it/s]
 27%|██▋       | 67/250 [01:00<02:35,  1.18it/s]
 27%|██▋       | 68/250 [01:01<02:25,  1.25it/s]
 28%|██▊       | 70/250 [01:01<01:26,  2.08it/s]
 28%|██▊       | 71/250 [01:01<01:15,  2.37it/s]
 29%|██▉       | 73/250 [01:01<00:49,  3.58it/s]
 30%|███       | 75/250 [01:02<00:40,  4.28it/s]
 30%|███       | 76/250 [01:02<00:44,  3.88it/s]
 31%|███       | 77/250 [01:04<01:39,  1.75it/s]
 31%|███       | 78/250 [01:04<01:31,  1.89it/s]
 32%|███▏      | 79/250 [01:04<01:13,  2.34it/s]
 32%|███▏      | 80/250 [01:07<03:21,  1.19s/it]
 32%|███▏      | 81/250 [01:08<03:03,  1.09s/it]
 33%|███▎      | 82/250 [01:09<02:26,  1.15it/s]
 33%|███▎      | 83/250 [01:10<02:31,  1.10it/s]
 34%|███▎      | 84/250 [01:11<02:36,  1.06it/s]
 34%|███▍      | 85/250 [01:11<01:55,  1.43it/s]
 35%|███▍      | 87/250 [01:11<01:10,  2.32it/s]
 35%|███▌      | 88/250 [01:12<01:35,  1.69it/s]
 36%|███▌      | 90/250 [01:13<01:25,  1.86it/s]
 36%|███▋      | 91/250 [01:13<01:21,  1.96it/s]
 37%|███▋      | 92/250 [01:15<02:14,  1.17it/s]
 37%|███▋      | 93/250 [01:16<01:54,  1.37it/s]
 38%|███▊      | 94/250 [01:17<02:22,  1.10it/s]
 38%|███▊      | 95/250 [01:20<03:32,  1.37s/it]
 38%|███▊      | 96/250 [01:21<03:12,  1.25s/it]
 39%|███▉      | 97/250 [01:21<02:43,  1.07s/it]
 39%|███▉      | 98/250 [01:22<02:11,  1.15it/s]
 40%|███▉      | 99/250 [01:22<01:40,  1.51it/s]
 40%|████      | 100/250 [01:22<01:17,  1.94it/s]
 40%|████      | 101/250 [01:23<01:18,  1.89it/s]
 41%|████      | 102/250 [01:23<01:02,  2.37it/s]
 41%|████      | 103/250 [01:28<04:45,  1.94s/it]
 42%|████▏     | 104/250 [01:28<03:26,  1.41s/it]
 42%|████▏     | 105/250 [01:30<03:50,  1.59s/it]
 42%|████▏     | 106/250 [01:31<02:59,  1.25s/it]
 43%|████▎     | 107/250 [01:32<02:58,  1.25s/it]
 43%|████▎     | 108/250 [01:32<02:16,  1.04it/s]
 44%|████▍     | 110/250 [01:33<01:19,  1.75it/s]
 44%|████▍     | 111/250 [01:33<01:11,  1.94it/s]
 45%|████▍     | 112/250 [01:36<02:26,  1.06s/it]
 45%|████▌     | 113/250 [01:36<01:56,  1.17it/s]
 46%|████▌     | 114/250 [01:36<01:44,  1.30it/s]
 46%|████▌     | 115/250 [01:37<01:56,  1.16it/s]
 47%|████▋     | 117/250 [01:38<01:14,  1.78it/s]
 47%|████▋     | 118/250 [01:39<01:37,  1.36it/s]
 48%|████▊     | 119/250 [01:40<01:42,  1.28it/s]
 48%|████▊     | 120/250 [01:41<01:57,  1.11it/s]
 48%|████▊     | 121/250 [01:45<03:22,  1.57s/it]
 49%|████▉     | 123/250 [01:45<02:08,  1.01s/it]
 50%|████▉     | 124/250 [01:47<02:34,  1.23s/it]
 50%|█████     | 125/250 [01:48<02:10,  1.04s/it]
 50%|█████     | 126/250 [01:48<01:47,  1.15it/s]
 51%|█████     | 127/250 [01:50<02:11,  1.07s/it]
 52%|█████▏    | 129/250 [01:50<01:33,  1.30it/s]
 52%|█████▏    | 130/250 [01:51<01:16,  1.56it/s]
 52%|█████▏    | 131/250 [01:51<01:11,  1.66it/s]
 53%|█████▎    | 132/250 [01:52<01:28,  1.33it/s]
 53%|█████▎    | 133/250 [01:52<01:10,  1.65it/s]
 54%|█████▎    | 134/250 [01:53<01:19,  1.46it/s]
 54%|█████▍    | 135/250 [01:54<01:01,  1.88it/s]
 54%|█████▍    | 136/250 [01:55<01:27,  1.30it/s]
 55%|█████▌    | 138/250 [01:55<00:54,  2.05it/s]
 56%|█████▌    | 139/250 [01:56<00:56,  1.98it/s]
 56%|█████▌    | 140/250 [01:56<00:59,  1.86it/s]
 56%|█████▋    | 141/250 [01:57<01:15,  1.45it/s]
 57%|█████▋    | 142/250 [01:59<01:25,  1.26it/s]
 57%|█████▋    | 143/250 [02:00<01:50,  1.03s/it]
 58%|█████▊    | 144/250 [02:01<01:49,  1.04s/it]
 58%|█████▊    | 146/250 [02:02<01:09,  1.49it/s]
 59%|█████▉    | 148/250 [02:02<00:46,  2.21it/s]
 60%|█████▉    | 149/250 [02:02<00:44,  2.27it/s]
 60%|██████    | 151/250 [02:04<00:55,  1.79it/s]
 61%|██████    | 152/250 [02:06<01:35,  1.03it/s]
 61%|██████    | 153/250 [02:08<01:59,  1.23s/it]
 62%|██████▏   | 154/250 [02:09<01:32,  1.04it/s]
 62%|██████▏   | 155/250 [02:11<02:06,  1.33s/it]
 62%|██████▏   | 156/250 [02:11<01:40,  1.07s/it]
 63%|██████▎   | 157/250 [02:12<01:20,  1.15it/s]
 63%|██████▎   | 158/250 [02:12<01:03,  1.45it/s]
 64%|██████▎   | 159/250 [02:12<00:59,  1.53it/s]
 64%|██████▍   | 160/250 [02:15<01:39,  1.10s/it]
 64%|██████▍   | 161/250 [02:15<01:19,  1.13it/s]
 65%|██████▍   | 162/250 [02:17<01:37,  1.11s/it]
 65%|██████▌   | 163/250 [02:17<01:10,  1.23it/s]
 66%|██████▌   | 164/250 [02:17<00:52,  1.65it/s]
 66%|██████▋   | 166/250 [02:17<00:34,  2.41it/s]
 67%|██████▋   | 167/250 [02:18<00:39,  2.12it/s]
 67%|██████▋   | 168/250 [02:19<00:59,  1.39it/s]
 68%|██████▊   | 169/250 [02:20<00:52,  1.56it/s]
 68%|██████▊   | 170/250 [02:21<01:03,  1.26it/s]
 68%|██████▊   | 171/250 [02:21<00:53,  1.48it/s]
 69%|██████▉   | 172/250 [02:24<01:47,  1.38s/it]
 69%|██████▉   | 173/250 [02:26<01:55,  1.50s/it]
 70%|███████   | 175/250 [02:26<01:04,  1.16it/s]
 70%|███████   | 176/250 [02:27<01:00,  1.22it/s]
 71%|███████   | 177/250 [02:27<00:48,  1.52it/s]
 71%|███████   | 178/250 [02:27<00:38,  1.89it/s]
 72%|███████▏  | 179/250 [02:28<00:32,  2.21it/s]
 72%|███████▏  | 180/250 [02:28<00:25,  2.76it/s]
 72%|███████▏  | 181/250 [02:29<00:37,  1.83it/s]
 73%|███████▎  | 182/250 [02:30<00:43,  1.55it/s]
 73%|███████▎  | 183/250 [02:31<00:46,  1.45it/s]
 74%|███████▎  | 184/250 [02:31<00:37,  1.77it/s]
 74%|███████▍  | 185/250 [02:34<01:33,  1.43s/it]
 74%|███████▍  | 186/250 [02:35<01:16,  1.19s/it]
 75%|███████▍  | 187/250 [02:36<01:14,  1.19s/it]
 75%|███████▌  | 188/250 [02:36<00:55,  1.12it/s]
 76%|███████▌  | 189/250 [02:37<00:58,  1.05it/s]
 76%|███████▌  | 190/250 [02:39<01:10,  1.18s/it]
 76%|███████▋  | 191/250 [02:39<00:53,  1.10it/s]
 77%|███████▋  | 192/250 [02:40<00:48,  1.20it/s]
 77%|███████▋  | 193/250 [02:41<00:44,  1.29it/s]
 78%|███████▊  | 194/250 [02:41<00:34,  1.62it/s]
 78%|███████▊  | 195/250 [02:42<00:38,  1.43it/s]
 78%|███████▊  | 196/250 [02:43<00:43,  1.25it/s]
 79%|███████▉  | 197/250 [02:43<00:31,  1.69it/s]
 80%|███████▉  | 199/250 [02:43<00:17,  2.92it/s]
 80%|████████  | 200/250 [02:45<00:32,  1.54it/s]
 80%|████████  | 201/250 [02:46<00:40,  1.20it/s]
 81%|████████  | 202/250 [02:46<00:33,  1.45it/s]
 81%|████████  | 203/250 [02:47<00:30,  1.57it/s]
 82%|████████▏ | 204/250 [02:50<00:57,  1.24s/it]
 82%|████████▏ | 205/250 [02:50<00:46,  1.04s/it]
 82%|████████▏ | 206/250 [02:50<00:35,  1.24it/s]
 83%|████████▎ | 207/250 [02:51<00:27,  1.59it/s]
 84%|████████▎ | 209/250 [02:51<00:20,  1.97it/s]
 84%|████████▍ | 211/250 [02:54<00:33,  1.15it/s]
 85%|████████▍ | 212/250 [02:55<00:34,  1.09it/s]
 85%|████████▌ | 213/250 [02:56<00:29,  1.26it/s]
 86%|████████▌ | 214/250 [02:57<00:34,  1.05it/s]
 86%|████████▌ | 215/250 [02:57<00:26,  1.35it/s]
 86%|████████▋ | 216/250 [02:58<00:29,  1.15it/s]
 87%|████████▋ | 217/250 [02:59<00:30,  1.10it/s]
 87%|████████▋ | 218/250 [03:00<00:28,  1.11it/s]
 88%|████████▊ | 219/250 [03:01<00:22,  1.37it/s]
 88%|████████▊ | 220/250 [03:02<00:26,  1.13it/s]
 88%|████████▊ | 221/250 [03:02<00:22,  1.30it/s]
 89%|████████▉ | 222/250 [03:04<00:29,  1.04s/it]
 89%|████████▉ | 223/250 [03:05<00:25,  1.04it/s]
 90%|████████▉ | 224/250 [03:05<00:18,  1.40it/s]
 90%|█████████ | 225/250 [03:06<00:20,  1.21it/s]
 90%|█████████ | 226/250 [03:07<00:19,  1.22it/s]
 91%|█████████ | 227/250 [03:07<00:17,  1.31it/s]
 91%|█████████ | 228/250 [03:08<00:15,  1.38it/s]
 92%|█████████▏| 229/250 [03:08<00:12,  1.65it/s]
 92%|█████████▏| 230/250 [03:09<00:13,  1.48it/s]
 92%|█████████▏| 231/250 [03:09<00:09,  1.97it/s]
 93%|█████████▎| 232/250 [03:10<00:09,  1.90it/s]
 93%|█████████▎| 233/250 [03:10<00:07,  2.22it/s]
 94%|█████████▎| 234/250 [03:11<00:08,  1.87it/s]
 94%|█████████▍| 235/250 [03:11<00:07,  2.02it/s]
 94%|█████████▍| 236/250 [03:12<00:08,  1.71it/s]
 95%|█████████▍| 237/250 [03:14<00:11,  1.12it/s]
 95%|█████████▌| 238/250 [03:15<00:10,  1.12it/s]
 96%|█████████▌| 239/250 [03:15<00:09,  1.20it/s]
 96%|█████████▌| 240/250 [03:18<00:12,  1.28s/it]
 96%|█████████▋| 241/250 [03:18<00:08,  1.08it/s]
 97%|█████████▋| 243/250 [03:18<00:04,  1.62it/s]
 98%|█████████▊| 244/250 [03:19<00:03,  1.85it/s]
 98%|█████████▊| 245/250 [03:19<00:02,  2.34it/s]
 99%|█████████▉| 247/250 [03:19<00:00,  3.46it/s]
 99%|█████████▉| 248/250 [03:21<00:01,  1.36it/s]
100%|█████████▉| 249/250 [03:26<00:01,  1.71s/it]
100%|██████████| 250/250 [03:27<00:00,  1.49s/it]
100%|██████████| 250/250 [03:27<00:00,  1.21it/s]
2025-05-16 14:26:07,997 - modnet - INFO - Loss per individual: ind 0: 44.981 	ind 1: 37.099 	ind 2: 38.842 	ind 3: 37.216 	ind 4: 39.023 	ind 5: 39.083 	ind 6: 40.124 	ind 7: 44.099 	ind 8: 38.904 	ind 9: 38.997 	ind 10: 37.619 	ind 11: 38.652 	ind 12: 44.649 	ind 13: 39.159 	ind 14: 38.658 	ind 15: 38.483 	ind 16: 40.167 	ind 17: 39.140 	ind 18: 40.132 	ind 19: 50.907 	ind 20: 50.280 	ind 21: 39.424 	ind 22: 38.809 	ind 23: 44.174 	ind 24: 38.819 	ind 25: 43.336 	ind 26: 48.772 	ind 27: 39.492 	ind 28: 38.541 	ind 29: 49.248 	ind 30: 40.600 	ind 31: 38.564 	ind 32: 43.826 	ind 33: 37.366 	ind 34: 41.186 	ind 35: 38.161 	ind 36: 37.672 	ind 37: 40.031 	ind 38: 38.368 	ind 39: 40.125 	ind 40: 44.716 	ind 41: 40.459 	ind 42: 48.720 	ind 43: 43.013 	ind 44: 37.726 	ind 45: 39.377 	ind 46: 40.145 	ind 47: 45.806 	ind 48: 44.028 	ind 49: 38.437 	
2025-05-16 14:26:07,999 - modnet - INFO - After generation 18:
2025-05-16 14:26:07,999 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 256, 'fraction1': 0.25, 'fraction2': 1, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 360, 'n_layers': 3, 'layer_mults': None}
2025-05-16 14:26:07,999 - modnet - INFO - Best validation loss: 36.0490
2025-05-16 14:26:07,999 - modnet - INFO - Generation number 19

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:13<55:33, 13.39s/it]
  1%|          | 2/250 [00:17<31:49,  7.70s/it]
  1%|          | 3/250 [00:17<17:29,  4.25s/it]
  2%|▏         | 4/250 [00:17<10:48,  2.63s/it]
  2%|▏         | 5/250 [00:18<08:28,  2.07s/it]
  2%|▏         | 6/250 [00:19<07:15,  1.78s/it]
  3%|▎         | 7/250 [00:20<05:54,  1.46s/it]
  3%|▎         | 8/250 [00:20<04:08,  1.03s/it]
  4%|▍         | 10/250 [00:21<03:02,  1.32it/s]
  4%|▍         | 11/250 [00:21<02:21,  1.69it/s]
  5%|▍         | 12/250 [00:21<01:50,  2.15it/s]
  5%|▌         | 13/250 [00:22<01:47,  2.20it/s]
  6%|▌         | 14/250 [00:22<01:50,  2.14it/s]
  6%|▌         | 15/250 [00:22<01:34,  2.48it/s]
  6%|▋         | 16/250 [00:23<01:36,  2.42it/s]
  7%|▋         | 17/250 [00:23<01:38,  2.35it/s]
  7%|▋         | 18/250 [00:23<01:16,  3.02it/s]
  8%|▊         | 19/250 [00:24<01:18,  2.93it/s]
  8%|▊         | 20/250 [00:24<01:11,  3.24it/s]
  9%|▉         | 22/250 [00:24<00:47,  4.75it/s]
 10%|▉         | 24/250 [00:25<01:20,  2.79it/s]
 10%|█         | 25/250 [00:26<01:11,  3.15it/s]
 10%|█         | 26/250 [00:26<01:05,  3.44it/s]
 11%|█         | 27/250 [00:27<01:54,  1.95it/s]
 11%|█         | 28/250 [00:28<02:23,  1.55it/s]
 12%|█▏        | 29/250 [00:29<02:35,  1.42it/s]
 12%|█▏        | 30/250 [00:29<02:00,  1.82it/s]
 12%|█▏        | 31/250 [00:29<01:32,  2.37it/s]
 13%|█▎        | 32/250 [00:29<01:15,  2.89it/s]
 13%|█▎        | 33/250 [00:29<01:01,  3.53it/s]
 14%|█▎        | 34/250 [00:35<06:13,  1.73s/it]
 14%|█▍        | 35/250 [00:36<05:48,  1.62s/it]
 14%|█▍        | 36/250 [00:38<05:50,  1.64s/it]
 15%|█▍        | 37/250 [00:38<04:18,  1.21s/it]
 15%|█▌        | 38/250 [00:38<03:34,  1.01s/it]
 16%|█▌        | 39/250 [00:39<02:57,  1.19it/s]
 16%|█▌        | 40/250 [00:39<02:15,  1.55it/s]
 16%|█▋        | 41/250 [00:40<02:24,  1.45it/s]
 17%|█▋        | 42/250 [00:41<02:51,  1.21it/s]
 17%|█▋        | 43/250 [00:43<03:43,  1.08s/it]
 18%|█▊        | 45/250 [00:43<02:09,  1.58it/s]
 18%|█▊        | 46/250 [00:44<02:15,  1.51it/s]
 19%|█▉        | 47/250 [00:45<02:51,  1.18it/s]
 19%|█▉        | 48/250 [00:47<03:53,  1.16s/it]
 20%|█▉        | 49/250 [00:48<03:27,  1.03s/it]
 20%|██        | 50/250 [00:48<02:59,  1.12it/s]
 20%|██        | 51/250 [00:51<04:57,  1.50s/it]
 21%|██        | 52/250 [00:52<03:54,  1.18s/it]
 21%|██        | 53/250 [00:53<04:05,  1.25s/it]
 22%|██▏       | 54/250 [00:53<03:14,  1.01it/s]
 22%|██▏       | 55/250 [00:55<04:01,  1.24s/it]
 23%|██▎       | 57/250 [00:55<02:20,  1.37it/s]
 23%|██▎       | 58/250 [00:56<02:11,  1.46it/s]
 24%|██▎       | 59/250 [00:56<01:42,  1.87it/s]
 24%|██▍       | 60/250 [00:56<01:35,  1.99it/s]
 25%|██▍       | 62/250 [00:57<01:18,  2.40it/s]
 25%|██▌       | 63/250 [00:57<01:06,  2.79it/s]
 26%|██▌       | 64/250 [00:59<02:14,  1.38it/s]
 26%|██▌       | 65/250 [01:00<02:10,  1.42it/s]
 26%|██▋       | 66/250 [01:00<01:40,  1.83it/s]
 27%|██▋       | 67/250 [01:01<02:31,  1.21it/s]
 27%|██▋       | 68/250 [01:02<01:59,  1.52it/s]
 28%|██▊       | 69/250 [01:02<02:07,  1.42it/s]
 28%|██▊       | 70/250 [01:03<02:21,  1.28it/s]
 28%|██▊       | 71/250 [01:05<02:45,  1.08it/s]
 29%|██▉       | 72/250 [01:06<03:26,  1.16s/it]
 29%|██▉       | 73/250 [01:08<03:41,  1.25s/it]
 30%|██▉       | 74/250 [01:08<02:55,  1.00it/s]
 30%|███       | 76/250 [01:09<01:55,  1.50it/s]
 31%|███       | 77/250 [01:10<02:19,  1.24it/s]
 31%|███       | 78/250 [01:11<02:11,  1.31it/s]
 32%|███▏      | 79/250 [01:11<01:40,  1.70it/s]
 32%|███▏      | 80/250 [01:15<04:13,  1.49s/it]
 32%|███▏      | 81/250 [01:15<03:22,  1.20s/it]
 33%|███▎      | 82/250 [01:16<02:59,  1.07s/it]
 33%|███▎      | 83/250 [01:17<02:40,  1.04it/s]
 34%|███▎      | 84/250 [01:17<02:09,  1.28it/s]
 34%|███▍      | 85/250 [01:17<02:00,  1.37it/s]
 34%|███▍      | 86/250 [01:18<02:10,  1.26it/s]
 35%|███▍      | 87/250 [01:19<01:49,  1.48it/s]
 35%|███▌      | 88/250 [01:19<01:43,  1.56it/s]
 36%|███▌      | 89/250 [01:20<01:21,  1.97it/s]
 36%|███▌      | 90/250 [01:20<01:07,  2.36it/s]
 36%|███▋      | 91/250 [01:21<02:00,  1.32it/s]
 37%|███▋      | 92/250 [01:22<01:43,  1.53it/s]
 37%|███▋      | 93/250 [01:22<01:37,  1.61it/s]
 38%|███▊      | 94/250 [01:24<02:26,  1.06it/s]
 38%|███▊      | 95/250 [01:26<02:56,  1.14s/it]
 39%|███▉      | 97/250 [01:26<01:38,  1.55it/s]
 39%|███▉      | 98/250 [01:26<01:30,  1.68it/s]
 40%|███▉      | 99/250 [01:27<01:21,  1.85it/s]
 40%|████      | 100/250 [01:28<01:46,  1.40it/s]
 40%|████      | 101/250 [01:29<02:05,  1.19it/s]
 41%|████      | 102/250 [01:29<01:53,  1.31it/s]
 41%|████      | 103/250 [01:30<01:54,  1.29it/s]
 42%|████▏     | 104/250 [01:31<01:29,  1.63it/s]
 42%|████▏     | 105/250 [01:31<01:30,  1.60it/s]
 42%|████▏     | 106/250 [01:33<02:04,  1.16it/s]
 43%|████▎     | 107/250 [01:37<04:31,  1.90s/it]
 43%|████▎     | 108/250 [01:38<03:53,  1.64s/it]
 44%|████▎     | 109/250 [01:39<03:10,  1.35s/it]
 44%|████▍     | 110/250 [01:39<02:28,  1.06s/it]
 44%|████▍     | 111/250 [01:41<03:19,  1.44s/it]
 45%|████▌     | 113/250 [01:42<02:10,  1.05it/s]
 46%|████▌     | 114/250 [01:43<01:55,  1.18it/s]
 46%|████▌     | 115/250 [01:43<01:54,  1.18it/s]
 47%|████▋     | 117/250 [01:45<01:38,  1.35it/s]
 47%|████▋     | 118/250 [01:46<01:57,  1.12it/s]
 48%|████▊     | 119/250 [01:46<01:33,  1.40it/s]
 48%|████▊     | 120/250 [01:46<01:12,  1.80it/s]
 48%|████▊     | 121/250 [01:47<01:04,  2.00it/s]
 49%|████▉     | 122/250 [01:47<00:50,  2.55it/s]
 49%|████▉     | 123/250 [01:48<01:11,  1.77it/s]
 50%|████▉     | 124/250 [01:49<01:21,  1.54it/s]
 50%|█████     | 125/250 [01:49<01:04,  1.95it/s]
 50%|█████     | 126/250 [01:52<02:22,  1.15s/it]
 51%|█████     | 128/250 [01:52<01:25,  1.43it/s]
 52%|█████▏    | 129/250 [01:52<01:10,  1.71it/s]
 52%|█████▏    | 130/250 [01:53<01:17,  1.55it/s]
 52%|█████▏    | 131/250 [01:53<01:07,  1.77it/s]
 53%|█████▎    | 132/250 [01:54<01:25,  1.38it/s]
 53%|█████▎    | 133/250 [01:56<01:47,  1.09it/s]
 54%|█████▎    | 134/250 [01:56<01:25,  1.36it/s]
 54%|█████▍    | 135/250 [01:57<01:24,  1.36it/s]
 55%|█████▍    | 137/250 [01:57<00:48,  2.35it/s]
 55%|█████▌    | 138/250 [01:58<01:06,  1.67it/s]
 56%|█████▌    | 139/250 [01:58<00:55,  2.01it/s]
 56%|█████▌    | 140/250 [01:58<00:43,  2.52it/s]
 57%|█████▋    | 142/250 [01:59<00:48,  2.24it/s]
 57%|█████▋    | 143/250 [02:00<00:47,  2.24it/s]
 58%|█████▊    | 144/250 [02:00<00:46,  2.27it/s]
 58%|█████▊    | 145/250 [02:01<00:46,  2.26it/s]
 58%|█████▊    | 146/250 [02:01<00:45,  2.26it/s]
 59%|█████▉    | 147/250 [02:01<00:36,  2.86it/s]
 60%|█████▉    | 149/250 [02:02<00:24,  4.11it/s]
 60%|██████    | 150/250 [02:02<00:36,  2.76it/s]
 60%|██████    | 151/250 [02:03<00:34,  2.87it/s]
 61%|██████    | 153/250 [02:03<00:31,  3.03it/s]
 62%|██████▏   | 154/250 [02:07<01:47,  1.12s/it]
 62%|██████▏   | 155/250 [02:07<01:28,  1.07it/s]
 62%|██████▏   | 156/250 [02:08<01:12,  1.29it/s]
 63%|██████▎   | 157/250 [02:12<02:33,  1.65s/it]
 63%|██████▎   | 158/250 [02:12<02:05,  1.36s/it]
 64%|██████▎   | 159/250 [02:13<01:37,  1.07s/it]
 64%|██████▍   | 160/250 [02:13<01:20,  1.11it/s]
 64%|██████▍   | 161/250 [02:13<01:07,  1.32it/s]
 65%|██████▍   | 162/250 [02:14<01:01,  1.44it/s]
 66%|██████▌   | 164/250 [02:17<01:38,  1.14s/it]
 66%|██████▌   | 165/250 [02:18<01:19,  1.07it/s]
 66%|██████▋   | 166/250 [02:19<01:37,  1.16s/it]
 67%|██████▋   | 167/250 [02:20<01:21,  1.02it/s]
 67%|██████▋   | 168/250 [02:20<01:01,  1.34it/s]
 68%|██████▊   | 169/250 [02:22<01:19,  1.02it/s]
 68%|██████▊   | 170/250 [02:22<01:04,  1.23it/s]
 68%|██████▊   | 171/250 [02:22<00:50,  1.57it/s]
 69%|██████▉   | 172/250 [02:26<01:55,  1.48s/it]
 69%|██████▉   | 173/250 [02:27<01:40,  1.30s/it]
 70%|██████▉   | 174/250 [02:27<01:24,  1.11s/it]
 70%|███████   | 175/250 [02:28<01:17,  1.04s/it]
 70%|███████   | 176/250 [02:29<01:06,  1.12it/s]
 71%|███████   | 177/250 [02:29<00:55,  1.31it/s]
 71%|███████   | 178/250 [02:29<00:45,  1.59it/s]
 72%|███████▏  | 180/250 [02:30<00:33,  2.10it/s]
 72%|███████▏  | 181/250 [02:30<00:28,  2.46it/s]
 73%|███████▎  | 182/250 [02:31<00:28,  2.40it/s]
 73%|███████▎  | 183/250 [02:31<00:28,  2.32it/s]
 74%|███████▎  | 184/250 [02:32<00:37,  1.75it/s]
 74%|███████▍  | 185/250 [02:33<00:44,  1.47it/s]
 74%|███████▍  | 186/250 [02:33<00:32,  1.94it/s]
 75%|███████▍  | 187/250 [02:33<00:26,  2.40it/s]
 75%|███████▌  | 188/250 [02:34<00:23,  2.69it/s]
 76%|███████▌  | 189/250 [02:37<01:09,  1.14s/it]
 76%|███████▌  | 190/250 [02:37<00:53,  1.11it/s]
 76%|███████▋  | 191/250 [02:37<00:45,  1.31it/s]
 77%|███████▋  | 192/250 [02:38<00:49,  1.17it/s]
 77%|███████▋  | 193/250 [02:39<00:38,  1.47it/s]
 78%|███████▊  | 194/250 [02:39<00:34,  1.64it/s]
 78%|███████▊  | 195/250 [02:42<01:10,  1.28s/it]
 78%|███████▊  | 196/250 [02:44<01:21,  1.51s/it]
 79%|███████▉  | 198/250 [02:46<01:06,  1.27s/it]
 80%|███████▉  | 199/250 [02:47<00:58,  1.14s/it]
 80%|████████  | 200/250 [02:47<00:46,  1.07it/s]
 80%|████████  | 201/250 [02:47<00:36,  1.33it/s]
 81%|████████  | 202/250 [02:49<00:45,  1.06it/s]
 81%|████████  | 203/250 [02:49<00:40,  1.17it/s]
 82%|████████▏ | 205/250 [02:50<00:22,  2.01it/s]
 82%|████████▏ | 206/250 [02:50<00:19,  2.27it/s]
 83%|████████▎ | 208/250 [02:51<00:19,  2.19it/s]
 84%|████████▎ | 209/250 [02:51<00:15,  2.59it/s]
 84%|████████▍ | 211/250 [02:53<00:21,  1.83it/s]
 85%|████████▍ | 212/250 [02:53<00:19,  1.96it/s]
 85%|████████▌ | 213/250 [02:53<00:16,  2.24it/s]
 86%|████████▌ | 214/250 [02:53<00:13,  2.60it/s]
 86%|████████▌ | 215/250 [02:54<00:16,  2.11it/s]
 86%|████████▋ | 216/250 [02:54<00:14,  2.36it/s]
 87%|████████▋ | 217/250 [02:55<00:12,  2.66it/s]
 87%|████████▋ | 218/250 [02:55<00:16,  1.96it/s]
 88%|████████▊ | 219/250 [02:56<00:19,  1.62it/s]
 88%|████████▊ | 220/250 [02:57<00:15,  1.93it/s]
 88%|████████▊ | 221/250 [02:58<00:20,  1.45it/s]
 89%|████████▉ | 222/250 [02:58<00:18,  1.48it/s]
 89%|████████▉ | 223/250 [03:00<00:28,  1.07s/it]
 90%|████████▉ | 224/250 [03:01<00:24,  1.08it/s]
 90%|█████████ | 225/250 [03:01<00:18,  1.39it/s]
 90%|█████████ | 226/250 [03:02<00:21,  1.14it/s]
 91%|█████████ | 227/250 [03:05<00:29,  1.29s/it]
 91%|█████████ | 228/250 [03:07<00:33,  1.52s/it]
 92%|█████████▏| 229/250 [03:07<00:24,  1.16s/it]
 92%|█████████▏| 230/250 [03:08<00:23,  1.19s/it]
 93%|█████████▎| 232/250 [03:09<00:14,  1.25it/s]
 93%|█████████▎| 233/250 [03:10<00:15,  1.07it/s]
 94%|█████████▎| 234/250 [03:11<00:14,  1.12it/s]
 94%|█████████▍| 235/250 [03:13<00:15,  1.06s/it]
 94%|█████████▍| 236/250 [03:13<00:12,  1.14it/s]
 95%|█████████▍| 237/250 [03:13<00:09,  1.44it/s]
 95%|█████████▌| 238/250 [03:15<00:12,  1.06s/it]
 96%|█████████▌| 239/250 [03:16<00:09,  1.13it/s]
 96%|█████████▌| 240/250 [03:16<00:08,  1.20it/s]
 97%|█████████▋| 242/250 [03:19<00:07,  1.03it/s]
 97%|█████████▋| 243/250 [03:19<00:06,  1.15it/s]
 98%|█████████▊| 244/250 [03:21<00:06,  1.08s/it]
 98%|█████████▊| 245/250 [03:22<00:04,  1.02it/s]
 98%|█████████▊| 246/250 [03:22<00:02,  1.34it/s]
 99%|█████████▉| 247/250 [03:22<00:02,  1.45it/s]
 99%|█████████▉| 248/250 [03:24<00:02,  1.11s/it]
100%|█████████▉| 249/250 [03:25<00:00,  1.13it/s]
100%|██████████| 250/250 [03:28<00:00,  1.65s/it]
100%|██████████| 250/250 [03:28<00:00,  1.20it/s]
2025-05-16 14:29:36,899 - modnet - INFO - Loss per individual: ind 0: 39.371 	ind 1: 43.341 	ind 2: 44.711 	ind 3: 38.842 	ind 4: 43.740 	ind 5: 40.182 	ind 6: 43.916 	ind 7: 42.434 	ind 8: 43.509 	ind 9: 40.164 	ind 10: 39.092 	ind 11: 39.141 	ind 12: 45.197 	ind 13: 37.963 	ind 14: 38.282 	ind 15: 49.685 	ind 16: 37.502 	ind 17: 42.592 	ind 18: 37.924 	ind 19: 38.551 	ind 20: 38.565 	ind 21: 38.208 	ind 22: 40.737 	ind 23: 41.548 	ind 24: 39.709 	ind 25: 47.703 	ind 26: 37.901 	ind 27: 42.745 	ind 28: 37.794 	ind 29: 43.808 	ind 30: 38.242 	ind 31: 75.403 	ind 32: 39.612 	ind 33: 42.421 	ind 34: 38.655 	ind 35: 39.027 	ind 36: 36.433 	ind 37: 42.560 	ind 38: 38.005 	ind 39: 37.830 	ind 40: 40.908 	ind 41: 37.517 	ind 42: 39.770 	ind 43: 40.313 	ind 44: 53.594 	ind 45: 39.187 	ind 46: 46.094 	ind 47: 40.689 	ind 48: 38.247 	ind 49: 42.981 	
2025-05-16 14:29:36,900 - modnet - INFO - After generation 19:
2025-05-16 14:29:36,901 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 256, 'fraction1': 0.25, 'fraction2': 1, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 360, 'n_layers': 3, 'layer_mults': None}
2025-05-16 14:29:36,901 - modnet - INFO - Best validation loss: 36.0490
2025-05-16 14:29:36,901 - modnet - INFO - Starting grid search tuning over all layers.

  0%|          | 0/27 [00:00<?, ?it/s]
  4%|▎         | 1/27 [00:20<08:51, 20.43s/it]
  7%|▋         | 2/27 [00:25<04:48, 11.53s/it]
 11%|█         | 3/27 [00:26<02:42,  6.78s/it]
 15%|█▍        | 4/27 [00:27<01:37,  4.24s/it]
 22%|██▏       | 6/27 [00:27<00:45,  2.15s/it]
 26%|██▌       | 7/27 [00:29<00:39,  1.95s/it]
 30%|██▉       | 8/27 [00:31<00:41,  2.16s/it]
 37%|███▋      | 10/27 [00:32<00:23,  1.37s/it]
 41%|████      | 11/27 [00:33<00:18,  1.16s/it]
 44%|████▍     | 12/27 [00:33<00:14,  1.05it/s]
 48%|████▊     | 13/27 [00:34<00:11,  1.17it/s]
 52%|█████▏    | 14/27 [00:35<00:11,  1.12it/s]
 56%|█████▌    | 15/27 [00:35<00:09,  1.30it/s]
 59%|█████▉    | 16/27 [00:35<00:06,  1.62it/s]
 67%|██████▋   | 18/27 [00:36<00:04,  2.07it/s]
 70%|███████   | 19/27 [00:37<00:04,  1.97it/s]
 74%|███████▍  | 20/27 [00:37<00:03,  1.99it/s]
 78%|███████▊  | 21/27 [00:38<00:04,  1.36it/s]
 81%|████████▏ | 22/27 [00:39<00:03,  1.44it/s]
 89%|████████▉ | 24/27 [00:39<00:01,  2.30it/s]
 93%|█████████▎| 25/27 [00:40<00:00,  2.36it/s]
 96%|█████████▋| 26/27 [00:41<00:00,  1.78it/s]
100%|██████████| 27/27 [00:44<00:00,  1.25s/it]
100%|██████████| 27/27 [00:44<00:00,  1.64s/it]
2025-05-16 14:30:21,122 - modnet - INFO - Best layer multipliers: [1.2, 1.2, 0.8] with validation loss 35.2444
2025-05-16 14:30:22,537 - modnet - INFO - Final best model genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 256, 'fraction1': 0.25, 'fraction2': 1, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 360, 'n_layers': 3, 'layer_mults': [1.2, 1.2, 0.8]}
2025-05-16 14:30:22,537 - modnet - INFO - Final best model validation loss (from tuning): 35.2444
2025-05-16 14:30:24,548 - modnet - INFO - Model successfully saved as results/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_best_model_fold_1.pkl!
Saved best model for fold 1 to results/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_best_model_fold_1.pkl
2025-05-16 14:30:24.582647: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:24.595158: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
[Fold 1] MAE = 35.560 , MedianAE = 20.089 , MAPE = 6.015, √MSE = 72.262 
[Fold 1] MaxAE = 729.056 , slope = 0.96, R = 0.99
Fold 1 metrics saved.
Saved fold 1 complete results to results/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_fold_1_results.pkl
Training fold 2 ...
Processing fold 2 ...
2025-05-16 14:30:27,269 - modnet - INFO - Targets:
2025-05-16 14:30:27,270 - modnet - INFO - 1) target: regression
2025-05-16 14:30:28.512369: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:28.585016: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:28.641040: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:28.704723: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:28.782509: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:28.841262: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:28.899756: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:28.940520: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29,068 - modnet - INFO - Multiprocessing on 32 cores. Total of 128 cores available.
2025-05-16 14:30:29,068 - modnet - INFO - Generation number 0

  0%|          | 0/250 [00:00<?, ?it/s]2025-05-16 14:30:29.156560: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29.212503: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29.237947: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29.258101: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29.316573: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29.337002: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29.419109: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29.463534: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29.511921: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29.598640: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29.635239: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29.684430: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29.758790: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29.835889: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29.923598: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:29.975456: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:30.013107: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:30.048479: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:30.145856: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:30.173984: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:30.243080: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:30.339764: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:30.367494: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:30.452475: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 14:30:31.720696: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:31.721208: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:31.721245: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:31.721279: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:31.721560: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:31.797060: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:31.797502: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:31.797529: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:31.797561: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:31.798006: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:31.848384: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:31.848863: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:31.848893: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:31.848929: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:31.849361: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:31.912113: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:31.912612: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:31.912651: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:31.912688: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:31.913082: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:31.968698: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:31.969139: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:31.969169: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:31.969204: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:31.969631: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.022428: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.022898: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:32.022926: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:32.022958: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:32.023243: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.089219: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:32.091702: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.092191: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:32.092231: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:32.092269: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:32.092634: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.103119: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:32.137106: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.137552: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:32.137588: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:32.137620: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:32.137980: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.176923: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:32.191169: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:32.273805: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:32.288604: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:32.327278: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:32.328126: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.328604: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:32.328642: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:32.328676: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:32.329057: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.341177: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:32.349830: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:32.352131: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:32.394414: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:32.396615: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:32.416212: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.416653: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:32.416681: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:32.416713: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:32.417067: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.513336: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:32.514280: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.514760: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:32.514796: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:32.514829: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:32.515217: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.527137: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:32.547231: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:32.553401: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.553892: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:32.553926: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:32.553960: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:32.554293: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.561151: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:32.630293: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.630801: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:32.630838: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:32.630875: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:32.631226: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.700149: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:32.702226: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:32.772969: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.773480: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:32.773509: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:32.773542: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:32.773865: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.781529: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:32.790196: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:32.795106: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:32.808164: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:32.815126: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:32.817040: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:32.817849: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.818341: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:32.818369: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:32.818401: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:32.818643: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.895524: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:32.897440: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:32.902733: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.903222: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:32.903251: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:32.903285: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:32.903575: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.949485: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:32.950028: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:32.950068: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:32.950103: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:32.950377: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.004951: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.005439: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:33.005476: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:33.005510: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:33.005852: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.040263: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:33.042117: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:33.102531: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:33.104802: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:33.128617: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.129112: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:33.129144: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:33.129179: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:33.129495: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.180004: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:33.181749: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:33.182883: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.183383: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:33.183413: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:33.183445: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:33.183902: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.237108: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:33.239259: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:33.284677: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:33.286343: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:33.288495: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.289008: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:33.289042: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:33.289076: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:33.289396: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.332258: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.332789: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:33.332829: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:33.332866: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:33.333237: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.399922: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:33.400508: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.401013: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:33.401042: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:33.401076: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:33.401315: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.401538: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:33.455341: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:33.457396: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:33.531680: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.532179: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:33.532209: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:33.532242: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:33.532559: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.548086: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:33.549871: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:33.568922: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.569461: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:33.569501: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:33.569538: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:33.570024: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.596001: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:33.598116: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:33.628010: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.628518: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:33.628552: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:33.628584: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:33.628899: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.665316: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:33.667039: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:33.675329: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.675841: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:33.675869: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:33.675903: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:33.676223: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.727126: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.728029: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:33.728078: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:33.728118: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:33.728445: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.778833: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.779428: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:33.779480: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:33.779522: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:33.779843: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.785136: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:33.786772: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:33.819810: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:33.821584: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:33.839198: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.839672: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:33.839700: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:33.839731: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:33.840000: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:33.917249: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:33.919114: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:34.001541: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:34.003417: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:34.003764: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:34.004286: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:34.004320: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:34.004353: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:34.004793: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:34.065795: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:34.066424: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 14:30:34.066460: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 14:30:34.066496: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns269.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 14:30:34.066783: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 14:30:34.067290: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:34.069324: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:34.083286: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:34.097107: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:34.128318: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:34.130547: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:34.257334: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:34.259238: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz
2025-05-16 14:30:34.342298: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-16 14:30:34.344325: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445530000 Hz

  0%|          | 1/250 [00:09<39:59,  9.63s/it]
  1%|          | 3/250 [00:10<12:06,  2.94s/it]
  2%|▏         | 4/250 [00:10<08:07,  1.98s/it]
  2%|▏         | 5/250 [00:12<07:03,  1.73s/it]
  2%|▏         | 6/250 [00:12<05:23,  1.32s/it]
  3%|▎         | 7/250 [00:12<04:02,  1.00it/s]
  3%|▎         | 8/250 [00:13<02:55,  1.38it/s]
  4%|▎         | 9/250 [00:13<02:57,  1.36it/s]
  4%|▍         | 10/250 [00:14<02:17,  1.75it/s]
  4%|▍         | 11/250 [00:14<02:06,  1.88it/s]
  5%|▍         | 12/250 [00:15<02:26,  1.63it/s]
  5%|▌         | 13/250 [00:16<02:36,  1.52it/s]
  6%|▌         | 14/250 [00:16<02:17,  1.72it/s]
  6%|▌         | 15/250 [00:16<01:55,  2.04it/s]
  6%|▋         | 16/250 [00:17<02:32,  1.53it/s]
  7%|▋         | 17/250 [00:17<01:54,  2.04it/s]
  7%|▋         | 18/250 [00:18<01:28,  2.62it/s]
  8%|▊         | 20/250 [00:18<00:53,  4.26it/s]
  8%|▊         | 21/250 [00:18<01:20,  2.83it/s]
  9%|▉         | 22/250 [00:19<01:27,  2.59it/s]
  9%|▉         | 23/250 [00:19<01:12,  3.14it/s]
 10%|▉         | 24/250 [00:19<01:00,  3.74it/s]
 10%|█         | 26/250 [00:21<01:48,  2.07it/s]
 11%|█         | 27/250 [00:21<01:31,  2.43it/s]
 11%|█         | 28/250 [00:22<02:37,  1.41it/s]
 12%|█▏        | 29/250 [00:23<02:32,  1.45it/s]
 12%|█▏        | 30/250 [00:23<02:16,  1.62it/s]
 12%|█▏        | 31/250 [00:24<01:59,  1.83it/s]
 13%|█▎        | 33/250 [00:24<01:14,  2.91it/s]
 14%|█▎        | 34/250 [00:24<01:09,  3.09it/s]
 14%|█▍        | 35/250 [00:24<00:58,  3.65it/s]
 14%|█▍        | 36/250 [00:24<00:49,  4.34it/s]
 15%|█▍        | 37/250 [00:25<01:05,  3.23it/s]
 15%|█▌        | 38/250 [00:26<01:48,  1.96it/s]
 16%|█▌        | 39/250 [00:26<01:24,  2.51it/s]
 16%|█▌        | 40/250 [00:26<01:14,  2.82it/s]
 16%|█▋        | 41/250 [00:27<01:04,  3.24it/s]
 17%|█▋        | 43/250 [00:27<00:56,  3.65it/s]
 18%|█▊        | 44/250 [00:27<01:03,  3.24it/s]
 18%|█▊        | 45/250 [00:29<02:31,  1.35it/s]
 19%|█▉        | 47/250 [00:30<01:31,  2.21it/s]
 19%|█▉        | 48/250 [00:31<01:54,  1.76it/s]
 20%|█▉        | 49/250 [00:31<02:05,  1.61it/s]
 20%|██        | 50/250 [00:32<01:46,  1.89it/s]
 20%|██        | 51/250 [00:32<01:22,  2.40it/s]
 21%|██        | 52/250 [00:33<01:49,  1.80it/s]
 21%|██        | 53/250 [00:33<02:01,  1.63it/s]
 22%|██▏       | 54/250 [00:34<02:24,  1.35it/s]
 22%|██▏       | 55/250 [00:36<02:56,  1.11it/s]
 22%|██▏       | 56/250 [00:36<02:17,  1.41it/s]
 23%|██▎       | 57/250 [00:37<02:12,  1.45it/s]
 23%|██▎       | 58/250 [00:38<02:34,  1.24it/s]
 24%|██▍       | 60/250 [00:38<01:39,  1.91it/s]
 24%|██▍       | 61/250 [00:38<01:25,  2.20it/s]
 25%|██▍       | 62/250 [00:38<01:10,  2.65it/s]
 25%|██▌       | 63/250 [00:40<02:13,  1.40it/s]
 26%|██▌       | 64/250 [00:41<02:48,  1.10it/s]
 26%|██▌       | 65/250 [00:43<03:09,  1.02s/it]
 26%|██▋       | 66/250 [00:44<02:54,  1.06it/s]
 27%|██▋       | 67/250 [00:44<02:52,  1.06it/s]
 27%|██▋       | 68/250 [00:45<02:25,  1.25it/s]
 28%|██▊       | 69/250 [00:45<01:54,  1.59it/s]
 28%|██▊       | 70/250 [00:46<02:31,  1.19it/s]
 28%|██▊       | 71/250 [00:49<03:45,  1.26s/it]
 29%|██▉       | 73/250 [00:49<02:06,  1.39it/s]
 30%|███       | 75/250 [00:50<01:48,  1.61it/s]
 30%|███       | 76/250 [00:52<02:40,  1.08it/s]
 31%|███       | 78/250 [00:52<01:40,  1.70it/s]
 32%|███▏      | 80/250 [00:52<01:10,  2.41it/s]
 33%|███▎      | 82/250 [00:52<00:53,  3.13it/s]
 33%|███▎      | 83/250 [00:54<01:48,  1.54it/s]
 34%|███▎      | 84/250 [00:55<01:35,  1.74it/s]
 34%|███▍      | 85/250 [00:56<02:07,  1.30it/s]
 34%|███▍      | 86/250 [00:57<01:53,  1.44it/s]
 35%|███▍      | 87/250 [00:57<01:54,  1.43it/s]
 35%|███▌      | 88/250 [00:59<02:16,  1.18it/s]
 36%|███▌      | 89/250 [00:59<01:43,  1.55it/s]
 36%|███▌      | 90/250 [00:59<01:29,  1.78it/s]
 36%|███▋      | 91/250 [00:59<01:10,  2.25it/s]
 37%|███▋      | 92/250 [01:00<01:27,  1.81it/s]
 38%|███▊      | 94/250 [01:01<01:08,  2.26it/s]
 38%|███▊      | 95/250 [01:01<01:00,  2.57it/s]
 38%|███▊      | 96/250 [01:03<01:56,  1.32it/s]
 39%|███▉      | 97/250 [01:03<01:56,  1.32it/s]
 39%|███▉      | 98/250 [01:04<01:31,  1.66it/s]
 40%|███▉      | 99/250 [01:04<01:18,  1.91it/s]
 40%|████      | 101/250 [01:04<00:56,  2.64it/s]
 41%|████      | 102/250 [01:05<00:51,  2.87it/s]
 41%|████      | 103/250 [01:05<01:04,  2.29it/s]
 42%|████▏     | 105/250 [01:09<02:14,  1.08it/s]
 42%|████▏     | 106/250 [01:10<02:17,  1.05it/s]
 43%|████▎     | 107/250 [01:11<02:36,  1.09s/it]
 43%|████▎     | 108/250 [01:12<02:16,  1.04it/s]
 44%|████▎     | 109/250 [01:13<02:15,  1.04it/s]
 44%|████▍     | 111/250 [01:13<01:33,  1.48it/s]
 45%|████▍     | 112/250 [01:14<01:22,  1.67it/s]
 45%|████▌     | 113/250 [01:14<01:12,  1.90it/s]
 46%|████▌     | 114/250 [01:15<01:33,  1.45it/s]
 46%|████▌     | 115/250 [01:16<01:52,  1.19it/s]
 46%|████▋     | 116/250 [01:16<01:26,  1.55it/s]
 47%|████▋     | 117/250 [01:17<01:16,  1.73it/s]
 47%|████▋     | 118/250 [01:17<01:15,  1.75it/s]
 48%|████▊     | 119/250 [01:20<02:22,  1.09s/it]
 48%|████▊     | 120/250 [01:21<02:14,  1.03s/it]
 48%|████▊     | 121/250 [01:22<02:10,  1.01s/it]
 49%|████▉     | 122/250 [01:22<01:40,  1.27it/s]
 49%|████▉     | 123/250 [01:22<01:34,  1.35it/s]
 50%|████▉     | 124/250 [01:24<01:59,  1.05it/s]
 50%|█████     | 125/250 [01:24<01:39,  1.26it/s]
 50%|█████     | 126/250 [01:25<01:43,  1.19it/s]
 51%|█████     | 127/250 [01:26<01:32,  1.33it/s]
 51%|█████     | 128/250 [01:27<01:29,  1.37it/s]
 52%|█████▏    | 129/250 [01:27<01:08,  1.78it/s]
 52%|█████▏    | 130/250 [01:28<01:31,  1.31it/s]
 52%|█████▏    | 131/250 [01:29<01:25,  1.39it/s]
 53%|█████▎    | 132/250 [01:29<01:03,  1.85it/s]
 53%|█████▎    | 133/250 [01:29<01:09,  1.69it/s]
 54%|█████▎    | 134/250 [01:31<02:01,  1.05s/it]
 54%|█████▍    | 136/250 [01:47<07:56,  4.18s/it]
 55%|█████▍    | 137/250 [01:47<06:03,  3.21s/it]
 55%|█████▌    | 138/250 [01:48<04:37,  2.48s/it]
 56%|█████▌    | 139/250 [01:49<03:57,  2.14s/it]
 56%|█████▌    | 140/250 [01:50<03:11,  1.74s/it]
 57%|█████▋    | 142/250 [01:51<02:04,  1.15s/it]
 57%|█████▋    | 143/250 [01:51<01:44,  1.03it/s]
 58%|█████▊    | 144/250 [01:53<02:17,  1.30s/it]
 58%|█████▊    | 145/250 [01:55<02:41,  1.54s/it]
 58%|█████▊    | 146/250 [01:56<01:59,  1.15s/it]
 59%|█████▉    | 147/250 [01:56<01:28,  1.17it/s]
 59%|█████▉    | 148/250 [01:56<01:22,  1.23it/s]
 60%|█████▉    | 149/250 [01:57<01:19,  1.27it/s]
 60%|██████    | 151/250 [01:58<00:52,  1.88it/s]
 61%|██████    | 153/250 [01:58<00:34,  2.82it/s]
 62%|██████▏   | 154/250 [01:58<00:31,  3.03it/s]
 62%|██████▏   | 156/250 [01:58<00:23,  3.97it/s]
 63%|██████▎   | 157/250 [01:59<00:24,  3.80it/s]
 64%|██████▎   | 159/250 [01:59<00:17,  5.32it/s]
 64%|██████▍   | 160/250 [01:59<00:22,  4.03it/s]
 64%|██████▍   | 161/250 [02:00<00:27,  3.24it/s]
 65%|██████▍   | 162/250 [02:00<00:36,  2.41it/s]
 66%|██████▌   | 164/250 [02:01<00:23,  3.72it/s]
 66%|██████▌   | 165/250 [02:01<00:29,  2.87it/s]
 66%|██████▋   | 166/250 [02:02<00:42,  1.96it/s]
 67%|██████▋   | 167/250 [02:03<00:44,  1.85it/s]
 67%|██████▋   | 168/250 [02:03<00:42,  1.92it/s]
 68%|██████▊   | 170/250 [02:03<00:27,  2.95it/s]
 68%|██████▊   | 171/250 [02:04<00:22,  3.48it/s]
 69%|██████▉   | 172/250 [02:04<00:18,  4.16it/s]
 69%|██████▉   | 173/250 [02:04<00:15,  4.83it/s]
 70%|███████   | 175/250 [02:04<00:11,  6.63it/s]
 71%|███████   | 177/250 [02:04<00:08,  8.41it/s]
 72%|███████▏  | 179/250 [02:04<00:06, 10.32it/s]
 73%|███████▎  | 183/250 [02:04<00:04, 13.60it/s]
 74%|███████▍  | 185/250 [02:05<00:12,  5.27it/s]
 75%|███████▍  | 187/250 [02:06<00:15,  4.00it/s]
 75%|███████▌  | 188/250 [02:07<00:15,  3.92it/s]
 76%|███████▌  | 189/250 [02:08<00:24,  2.48it/s]
 76%|███████▌  | 190/250 [02:08<00:24,  2.44it/s]
 76%|███████▋  | 191/250 [02:08<00:23,  2.53it/s]
 77%|███████▋  | 192/250 [02:08<00:19,  2.98it/s]
 78%|███████▊  | 194/250 [02:09<00:12,  4.38it/s]
 78%|███████▊  | 195/250 [02:09<00:14,  3.75it/s]
 78%|███████▊  | 196/250 [02:10<00:18,  2.85it/s]
 79%|███████▉  | 197/250 [02:10<00:24,  2.18it/s]
 79%|███████▉  | 198/250 [02:11<00:25,  2.08it/s]
 80%|███████▉  | 199/250 [02:11<00:20,  2.48it/s]
 80%|████████  | 200/250 [02:11<00:16,  3.03it/s]
 81%|████████  | 202/250 [02:11<00:10,  4.64it/s]
 81%|████████  | 203/250 [02:12<00:09,  4.71it/s]
 82%|████████▏ | 204/250 [02:12<00:08,  5.26it/s]
 82%|████████▏ | 205/250 [02:13<00:19,  2.27it/s]
 83%|████████▎ | 207/250 [02:13<00:14,  2.91it/s]
 83%|████████▎ | 208/250 [02:14<00:19,  2.11it/s]
 84%|████████▍ | 210/250 [02:15<00:16,  2.48it/s]
 84%|████████▍ | 211/250 [02:15<00:13,  2.92it/s]
 85%|████████▍ | 212/250 [02:16<00:17,  2.20it/s]
 85%|████████▌ | 213/250 [02:16<00:14,  2.59it/s]
 86%|████████▌ | 214/250 [02:16<00:12,  2.77it/s]
 86%|████████▌ | 215/250 [02:17<00:20,  1.67it/s]
 86%|████████▋ | 216/250 [02:18<00:15,  2.15it/s]
 87%|████████▋ | 217/250 [02:18<00:19,  1.71it/s]
 88%|████████▊ | 219/250 [02:19<00:12,  2.41it/s]
 88%|████████▊ | 220/250 [02:19<00:11,  2.56it/s]
 89%|████████▉ | 222/250 [02:20<00:09,  2.83it/s]
 90%|████████▉ | 224/250 [02:20<00:06,  4.07it/s]
 90%|█████████ | 225/250 [02:20<00:05,  4.48it/s]
 90%|█████████ | 226/250 [02:20<00:05,  4.58it/s]
 91%|█████████ | 228/250 [02:21<00:04,  4.53it/s]
 92%|█████████▏| 229/250 [02:21<00:07,  2.92it/s]
 92%|█████████▏| 230/250 [02:22<00:07,  2.63it/s]
 93%|█████████▎| 232/250 [02:22<00:05,  3.44it/s]
 93%|█████████▎| 233/250 [02:23<00:04,  3.54it/s]
 94%|█████████▍| 235/250 [02:23<00:02,  5.08it/s]
 95%|█████████▍| 237/250 [02:23<00:02,  5.09it/s]
 95%|█████████▌| 238/250 [02:23<00:02,  4.36it/s]
 96%|█████████▋| 241/250 [02:24<00:01,  6.14it/s]
 97%|█████████▋| 242/250 [02:24<00:01,  6.59it/s]
 97%|█████████▋| 243/250 [02:24<00:01,  5.45it/s]
 98%|█████████▊| 244/250 [02:25<00:02,  2.80it/s]
 98%|█████████▊| 245/250 [02:25<00:01,  3.34it/s]
 98%|█████████▊| 246/250 [02:27<00:02,  1.37it/s]
 99%|█████████▉| 247/250 [02:29<00:02,  1.09it/s]
 99%|█████████▉| 248/250 [02:30<00:02,  1.03s/it]
100%|█████████▉| 249/250 [02:30<00:00,  1.13it/s]
100%|██████████| 250/250 [02:31<00:00,  1.52it/s]
100%|██████████| 250/250 [02:31<00:00,  1.66it/s]
2025-05-16 14:33:00,118 - modnet - INFO - Loss per individual: ind 0: 64.659 	ind 1: 55.717 	ind 2: 312.494 	ind 3: 141.832 	ind 4: 109.238 	ind 5: 165.172 	ind 6: 120.049 	ind 7: 150.879 	ind 8: 81.195 	ind 9: 58.944 	ind 10: 241.296 	ind 11: 83.996 	ind 12: 97.123 	ind 13: 44.483 	ind 14: 107.226 	ind 15: 326.694 	ind 16: 70.850 	ind 17: 53.960 	ind 18: 311.802 	ind 19: 191.066 	ind 20: 38.614 	ind 21: 164.687 	ind 22: 160.354 	ind 23: 52.938 	ind 24: 97.375 	ind 25: 311.938 	ind 26: 62.802 	ind 27: 311.910 	ind 28: 102.864 	ind 29: 180.338 	ind 30: 217.655 	ind 31: 311.664 	ind 32: 146.451 	ind 33: 51.663 	ind 34: 47.749 	ind 35: 276.474 	ind 36: 65.878 	ind 37: 60.670 	ind 38: 177.748 	ind 39: 61.104 	ind 40: 151.426 	ind 41: 58.708 	ind 42: 327.819 	ind 43: 86.879 	ind 44: 53.105 	ind 45: 117.239 	ind 46: 146.050 	ind 47: 55.741 	ind 48: 106.655 	ind 49: 311.920 	
2025-05-16 14:33:00,119 - modnet - INFO - Initial best model details:
2025-05-16 14:33:00,119 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 64, 'fraction1': 1, 'fraction2': 0.5, 'fraction3': 1, 'fraction4': 0.5, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 220, 'n_layers': 5, 'layer_mults': None}
2025-05-16 14:33:00,119 - modnet - INFO - Initial validation loss: 38.6143
2025-05-16 14:33:00,119 - modnet - INFO - Generation number 1

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:06<28:06,  6.77s/it]
  1%|          | 2/250 [00:07<12:19,  2.98s/it]
  1%|          | 3/250 [00:07<07:17,  1.77s/it]
  2%|▏         | 4/250 [00:07<05:05,  1.24s/it]
  2%|▏         | 5/250 [00:08<03:35,  1.13it/s]
  3%|▎         | 7/250 [00:10<03:43,  1.09it/s]
  3%|▎         | 8/250 [00:10<03:11,  1.26it/s]
  4%|▎         | 9/250 [00:10<02:25,  1.65it/s]
  4%|▍         | 10/250 [00:10<02:12,  1.81it/s]
  4%|▍         | 11/250 [00:11<01:56,  2.06it/s]
  5%|▍         | 12/250 [00:11<01:56,  2.05it/s]
  5%|▌         | 13/250 [00:11<01:34,  2.52it/s]
  6%|▌         | 14/250 [00:12<01:16,  3.07it/s]
  6%|▋         | 16/250 [00:12<01:07,  3.46it/s]
  7%|▋         | 17/250 [00:13<01:14,  3.14it/s]
  7%|▋         | 18/250 [00:13<01:04,  3.61it/s]
  8%|▊         | 19/250 [00:13<01:26,  2.67it/s]
  8%|▊         | 20/250 [00:14<01:14,  3.07it/s]
  8%|▊         | 21/250 [00:14<01:21,  2.80it/s]
  9%|▉         | 22/250 [00:14<01:25,  2.68it/s]
 10%|▉         | 24/250 [00:15<01:05,  3.43it/s]
 10%|█         | 25/250 [00:15<00:57,  3.94it/s]
 10%|█         | 26/250 [00:15<01:07,  3.34it/s]
 11%|█         | 27/250 [00:16<00:58,  3.80it/s]
 11%|█         | 28/250 [00:16<01:25,  2.58it/s]
 12%|█▏        | 29/250 [00:17<01:23,  2.66it/s]
 12%|█▏        | 31/250 [00:17<01:22,  2.64it/s]
 13%|█▎        | 32/250 [00:17<01:09,  3.12it/s]
 14%|█▎        | 34/250 [00:18<01:07,  3.20it/s]
 14%|█▍        | 35/250 [00:20<02:12,  1.62it/s]
 14%|█▍        | 36/250 [00:20<02:04,  1.72it/s]
 15%|█▍        | 37/250 [00:21<02:25,  1.46it/s]
 15%|█▌        | 38/250 [00:21<01:57,  1.80it/s]
 16%|█▌        | 40/250 [00:22<01:42,  2.04it/s]
 16%|█▋        | 41/250 [00:23<02:21,  1.48it/s]
 17%|█▋        | 42/250 [00:24<02:08,  1.62it/s]
 17%|█▋        | 43/250 [00:24<01:47,  1.92it/s]
 18%|█▊        | 44/250 [00:25<01:52,  1.83it/s]
 18%|█▊        | 46/250 [00:25<01:25,  2.40it/s]
 19%|█▉        | 47/250 [00:26<01:38,  2.07it/s]
 20%|█▉        | 49/250 [00:26<01:06,  3.04it/s]
 20%|██        | 50/250 [00:26<01:00,  3.30it/s]
 20%|██        | 51/250 [00:27<00:55,  3.62it/s]
 21%|██        | 52/250 [00:29<02:19,  1.42it/s]
 21%|██        | 53/250 [00:29<02:03,  1.60it/s]
 22%|██▏       | 54/250 [00:29<01:57,  1.67it/s]
 22%|██▏       | 55/250 [00:30<01:53,  1.71it/s]
 23%|██▎       | 57/250 [00:32<02:14,  1.43it/s]
 23%|██▎       | 58/250 [00:32<01:59,  1.61it/s]
 24%|██▎       | 59/250 [00:33<02:36,  1.22it/s]
 24%|██▍       | 60/250 [00:34<02:36,  1.22it/s]
 24%|██▍       | 61/250 [00:35<02:04,  1.52it/s]
 25%|██▌       | 63/250 [00:35<01:22,  2.27it/s]
 26%|██▌       | 64/250 [00:36<01:52,  1.65it/s]
 26%|██▌       | 65/250 [00:37<01:59,  1.54it/s]
 26%|██▋       | 66/250 [00:38<02:22,  1.30it/s]
 27%|██▋       | 67/250 [00:38<01:47,  1.70it/s]
 27%|██▋       | 68/250 [00:38<01:22,  2.20it/s]
 28%|██▊       | 69/250 [00:38<01:03,  2.83it/s]
 28%|██▊       | 70/250 [00:38<00:50,  3.54it/s]
 28%|██▊       | 71/250 [00:39<00:52,  3.41it/s]
 29%|██▉       | 72/250 [00:39<01:06,  2.66it/s]
 29%|██▉       | 73/250 [00:39<01:01,  2.86it/s]
 30%|██▉       | 74/250 [00:40<00:59,  2.98it/s]
 30%|███       | 75/250 [00:40<00:47,  3.68it/s]
 30%|███       | 76/250 [00:40<00:42,  4.11it/s]
 31%|███       | 78/250 [00:40<00:28,  6.09it/s]
 32%|███▏      | 80/250 [00:41<00:47,  3.60it/s]
 32%|███▏      | 81/250 [00:42<00:52,  3.23it/s]
 33%|███▎      | 82/250 [00:42<00:50,  3.34it/s]
 33%|███▎      | 83/250 [00:42<00:47,  3.55it/s]
 34%|███▎      | 84/250 [00:42<00:41,  3.99it/s]
 34%|███▍      | 86/250 [00:44<01:26,  1.91it/s]
 35%|███▍      | 87/250 [00:44<01:23,  1.94it/s]
 35%|███▌      | 88/250 [00:45<01:24,  1.91it/s]
 36%|███▌      | 89/250 [00:45<01:11,  2.25it/s]
 36%|███▋      | 91/250 [00:46<00:53,  2.96it/s]
 37%|███▋      | 92/250 [00:46<00:50,  3.14it/s]
 37%|███▋      | 93/250 [00:47<01:28,  1.78it/s]
 38%|███▊      | 94/250 [00:48<01:44,  1.49it/s]
 38%|███▊      | 96/250 [00:49<01:33,  1.64it/s]
 39%|███▉      | 97/250 [00:50<01:37,  1.57it/s]
 39%|███▉      | 98/250 [00:51<01:39,  1.53it/s]
 40%|███▉      | 99/250 [00:51<01:30,  1.66it/s]
 40%|████      | 101/250 [00:51<01:00,  2.46it/s]
 41%|████      | 103/250 [00:52<00:43,  3.35it/s]
 42%|████▏     | 104/250 [00:52<00:41,  3.55it/s]
 42%|████▏     | 105/250 [00:52<00:51,  2.81it/s]
 42%|████▏     | 106/250 [00:53<00:51,  2.80it/s]
 43%|████▎     | 107/250 [00:53<00:42,  3.38it/s]
 43%|████▎     | 108/250 [00:53<00:36,  3.93it/s]
 44%|████▍     | 110/250 [00:53<00:29,  4.67it/s]
 45%|████▍     | 112/250 [00:54<00:21,  6.53it/s]
 45%|████▌     | 113/250 [00:54<00:21,  6.33it/s]
 46%|████▌     | 114/250 [00:54<00:26,  5.16it/s]
 46%|████▋     | 116/250 [00:56<00:57,  2.32it/s]
 47%|████▋     | 117/250 [00:56<00:48,  2.72it/s]
 47%|████▋     | 118/250 [00:57<01:17,  1.70it/s]
 48%|████▊     | 119/250 [00:58<01:47,  1.22it/s]
 48%|████▊     | 120/250 [00:59<01:46,  1.22it/s]
 48%|████▊     | 121/250 [01:01<02:04,  1.03it/s]
 49%|████▉     | 122/250 [01:01<01:40,  1.28it/s]
 50%|████▉     | 124/250 [01:01<01:04,  1.95it/s]
 50%|█████     | 125/250 [01:01<00:53,  2.36it/s]
 51%|█████     | 127/250 [01:03<01:06,  1.86it/s]
 51%|█████     | 128/250 [01:03<01:05,  1.87it/s]
 52%|█████▏    | 129/250 [01:04<01:08,  1.77it/s]
 52%|█████▏    | 130/250 [01:04<00:59,  2.03it/s]
 53%|█████▎    | 132/250 [01:05<00:38,  3.07it/s]
 53%|█████▎    | 133/250 [01:05<00:40,  2.90it/s]
 54%|█████▎    | 134/250 [01:06<01:08,  1.70it/s]
 54%|█████▍    | 135/250 [01:07<01:01,  1.88it/s]
 54%|█████▍    | 136/250 [01:07<01:06,  1.71it/s]
 55%|█████▍    | 137/250 [01:09<01:27,  1.29it/s]
 55%|█████▌    | 138/250 [01:09<01:29,  1.26it/s]
 56%|█████▌    | 139/250 [01:10<01:13,  1.51it/s]
 56%|█████▌    | 140/250 [01:10<00:56,  1.95it/s]
 56%|█████▋    | 141/250 [01:10<00:48,  2.23it/s]
 57%|█████▋    | 142/250 [01:11<00:43,  2.47it/s]
 58%|█████▊    | 144/250 [01:11<00:30,  3.49it/s]
 58%|█████▊    | 145/250 [01:12<00:51,  2.06it/s]
 58%|█████▊    | 146/250 [01:15<01:46,  1.03s/it]
 59%|█████▉    | 148/250 [01:16<01:24,  1.20it/s]
 60%|██████    | 150/250 [01:17<01:13,  1.36it/s]
 60%|██████    | 151/250 [01:18<01:19,  1.24it/s]
 61%|██████    | 152/250 [01:19<01:18,  1.26it/s]
 61%|██████    | 153/250 [01:19<01:00,  1.60it/s]
 62%|██████▏   | 154/250 [01:19<00:54,  1.75it/s]
 62%|██████▏   | 155/250 [01:20<01:03,  1.49it/s]
 63%|██████▎   | 157/250 [01:20<00:38,  2.40it/s]
 63%|██████▎   | 158/250 [01:21<00:35,  2.62it/s]
 64%|██████▍   | 160/250 [01:21<00:22,  4.03it/s]
 64%|██████▍   | 161/250 [01:21<00:29,  3.06it/s]
 65%|██████▍   | 162/250 [01:22<00:31,  2.82it/s]
 65%|██████▌   | 163/250 [01:22<00:27,  3.22it/s]
 66%|██████▌   | 164/250 [01:23<00:40,  2.11it/s]
 66%|██████▌   | 165/250 [01:23<00:38,  2.21it/s]
 66%|██████▋   | 166/250 [01:24<00:39,  2.15it/s]
 67%|██████▋   | 167/250 [01:25<00:47,  1.74it/s]
 68%|██████▊   | 169/250 [01:25<00:41,  1.96it/s]
 68%|██████▊   | 170/250 [01:26<00:35,  2.23it/s]
 68%|██████▊   | 171/250 [01:26<00:40,  1.97it/s]
 69%|██████▉   | 172/250 [01:27<00:34,  2.26it/s]
 70%|███████   | 175/250 [01:27<00:17,  4.29it/s]
 70%|███████   | 176/250 [01:28<00:31,  2.34it/s]
 71%|███████   | 178/250 [01:28<00:26,  2.76it/s]
 72%|███████▏  | 179/250 [01:29<00:26,  2.72it/s]
 72%|███████▏  | 180/250 [01:29<00:22,  3.15it/s]
 72%|███████▏  | 181/250 [01:29<00:22,  3.10it/s]
 73%|███████▎  | 182/250 [01:30<00:22,  3.02it/s]
 73%|███████▎  | 183/250 [01:30<00:19,  3.39it/s]
 74%|███████▎  | 184/250 [01:30<00:21,  3.08it/s]
 74%|███████▍  | 185/250 [01:31<00:28,  2.29it/s]
 74%|███████▍  | 186/250 [01:31<00:28,  2.22it/s]
 75%|███████▍  | 187/250 [01:32<00:29,  2.13it/s]
 75%|███████▌  | 188/250 [01:32<00:28,  2.20it/s]
 76%|███████▌  | 189/250 [01:33<00:25,  2.43it/s]
 76%|███████▌  | 190/250 [01:34<00:38,  1.56it/s]
 76%|███████▋  | 191/250 [01:35<00:38,  1.52it/s]
 77%|███████▋  | 192/250 [01:35<00:35,  1.65it/s]
 77%|███████▋  | 193/250 [01:35<00:27,  2.04it/s]
 78%|███████▊  | 194/250 [01:35<00:21,  2.60it/s]
 78%|███████▊  | 196/250 [01:36<00:13,  3.92it/s]
 79%|███████▉  | 198/250 [01:36<00:14,  3.69it/s]
 80%|███████▉  | 199/250 [01:37<00:21,  2.43it/s]
 80%|████████  | 200/250 [01:38<00:26,  1.91it/s]
 80%|████████  | 201/250 [01:39<00:25,  1.91it/s]
 81%|████████  | 202/250 [01:39<00:22,  2.10it/s]
 81%|████████  | 203/250 [01:40<00:26,  1.80it/s]
 82%|████████▏ | 204/250 [01:40<00:25,  1.81it/s]
 82%|████████▏ | 205/250 [01:40<00:20,  2.19it/s]
 82%|████████▏ | 206/250 [01:41<00:21,  2.04it/s]
 83%|████████▎ | 207/250 [01:42<00:32,  1.34it/s]
 83%|████████▎ | 208/250 [01:42<00:23,  1.80it/s]
 84%|████████▎ | 209/250 [01:43<00:19,  2.05it/s]
 84%|████████▍ | 210/250 [01:43<00:18,  2.13it/s]
 85%|████████▍ | 212/250 [01:43<00:11,  3.40it/s]
 85%|████████▌ | 213/250 [01:44<00:15,  2.43it/s]
 86%|████████▌ | 215/250 [01:44<00:10,  3.44it/s]
 86%|████████▋ | 216/250 [01:45<00:09,  3.76it/s]
 87%|████████▋ | 217/250 [01:45<00:08,  3.74it/s]
 87%|████████▋ | 218/250 [01:46<00:12,  2.64it/s]
 88%|████████▊ | 220/250 [01:47<00:14,  2.01it/s]
 88%|████████▊ | 221/250 [01:49<00:22,  1.30it/s]
 89%|████████▉ | 222/250 [01:50<00:27,  1.01it/s]
 89%|████████▉ | 223/250 [01:51<00:23,  1.17it/s]
 90%|████████▉ | 224/250 [01:51<00:20,  1.25it/s]
 90%|█████████ | 225/250 [01:52<00:17,  1.45it/s]
 90%|█████████ | 226/250 [01:52<00:14,  1.65it/s]
 91%|█████████ | 227/250 [01:52<00:11,  2.09it/s]
 91%|█████████ | 228/250 [01:52<00:08,  2.71it/s]
 92%|█████████▏| 229/250 [01:53<00:09,  2.16it/s]
 93%|█████████▎| 232/250 [01:53<00:04,  4.46it/s]
 94%|█████████▎| 234/250 [01:55<00:06,  2.40it/s]
 94%|█████████▍| 235/250 [01:55<00:05,  2.70it/s]
 94%|█████████▍| 236/250 [01:56<00:07,  1.97it/s]
 95%|█████████▌| 238/250 [01:56<00:04,  2.59it/s]
 96%|█████████▌| 239/250 [01:57<00:03,  2.87it/s]
 96%|█████████▌| 240/250 [01:58<00:05,  1.68it/s]
 96%|█████████▋| 241/250 [01:58<00:04,  1.84it/s]
 97%|█████████▋| 242/250 [01:58<00:03,  2.26it/s]
 97%|█████████▋| 243/250 [01:59<00:02,  2.41it/s]
 98%|█████████▊| 244/250 [01:59<00:02,  2.98it/s]
 98%|█████████▊| 245/250 [02:01<00:04,  1.09it/s]
 98%|█████████▊| 246/250 [02:06<00:08,  2.10s/it]
 99%|█████████▉| 247/250 [02:08<00:06,  2.06s/it]
 99%|█████████▉| 248/250 [02:10<00:03,  1.85s/it]
100%|█████████▉| 249/250 [02:16<00:03,  3.13s/it]
100%|██████████| 250/250 [02:17<00:00,  2.43s/it]
100%|██████████| 250/250 [02:17<00:00,  1.82it/s]
2025-05-16 14:35:17,191 - modnet - INFO - Loss per individual: ind 0: 51.481 	ind 1: 44.587 	ind 2: 49.341 	ind 3: 55.562 	ind 4: 54.581 	ind 5: 39.216 	ind 6: 66.417 	ind 7: 101.468 	ind 8: 76.816 	ind 9: 43.261 	ind 10: 69.173 	ind 11: 47.470 	ind 12: 49.888 	ind 13: 207.774 	ind 14: 42.701 	ind 15: 39.552 	ind 16: 110.848 	ind 17: 44.210 	ind 18: 125.029 	ind 19: 314.791 	ind 20: 60.339 	ind 21: 37.279 	ind 22: 53.481 	ind 23: 110.426 	ind 24: 47.703 	ind 25: 45.275 	ind 26: 577.261 	ind 27: 201.380 	ind 28: 39.818 	ind 29: 40.832 	ind 30: 40.060 	ind 31: 40.247 	ind 32: 38.291 	ind 33: 60.357 	ind 34: 50.805 	ind 35: 52.903 	ind 36: 99.093 	ind 37: 38.535 	ind 38: 57.508 	ind 39: 70.580 	ind 40: 49.211 	ind 41: 51.011 	ind 42: 104.254 	ind 43: 45.447 	ind 44: 55.347 	ind 45: 311.925 	ind 46: 63.794 	ind 47: 50.785 	ind 48: 39.610 	ind 49: 213.320 	
2025-05-16 14:35:17,192 - modnet - INFO - After generation 1:
2025-05-16 14:35:17,192 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 256, 'fraction1': 1, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 64, 'n_feat': 580, 'n_layers': 4, 'layer_mults': None}
2025-05-16 14:35:17,192 - modnet - INFO - Best validation loss: 37.2793
2025-05-16 14:35:17,192 - modnet - INFO - Generation number 2

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:05<21:57,  5.29s/it]
  1%|          | 2/250 [00:05<09:49,  2.38s/it]
  1%|          | 3/250 [00:07<08:57,  2.18s/it]
  2%|▏         | 4/250 [00:10<09:46,  2.38s/it]
  2%|▏         | 5/250 [00:10<06:41,  1.64s/it]
  2%|▏         | 6/250 [00:11<05:19,  1.31s/it]
  3%|▎         | 7/250 [00:12<04:41,  1.16s/it]
  3%|▎         | 8/250 [00:12<03:29,  1.15it/s]
  4%|▎         | 9/250 [00:13<03:38,  1.11it/s]
  4%|▍         | 10/250 [00:14<03:33,  1.13it/s]
  4%|▍         | 11/250 [00:15<03:54,  1.02it/s]
  5%|▍         | 12/250 [00:17<05:42,  1.44s/it]
  5%|▌         | 13/250 [00:18<04:21,  1.10s/it]
  6%|▌         | 15/250 [00:18<02:45,  1.42it/s]
  6%|▋         | 16/250 [00:19<02:52,  1.35it/s]
  7%|▋         | 17/250 [00:20<02:45,  1.41it/s]
  7%|▋         | 18/250 [00:22<04:23,  1.14s/it]
  8%|▊         | 19/250 [00:22<03:29,  1.10it/s]
  8%|▊         | 20/250 [00:22<02:39,  1.44it/s]
  9%|▉         | 22/250 [00:23<01:39,  2.30it/s]
  9%|▉         | 23/250 [00:23<01:24,  2.70it/s]
 10%|▉         | 24/250 [00:25<02:59,  1.26it/s]
 10%|█         | 25/250 [00:26<03:01,  1.24it/s]
 10%|█         | 26/250 [00:26<02:18,  1.62it/s]
 11%|█         | 27/250 [00:27<02:28,  1.50it/s]
 11%|█         | 28/250 [00:27<02:20,  1.58it/s]
 12%|█▏        | 29/250 [00:27<01:45,  2.09it/s]
 12%|█▏        | 30/250 [00:27<01:27,  2.50it/s]
 12%|█▏        | 31/250 [00:28<01:29,  2.43it/s]
 13%|█▎        | 32/250 [00:28<01:14,  2.91it/s]
 13%|█▎        | 33/250 [00:29<01:32,  2.35it/s]
 14%|█▎        | 34/250 [00:29<01:42,  2.11it/s]
 14%|█▍        | 35/250 [00:30<01:50,  1.95it/s]
 14%|█▍        | 36/250 [00:30<01:25,  2.50it/s]
 15%|█▌        | 38/250 [00:30<00:56,  3.72it/s]
 16%|█▌        | 39/250 [00:31<00:57,  3.69it/s]
 16%|█▌        | 40/250 [00:31<00:49,  4.21it/s]
 16%|█▋        | 41/250 [00:31<00:48,  4.30it/s]
 17%|█▋        | 42/250 [00:33<02:21,  1.47it/s]
 18%|█▊        | 44/250 [00:33<01:39,  2.08it/s]
 18%|█▊        | 45/250 [00:34<01:52,  1.82it/s]
 19%|█▉        | 47/250 [00:34<01:17,  2.60it/s]
 20%|█▉        | 49/250 [00:34<00:54,  3.69it/s]
 20%|██        | 50/250 [00:35<01:25,  2.35it/s]
 21%|██        | 52/250 [00:36<01:03,  3.09it/s]
 22%|██▏       | 54/250 [00:36<00:46,  4.22it/s]
 22%|██▏       | 56/250 [00:36<00:35,  5.49it/s]
 23%|██▎       | 57/250 [00:36<00:32,  5.88it/s]
 23%|██▎       | 58/250 [00:38<01:35,  2.02it/s]
 24%|██▎       | 59/250 [00:38<01:35,  2.00it/s]
 24%|██▍       | 60/250 [00:39<01:35,  1.98it/s]
 24%|██▍       | 61/250 [00:40<02:17,  1.37it/s]
 25%|██▍       | 62/250 [00:41<02:37,  1.19it/s]
 25%|██▌       | 63/250 [00:41<02:00,  1.55it/s]
 26%|██▌       | 65/250 [00:42<01:10,  2.63it/s]
 27%|██▋       | 67/250 [00:42<01:01,  3.00it/s]
 27%|██▋       | 68/250 [00:44<01:50,  1.64it/s]
 28%|██▊       | 69/250 [00:45<02:17,  1.32it/s]
 28%|██▊       | 70/250 [00:45<02:03,  1.46it/s]
 28%|██▊       | 71/250 [00:48<03:20,  1.12s/it]
 29%|██▉       | 72/250 [00:48<02:40,  1.11it/s]
 29%|██▉       | 73/250 [00:49<03:02,  1.03s/it]
 30%|██▉       | 74/250 [00:50<02:17,  1.28it/s]
 30%|███       | 75/250 [00:50<02:02,  1.43it/s]
 30%|███       | 76/250 [00:51<01:58,  1.47it/s]
 31%|███       | 77/250 [00:51<01:46,  1.62it/s]
 31%|███       | 78/250 [00:52<02:16,  1.26it/s]
 32%|███▏      | 79/250 [00:53<01:41,  1.69it/s]
 32%|███▏      | 81/250 [00:53<01:00,  2.80it/s]
 33%|███▎      | 83/250 [00:53<01:00,  2.77it/s]
 34%|███▍      | 85/250 [00:54<00:48,  3.38it/s]
 34%|███▍      | 86/250 [00:55<01:16,  2.13it/s]
 35%|███▍      | 87/250 [00:55<01:07,  2.42it/s]
 35%|███▌      | 88/250 [00:55<00:58,  2.78it/s]
 36%|███▌      | 89/250 [00:55<00:49,  3.23it/s]
 36%|███▌      | 90/250 [00:56<00:55,  2.89it/s]
 36%|███▋      | 91/250 [00:56<00:44,  3.58it/s]
 37%|███▋      | 92/250 [00:56<00:36,  4.32it/s]
 37%|███▋      | 93/250 [00:57<00:52,  2.99it/s]
 38%|███▊      | 94/250 [00:57<00:51,  3.03it/s]
 38%|███▊      | 95/250 [00:59<02:07,  1.21it/s]
 38%|███▊      | 96/250 [01:01<03:05,  1.20s/it]
 39%|███▉      | 97/250 [01:03<03:11,  1.25s/it]
 39%|███▉      | 98/250 [01:03<02:50,  1.12s/it]
 40%|███▉      | 99/250 [01:05<03:24,  1.35s/it]
 40%|████      | 100/250 [01:06<02:50,  1.14s/it]
 40%|████      | 101/250 [01:08<03:22,  1.36s/it]
 41%|████      | 102/250 [01:08<02:34,  1.04s/it]
 41%|████      | 103/250 [01:11<04:07,  1.68s/it]
 42%|████▏     | 104/250 [01:12<03:06,  1.27s/it]
 42%|████▏     | 106/250 [01:12<02:09,  1.11it/s]
 43%|████▎     | 107/250 [01:13<01:49,  1.30it/s]
 43%|████▎     | 108/250 [01:14<01:54,  1.24it/s]
 44%|████▎     | 109/250 [01:15<01:51,  1.27it/s]
 44%|████▍     | 110/250 [01:15<01:37,  1.44it/s]
 44%|████▍     | 111/250 [01:15<01:14,  1.88it/s]
 45%|████▍     | 112/250 [01:15<01:02,  2.22it/s]
 45%|████▌     | 113/250 [01:16<00:52,  2.60it/s]
 46%|████▌     | 114/250 [01:16<00:47,  2.89it/s]
 46%|████▌     | 115/250 [01:16<00:40,  3.35it/s]
 46%|████▋     | 116/250 [01:16<00:33,  3.96it/s]
 47%|████▋     | 117/250 [01:17<00:55,  2.40it/s]
 47%|████▋     | 118/250 [01:17<00:44,  2.99it/s]
 48%|████▊     | 120/250 [01:18<00:50,  2.57it/s]
 48%|████▊     | 121/250 [01:19<01:01,  2.08it/s]
 49%|████▉     | 122/250 [01:19<00:51,  2.48it/s]
 49%|████▉     | 123/250 [01:20<01:20,  1.58it/s]
 50%|████▉     | 124/250 [01:20<01:01,  2.05it/s]
 50%|█████     | 125/250 [01:21<00:50,  2.47it/s]
 50%|█████     | 126/250 [01:21<00:53,  2.30it/s]
 51%|█████     | 127/250 [01:21<00:43,  2.83it/s]
 51%|█████     | 128/250 [01:21<00:34,  3.55it/s]
 52%|█████▏    | 129/250 [01:23<01:32,  1.30it/s]
 52%|█████▏    | 130/250 [01:23<01:08,  1.75it/s]
 52%|█████▏    | 131/250 [01:24<01:14,  1.61it/s]
 53%|█████▎    | 132/250 [01:25<01:13,  1.61it/s]
 53%|█████▎    | 133/250 [01:25<00:58,  2.00it/s]
 54%|█████▍    | 135/250 [01:25<00:35,  3.26it/s]
 54%|█████▍    | 136/250 [01:25<00:31,  3.58it/s]
 55%|█████▌    | 138/250 [01:26<00:39,  2.86it/s]
 56%|█████▌    | 139/250 [01:28<01:23,  1.33it/s]
 56%|█████▌    | 140/250 [01:28<01:05,  1.68it/s]
 57%|█████▋    | 142/250 [01:29<00:57,  1.88it/s]
 57%|█████▋    | 143/250 [01:30<00:52,  2.04it/s]
 58%|█████▊    | 144/250 [01:30<00:42,  2.50it/s]
 58%|█████▊    | 145/250 [01:30<00:40,  2.57it/s]
 58%|█████▊    | 146/250 [01:31<00:47,  2.18it/s]
 59%|█████▉    | 147/250 [01:32<01:06,  1.54it/s]
 59%|█████▉    | 148/250 [01:32<00:57,  1.77it/s]
 60%|█████▉    | 149/250 [01:32<00:45,  2.21it/s]
 60%|██████    | 150/250 [01:33<00:39,  2.53it/s]
 61%|██████    | 152/250 [01:34<00:52,  1.88it/s]
 61%|██████    | 153/250 [01:37<01:45,  1.09s/it]
 62%|██████▏   | 154/250 [01:37<01:20,  1.19it/s]
 62%|██████▏   | 155/250 [01:37<01:01,  1.53it/s]
 63%|██████▎   | 157/250 [01:37<00:39,  2.37it/s]
 63%|██████▎   | 158/250 [01:40<01:18,  1.17it/s]
 64%|██████▎   | 159/250 [01:40<01:01,  1.49it/s]
 64%|██████▍   | 160/250 [01:40<00:49,  1.80it/s]
 64%|██████▍   | 161/250 [01:41<01:01,  1.44it/s]
 65%|██████▍   | 162/250 [01:42<00:58,  1.52it/s]
 65%|██████▌   | 163/250 [01:43<01:12,  1.19it/s]
 66%|██████▌   | 164/250 [01:44<01:11,  1.21it/s]
 66%|██████▌   | 165/250 [01:44<00:53,  1.58it/s]
 66%|██████▋   | 166/250 [01:44<00:49,  1.68it/s]
 67%|██████▋   | 167/250 [01:45<00:56,  1.47it/s]
 67%|██████▋   | 168/250 [01:47<01:21,  1.01it/s]
 68%|██████▊   | 169/250 [01:48<01:16,  1.05it/s]
 68%|██████▊   | 170/250 [01:48<01:06,  1.21it/s]
 68%|██████▊   | 171/250 [01:49<00:53,  1.49it/s]
 69%|██████▉   | 172/250 [01:50<01:16,  1.02it/s]
 70%|██████▉   | 174/250 [01:51<00:46,  1.63it/s]
 70%|███████   | 175/250 [01:52<00:56,  1.33it/s]
 70%|███████   | 176/250 [01:53<00:59,  1.24it/s]
 71%|███████   | 177/250 [01:54<01:01,  1.18it/s]
 71%|███████   | 178/250 [01:55<01:02,  1.15it/s]
 72%|███████▏  | 180/250 [01:55<00:40,  1.74it/s]
 72%|███████▏  | 181/250 [01:56<00:37,  1.82it/s]
 73%|███████▎  | 182/250 [01:56<00:32,  2.11it/s]
 73%|███████▎  | 183/250 [01:57<00:36,  1.83it/s]
 74%|███████▎  | 184/250 [01:57<00:34,  1.90it/s]
 74%|███████▍  | 186/250 [01:57<00:23,  2.74it/s]
 75%|███████▍  | 187/250 [01:59<00:36,  1.72it/s]
 75%|███████▌  | 188/250 [01:59<00:29,  2.09it/s]
 76%|███████▌  | 190/250 [02:01<00:40,  1.48it/s]
 76%|███████▋  | 191/250 [02:01<00:32,  1.84it/s]
 77%|███████▋  | 192/250 [02:03<00:49,  1.18it/s]
 77%|███████▋  | 193/250 [02:03<00:38,  1.46it/s]
 78%|███████▊  | 195/250 [02:04<00:31,  1.75it/s]
 79%|███████▉  | 197/250 [02:04<00:21,  2.46it/s]
 79%|███████▉  | 198/250 [02:04<00:19,  2.72it/s]
 80%|███████▉  | 199/250 [02:05<00:23,  2.19it/s]
 80%|████████  | 200/250 [02:05<00:21,  2.35it/s]
 81%|████████  | 202/250 [02:06<00:14,  3.30it/s]
 81%|████████  | 203/250 [02:06<00:12,  3.81it/s]
 82%|████████▏ | 204/250 [02:06<00:13,  3.41it/s]
 82%|████████▏ | 205/250 [02:06<00:12,  3.59it/s]
 82%|████████▏ | 206/250 [02:07<00:12,  3.39it/s]
 83%|████████▎ | 207/250 [02:07<00:19,  2.25it/s]
 84%|████████▎ | 209/250 [02:08<00:15,  2.70it/s]
 84%|████████▍ | 211/250 [02:09<00:12,  3.10it/s]
 85%|████████▍ | 212/250 [02:10<00:19,  1.98it/s]
 85%|████████▌ | 213/250 [02:10<00:15,  2.42it/s]
 86%|████████▌ | 214/250 [02:11<00:19,  1.85it/s]
 86%|████████▌ | 215/250 [02:12<00:28,  1.24it/s]
 86%|████████▋ | 216/250 [02:15<00:44,  1.30s/it]
 87%|████████▋ | 218/250 [02:15<00:26,  1.22it/s]
 88%|████████▊ | 219/250 [02:16<00:23,  1.31it/s]
 88%|████████▊ | 220/250 [02:17<00:23,  1.30it/s]
 88%|████████▊ | 221/250 [02:17<00:18,  1.55it/s]
 89%|████████▉ | 223/250 [02:18<00:13,  1.97it/s]
 90%|████████▉ | 224/250 [02:18<00:11,  2.35it/s]
 90%|█████████ | 226/250 [02:20<00:14,  1.65it/s]
 91%|█████████ | 227/250 [02:20<00:13,  1.66it/s]
 92%|█████████▏| 229/250 [02:21<00:10,  2.00it/s]
 92%|█████████▏| 231/250 [02:21<00:06,  2.76it/s]
 93%|█████████▎| 232/250 [02:22<00:07,  2.37it/s]
 93%|█████████▎| 233/250 [02:22<00:06,  2.73it/s]
 94%|█████████▎| 234/250 [02:22<00:05,  3.13it/s]
 94%|█████████▍| 235/250 [02:22<00:04,  3.28it/s]
 94%|█████████▍| 236/250 [02:23<00:04,  3.20it/s]
 95%|█████████▍| 237/250 [02:23<00:03,  3.30it/s]
 95%|█████████▌| 238/250 [02:23<00:04,  2.58it/s]
 96%|█████████▌| 239/250 [02:24<00:04,  2.36it/s]
 96%|█████████▌| 240/250 [02:24<00:03,  2.57it/s]
 97%|█████████▋| 243/250 [02:26<00:02,  2.44it/s]
 98%|█████████▊| 244/250 [02:27<00:03,  1.52it/s]
 98%|█████████▊| 245/250 [02:28<00:03,  1.40it/s]
 98%|█████████▊| 246/250 [02:33<00:06,  1.66s/it]
 99%|█████████▉| 247/250 [02:33<00:04,  1.38s/it]
 99%|█████████▉| 248/250 [02:34<00:02,  1.09s/it]
100%|██████████| 250/250 [02:34<00:00,  1.55it/s]
100%|██████████| 250/250 [02:34<00:00,  1.62it/s]
2025-05-16 14:37:51,384 - modnet - INFO - Loss per individual: ind 0: 38.719 	ind 1: 39.141 	ind 2: 43.331 	ind 3: 39.071 	ind 4: 50.250 	ind 5: 42.793 	ind 6: 38.502 	ind 7: 43.774 	ind 8: 42.088 	ind 9: 54.445 	ind 10: 68.603 	ind 11: 39.530 	ind 12: 41.526 	ind 13: 49.943 	ind 14: 51.680 	ind 15: 40.211 	ind 16: 41.728 	ind 17: 42.004 	ind 18: 39.896 	ind 19: 56.605 	ind 20: 39.190 	ind 21: 39.061 	ind 22: 39.287 	ind 23: 38.167 	ind 24: 42.096 	ind 25: 40.362 	ind 26: 40.249 	ind 27: 93.671 	ind 28: 39.355 	ind 29: 63.636 	ind 30: 71.931 	ind 31: 44.620 	ind 32: 39.708 	ind 33: 38.957 	ind 34: 56.352 	ind 35: 37.032 	ind 36: 39.778 	ind 37: 43.293 	ind 38: 38.358 	ind 39: 39.195 	ind 40: 202.175 	ind 41: 80.574 	ind 42: 87.643 	ind 43: 47.794 	ind 44: 40.714 	ind 45: 43.759 	ind 46: 39.624 	ind 47: 39.134 	ind 48: 47.005 	ind 49: 48.318 	
2025-05-16 14:37:51,385 - modnet - INFO - After generation 2:
2025-05-16 14:37:51,385 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 64, 'fraction1': 1, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 0.25, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 220, 'n_layers': 5, 'layer_mults': None}
2025-05-16 14:37:51,385 - modnet - INFO - Best validation loss: 37.0315
2025-05-16 14:37:51,385 - modnet - INFO - Generation number 3

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:09<40:34,  9.78s/it]
  1%|          | 2/250 [00:10<19:24,  4.70s/it]
  1%|          | 3/250 [00:11<11:01,  2.68s/it]
  2%|▏         | 4/250 [00:11<07:03,  1.72s/it]
  2%|▏         | 5/250 [00:12<05:59,  1.47s/it]
  2%|▏         | 6/250 [00:13<05:40,  1.40s/it]
  3%|▎         | 7/250 [00:14<04:22,  1.08s/it]
  4%|▎         | 9/250 [00:14<02:34,  1.56it/s]
  4%|▍         | 10/250 [00:14<02:18,  1.73it/s]
  5%|▍         | 12/250 [00:15<01:48,  2.19it/s]
  5%|▌         | 13/250 [00:16<02:25,  1.63it/s]
  6%|▌         | 14/250 [00:16<02:04,  1.89it/s]
  6%|▌         | 15/250 [00:17<02:10,  1.81it/s]
  6%|▋         | 16/250 [00:18<02:14,  1.74it/s]
  7%|▋         | 17/250 [00:18<02:24,  1.62it/s]
  7%|▋         | 18/250 [00:19<01:52,  2.06it/s]
  8%|▊         | 19/250 [00:19<01:43,  2.22it/s]
  8%|▊         | 20/250 [00:20<02:43,  1.41it/s]
  8%|▊         | 21/250 [00:20<02:10,  1.75it/s]
  9%|▉         | 23/250 [00:21<01:31,  2.48it/s]
 10%|▉         | 24/250 [00:21<01:35,  2.37it/s]
 10%|█         | 25/250 [00:21<01:17,  2.89it/s]
 10%|█         | 26/250 [00:22<01:15,  2.98it/s]
 12%|█▏        | 29/250 [00:24<02:08,  1.72it/s]
 12%|█▏        | 30/250 [00:25<02:00,  1.83it/s]
 12%|█▏        | 31/250 [00:25<01:54,  1.91it/s]
 13%|█▎        | 32/250 [00:26<02:09,  1.68it/s]
 13%|█▎        | 33/250 [00:27<02:25,  1.49it/s]
 14%|█▎        | 34/250 [00:27<02:13,  1.62it/s]
 14%|█▍        | 35/250 [00:29<03:20,  1.07it/s]
 14%|█▍        | 36/250 [00:30<02:58,  1.20it/s]
 15%|█▌        | 38/250 [00:30<02:11,  1.61it/s]
 16%|█▌        | 39/250 [00:31<02:19,  1.51it/s]
 16%|█▌        | 40/250 [00:31<02:03,  1.71it/s]
 16%|█▋        | 41/250 [00:32<02:09,  1.61it/s]
 17%|█▋        | 42/250 [00:33<01:58,  1.75it/s]
 17%|█▋        | 43/250 [00:33<01:51,  1.86it/s]
 18%|█▊        | 44/250 [00:35<03:28,  1.01s/it]
 18%|█▊        | 46/250 [00:36<02:17,  1.48it/s]
 19%|█▉        | 47/250 [00:37<02:34,  1.31it/s]
 19%|█▉        | 48/250 [00:38<02:55,  1.15it/s]
 20%|█▉        | 49/250 [00:39<03:07,  1.07it/s]
 20%|██        | 50/250 [00:40<02:51,  1.17it/s]
 20%|██        | 51/250 [00:40<02:24,  1.38it/s]
 21%|██        | 52/250 [00:41<02:44,  1.20it/s]
 22%|██▏       | 54/250 [00:42<01:50,  1.77it/s]
 22%|██▏       | 55/250 [00:42<01:52,  1.74it/s]
 22%|██▏       | 56/250 [00:43<01:43,  1.87it/s]
 23%|██▎       | 57/250 [00:43<01:27,  2.22it/s]
 24%|██▎       | 59/250 [00:43<01:06,  2.88it/s]
 24%|██▍       | 60/250 [00:45<01:56,  1.63it/s]
 24%|██▍       | 61/250 [00:45<01:43,  1.83it/s]
 25%|██▍       | 62/250 [00:46<02:01,  1.55it/s]
 26%|██▌       | 64/250 [00:46<01:15,  2.45it/s]
 26%|██▌       | 65/250 [00:46<01:05,  2.82it/s]
 26%|██▋       | 66/250 [00:49<02:41,  1.14it/s]
 27%|██▋       | 67/250 [00:49<02:13,  1.37it/s]
 27%|██▋       | 68/250 [00:50<02:25,  1.25it/s]
 28%|██▊       | 69/250 [00:54<04:38,  1.54s/it]
 28%|██▊       | 70/250 [00:54<03:24,  1.14s/it]
 29%|██▉       | 72/250 [00:54<01:58,  1.50it/s]
 30%|██▉       | 74/250 [00:55<01:40,  1.75it/s]
 30%|███       | 76/250 [00:55<01:14,  2.32it/s]
 31%|███       | 77/250 [00:55<01:03,  2.73it/s]
 31%|███       | 78/250 [00:56<01:00,  2.84it/s]
 32%|███▏      | 79/250 [00:56<01:15,  2.26it/s]
 32%|███▏      | 80/250 [00:57<01:18,  2.17it/s]
 32%|███▏      | 81/250 [00:57<01:03,  2.68it/s]
 33%|███▎      | 82/250 [00:57<00:57,  2.91it/s]
 33%|███▎      | 83/250 [00:59<01:43,  1.61it/s]
 34%|███▎      | 84/250 [00:59<01:27,  1.91it/s]
 34%|███▍      | 85/250 [01:00<02:03,  1.33it/s]
 34%|███▍      | 86/250 [01:00<01:31,  1.78it/s]
 35%|███▍      | 87/250 [01:01<01:18,  2.08it/s]
 36%|███▌      | 89/250 [01:01<00:48,  3.29it/s]
 36%|███▋      | 91/250 [01:01<00:33,  4.75it/s]
 37%|███▋      | 93/250 [01:01<00:29,  5.27it/s]
 38%|███▊      | 94/250 [01:02<00:37,  4.20it/s]
 38%|███▊      | 95/250 [01:02<00:38,  4.03it/s]
 38%|███▊      | 96/250 [01:02<00:33,  4.65it/s]
 39%|███▉      | 97/250 [01:03<00:56,  2.70it/s]
 39%|███▉      | 98/250 [01:05<02:29,  1.02it/s]
 40%|████      | 100/250 [01:06<01:38,  1.52it/s]
 40%|████      | 101/250 [01:06<01:32,  1.60it/s]
 41%|████      | 102/250 [01:07<01:19,  1.85it/s]
 42%|████▏     | 104/250 [01:09<01:45,  1.38it/s]
 42%|████▏     | 105/250 [01:11<02:34,  1.06s/it]
 42%|████▏     | 106/250 [01:12<02:22,  1.01it/s]
 43%|████▎     | 107/250 [01:12<01:56,  1.23it/s]
 43%|████▎     | 108/250 [01:13<02:04,  1.14it/s]
 44%|████▎     | 109/250 [01:14<01:50,  1.27it/s]
 44%|████▍     | 110/250 [01:14<01:25,  1.64it/s]
 44%|████▍     | 111/250 [01:15<01:36,  1.45it/s]
 45%|████▌     | 113/250 [01:15<01:05,  2.11it/s]
 46%|████▌     | 114/250 [01:15<00:53,  2.54it/s]
 46%|████▌     | 115/250 [01:17<01:52,  1.20it/s]
 46%|████▋     | 116/250 [01:18<01:57,  1.14it/s]
 47%|████▋     | 117/250 [01:19<02:02,  1.09it/s]
 47%|████▋     | 118/250 [01:20<02:03,  1.07it/s]
 48%|████▊     | 119/250 [01:21<01:40,  1.30it/s]
 48%|████▊     | 120/250 [01:22<01:49,  1.19it/s]
 49%|████▉     | 122/250 [01:22<01:04,  1.99it/s]
 49%|████▉     | 123/250 [01:22<00:51,  2.46it/s]
 50%|█████     | 125/250 [01:22<00:34,  3.58it/s]
 50%|█████     | 126/250 [01:23<00:51,  2.41it/s]
 51%|█████     | 127/250 [01:23<00:46,  2.64it/s]
 51%|█████     | 128/250 [01:24<01:03,  1.93it/s]
 52%|█████▏    | 130/250 [01:24<00:39,  3.00it/s]
 52%|█████▏    | 131/250 [01:25<00:35,  3.34it/s]
 53%|█████▎    | 132/250 [01:25<00:32,  3.61it/s]
 53%|█████▎    | 133/250 [01:26<00:49,  2.38it/s]
 54%|█████▎    | 134/250 [01:26<00:55,  2.09it/s]
 54%|█████▍    | 135/250 [01:27<01:13,  1.56it/s]
 54%|█████▍    | 136/250 [01:27<00:56,  2.02it/s]
 55%|█████▍    | 137/250 [01:28<00:52,  2.15it/s]
 55%|█████▌    | 138/250 [01:29<01:06,  1.69it/s]
 56%|█████▌    | 139/250 [01:30<01:25,  1.29it/s]
 56%|█████▌    | 140/250 [01:31<01:31,  1.20it/s]
 56%|█████▋    | 141/250 [01:32<01:37,  1.12it/s]
 57%|█████▋    | 142/250 [01:34<02:13,  1.24s/it]
 57%|█████▋    | 143/250 [01:34<01:36,  1.11it/s]
 58%|█████▊    | 144/250 [01:35<01:29,  1.19it/s]
 58%|█████▊    | 145/250 [01:37<02:15,  1.29s/it]
 58%|█████▊    | 146/250 [01:38<01:47,  1.03s/it]
 59%|█████▉    | 147/250 [01:38<01:27,  1.18it/s]
 59%|█████▉    | 148/250 [01:40<02:08,  1.26s/it]
 60%|█████▉    | 149/250 [01:41<01:57,  1.17s/it]
 60%|██████    | 150/250 [01:43<02:32,  1.53s/it]
 60%|██████    | 151/250 [01:47<03:27,  2.10s/it]
 61%|██████    | 152/250 [01:47<02:29,  1.52s/it]
 61%|██████    | 153/250 [01:48<02:17,  1.42s/it]
 62%|██████▏   | 155/250 [01:49<01:18,  1.22it/s]
 62%|██████▏   | 156/250 [01:49<01:05,  1.43it/s]
 63%|██████▎   | 157/250 [01:49<00:59,  1.56it/s]
 63%|██████▎   | 158/250 [01:50<00:50,  1.82it/s]
 64%|██████▎   | 159/250 [01:51<01:14,  1.21it/s]
 64%|██████▍   | 160/250 [01:52<01:11,  1.26it/s]
 64%|██████▍   | 161/250 [01:52<00:56,  1.57it/s]
 65%|██████▍   | 162/250 [01:52<00:44,  1.99it/s]
 65%|██████▌   | 163/250 [01:53<00:48,  1.80it/s]
 66%|██████▌   | 164/250 [01:53<00:42,  2.03it/s]
 66%|██████▋   | 166/250 [01:54<00:25,  3.25it/s]
 67%|██████▋   | 167/250 [01:55<00:44,  1.85it/s]
 68%|██████▊   | 169/250 [01:55<00:28,  2.83it/s]
 68%|██████▊   | 170/250 [01:55<00:25,  3.12it/s]
 68%|██████▊   | 171/250 [01:56<00:34,  2.28it/s]
 69%|██████▉   | 172/250 [01:56<00:30,  2.56it/s]
 69%|██████▉   | 173/250 [01:56<00:25,  2.96it/s]
 70%|███████   | 175/250 [01:57<00:30,  2.49it/s]
 70%|███████   | 176/250 [01:58<00:37,  1.96it/s]
 71%|███████   | 177/250 [01:58<00:29,  2.45it/s]
 71%|███████   | 178/250 [01:58<00:24,  2.97it/s]
 72%|███████▏  | 179/250 [01:59<00:21,  3.35it/s]
 72%|███████▏  | 180/250 [01:59<00:17,  3.99it/s]
 72%|███████▏  | 181/250 [01:59<00:16,  4.17it/s]
 73%|███████▎  | 183/250 [02:00<00:19,  3.37it/s]
 74%|███████▎  | 184/250 [02:00<00:25,  2.57it/s]
 74%|███████▍  | 185/250 [02:01<00:22,  2.95it/s]
 74%|███████▍  | 186/250 [02:01<00:27,  2.33it/s]
 75%|███████▍  | 187/250 [02:02<00:25,  2.51it/s]
 75%|███████▌  | 188/250 [02:02<00:31,  1.94it/s]
 76%|███████▌  | 189/250 [02:04<00:43,  1.41it/s]
 76%|███████▌  | 190/250 [02:04<00:44,  1.34it/s]
 76%|███████▋  | 191/250 [02:05<00:42,  1.40it/s]
 77%|███████▋  | 192/250 [02:06<00:41,  1.41it/s]
 77%|███████▋  | 193/250 [02:06<00:38,  1.47it/s]
 78%|███████▊  | 194/250 [02:07<00:36,  1.53it/s]
 78%|███████▊  | 195/250 [02:08<00:37,  1.46it/s]
 79%|███████▉  | 197/250 [02:11<00:58,  1.11s/it]
 79%|███████▉  | 198/250 [02:11<00:46,  1.12it/s]
 80%|████████  | 200/250 [02:12<00:36,  1.37it/s]
 80%|████████  | 201/250 [02:13<00:38,  1.27it/s]
 81%|████████  | 202/250 [02:14<00:37,  1.28it/s]
 81%|████████  | 203/250 [02:14<00:32,  1.46it/s]
 82%|████████▏ | 205/250 [02:17<00:47,  1.05s/it]
 82%|████████▏ | 206/250 [02:18<00:40,  1.10it/s]
 83%|████████▎ | 207/250 [02:18<00:30,  1.41it/s]
 83%|████████▎ | 208/250 [02:19<00:32,  1.28it/s]
 84%|████████▎ | 209/250 [02:20<00:35,  1.17it/s]
 84%|████████▍ | 210/250 [02:21<00:30,  1.32it/s]
 84%|████████▍ | 211/250 [02:21<00:26,  1.48it/s]
 85%|████████▍ | 212/250 [02:22<00:28,  1.34it/s]
 85%|████████▌ | 213/250 [02:22<00:21,  1.75it/s]
 86%|████████▌ | 214/250 [02:23<00:24,  1.47it/s]
 86%|████████▌ | 215/250 [02:23<00:21,  1.63it/s]
 87%|████████▋ | 217/250 [02:24<00:12,  2.75it/s]
 87%|████████▋ | 218/250 [02:26<00:23,  1.34it/s]
 88%|████████▊ | 219/250 [02:26<00:18,  1.70it/s]
 88%|████████▊ | 221/250 [02:26<00:10,  2.74it/s]
 89%|████████▉ | 222/250 [02:27<00:13,  2.03it/s]
 89%|████████▉ | 223/250 [02:28<00:21,  1.23it/s]
 90%|█████████ | 225/250 [02:30<00:19,  1.30it/s]
 90%|█████████ | 226/250 [02:30<00:16,  1.46it/s]
 91%|█████████ | 227/250 [02:30<00:13,  1.77it/s]
 92%|█████████▏| 230/250 [02:31<00:05,  3.34it/s]
 92%|█████████▏| 231/250 [02:31<00:07,  2.54it/s]
 93%|█████████▎| 232/250 [02:32<00:08,  2.13it/s]
 93%|█████████▎| 233/250 [02:33<00:08,  1.99it/s]
 94%|█████████▎| 234/250 [02:33<00:06,  2.44it/s]
 94%|█████████▍| 235/250 [02:33<00:06,  2.47it/s]
 94%|█████████▍| 236/250 [02:33<00:04,  3.01it/s]
 95%|█████████▍| 237/250 [02:34<00:06,  1.93it/s]
 96%|█████████▌| 239/250 [02:35<00:05,  2.00it/s]
 96%|█████████▌| 240/250 [02:37<00:07,  1.33it/s]
 96%|█████████▋| 241/250 [02:37<00:05,  1.57it/s]
 97%|█████████▋| 243/250 [02:38<00:03,  1.91it/s]
 98%|█████████▊| 244/250 [02:39<00:03,  1.72it/s]
 98%|█████████▊| 245/250 [02:39<00:02,  1.97it/s]
 98%|█████████▊| 246/250 [02:40<00:02,  1.38it/s]
 99%|█████████▉| 247/250 [02:43<00:04,  1.38s/it]
 99%|█████████▉| 248/250 [02:44<00:02,  1.08s/it]
100%|█████████▉| 249/250 [02:46<00:01,  1.45s/it]
100%|██████████| 250/250 [02:48<00:00,  1.46s/it]
100%|██████████| 250/250 [02:48<00:00,  1.49it/s]
2025-05-16 14:40:39,553 - modnet - INFO - Loss per individual: ind 0: 40.045 	ind 1: 41.718 	ind 2: 49.000 	ind 3: 46.168 	ind 4: 63.081 	ind 5: 47.517 	ind 6: 38.002 	ind 7: 40.549 	ind 8: 42.247 	ind 9: 50.515 	ind 10: 48.471 	ind 11: 38.489 	ind 12: 39.760 	ind 13: 43.197 	ind 14: 41.084 	ind 15: 39.267 	ind 16: 42.359 	ind 17: 49.111 	ind 18: 43.581 	ind 19: 51.364 	ind 20: 41.443 	ind 21: 42.205 	ind 22: 51.924 	ind 23: 45.898 	ind 24: 38.787 	ind 25: 37.868 	ind 26: 46.117 	ind 27: 43.797 	ind 28: 47.294 	ind 29: 44.092 	ind 30: 36.870 	ind 31: 40.800 	ind 32: 39.633 	ind 33: 45.351 	ind 34: 38.403 	ind 35: 45.890 	ind 36: 49.766 	ind 37: 79.867 	ind 38: 39.442 	ind 39: 39.547 	ind 40: 42.262 	ind 41: 38.679 	ind 42: 39.837 	ind 43: 53.556 	ind 44: 44.253 	ind 45: 39.133 	ind 46: 41.550 	ind 47: 40.822 	ind 48: 54.224 	ind 49: 39.577 	
2025-05-16 14:40:39,555 - modnet - INFO - After generation 3:
2025-05-16 14:40:39,555 - modnet - INFO - Best individual genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 160, 'fraction1': 1, 'fraction2': 0.5, 'fraction3': 1, 'fraction4': 0.75, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 220, 'n_layers': 2, 'layer_mults': None}
2025-05-16 14:40:39,555 - modnet - INFO - Best validation loss: 36.8698
2025-05-16 14:40:39,555 - modnet - INFO - Generation number 4

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:06<25:10,  6.07s/it]
  1%|          | 3/250 [00:06<07:02,  1.71s/it]
  2%|▏         | 4/250 [00:07<06:11,  1.51s/it]
  2%|▏         | 5/250 [00:09<06:30,  1.59s/it]
  2%|▏         | 6/250 [00:09<04:40,  1.15s/it]
  3%|▎         | 7/250 [00:11<05:14,  1.29s/it]
  3%|▎         | 8/250 [00:13<06:39,  1.65s/it]
  4%|▎         | 9/250 [00:13<05:00,  1.25s/it]
  4%|▍         | 10/250 [00:15<04:56,  1.24s/it]
  4%|▍         | 11/250 [00:15<04:06,  1.03s/it]
  5%|▍         | 12/250 [00:17<04:43,  1.19s/it]
  6%|▌         | 14/250 [00:17<02:40,  1.47it/s]
  6%|▌         | 15/250 [00:18<02:50,  1.38it/s]
  6%|▋         | 16/250 [00:19<02:50,  1.37it/s]
  7%|▋         | 18/250 [00:19<01:51,  2.08it/s]
  8%|▊         | 19/250 [00:19<01:54,  2.02it/s]
  8%|▊         | 21/250 [00:20<01:15,  3.01it/s]
  9%|▉         | 22/250 [00:20<01:12,  3.16it/s]
  9%|▉         | 23/250 [00:21<01:44,  2.16it/s]
 10%|▉         | 24/250 [00:21<01:46,  2.12it/s]
 10%|█         | 25/250 [00:22<01:48,  2.08it/s]
 10%|█         | 26/250 [00:24<03:21,  1.11it/s]
 11%|█         | 27/250 [00:24<02:54,  1.28it/s]
 12%|█▏        | 29/250 [00:25<01:57,  1.89it/s]
 12%|█▏        | 30/250 [00:26<02:21,  1.55it/s]
 12%|█▏        | 31/250 [00:26<02:04,  1.76it/s]
 13%|█▎        | 33/250 [00:26<01:23,  2.61it/s]
 14%|█▍        | 35/250 [00:28<02:13,  1.61it/s]
 14%|█▍        | 36/250 [00:29<02:33,  1.39it/s]
 15%|█▌        | 38/250 [00:31<02:38,  1.34it/s]
 16%|█▌        | 39/250 [00:31<02:09,  1.64it/s]
 16%|█▌        | 40/250 [00:31<01:51,  1.88it/s]
 16%|█▋        | 41/250 [00:32<01:32,  2.25it/s]
 17%|█▋        | 42/250 [00:33<02:07,  1.64it/s]
 17%|█▋        | 43/250 [00:34<02:46,  1.24it/s]
 18%|█▊        | 44/250 [00:35<02:59,  1.15it/s]
 18%|█▊        | 45/250 [00:35<02:15,  1.52it/s]
 18%|█▊        | 46/250 [00:35<01:55,  1.77it/s]
 19%|█▉        | 47/250 [00:37<03:08,  1.08it/s]
 19%|█▉        | 48/250 [00:38<03:21,  1.00it/s]
 20%|██        | 50/250 [00:39<01:56,  1.72it/s]
 20%|██        | 51/250 [00:39<01:55,  1.72it/s]
 21%|██        | 52/250 [00:39<01:31,  2.16it/s]
 21%|██        | 53/250 [00:40<01:45,  1.86it/s]
 22%|██▏       | 54/250 [00:41<01:47,  1.83it/s]
 22%|██▏       | 55/250 [00:41<01:55,  1.69it/s]
 23%|██▎       | 57/250 [00:42<01:27,  2.20it/s]
 23%|██▎       | 58/250 [00:42<01:17,  2.46it/s]
 24%|██▍       | 60/250 [00:43<01:02,  3.03it/s]
 24%|██▍       | 61/250 [00:44<01:31,  2.06it/s]
 25%|██▍       | 62/250 [00:44<01:41,  1.86it/s]
 25%|██▌       | 63/250 [00:44<01:20,  2.32it/s]
 26%|██▌       | 64/250 [00:45<01:11,  2.61it/s]
 26%|██▌       | 65/250 [00:46<01:39,  1.86it/s]
 26%|██▋       | 66/250 [00:46<01:17,  2.37it/s]
 27%|██▋       | 67/250 [00:46<01:28,  2.07it/s]
 27%|██▋       | 68/250 [00:47<01:17,  2.34it/s]
 28%|██▊       | 69/250 [00:48<02:15,  1.33it/s]
 28%|██▊       | 70/250 [00:49<02:31,  1.19it/s]
 28%|██▊       | 71/250 [00:49<01:55,  1.55it/s]
 29%|██▉       | 73/250 [00:50<01:29,  1.99it/s]slurmstepd: error: *** JOB 8538814 ON cns269 CANCELLED AT 2025-05-16T14:41:30 ***

Resources Used

Total Memory used                        - MEM              : 24GiB
Total CPU Time                           - CPU_Time         : 1-18:49:04
Execution Time                           - Wall_Time        : 01:20:17
total programme cpu time                 - Total_CPU        : 1-09:13:20
Total_CPU / CPU_Time  (%)                - ETA              : 77%
Number of alloc CPU                      - NCPUS            : 32
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 48
Mobilized Resources x Execution Time     - R_Wall_Time      : 2-16:13:36
CPU_Time / R_Wall_Time (%)               - ALPHA            : 66%
Energy (Joules)                                             : 1034453

