Job started on Thu Jul 10 12:52:16 CEST 2025
Running on node(s): cns266
Detected 'coGN' in feature_sets. Using specific data file: ../data/matbench_perovskites/matbench_perovskites_featurizedMM2020*_mattervial_coGNadj.csv
Using data file: ../data/matbench_perovskites/matbench_perovskites_featurizedMM2020Struct_mattervial_coGNadj.csv
Loaded data with shape: (18928, 9345)

Selecting features for feature_sets: ['megnet_mm', 'megnet_ofm', 'mvl_all', 'roost_oqmd', 'sisso', 'orb_v3', 'cogn_fold5']
 - Matching columns for 'megnet_mm' using pattern: ^MEGNet_MatMinerEncoded_v1_
   Found 758 columns for 'megnet_mm'.
 - Matching columns for 'megnet_ofm' using pattern: ^MEGNet_OFMEncoded_v1_
   Found 188 columns for 'megnet_ofm'.
 - Matching columns for 'mvl_all' using pattern: ^MVL(16|32)_
   Found 288 columns for 'mvl_all'.
 - Matching columns for 'roost_oqmd' using pattern: ^ROOST_oqmd_eform_
   Found 128 columns for 'roost_oqmd'.
 - Matching columns for 'sisso' using pattern: ^SISSO_
   Found 272 columns for 'sisso'.
 - Matching columns for 'orb_v3' using pattern: ^ORB_v3_
   Found 1792 columns for 'orb_v3'.
Traceback (most recent call last):
  File "generate_moddata_files.py", line 305, in <module>
    main(matbench_set=args.matbench_set, feature_sets=feature_sets_list)
  File "generate_moddata_files.py", line 242, in main
    raise ValueError(
ValueError: Unknown or unmatched feature set name: 'cogn_fold5'. Valid keys: ['matminer', 'sisso', 'sisso_v2', 'sisso_mb_dielectric', 'sisso_mb_phonons', 'sisso_mb_perovskites', 'sisso_mb_expt_is_metal', 'sisso_mb_steels', 'sisso_mb_jdft2d', 'sisso_mb_log_gvrh', 'sisso_mb_mp_e_form', 'sisso_noemd_hse_pbe_diff', 'sisso_noemd_shg', 'sisso_mb_log_kvrh', 'sisso_mb_glass', 'sisso_mb_mp_is_metal', 'sisso_mb_mp_gap', 'sisso_mb_expt_gap', 'roost_mpgap_lo', 'roost_mpgap_lmp', 'roost_oqmd_lo', 'roost_oqmd_lmp', 'megnet_mm', 'megnet_ofm', 'mvl32', 'mvl16', 'roost_lmp', 'roost_lo', 'roost_mpgap', 'roost_oqmd', 'roost_all', 'megnet_all', 'mvl_all', 'orb_v3', 'cogn_fold0', 'cogn_fold1', 'cogn_fold2', 'cogn_fold3', 'cogn_fold4']
Job finished on Thu Jul 10 12:52:59 CEST 2025
