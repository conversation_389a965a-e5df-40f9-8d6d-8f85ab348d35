Job started on Wed May 21 23:09:50 CEST 2025
Running on node(s): cns266
2025-05-21 23:10:07.650005: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 64 jobs
Preparing nested CV run for task 'matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso'
Found 1 matching files for matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso
2025-05-21 23:10:22,012 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f4d28435820> object, created with modnet version 0.1.13
GA settings:
    Population size: 30
    Number of generations: 30
    Early stopping: 6
    Refit: False
Preparing fold 1 ...
2025-05-21 23:10:22,056 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f4d28435970> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f1
2025-05-21 23:10:22,093 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f1!
Preparing fold 2 ...
2025-05-21 23:10:22,136 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f4d28209820> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f2
2025-05-21 23:10:22,290 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f2!
Preparing fold 3 ...
2025-05-21 23:10:22,328 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f4d27bf7c40> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f3
2025-05-21 23:10:22,365 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f3!
Preparing fold 4 ...
2025-05-21 23:10:22,402 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f4d28100430> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f4
2025-05-21 23:10:22,440 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f4!
Preparing fold 5 ...
2025-05-21 23:10:22,477 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f4d278010a0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f5
2025-05-21 23:10:22,514 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f5!
Training fold 1 ...
Processing fold 1 ...
2025-05-21 23:10:22.525898: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:10:22.526560: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:10:22.526611: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:10:22.526663: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:10:22.527063: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:10:22,667 - modnet - INFO - Targets:
2025-05-21 23:10:22,667 - modnet - INFO - 1) target: regression
2025-05-21 23:10:49,346 - modnet - INFO - Multiprocessing on 64 cores. Total of 128 cores available.
2025-05-21 23:10:49,346 - modnet - INFO - === Generation 0 ===
Population initialized with 60 individuals.
2025-05-21 23:10:49,350 - modnet - INFO - Generating 3 splits of 80/20  for Generation 0

  0%|          | 0/180 [00:00<?, ?it/s]
  1%|          | 1/180 [00:10<32:12, 10.79s/it]
  1%|          | 2/180 [00:13<17:37,  5.94s/it]
  2%|▏         | 3/180 [00:14<10:27,  3.55s/it]
  2%|▏         | 4/180 [00:14<06:31,  2.22s/it]
  3%|▎         | 5/180 [00:14<04:16,  1.46s/it]
  3%|▎         | 6/180 [00:15<03:45,  1.30s/it]
  4%|▍         | 8/180 [00:16<02:26,  1.17it/s]
  5%|▌         | 9/180 [00:16<02:13,  1.28it/s]
  6%|▌         | 10/180 [00:16<01:48,  1.57it/s]
  7%|▋         | 12/180 [00:17<01:04,  2.62it/s]
  7%|▋         | 13/180 [00:17<00:53,  3.11it/s]
  8%|▊         | 15/180 [00:17<00:36,  4.56it/s]
 10%|█         | 18/180 [00:17<00:31,  5.15it/s]
 11%|█         | 20/180 [00:18<00:27,  5.84it/s]
 12%|█▏        | 22/180 [00:18<00:22,  6.93it/s]
 13%|█▎        | 24/180 [00:18<00:19,  8.06it/s]
 14%|█▍        | 26/180 [00:19<00:32,  4.77it/s]
 16%|█▌        | 28/180 [00:19<00:32,  4.70it/s]
 16%|█▌        | 29/180 [00:19<00:31,  4.85it/s]
 18%|█▊        | 32/180 [00:19<00:20,  7.28it/s]
 19%|█▉        | 34/180 [00:20<00:26,  5.47it/s]
 20%|██        | 36/180 [00:20<00:20,  6.94it/s]
 22%|██▏       | 39/180 [00:20<00:16,  8.62it/s]
 23%|██▎       | 41/180 [00:20<00:13, 10.17it/s]
 24%|██▍       | 43/180 [00:21<00:13,  9.80it/s]
 25%|██▌       | 45/180 [00:21<00:14,  9.44it/s]
 26%|██▌       | 47/180 [00:21<00:17,  7.53it/s]
 27%|██▋       | 49/180 [00:21<00:14,  8.92it/s]
 28%|██▊       | 51/180 [00:22<00:16,  8.06it/s]
 29%|██▉       | 53/180 [00:22<00:21,  5.90it/s]
 31%|███       | 56/180 [00:23<00:18,  6.87it/s]
 32%|███▏      | 58/180 [00:23<00:21,  5.56it/s]
 33%|███▎      | 60/180 [00:23<00:17,  6.87it/s]
 34%|███▍      | 62/180 [00:23<00:15,  7.57it/s]
 36%|███▌      | 65/180 [00:24<00:11, 10.31it/s]
 37%|███▋      | 67/180 [00:24<00:09, 11.81it/s]
 38%|███▊      | 69/180 [00:24<00:10, 10.53it/s]
 39%|███▉      | 71/180 [00:25<00:16,  6.47it/s]
 41%|████      | 73/180 [00:25<00:16,  6.58it/s]
 42%|████▏     | 75/180 [00:25<00:14,  7.49it/s]
 43%|████▎     | 77/180 [00:25<00:14,  7.11it/s]
 43%|████▎     | 78/180 [00:26<00:16,  6.13it/s]
 44%|████▍     | 79/180 [00:26<00:15,  6.37it/s]
 44%|████▍     | 80/180 [00:26<00:15,  6.53it/s]
 46%|████▌     | 82/180 [00:26<00:12,  7.76it/s]
 47%|████▋     | 84/180 [00:26<00:10,  9.46it/s]
 48%|████▊     | 86/180 [00:27<00:15,  6.14it/s]
 49%|████▉     | 88/180 [00:27<00:11,  7.71it/s]
 50%|█████     | 90/180 [00:27<00:12,  7.16it/s]
 51%|█████     | 92/180 [00:28<00:13,  6.53it/s]
 52%|█████▏    | 94/180 [00:28<00:17,  5.02it/s]
 53%|█████▎    | 96/180 [00:29<00:17,  4.91it/s]
 54%|█████▍    | 97/180 [00:29<00:17,  4.84it/s]
 56%|█████▌    | 100/180 [00:29<00:10,  7.54it/s]
 57%|█████▋    | 102/180 [00:29<00:10,  7.63it/s]
 58%|█████▊    | 104/180 [00:30<00:11,  6.67it/s]
 58%|█████▊    | 105/180 [00:30<00:17,  4.40it/s]
 59%|█████▉    | 106/180 [00:30<00:15,  4.69it/s]
 59%|█████▉    | 107/180 [00:31<00:23,  3.15it/s]
 60%|██████    | 108/180 [00:32<00:27,  2.60it/s]
 61%|██████    | 109/180 [00:32<00:23,  2.97it/s]
 61%|██████    | 110/180 [00:32<00:20,  3.38it/s]
 62%|██████▏   | 111/180 [00:32<00:19,  3.63it/s]
 62%|██████▏   | 112/180 [00:32<00:17,  3.79it/s]
 63%|██████▎   | 114/180 [00:33<00:16,  3.93it/s]
 64%|██████▍   | 116/180 [00:33<00:13,  4.65it/s]
 65%|██████▌   | 117/180 [00:33<00:13,  4.63it/s]
 66%|██████▌   | 119/180 [00:34<00:10,  5.69it/s]
 67%|██████▋   | 120/180 [00:34<00:10,  5.52it/s]
 68%|██████▊   | 122/180 [00:34<00:10,  5.43it/s]
 69%|██████▉   | 124/180 [00:35<00:09,  5.67it/s]
 70%|███████   | 126/180 [00:35<00:07,  7.19it/s]
 71%|███████   | 128/180 [00:35<00:06,  8.31it/s]
 72%|███████▏  | 129/180 [00:35<00:08,  5.79it/s]
 72%|███████▏  | 130/180 [00:35<00:07,  6.36it/s]
 74%|███████▍  | 133/180 [00:35<00:05,  9.30it/s]
 75%|███████▌  | 135/180 [00:36<00:04,  9.84it/s]
 76%|███████▌  | 137/180 [00:36<00:03, 11.56it/s]
 77%|███████▋  | 139/180 [00:36<00:03, 10.66it/s]
 80%|████████  | 144/180 [00:36<00:02, 16.94it/s]
 81%|████████  | 146/180 [00:36<00:02, 11.98it/s]
 82%|████████▏ | 148/180 [00:37<00:02, 12.83it/s]
 83%|████████▎ | 150/180 [00:37<00:02, 10.08it/s]
 84%|████████▍ | 152/180 [00:37<00:02, 11.58it/s]
 86%|████████▌ | 154/180 [00:37<00:02, 11.34it/s]
 87%|████████▋ | 156/180 [00:38<00:02,  8.14it/s]
 88%|████████▊ | 159/180 [00:38<00:02,  8.09it/s]
 89%|████████▉ | 161/180 [00:39<00:03,  6.19it/s]
 91%|█████████ | 163/180 [00:39<00:02,  7.59it/s]
 92%|█████████▏| 165/180 [00:39<00:01,  8.22it/s]
 93%|█████████▎| 167/180 [00:39<00:01,  6.94it/s]
 93%|█████████▎| 168/180 [00:40<00:02,  4.95it/s]
 94%|█████████▍| 169/180 [00:40<00:02,  4.14it/s]
 95%|█████████▌| 171/180 [00:40<00:01,  5.32it/s]
 96%|█████████▌| 173/180 [00:41<00:01,  5.36it/s]
 98%|█████████▊| 176/180 [00:41<00:00,  7.32it/s]
 98%|█████████▊| 177/180 [00:41<00:00,  7.33it/s]
 99%|█████████▉| 178/180 [00:42<00:00,  3.32it/s]
 99%|█████████▉| 179/180 [00:42<00:00,  3.62it/s]
100%|██████████| 180/180 [00:52<00:00,  2.49s/it]
100%|██████████| 180/180 [00:52<00:00,  3.44it/s]
2025-05-21 23:11:42,419 - modnet - INFO - Loss per individual: ind0:51.846   ind1:59.579   ind2:42.815   ind3:49.764   ind4:44.975   ind5:56.161   ind6:105.034   ind7:98.129   ind8:51.560   ind9:80.319   ind10:50.888   ind11:65.733   ind12:47.012   ind13:59.431   ind14:51.508   ind15:76.963   ind16:90.051   ind17:58.773   ind18:49.336   ind19:562.557   ind20:45.623   ind21:55.782   ind22:65.819   ind23:65.611   ind24:82.368   ind25:55.955   ind26:109.995   ind27:53.331   ind28:51.560   ind29:68.304   ind30:98.688   ind31:52.526   ind32:53.160   ind33:75.952   ind34:64.485   ind35:126.180   ind36:52.918   ind37:62.447   ind38:69.282   ind39:72.308   ind40:41.642   ind41:59.967   ind42:46.323   ind43:56.006   ind44:50.224   ind45:48.624   ind46:73.175   ind47:57.248   ind48:54.062   ind49:42.368   ind50:64.873   ind51:63.395   ind52:60.424   ind53:49.999   ind54:70.779   ind55:91.900   ind56:55.528   ind57:49.596   ind58:69.539   ind59:156.513
2025-05-21 23:11:42,425 - modnet - INFO - Generation 0 best loss:  41.6422
2025-05-21 23:11:42,425 - modnet - INFO - Generation 0 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 32, 'fraction1': 1, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 0.75, 'xscale': 'minmax', 'lr': 0.01, 'batch_size': 32, 'n_feat': 600, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:11:42,426 - modnet - INFO - === Generation 1 ===

  0%|          | 0/90 [00:00<?, ?it/s]
  1%|          | 1/90 [00:03<04:50,  3.26s/it]
  2%|▏         | 2/90 [00:04<02:52,  1.96s/it]
  3%|▎         | 3/90 [00:04<01:46,  1.22s/it]
  4%|▍         | 4/90 [00:04<01:08,  1.25it/s]
  7%|▋         | 6/90 [00:05<00:36,  2.28it/s]
  8%|▊         | 7/90 [00:05<00:34,  2.39it/s]
 10%|█         | 9/90 [00:05<00:25,  3.24it/s]
 11%|█         | 10/90 [00:06<00:34,  2.31it/s]
 12%|█▏        | 11/90 [00:06<00:28,  2.78it/s]
 13%|█▎        | 12/90 [00:06<00:24,  3.15it/s]
 14%|█▍        | 13/90 [00:07<00:31,  2.48it/s]
 17%|█▋        | 15/90 [00:07<00:18,  3.99it/s]
 19%|█▉        | 17/90 [00:07<00:12,  5.75it/s]
 21%|██        | 19/90 [00:08<00:15,  4.61it/s]
 22%|██▏       | 20/90 [00:08<00:16,  4.27it/s]
 24%|██▍       | 22/90 [00:08<00:11,  5.83it/s]
 27%|██▋       | 24/90 [00:08<00:09,  7.25it/s]
 29%|██▉       | 26/90 [00:09<00:07,  9.13it/s]
 31%|███       | 28/90 [00:09<00:06,  9.26it/s]
 33%|███▎      | 30/90 [00:09<00:10,  5.76it/s]
 37%|███▋      | 33/90 [00:09<00:06,  8.45it/s]
 39%|███▉      | 35/90 [00:10<00:05,  9.25it/s]
 43%|████▎     | 39/90 [00:10<00:04, 11.18it/s]
 46%|████▌     | 41/90 [00:10<00:05,  8.21it/s]
 48%|████▊     | 43/90 [00:11<00:05,  9.10it/s]
 50%|█████     | 45/90 [00:11<00:04,  9.53it/s]
 52%|█████▏    | 47/90 [00:11<00:06,  6.89it/s]
 56%|█████▌    | 50/90 [00:12<00:05,  7.00it/s]
 58%|█████▊    | 52/90 [00:12<00:04,  7.65it/s]
 59%|█████▉    | 53/90 [00:12<00:06,  6.12it/s]
 61%|██████    | 55/90 [00:13<00:07,  4.49it/s]
 62%|██████▏   | 56/90 [00:13<00:08,  4.21it/s]
 64%|██████▍   | 58/90 [00:13<00:05,  5.51it/s]
 66%|██████▌   | 59/90 [00:13<00:05,  5.85it/s]
 67%|██████▋   | 60/90 [00:14<00:04,  6.35it/s]
 69%|██████▉   | 62/90 [00:14<00:05,  5.35it/s]
 71%|███████   | 64/90 [00:14<00:03,  6.76it/s]
 72%|███████▏  | 65/90 [00:14<00:03,  7.04it/s]
 74%|███████▍  | 67/90 [00:14<00:02,  8.00it/s]
 77%|███████▋  | 69/90 [00:15<00:02,  8.41it/s]
 79%|███████▉  | 71/90 [00:15<00:02,  7.74it/s]
 82%|████████▏ | 74/90 [00:15<00:01, 10.26it/s]
 86%|████████▌ | 77/90 [00:16<00:01,  7.94it/s]
 89%|████████▉ | 80/90 [00:16<00:01,  9.70it/s]
 91%|█████████ | 82/90 [00:16<00:00,  9.92it/s]
 93%|█████████▎| 84/90 [00:16<00:00,  8.75it/s]
 96%|█████████▌| 86/90 [00:17<00:00,  9.41it/s]
 98%|█████████▊| 88/90 [00:17<00:00,  9.26it/s]
100%|██████████| 90/90 [00:20<00:00,  1.99it/s]
100%|██████████| 90/90 [00:20<00:00,  4.45it/s]
2025-05-21 23:12:02,885 - modnet - INFO - Loss per individual: ind0:43.237   ind1:72.702   ind2:50.615   ind3:41.705   ind4:44.775   ind5:43.906   ind6:54.600   ind7:42.650   ind8:47.976   ind9:51.831   ind10:57.325   ind11:44.387   ind12:50.665   ind13:42.788   ind14:48.762   ind15:43.793   ind16:44.436   ind17:561.587   ind18:44.993   ind19:50.942   ind20:47.670   ind21:46.064   ind22:58.592   ind23:46.980   ind24:41.524   ind25:42.463   ind26:43.886   ind27:45.055   ind28:49.928   ind29:47.081
2025-05-21 23:12:02,891 - modnet - INFO - Generation 1 best loss: 41.5239
2025-05-21 23:12:02,891 - modnet - INFO - Generation 1 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 32, 'fraction1': 0.25, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.01, 'batch_size': 32, 'n_feat': 600, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:12:02,891 - modnet - INFO - === Generation 2 ===

  0%|          | 0/90 [00:00<?, ?it/s]
  1%|          | 1/90 [00:01<02:08,  1.45s/it]
  3%|▎         | 3/90 [00:01<00:37,  2.34it/s]
  4%|▍         | 4/90 [00:03<01:18,  1.10it/s]
  6%|▌         | 5/90 [00:03<00:57,  1.49it/s]
  7%|▋         | 6/90 [00:03<00:43,  1.94it/s]
  8%|▊         | 7/90 [00:03<00:34,  2.38it/s]
  9%|▉         | 8/90 [00:05<00:53,  1.53it/s]
 12%|█▏        | 11/90 [00:05<00:26,  3.02it/s]
 14%|█▍        | 13/90 [00:05<00:18,  4.27it/s]
 16%|█▌        | 14/90 [00:06<00:22,  3.35it/s]
 17%|█▋        | 15/90 [00:06<00:20,  3.68it/s]
 18%|█▊        | 16/90 [00:06<00:26,  2.83it/s]
 19%|█▉        | 17/90 [00:07<00:26,  2.74it/s]
 20%|██        | 18/90 [00:07<00:24,  2.90it/s]
 22%|██▏       | 20/90 [00:08<00:20,  3.35it/s]
 23%|██▎       | 21/90 [00:08<00:17,  3.96it/s]
 26%|██▌       | 23/90 [00:08<00:16,  4.16it/s]
 27%|██▋       | 24/90 [00:08<00:15,  4.20it/s]
 28%|██▊       | 25/90 [00:09<00:20,  3.11it/s]
 30%|███       | 27/90 [00:09<00:18,  3.41it/s]
 32%|███▏      | 29/90 [00:10<00:15,  3.81it/s]
 33%|███▎      | 30/90 [00:10<00:17,  3.47it/s]
 37%|███▋      | 33/90 [00:10<00:10,  5.67it/s]
 39%|███▉      | 35/90 [00:10<00:07,  7.11it/s]
 41%|████      | 37/90 [00:11<00:06,  8.15it/s]
 46%|████▌     | 41/90 [00:11<00:04, 12.24it/s]
 48%|████▊     | 43/90 [00:11<00:03, 12.88it/s]
 50%|█████     | 45/90 [00:11<00:03, 12.40it/s]
 53%|█████▎    | 48/90 [00:11<00:03, 12.80it/s]
 57%|█████▋    | 51/90 [00:12<00:02, 14.11it/s]
 59%|█████▉    | 53/90 [00:12<00:05,  7.39it/s]
 61%|██████    | 55/90 [00:13<00:05,  6.35it/s]
 63%|██████▎   | 57/90 [00:13<00:04,  7.64it/s]
 68%|██████▊   | 61/90 [00:13<00:02, 11.13it/s]
 71%|███████   | 64/90 [00:13<00:01, 13.53it/s]
 73%|███████▎  | 66/90 [00:13<00:01, 12.14it/s]
 76%|███████▌  | 68/90 [00:14<00:02,  9.93it/s]
 78%|███████▊  | 70/90 [00:14<00:02,  9.09it/s]
 80%|████████  | 72/90 [00:14<00:01,  9.17it/s]
 82%|████████▏ | 74/90 [00:14<00:01, 10.52it/s]
 84%|████████▍ | 76/90 [00:15<00:02,  4.80it/s]
 86%|████████▌ | 77/90 [00:15<00:03,  4.29it/s]
 88%|████████▊ | 79/90 [00:16<00:01,  5.53it/s]
 90%|█████████ | 81/90 [00:16<00:01,  4.59it/s]
 91%|█████████ | 82/90 [00:17<00:03,  2.40it/s]
 92%|█████████▏| 83/90 [00:18<00:03,  2.27it/s]
 93%|█████████▎| 84/90 [00:18<00:02,  2.55it/s]
 94%|█████████▍| 85/90 [00:20<00:03,  1.44it/s]
 96%|█████████▌| 86/90 [00:20<00:02,  1.70it/s]
 97%|█████████▋| 87/90 [00:23<00:03,  1.20s/it]
 98%|█████████▊| 88/90 [00:24<00:02,  1.17s/it]
 99%|█████████▉| 89/90 [00:29<00:02,  2.17s/it]
100%|██████████| 90/90 [00:34<00:00,  3.12s/it]
100%|██████████| 90/90 [00:34<00:00,  2.59it/s]
2025-05-21 23:12:37,859 - modnet - INFO - Loss per individual: ind0:562.557   ind1:41.951   ind2:46.934   ind3:43.095   ind4:54.306   ind5:42.114   ind6:60.957   ind7:49.595   ind8:46.485   ind9:46.373   ind10:55.045   ind11:56.084   ind12:43.503   ind13:43.446   ind14:134.214   ind15:44.706   ind16:516.591   ind17:41.585   ind18:54.106   ind19:49.264   ind20:40.830   ind21:44.909   ind22:53.432   ind23:43.050   ind24:45.629   ind25:41.466   ind26:57.198   ind27:52.739   ind28:45.108   ind29:550.776
2025-05-21 23:12:37,865 - modnet - INFO - Generation 2 best loss: 40.8297
2025-05-21 23:12:37,865 - modnet - INFO - Generation 2 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.25, 'fraction2': 1, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.01, 'batch_size': 16, 'n_feat': 600, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:12:37,865 - modnet - INFO - === Generation 3 ===

  0%|          | 0/90 [00:00<?, ?it/s]
  1%|          | 1/90 [00:02<03:00,  2.03s/it]
  2%|▏         | 2/90 [00:02<01:28,  1.00s/it]
  3%|▎         | 3/90 [00:02<00:51,  1.68it/s]
  6%|▌         | 5/90 [00:02<00:25,  3.40it/s]
  7%|▋         | 6/90 [00:02<00:20,  4.16it/s]
  8%|▊         | 7/90 [00:04<00:51,  1.62it/s]
  9%|▉         | 8/90 [00:04<00:53,  1.53it/s]
 10%|█         | 9/90 [00:05<00:49,  1.65it/s]
 11%|█         | 10/90 [00:05<00:40,  1.99it/s]
 12%|█▏        | 11/90 [00:05<00:31,  2.51it/s]
 14%|█▍        | 13/90 [00:06<00:29,  2.64it/s]
 16%|█▌        | 14/90 [00:06<00:24,  3.12it/s]
 17%|█▋        | 15/90 [00:07<00:25,  2.89it/s]
 18%|█▊        | 16/90 [00:07<00:23,  3.14it/s]
 19%|█▉        | 17/90 [00:07<00:20,  3.48it/s]
 20%|██        | 18/90 [00:07<00:22,  3.25it/s]
 22%|██▏       | 20/90 [00:08<00:15,  4.41it/s]
 23%|██▎       | 21/90 [00:08<00:22,  3.08it/s]
 24%|██▍       | 22/90 [00:08<00:18,  3.60it/s]
 27%|██▋       | 24/90 [00:09<00:16,  4.06it/s]
 29%|██▉       | 26/90 [00:09<00:11,  5.50it/s]
 32%|███▏      | 29/90 [00:09<00:07,  8.36it/s]
 34%|███▍      | 31/90 [00:09<00:06,  8.71it/s]
 37%|███▋      | 33/90 [00:10<00:11,  4.83it/s]
 38%|███▊      | 34/90 [00:10<00:10,  5.31it/s]
 41%|████      | 37/90 [00:10<00:06,  7.65it/s]
 43%|████▎     | 39/90 [00:11<00:06,  8.14it/s]
 46%|████▌     | 41/90 [00:11<00:06,  7.61it/s]
 48%|████▊     | 43/90 [00:11<00:05,  8.70it/s]
 51%|█████     | 46/90 [00:11<00:03, 11.56it/s]
 53%|█████▎    | 48/90 [00:11<00:03, 11.20it/s]
 56%|█████▌    | 50/90 [00:12<00:03, 11.28it/s]
 58%|█████▊    | 52/90 [00:12<00:03, 11.00it/s]
 60%|██████    | 54/90 [00:12<00:02, 12.10it/s]
 62%|██████▏   | 56/90 [00:12<00:03,  9.44it/s]
 64%|██████▍   | 58/90 [00:12<00:03, 10.10it/s]
 68%|██████▊   | 61/90 [00:13<00:02, 12.65it/s]
 73%|███████▎  | 66/90 [00:13<00:01, 18.38it/s]
 77%|███████▋  | 69/90 [00:13<00:01, 13.25it/s]
 79%|███████▉  | 71/90 [00:13<00:01, 12.31it/s]
 81%|████████  | 73/90 [00:14<00:02,  5.87it/s]
 83%|████████▎ | 75/90 [00:15<00:02,  5.46it/s]
 86%|████████▌ | 77/90 [00:15<00:02,  6.10it/s]
 87%|████████▋ | 78/90 [00:15<00:02,  5.44it/s]
 91%|█████████ | 82/90 [00:15<00:00,  8.92it/s]
 93%|█████████▎| 84/90 [00:16<00:01,  4.62it/s]
 96%|█████████▌| 86/90 [00:17<00:00,  4.55it/s]
 97%|█████████▋| 87/90 [00:18<00:01,  2.96it/s]
 98%|█████████▊| 88/90 [00:19<00:00,  2.06it/s]
 99%|█████████▉| 89/90 [00:20<00:00,  1.85it/s]
100%|██████████| 90/90 [00:22<00:00,  1.14it/s]
100%|██████████| 90/90 [00:22<00:00,  4.07it/s]
2025-05-21 23:13:00,214 - modnet - INFO - Loss per individual: ind0:550.776   ind1:43.350   ind2:43.292   ind3:40.496   ind4:78.516   ind5:42.422   ind6:558.527   ind7:43.224   ind8:41.017   ind9:53.855   ind10:42.737   ind11:54.187   ind12:41.798   ind13:50.701   ind14:47.740   ind15:43.705   ind16:45.475   ind17:44.687   ind18:43.194   ind19:46.184   ind20:47.829   ind21:562.557   ind22:43.330   ind23:43.382   ind24:40.245   ind25:45.849   ind26:43.058   ind27:45.366   ind28:41.291   ind29:550.776
2025-05-21 23:13:00,221 - modnet - INFO - Generation 3 best loss: 40.2450
2025-05-21 23:13:00,221 - modnet - INFO - Generation 3 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.25, 'fraction2': 0.5, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.01, 'batch_size': 16, 'n_feat': 600, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:13:00,221 - modnet - INFO - === Generation 4 ===

  0%|          | 0/90 [00:00<?, ?it/s]
  1%|          | 1/90 [00:04<06:02,  4.07s/it]
  2%|▏         | 2/90 [00:04<02:38,  1.80s/it]
  3%|▎         | 3/90 [00:04<01:40,  1.16s/it]
  4%|▍         | 4/90 [00:05<01:20,  1.07it/s]
  7%|▋         | 6/90 [00:05<00:42,  1.98it/s]
  8%|▊         | 7/90 [00:05<00:32,  2.54it/s]
  9%|▉         | 8/90 [00:06<00:45,  1.79it/s]
 10%|█         | 9/90 [00:06<00:36,  2.23it/s]
 11%|█         | 10/90 [00:07<00:38,  2.06it/s]
 13%|█▎        | 12/90 [00:08<00:41,  1.89it/s]
 14%|█▍        | 13/90 [00:08<00:33,  2.27it/s]
 16%|█▌        | 14/90 [00:09<00:32,  2.36it/s]
 19%|█▉        | 17/90 [00:09<00:16,  4.33it/s]
 21%|██        | 19/90 [00:09<00:12,  5.68it/s]
 24%|██▍       | 22/90 [00:09<00:09,  7.38it/s]
 27%|██▋       | 24/90 [00:10<00:15,  4.34it/s]
 31%|███       | 28/90 [00:10<00:08,  7.01it/s]
 34%|███▍      | 31/90 [00:10<00:07,  7.65it/s]
 37%|███▋      | 33/90 [00:11<00:06,  8.27it/s]
 39%|███▉      | 35/90 [00:11<00:06,  8.04it/s]
 41%|████      | 37/90 [00:11<00:07,  7.56it/s]
 44%|████▍     | 40/90 [00:11<00:04, 10.09it/s]
 47%|████▋     | 42/90 [00:12<00:07,  6.08it/s]
 51%|█████     | 46/90 [00:12<00:04,  9.42it/s]
 54%|█████▍    | 49/90 [00:12<00:03, 11.78it/s]
 58%|█████▊    | 52/90 [00:13<00:04,  8.35it/s]
 61%|██████    | 55/90 [00:13<00:03, 10.57it/s]
 63%|██████▎   | 57/90 [00:13<00:03, 10.90it/s]
 66%|██████▌   | 59/90 [00:14<00:05,  6.05it/s]
 68%|██████▊   | 61/90 [00:14<00:05,  5.47it/s]
 72%|███████▏  | 65/90 [00:15<00:03,  7.48it/s]
 74%|███████▍  | 67/90 [00:15<00:03,  7.10it/s]
 76%|███████▌  | 68/90 [00:15<00:03,  6.70it/s]
 78%|███████▊  | 70/90 [00:15<00:02,  8.08it/s]
 82%|████████▏ | 74/90 [00:16<00:01, 10.35it/s]
 84%|████████▍ | 76/90 [00:16<00:01, 10.33it/s]
 87%|████████▋ | 78/90 [00:16<00:01,  6.96it/s]
 88%|████████▊ | 79/90 [00:16<00:01,  6.86it/s]
 90%|█████████ | 81/90 [00:17<00:01,  7.36it/s]
 92%|█████████▏| 83/90 [00:17<00:01,  5.75it/s]
 93%|█████████▎| 84/90 [00:17<00:00,  6.06it/s]
 94%|█████████▍| 85/90 [00:18<00:00,  5.11it/s]
 96%|█████████▌| 86/90 [00:19<00:01,  2.52it/s]
 97%|█████████▋| 87/90 [00:19<00:01,  2.49it/s]
 99%|█████████▉| 89/90 [00:20<00:00,  2.82it/s]
100%|██████████| 90/90 [00:25<00:00,  1.47s/it]
100%|██████████| 90/90 [00:25<00:00,  3.53it/s]
2025-05-21 23:13:25,998 - modnet - INFO - Loss per individual: ind0:54.411   ind1:42.140   ind2:45.226   ind3:42.903   ind4:41.995   ind5:41.459   ind6:40.795   ind7:42.987   ind8:42.131   ind9:41.798   ind10:47.078   ind11:43.758   ind12:45.757   ind13:44.334   ind14:42.776   ind15:50.966   ind16:558.527   ind17:41.953   ind18:40.343   ind19:550.776   ind20:43.829   ind21:42.343   ind22:40.490   ind23:42.268   ind24:46.845   ind25:558.527   ind26:45.435   ind27:42.184   ind28:44.400   ind29:41.124
2025-05-21 23:13:26,007 - modnet - INFO - Generation 4 best loss: 40.2450
2025-05-21 23:13:26,007 - modnet - INFO - Generation 4 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.25, 'fraction2': 0.5, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.01, 'batch_size': 16, 'n_feat': 600, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:13:26,007 - modnet - INFO - === Generation 5 ===

  0%|          | 0/90 [00:00<?, ?it/s]
  1%|          | 1/90 [00:01<02:57,  1.99s/it]
  2%|▏         | 2/90 [00:02<01:17,  1.13it/s]
  4%|▍         | 4/90 [00:02<00:47,  1.82it/s]
  7%|▋         | 6/90 [00:02<00:26,  3.15it/s]
  9%|▉         | 8/90 [00:03<00:21,  3.74it/s]
 10%|█         | 9/90 [00:03<00:20,  4.01it/s]
 11%|█         | 10/90 [00:04<00:29,  2.71it/s]
 14%|█▍        | 13/90 [00:04<00:19,  3.86it/s]
 16%|█▌        | 14/90 [00:05<00:26,  2.87it/s]
 18%|█▊        | 16/90 [00:05<00:21,  3.37it/s]
 19%|█▉        | 17/90 [00:07<00:40,  1.81it/s]
 20%|██        | 18/90 [00:07<00:32,  2.19it/s]
 21%|██        | 19/90 [00:07<00:31,  2.28it/s]
 22%|██▏       | 20/90 [00:08<00:27,  2.54it/s]
 23%|██▎       | 21/90 [00:08<00:24,  2.77it/s]
 26%|██▌       | 23/90 [00:08<00:15,  4.24it/s]
 29%|██▉       | 26/90 [00:09<00:12,  4.99it/s]
 30%|███       | 27/90 [00:09<00:14,  4.42it/s]
 32%|███▏      | 29/90 [00:09<00:10,  5.57it/s]
 36%|███▌      | 32/90 [00:10<00:10,  5.71it/s]
 37%|███▋      | 33/90 [00:10<00:11,  5.08it/s]
 39%|███▉      | 35/90 [00:10<00:08,  6.60it/s]
 41%|████      | 37/90 [00:10<00:06,  8.33it/s]
 43%|████▎     | 39/90 [00:10<00:05,  9.49it/s]
 47%|████▋     | 42/90 [00:10<00:03, 12.88it/s]
 52%|█████▏    | 47/90 [00:10<00:02, 19.76it/s]
 56%|█████▌    | 50/90 [00:11<00:01, 20.56it/s]
 59%|█████▉    | 53/90 [00:11<00:02, 16.93it/s]
 62%|██████▏   | 56/90 [00:11<00:02, 16.03it/s]
 64%|██████▍   | 58/90 [00:11<00:02, 15.49it/s]
 68%|██████▊   | 61/90 [00:11<00:02, 13.86it/s]
 70%|███████   | 63/90 [00:12<00:01, 14.45it/s]
 72%|███████▏  | 65/90 [00:12<00:01, 13.28it/s]
 76%|███████▌  | 68/90 [00:12<00:01, 14.44it/s]
 78%|███████▊  | 70/90 [00:12<00:01, 11.40it/s]
 80%|████████  | 72/90 [00:12<00:01, 12.79it/s]
 82%|████████▏ | 74/90 [00:13<00:02,  7.49it/s]
 84%|████████▍ | 76/90 [00:14<00:02,  5.14it/s]
 86%|████████▌ | 77/90 [00:14<00:02,  4.75it/s]
 88%|████████▊ | 79/90 [00:14<00:01,  6.05it/s]
 90%|█████████ | 81/90 [00:15<00:02,  4.27it/s]
 94%|█████████▍| 85/90 [00:15<00:00,  5.56it/s]
 97%|█████████▋| 87/90 [00:16<00:00,  6.01it/s]
 98%|█████████▊| 88/90 [00:18<00:01,  1.88it/s]
 99%|█████████▉| 89/90 [00:21<00:00,  1.05it/s]
100%|██████████| 90/90 [00:23<00:00,  1.10s/it]
100%|██████████| 90/90 [00:23<00:00,  3.89it/s]
2025-05-21 23:13:49,435 - modnet - INFO - Loss per individual: ind0:51.920   ind1:40.953   ind2:43.163   ind3:558.527   ind4:41.041   ind5:43.988   ind6:516.591   ind7:43.156   ind8:558.527   ind9:135.208   ind10:48.079   ind11:40.328   ind12:40.678   ind13:42.976   ind14:41.820   ind15:45.908   ind16:42.261   ind17:41.457   ind18:565.347   ind19:45.079   ind20:45.539   ind21:43.378   ind22:558.527   ind23:45.803   ind24:54.640   ind25:64.217   ind26:44.210   ind27:43.116   ind28:565.312   ind29:562.557
2025-05-21 23:13:49,455 - modnet - INFO - Generation 5 best loss: 40.2450
2025-05-21 23:13:49,455 - modnet - INFO - Generation 5 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.25, 'fraction2': 0.5, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.01, 'batch_size': 16, 'n_feat': 600, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:13:49,455 - modnet - INFO - === Generation 6 ===

  0%|          | 0/90 [00:00<?, ?it/s]
  1%|          | 1/90 [00:02<04:04,  2.74s/it]
  2%|▏         | 2/90 [00:02<01:44,  1.19s/it]
  4%|▍         | 4/90 [00:03<00:49,  1.75it/s]
  7%|▋         | 6/90 [00:03<00:28,  2.99it/s]
  8%|▊         | 7/90 [00:04<00:49,  1.68it/s]
  9%|▉         | 8/90 [00:04<00:39,  2.06it/s]
 11%|█         | 10/90 [00:05<00:29,  2.71it/s]
 12%|█▏        | 11/90 [00:05<00:29,  2.64it/s]
 13%|█▎        | 12/90 [00:06<00:32,  2.39it/s]
 14%|█▍        | 13/90 [00:06<00:35,  2.16it/s]
 16%|█▌        | 14/90 [00:07<00:43,  1.76it/s]
 19%|█▉        | 17/90 [00:08<00:23,  3.05it/s]
 21%|██        | 19/90 [00:08<00:18,  3.90it/s]
 23%|██▎       | 21/90 [00:08<00:14,  4.80it/s]
 24%|██▍       | 22/90 [00:08<00:13,  4.94it/s]
 27%|██▋       | 24/90 [00:09<00:13,  5.00it/s]
 28%|██▊       | 25/90 [00:09<00:12,  5.00it/s]
 29%|██▉       | 26/90 [00:09<00:11,  5.47it/s]
 32%|███▏      | 29/90 [00:09<00:10,  5.85it/s]
 33%|███▎      | 30/90 [00:10<00:09,  6.34it/s]
 34%|███▍      | 31/90 [00:10<00:10,  5.69it/s]
 37%|███▋      | 33/90 [00:10<00:07,  7.55it/s]
 39%|███▉      | 35/90 [00:10<00:06,  8.58it/s]
 42%|████▏     | 38/90 [00:10<00:04, 11.22it/s]
 44%|████▍     | 40/90 [00:10<00:04, 11.98it/s]
 48%|████▊     | 43/90 [00:11<00:03, 14.71it/s]
 50%|█████     | 45/90 [00:11<00:04, 11.00it/s]
 52%|█████▏    | 47/90 [00:11<00:03, 11.41it/s]
 54%|█████▍    | 49/90 [00:11<00:03, 11.16it/s]
 60%|██████    | 54/90 [00:11<00:01, 18.07it/s]
 63%|██████▎   | 57/90 [00:12<00:02, 12.10it/s]
 66%|██████▌   | 59/90 [00:12<00:03,  9.05it/s]
 68%|██████▊   | 61/90 [00:13<00:04,  7.09it/s]
 70%|███████   | 63/90 [00:13<00:03,  7.80it/s]
 72%|███████▏  | 65/90 [00:13<00:04,  6.22it/s]
 73%|███████▎  | 66/90 [00:14<00:04,  5.14it/s]
 74%|███████▍  | 67/90 [00:14<00:04,  5.61it/s]
 78%|███████▊  | 70/90 [00:14<00:02,  7.60it/s]
 79%|███████▉  | 71/90 [00:14<00:02,  6.69it/s]
 81%|████████  | 73/90 [00:14<00:02,  8.20it/s]
 84%|████████▍ | 76/90 [00:15<00:01,  8.34it/s]
 87%|████████▋ | 78/90 [00:16<00:03,  3.95it/s]
 90%|█████████ | 81/90 [00:16<00:01,  5.10it/s]
 91%|█████████ | 82/90 [00:17<00:01,  4.23it/s]
 92%|█████████▏| 83/90 [00:17<00:01,  3.89it/s]
 94%|█████████▍| 85/90 [00:17<00:01,  4.82it/s]
 96%|█████████▌| 86/90 [00:18<00:00,  4.15it/s]
 97%|█████████▋| 87/90 [00:18<00:00,  3.88it/s]
 99%|█████████▉| 89/90 [00:18<00:00,  5.17it/s]
100%|██████████| 90/90 [00:23<00:00,  1.11s/it]
100%|██████████| 90/90 [00:23<00:00,  3.91it/s]
2025-05-21 23:14:12,781 - modnet - INFO - Loss per individual: ind0:45.680   ind1:43.256   ind2:45.690   ind3:41.356   ind4:550.776   ind5:43.029   ind6:47.246   ind7:44.683   ind8:45.078   ind9:46.571   ind10:564.038   ind11:39.175   ind12:96.200   ind13:44.291   ind14:40.584   ind15:41.223   ind16:42.605   ind17:565.781   ind18:42.526   ind19:41.472   ind20:148.167   ind21:40.451   ind22:44.269   ind23:47.065   ind24:75.673   ind25:564.038   ind26:42.790   ind27:47.156   ind28:561.587   ind29:44.825
2025-05-21 23:14:12,794 - modnet - INFO - Generation 6 best loss: 39.1747
2025-05-21 23:14:12,794 - modnet - INFO - Generation 6 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 1, 'fraction2': 0.5, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.01, 'batch_size': 16, 'n_feat': 600, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:14:12,794 - modnet - INFO - === Generation 7 ===

  0%|          | 0/90 [00:00<?, ?it/s]
  1%|          | 1/90 [00:02<03:25,  2.31s/it]
  3%|▎         | 3/90 [00:02<00:56,  1.55it/s]
  4%|▍         | 4/90 [00:03<00:57,  1.49it/s]
  6%|▌         | 5/90 [00:03<00:41,  2.07it/s]
  7%|▋         | 6/90 [00:03<00:30,  2.77it/s]
  8%|▊         | 7/90 [00:04<01:02,  1.33it/s]
 11%|█         | 10/90 [00:05<00:30,  2.59it/s]
 12%|█▏        | 11/90 [00:05<00:30,  2.61it/s]
 13%|█▎        | 12/90 [00:06<00:35,  2.19it/s]
 14%|█▍        | 13/90 [00:06<00:36,  2.14it/s]
 16%|█▌        | 14/90 [00:07<00:31,  2.40it/s]
 17%|█▋        | 15/90 [00:07<00:29,  2.57it/s]
 20%|██        | 18/90 [00:07<00:18,  3.85it/s]
 21%|██        | 19/90 [00:08<00:19,  3.68it/s]
 22%|██▏       | 20/90 [00:08<00:17,  3.91it/s]
 23%|██▎       | 21/90 [00:08<00:15,  4.56it/s]
 24%|██▍       | 22/90 [00:08<00:16,  4.16it/s]
 26%|██▌       | 23/90 [00:08<00:14,  4.66it/s]
 27%|██▋       | 24/90 [00:09<00:19,  3.35it/s]
 28%|██▊       | 25/90 [00:09<00:18,  3.58it/s]
 30%|███       | 27/90 [00:10<00:14,  4.37it/s]
 34%|███▍      | 31/90 [00:10<00:07,  7.91it/s]
 36%|███▌      | 32/90 [00:10<00:07,  8.13it/s]
 40%|████      | 36/90 [00:10<00:04, 13.21it/s]
 42%|████▏     | 38/90 [00:10<00:04, 12.46it/s]
 44%|████▍     | 40/90 [00:10<00:04, 12.01it/s]
 48%|████▊     | 43/90 [00:10<00:03, 14.04it/s]
 50%|█████     | 45/90 [00:11<00:03, 12.23it/s]
 56%|█████▌    | 50/90 [00:11<00:02, 19.13it/s]
 59%|█████▉    | 53/90 [00:11<00:02, 14.62it/s]
 61%|██████    | 55/90 [00:11<00:02, 12.97it/s]
 63%|██████▎   | 57/90 [00:12<00:02, 11.51it/s]
 66%|██████▌   | 59/90 [00:12<00:02, 11.46it/s]
 68%|██████▊   | 61/90 [00:12<00:03,  8.72it/s]
 71%|███████   | 64/90 [00:13<00:03,  8.29it/s]
 72%|███████▏  | 65/90 [00:13<00:03,  6.42it/s]
 73%|███████▎  | 66/90 [00:13<00:03,  6.56it/s]
 76%|███████▌  | 68/90 [00:13<00:02,  8.17it/s]
 78%|███████▊  | 70/90 [00:13<00:02,  8.67it/s]
 80%|████████  | 72/90 [00:14<00:02,  6.02it/s]
 81%|████████  | 73/90 [00:14<00:02,  6.48it/s]
 82%|████████▏ | 74/90 [00:14<00:02,  6.76it/s]
 83%|████████▎ | 75/90 [00:14<00:02,  7.15it/s]
 86%|████████▌ | 77/90 [00:15<00:03,  4.27it/s]
 87%|████████▋ | 78/90 [00:15<00:02,  4.50it/s]
 88%|████████▊ | 79/90 [00:16<00:02,  3.96it/s]
 89%|████████▉ | 80/90 [00:16<00:02,  3.87it/s]
 90%|█████████ | 81/90 [00:16<00:03,  2.83it/s]
 91%|█████████ | 82/90 [00:17<00:02,  3.36it/s]
 93%|█████████▎| 84/90 [00:17<00:01,  3.47it/s]
 94%|█████████▍| 85/90 [00:18<00:02,  1.86it/s]
 96%|█████████▌| 86/90 [00:19<00:02,  1.80it/s]
 97%|█████████▋| 87/90 [00:25<00:05,  1.87s/it]
 98%|█████████▊| 88/90 [00:27<00:04,  2.02s/it]
 99%|█████████▉| 89/90 [00:27<00:01,  1.54s/it]
100%|██████████| 90/90 [00:32<00:00,  2.41s/it]
100%|██████████| 90/90 [00:32<00:00,  2.78it/s]
2025-05-21 23:14:45,443 - modnet - INFO - Loss per individual: ind0:54.377   ind1:45.095   ind2:41.319   ind3:48.261   ind4:52.340   ind5:42.497   ind6:565.347   ind7:43.396   ind8:40.857   ind9:558.527   ind10:40.834   ind11:41.418   ind12:46.270   ind13:44.163   ind14:47.308   ind15:45.714   ind16:41.207   ind17:550.776   ind18:43.192   ind19:42.145   ind20:42.064   ind21:40.636   ind22:41.017   ind23:564.038   ind24:46.003   ind25:42.380   ind26:50.028   ind27:39.908   ind28:42.959   ind29:47.941
2025-05-21 23:14:45,455 - modnet - INFO - Generation 7 best loss: 39.1747
2025-05-21 23:14:45,455 - modnet - INFO - Generation 7 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 1, 'fraction2': 0.5, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.01, 'batch_size': 16, 'n_feat': 600, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:14:45,455 - modnet - INFO - === Generation 8 ===

  0%|          | 0/90 [00:00<?, ?it/s]
  1%|          | 1/90 [00:05<07:36,  5.13s/it]
  4%|▍         | 4/90 [00:05<01:33,  1.09s/it]
  6%|▌         | 5/90 [00:06<01:18,  1.09it/s]
  7%|▋         | 6/90 [00:07<01:23,  1.00it/s]
  8%|▊         | 7/90 [00:07<01:02,  1.34it/s]
  9%|▉         | 8/90 [00:07<00:50,  1.63it/s]
 10%|█         | 9/90 [00:08<00:52,  1.54it/s]
 11%|█         | 10/90 [00:08<00:42,  1.89it/s]
 12%|█▏        | 11/90 [00:08<00:32,  2.46it/s]
 13%|█▎        | 12/90 [00:08<00:25,  3.12it/s]
 14%|█▍        | 13/90 [00:09<00:21,  3.51it/s]
 16%|█▌        | 14/90 [00:09<00:18,  4.19it/s]
 17%|█▋        | 15/90 [00:09<00:15,  4.70it/s]
 18%|█▊        | 16/90 [00:09<00:13,  5.47it/s]
 19%|█▉        | 17/90 [00:09<00:11,  6.19it/s]
 21%|██        | 19/90 [00:09<00:08,  8.74it/s]
 23%|██▎       | 21/90 [00:09<00:07,  9.55it/s]
 26%|██▌       | 23/90 [00:10<00:06, 10.70it/s]
 28%|██▊       | 25/90 [00:10<00:09,  7.16it/s]
 29%|██▉       | 26/90 [00:10<00:10,  6.16it/s]
 31%|███       | 28/90 [00:10<00:07,  8.04it/s]
 34%|███▍      | 31/90 [00:11<00:07,  8.02it/s]
 36%|███▌      | 32/90 [00:11<00:07,  7.33it/s]
 38%|███▊      | 34/90 [00:11<00:07,  7.91it/s]
 40%|████      | 36/90 [00:11<00:06,  8.98it/s]
 43%|████▎     | 39/90 [00:11<00:04, 11.16it/s]
 47%|████▋     | 42/90 [00:12<00:03, 13.52it/s]
 49%|████▉     | 44/90 [00:12<00:03, 13.78it/s]
 51%|█████     | 46/90 [00:12<00:03, 12.08it/s]
 53%|█████▎    | 48/90 [00:12<00:05,  8.19it/s]
 56%|█████▌    | 50/90 [00:13<00:05,  7.41it/s]
 58%|█████▊    | 52/90 [00:13<00:04,  8.05it/s]
 60%|██████    | 54/90 [00:13<00:04,  7.59it/s]
 62%|██████▏   | 56/90 [00:14<00:05,  5.94it/s]
 63%|██████▎   | 57/90 [00:14<00:05,  5.95it/s]
 66%|██████▌   | 59/90 [00:14<00:04,  7.15it/s]
 68%|██████▊   | 61/90 [00:14<00:03,  8.81it/s]
 70%|███████   | 63/90 [00:14<00:03,  8.22it/s]
 72%|███████▏  | 65/90 [00:15<00:02,  9.12it/s]
 74%|███████▍  | 67/90 [00:15<00:02, 10.58it/s]
 77%|███████▋  | 69/90 [00:15<00:02,  9.81it/s]
 79%|███████▉  | 71/90 [00:15<00:01, 10.10it/s]
 81%|████████  | 73/90 [00:16<00:02,  6.77it/s]
 82%|████████▏ | 74/90 [00:16<00:03,  4.97it/s]
 83%|████████▎ | 75/90 [00:17<00:04,  3.56it/s]
 84%|████████▍ | 76/90 [00:17<00:03,  3.62it/s]
 86%|████████▌ | 77/90 [00:17<00:03,  4.11it/s]
 87%|████████▋ | 78/90 [00:17<00:02,  4.30it/s]
 88%|████████▊ | 79/90 [00:18<00:03,  3.32it/s]
 90%|█████████ | 81/90 [00:18<00:02,  4.29it/s]
 92%|█████████▏| 83/90 [00:18<00:01,  5.37it/s]
 96%|█████████▌| 86/90 [00:19<00:00,  4.69it/s]
 97%|█████████▋| 87/90 [00:20<00:00,  3.72it/s]
 98%|█████████▊| 88/90 [00:21<00:00,  2.36it/s]
 99%|█████████▉| 89/90 [00:23<00:00,  1.30it/s]
100%|██████████| 90/90 [00:23<00:00,  1.42it/s]
100%|██████████| 90/90 [00:23<00:00,  3.81it/s]
2025-05-21 23:15:09,355 - modnet - INFO - Loss per individual: ind0:41.270   ind1:43.503   ind2:42.963   ind3:41.911   ind4:43.428   ind5:43.327   ind6:41.106   ind7:48.427   ind8:39.487   ind9:44.457   ind10:48.340   ind11:64.263   ind12:43.605   ind13:40.910   ind14:42.888   ind15:49.119   ind16:558.527   ind17:41.692   ind18:41.841   ind19:45.530   ind20:43.193   ind21:45.087   ind22:42.865   ind23:48.069   ind24:565.347   ind25:44.025   ind26:565.312   ind27:41.484   ind28:43.078   ind29:40.511
2025-05-21 23:15:09,375 - modnet - INFO - Generation 8 best loss: 39.1747
2025-05-21 23:15:09,375 - modnet - INFO - Generation 8 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 1, 'fraction2': 0.5, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.01, 'batch_size': 16, 'n_feat': 600, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:15:09,375 - modnet - INFO - === Generation 9 ===

  0%|          | 0/90 [00:00<?, ?it/s]
  1%|          | 1/90 [00:03<05:31,  3.73s/it]
  2%|▏         | 2/90 [00:03<02:24,  1.64s/it]
  4%|▍         | 4/90 [00:06<01:59,  1.39s/it]
  6%|▌         | 5/90 [00:07<01:38,  1.16s/it]
  7%|▋         | 6/90 [00:07<01:19,  1.06it/s]
  8%|▊         | 7/90 [00:07<01:00,  1.38it/s]
 10%|█         | 9/90 [00:08<00:38,  2.11it/s]
 12%|█▏        | 11/90 [00:08<00:25,  3.05it/s]
 14%|█▍        | 13/90 [00:08<00:18,  4.17it/s]
 16%|█▌        | 14/90 [00:08<00:16,  4.48it/s]
 18%|█▊        | 16/90 [00:08<00:14,  5.18it/s]
 19%|█▉        | 17/90 [00:09<00:15,  4.80it/s]
 21%|██        | 19/90 [00:09<00:12,  5.81it/s]
 22%|██▏       | 20/90 [00:09<00:11,  5.96it/s]
 24%|██▍       | 22/90 [00:09<00:08,  7.94it/s]
 28%|██▊       | 25/90 [00:10<00:08,  8.09it/s]
 32%|███▏      | 29/90 [00:10<00:04, 12.25it/s]
 34%|███▍      | 31/90 [00:10<00:05, 11.47it/s]
 37%|███▋      | 33/90 [00:10<00:07,  7.48it/s]
 40%|████      | 36/90 [00:11<00:05,  9.18it/s]
 42%|████▏     | 38/90 [00:11<00:06,  8.47it/s]
 44%|████▍     | 40/90 [00:11<00:06,  7.42it/s]
 47%|████▋     | 42/90 [00:11<00:05,  8.81it/s]
 49%|████▉     | 44/90 [00:11<00:04,  9.85it/s]
 51%|█████     | 46/90 [00:12<00:08,  5.12it/s]
 59%|█████▉    | 53/90 [00:12<00:03, 11.15it/s]
 62%|██████▏   | 56/90 [00:13<00:03, 11.03it/s]
 66%|██████▌   | 59/90 [00:13<00:02, 12.39it/s]
 69%|██████▉   | 62/90 [00:13<00:02, 13.96it/s]
 72%|███████▏  | 65/90 [00:13<00:02, 11.39it/s]
 74%|███████▍  | 67/90 [00:14<00:02, 10.27it/s]
 77%|███████▋  | 69/90 [00:14<00:02,  9.04it/s]
 79%|███████▉  | 71/90 [00:14<00:01, 10.11it/s]
 81%|████████  | 73/90 [00:15<00:02,  6.43it/s]
 86%|████████▌ | 77/90 [00:15<00:01,  8.35it/s]
 88%|████████▊ | 79/90 [00:15<00:01,  7.43it/s]
 89%|████████▉ | 80/90 [00:16<00:01,  7.51it/s]
 91%|█████████ | 82/90 [00:16<00:00,  8.60it/s]
 93%|█████████▎| 84/90 [00:16<00:00,  8.60it/s]
 96%|█████████▌| 86/90 [00:17<00:00,  4.77it/s]
 97%|█████████▋| 87/90 [00:17<00:00,  3.69it/s]
 98%|█████████▊| 88/90 [00:18<00:00,  3.19it/s]
 99%|█████████▉| 89/90 [00:18<00:00,  3.52it/s]
100%|██████████| 90/90 [00:18<00:00,  3.94it/s]
100%|██████████| 90/90 [00:18<00:00,  4.82it/s]
2025-05-21 23:15:28,347 - modnet - INFO - Loss per individual: ind0:43.030   ind1:43.249   ind2:41.211   ind3:41.492   ind4:42.972   ind5:44.177   ind6:43.248   ind7:43.998   ind8:44.058   ind9:44.637   ind10:44.224   ind11:43.104   ind12:42.919   ind13:562.557   ind14:44.428   ind15:47.794   ind16:41.713   ind17:40.143   ind18:42.512   ind19:50.669   ind20:44.447   ind21:43.248   ind22:41.889   ind23:43.880   ind24:42.276   ind25:45.758   ind26:44.559   ind27:561.587   ind28:43.977   ind29:41.627
2025-05-21 23:15:28,358 - modnet - INFO - Generation 9 best loss: 39.1747
2025-05-21 23:15:28,358 - modnet - INFO - Generation 9 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 1, 'fraction2': 0.5, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.01, 'batch_size': 16, 'n_feat': 600, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:15:28,358 - modnet - INFO - === Generation 10 ===
2025-05-21 23:15:28,359 - modnet - INFO - Increase repeats 3->5, re-evaluating parents

  0%|          | 0/150 [00:00<?, ?it/s]
  1%|          | 1/150 [00:05<12:45,  5.14s/it]
  1%|▏         | 2/150 [00:05<05:40,  2.30s/it]
  2%|▏         | 3/150 [00:05<03:39,  1.50s/it]
  3%|▎         | 4/150 [00:06<02:38,  1.09s/it]
  3%|▎         | 5/150 [00:06<01:47,  1.35it/s]
  5%|▍         | 7/150 [00:06<01:05,  2.20it/s]
  6%|▌         | 9/150 [00:07<00:42,  3.35it/s]
  7%|▋         | 10/150 [00:07<00:36,  3.85it/s]
  7%|▋         | 11/150 [00:07<00:48,  2.85it/s]
  8%|▊         | 12/150 [00:08<00:52,  2.62it/s]
  9%|▉         | 14/150 [00:08<00:35,  3.83it/s]
 10%|█         | 15/150 [00:08<00:32,  4.11it/s]
 11%|█▏        | 17/150 [00:09<00:30,  4.37it/s]
 13%|█▎        | 19/150 [00:09<00:23,  5.68it/s]
 13%|█▎        | 20/150 [00:09<00:25,  5.06it/s]
 14%|█▍        | 21/150 [00:10<00:36,  3.58it/s]
 16%|█▌        | 24/150 [00:10<00:23,  5.32it/s]
 17%|█▋        | 25/150 [00:10<00:22,  5.56it/s]
 19%|█▊        | 28/150 [00:10<00:14,  8.41it/s]
 20%|██        | 30/150 [00:10<00:15,  7.71it/s]
 21%|██▏       | 32/150 [00:11<00:13,  8.89it/s]
 23%|██▎       | 34/150 [00:11<00:12,  9.02it/s]
 25%|██▍       | 37/150 [00:11<00:10, 11.12it/s]
 26%|██▌       | 39/150 [00:11<00:10, 11.03it/s]
 28%|██▊       | 42/150 [00:12<00:10,  9.97it/s]
 29%|██▉       | 44/150 [00:13<00:21,  4.84it/s]
 31%|███▏      | 47/150 [00:13<00:15,  6.71it/s]
 33%|███▎      | 49/150 [00:13<00:16,  6.08it/s]
 34%|███▍      | 51/150 [00:13<00:15,  6.56it/s]
 35%|███▌      | 53/150 [00:14<00:15,  6.44it/s]
 36%|███▌      | 54/150 [00:14<00:16,  5.66it/s]
 37%|███▋      | 56/150 [00:14<00:13,  6.96it/s]
 39%|███▉      | 59/150 [00:14<00:09,  9.73it/s]
 41%|████      | 61/150 [00:14<00:08, 10.38it/s]
 42%|████▏     | 63/150 [00:15<00:11,  7.51it/s]
 43%|████▎     | 65/150 [00:15<00:13,  6.49it/s]
 45%|████▍     | 67/150 [00:15<00:10,  7.65it/s]
 46%|████▌     | 69/150 [00:16<00:11,  7.17it/s]
 47%|████▋     | 70/150 [00:16<00:11,  7.21it/s]
 47%|████▋     | 71/150 [00:16<00:13,  5.76it/s]
 49%|████▊     | 73/150 [00:16<00:10,  7.12it/s]
 50%|█████     | 75/150 [00:17<00:08,  8.45it/s]
 51%|█████▏    | 77/150 [00:17<00:07,  9.44it/s]
 53%|█████▎    | 79/150 [00:17<00:11,  6.07it/s]
 54%|█████▍    | 81/150 [00:17<00:09,  7.12it/s]
 55%|█████▌    | 83/150 [00:18<00:08,  7.78it/s]
 56%|█████▌    | 84/150 [00:18<00:09,  6.71it/s]
 57%|█████▋    | 85/150 [00:18<00:10,  5.92it/s]
 57%|█████▋    | 86/150 [00:18<00:12,  5.31it/s]
 58%|█████▊    | 87/150 [00:19<00:17,  3.58it/s]
 59%|█████▊    | 88/150 [00:19<00:18,  3.43it/s]
 59%|█████▉    | 89/150 [00:19<00:16,  3.67it/s]
 61%|██████    | 91/150 [00:20<00:12,  4.69it/s]
 61%|██████▏   | 92/150 [00:20<00:10,  5.33it/s]
 62%|██████▏   | 93/150 [00:20<00:10,  5.68it/s]
 64%|██████▍   | 96/150 [00:20<00:05,  9.08it/s]
 65%|██████▌   | 98/150 [00:20<00:05,  8.99it/s]
 67%|██████▋   | 100/150 [00:21<00:05,  9.57it/s]
 68%|██████▊   | 102/150 [00:21<00:05,  8.72it/s]
 69%|██████▉   | 104/150 [00:21<00:04, 10.48it/s]
 71%|███████▏  | 107/150 [00:21<00:04,  9.31it/s]
 73%|███████▎  | 109/150 [00:22<00:05,  7.62it/s]
 75%|███████▍  | 112/150 [00:22<00:03, 10.29it/s]
 76%|███████▌  | 114/150 [00:22<00:03, 10.33it/s]
 77%|███████▋  | 116/150 [00:22<00:03, 11.08it/s]
 79%|███████▊  | 118/150 [00:23<00:05,  6.25it/s]
 80%|████████  | 120/150 [00:23<00:04,  7.35it/s]
 82%|████████▏ | 123/150 [00:23<00:03,  8.41it/s]
 83%|████████▎ | 125/150 [00:24<00:04,  6.19it/s]
 85%|████████▍ | 127/150 [00:24<00:03,  7.60it/s]
 86%|████████▌ | 129/150 [00:24<00:02,  7.94it/s]
 88%|████████▊ | 132/150 [00:24<00:01, 10.47it/s]
 89%|████████▉ | 134/150 [00:25<00:01,  9.25it/s]
 91%|█████████ | 136/150 [00:25<00:01,  7.62it/s]
 92%|█████████▏| 138/150 [00:25<00:01,  6.61it/s]
 93%|█████████▎| 140/150 [00:25<00:01,  8.10it/s]
 95%|█████████▍| 142/150 [00:26<00:01,  7.67it/s]
 96%|█████████▌| 144/150 [00:26<00:00,  6.06it/s]
 98%|█████████▊| 147/150 [00:27<00:00,  7.24it/s]
 99%|█████████▉| 149/150 [00:27<00:00,  8.57it/s]
100%|██████████| 150/150 [00:28<00:00,  5.19it/s]
2025-05-21 23:15:57,686 - modnet - INFO - Loss per individual: ind0:43.039   ind1:41.498   ind2:39.991   ind3:45.661   ind4:39.284   ind5:42.007   ind6:42.119   ind7:41.526   ind8:41.065   ind9:42.055   ind10:39.884   ind11:40.346   ind12:40.677   ind13:43.023   ind14:42.979   ind15:41.392   ind16:40.761   ind17:39.648   ind18:40.864   ind19:41.586   ind20:40.076   ind21:43.964   ind22:40.703   ind23:41.762   ind24:44.468   ind25:42.324   ind26:42.526   ind27:41.743   ind28:42.435   ind29:39.789

  0%|          | 0/150 [00:00<?, ?it/s]
  1%|          | 1/150 [00:02<06:34,  2.65s/it]
  1%|▏         | 2/150 [00:02<02:50,  1.15s/it]
  3%|▎         | 5/150 [00:02<00:52,  2.75it/s]
  5%|▍         | 7/150 [00:03<00:34,  4.13it/s]
  6%|▌         | 9/150 [00:03<00:24,  5.69it/s]
  7%|▋         | 11/150 [00:03<00:18,  7.35it/s]
  9%|▊         | 13/150 [00:03<00:15,  8.97it/s]
 10%|█         | 15/150 [00:03<00:12, 10.78it/s]
 11%|█▏        | 17/150 [00:03<00:11, 11.44it/s]
 13%|█▎        | 19/150 [00:03<00:11, 11.02it/s]
 14%|█▍        | 21/150 [00:03<00:10, 12.34it/s]
 15%|█▌        | 23/150 [00:04<00:09, 12.87it/s]
 17%|█▋        | 25/150 [00:04<00:09, 13.59it/s]
 19%|█▊        | 28/150 [00:04<00:07, 16.04it/s]
 20%|██        | 30/150 [00:04<00:07, 16.41it/s]
 21%|██▏       | 32/150 [00:04<00:07, 15.55it/s]
 23%|██▎       | 35/150 [00:04<00:06, 18.08it/s]
 25%|██▍       | 37/150 [00:04<00:06, 17.08it/s]
 26%|██▌       | 39/150 [00:05<00:06, 17.18it/s]
 27%|██▋       | 41/150 [00:05<00:06, 16.44it/s]
 29%|██▊       | 43/150 [00:05<00:06, 15.89it/s]
 30%|███       | 45/150 [00:05<00:07, 13.59it/s]
 31%|███▏      | 47/150 [00:05<00:12,  8.11it/s]
 33%|███▎      | 49/150 [00:06<00:10,  9.37it/s]
 34%|███▍      | 51/150 [00:06<00:17,  5.60it/s]
 35%|███▍      | 52/150 [00:07<00:19,  5.12it/s]
 36%|███▌      | 54/150 [00:07<00:15,  6.35it/s]
 37%|███▋      | 56/150 [00:07<00:12,  7.65it/s]
 39%|███▊      | 58/150 [00:07<00:13,  6.62it/s]
 40%|████      | 60/150 [00:07<00:11,  8.09it/s]
 41%|████▏     | 62/150 [00:08<00:09,  9.39it/s]
 43%|████▎     | 64/150 [00:08<00:08, 10.29it/s]
 45%|████▍     | 67/150 [00:08<00:06, 12.27it/s]
 46%|████▌     | 69/150 [00:08<00:11,  7.18it/s]
 47%|████▋     | 71/150 [00:10<00:20,  3.81it/s]
 49%|████▊     | 73/150 [00:10<00:16,  4.68it/s]
 49%|████▉     | 74/150 [00:10<00:19,  3.93it/s]
 51%|█████     | 76/150 [00:11<00:16,  4.52it/s]
 52%|█████▏    | 78/150 [00:11<00:18,  3.96it/s]
 53%|█████▎    | 80/150 [00:11<00:13,  5.17it/s]
 55%|█████▍    | 82/150 [00:12<00:11,  6.01it/s]
 55%|█████▌    | 83/150 [00:12<00:10,  6.36it/s]
 57%|█████▋    | 85/150 [00:12<00:07,  8.28it/s]
 58%|█████▊    | 87/150 [00:12<00:08,  7.65it/s]
 59%|█████▉    | 89/150 [00:12<00:07,  8.44it/s]
 61%|██████    | 91/150 [00:13<00:09,  5.96it/s]
 62%|██████▏   | 93/150 [00:13<00:10,  5.57it/s]
 64%|██████▍   | 96/150 [00:13<00:07,  7.61it/s]
 65%|██████▌   | 98/150 [00:14<00:06,  8.59it/s]
 67%|██████▋   | 100/150 [00:14<00:05,  8.93it/s]
 69%|██████▊   | 103/150 [00:14<00:04, 10.89it/s]
 71%|███████▏  | 107/150 [00:14<00:03, 13.97it/s]
 73%|███████▎  | 109/150 [00:14<00:02, 13.88it/s]
 74%|███████▍  | 111/150 [00:14<00:03, 11.93it/s]
 75%|███████▌  | 113/150 [00:15<00:03, 10.43it/s]
 77%|███████▋  | 115/150 [00:15<00:04,  8.22it/s]
 79%|███████▉  | 119/150 [00:15<00:02, 11.87it/s]
 81%|████████  | 121/150 [00:15<00:02, 12.96it/s]
 82%|████████▏ | 123/150 [00:15<00:01, 13.84it/s]
 83%|████████▎ | 125/150 [00:16<00:01, 14.34it/s]
 85%|████████▍ | 127/150 [00:16<00:02, 11.37it/s]
 86%|████████▌ | 129/150 [00:16<00:01, 12.05it/s]
 88%|████████▊ | 132/150 [00:16<00:01, 11.81it/s]
 89%|████████▉ | 134/150 [00:17<00:01,  8.63it/s]
 91%|█████████ | 136/150 [00:17<00:01,  7.63it/s]
 91%|█████████▏| 137/150 [00:17<00:01,  7.67it/s]
 92%|█████████▏| 138/150 [00:18<00:02,  5.69it/s]
 93%|█████████▎| 140/150 [00:18<00:01,  7.31it/s]
 94%|█████████▍| 141/150 [00:18<00:01,  6.10it/s]
 97%|█████████▋| 145/150 [00:18<00:00,  7.49it/s]
 97%|█████████▋| 146/150 [00:18<00:00,  7.79it/s]
 98%|█████████▊| 147/150 [00:19<00:00,  6.99it/s]
 99%|█████████▊| 148/150 [00:19<00:00,  4.68it/s]
 99%|█████████▉| 149/150 [00:20<00:00,  3.32it/s]
100%|██████████| 150/150 [00:20<00:00,  3.47it/s]
100%|██████████| 150/150 [00:20<00:00,  7.33it/s]
2025-05-21 23:16:18,576 - modnet - INFO - Loss per individual: ind0:41.367   ind1:557.152   ind2:571.381   ind3:568.932   ind4:568.932   ind5:557.152   ind6:557.152   ind7:492.586   ind8:564.902   ind9:40.954   ind10:564.902   ind11:54.470   ind12:41.127   ind13:40.768   ind14:568.932   ind15:41.698   ind16:41.659   ind17:492.586   ind18:51.298   ind19:557.152   ind20:571.722   ind21:39.860   ind22:59.718   ind23:43.759   ind24:43.480   ind25:40.617   ind26:42.106   ind27:47.748   ind28:567.962   ind29:41.813
2025-05-21 23:16:18,854 - modnet - INFO - Generation 10 best loss: 39.2841
2025-05-21 23:16:18,855 - modnet - INFO - Generation 10 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.25, 'fraction2': 0.5, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.01, 'batch_size': 16, 'n_feat': 600, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:16:18,855 - modnet - INFO - === Generation 11 ===

  0%|          | 0/150 [00:00<?, ?it/s]
  1%|          | 1/150 [00:02<06:35,  2.66s/it]
  3%|▎         | 5/150 [00:02<01:01,  2.37it/s]
  5%|▍         | 7/150 [00:02<00:42,  3.40it/s]
  6%|▌         | 9/150 [00:03<00:36,  3.89it/s]
  7%|▋         | 11/150 [00:03<00:27,  5.13it/s]
  9%|▊         | 13/150 [00:03<00:21,  6.40it/s]
 10%|█         | 15/150 [00:03<00:16,  8.04it/s]
 11%|█▏        | 17/150 [00:05<00:50,  2.66it/s]
 13%|█▎        | 19/150 [00:05<00:37,  3.53it/s]
 14%|█▍        | 21/150 [00:06<00:32,  3.97it/s]
 15%|█▍        | 22/150 [00:06<00:33,  3.83it/s]
 16%|█▌        | 24/150 [00:06<00:24,  5.06it/s]
 17%|█▋        | 25/150 [00:06<00:29,  4.17it/s]
 18%|█▊        | 27/150 [00:07<00:23,  5.18it/s]
 19%|█▉        | 29/150 [00:07<00:18,  6.53it/s]
 21%|██        | 31/150 [00:07<00:15,  7.58it/s]
 22%|██▏       | 33/150 [00:08<00:22,  5.31it/s]
 23%|██▎       | 34/150 [00:08<00:20,  5.74it/s]
 23%|██▎       | 35/150 [00:08<00:18,  6.27it/s]
 25%|██▍       | 37/150 [00:08<00:13,  8.36it/s]
 26%|██▌       | 39/150 [00:09<00:21,  5.25it/s]
 27%|██▋       | 41/150 [00:09<00:16,  6.75it/s]
 29%|██▊       | 43/150 [00:09<00:23,  4.63it/s]
 29%|██▉       | 44/150 [00:10<00:21,  5.01it/s]
 30%|███       | 45/150 [00:10<00:19,  5.43it/s]
 31%|███▏      | 47/150 [00:10<00:18,  5.65it/s]
 32%|███▏      | 48/150 [00:10<00:19,  5.17it/s]
 33%|███▎      | 49/150 [00:11<00:20,  5.03it/s]
 33%|███▎      | 50/150 [00:11<00:21,  4.64it/s]
 35%|███▍      | 52/150 [00:11<00:15,  6.45it/s]
 36%|███▌      | 54/150 [00:11<00:11,  8.36it/s]
 37%|███▋      | 56/150 [00:11<00:11,  8.19it/s]
 39%|███▉      | 59/150 [00:12<00:09,  9.71it/s]
 41%|████      | 61/150 [00:12<00:07, 11.28it/s]
 42%|████▏     | 63/150 [00:12<00:06, 12.83it/s]
 43%|████▎     | 65/150 [00:12<00:06, 12.87it/s]
 45%|████▍     | 67/150 [00:12<00:06, 12.25it/s]
 46%|████▌     | 69/150 [00:12<00:07, 10.17it/s]
 47%|████▋     | 71/150 [00:13<00:11,  6.83it/s]
 49%|████▊     | 73/150 [00:13<00:11,  6.64it/s]
 50%|█████     | 75/150 [00:13<00:11,  6.69it/s]
 51%|█████▏    | 77/150 [00:14<00:09,  7.61it/s]
 52%|█████▏    | 78/150 [00:14<00:09,  7.71it/s]
 53%|█████▎    | 79/150 [00:14<00:11,  6.29it/s]
 53%|█████▎    | 80/150 [00:14<00:15,  4.62it/s]
 55%|█████▍    | 82/150 [00:15<00:12,  5.35it/s]
 55%|█████▌    | 83/150 [00:15<00:12,  5.35it/s]
 57%|█████▋    | 85/150 [00:15<00:09,  7.21it/s]
 58%|█████▊    | 87/150 [00:15<00:08,  7.87it/s]
 59%|█████▊    | 88/150 [00:15<00:08,  7.72it/s]
 61%|██████    | 91/150 [00:16<00:06,  9.67it/s]
 62%|██████▏   | 93/150 [00:16<00:09,  5.97it/s]
 63%|██████▎   | 94/150 [00:16<00:08,  6.43it/s]
 63%|██████▎   | 95/150 [00:17<00:09,  5.95it/s]
 64%|██████▍   | 96/150 [00:17<00:14,  3.84it/s]
 65%|██████▍   | 97/150 [00:17<00:11,  4.51it/s]
 66%|██████▌   | 99/150 [00:17<00:07,  6.58it/s]
 67%|██████▋   | 101/150 [00:18<00:08,  5.56it/s]
 69%|██████▊   | 103/150 [00:18<00:08,  5.57it/s]
 70%|███████   | 105/150 [00:18<00:06,  6.71it/s]
 71%|███████▏  | 107/150 [00:18<00:05,  8.42it/s]
 73%|███████▎  | 109/150 [00:19<00:04,  9.72it/s]
 74%|███████▍  | 111/150 [00:19<00:04,  7.95it/s]
 76%|███████▌  | 114/150 [00:19<00:03,  9.91it/s]
 77%|███████▋  | 116/150 [00:19<00:03, 10.80it/s]
 79%|███████▉  | 119/150 [00:19<00:02, 11.59it/s]
 81%|████████  | 121/150 [00:20<00:04,  7.12it/s]
 82%|████████▏ | 123/150 [00:20<00:03,  7.67it/s]
 83%|████████▎ | 125/150 [00:20<00:02,  9.12it/s]
 86%|████████▌ | 129/150 [00:21<00:01, 11.03it/s]
 88%|████████▊ | 132/150 [00:21<00:01, 13.68it/s]
 89%|████████▉ | 134/150 [00:21<00:01, 13.98it/s]
 91%|█████████▏| 137/150 [00:21<00:00, 16.15it/s]
 94%|█████████▍| 141/150 [00:21<00:00, 13.77it/s]
 95%|█████████▌| 143/150 [00:22<00:00, 14.22it/s]
 97%|█████████▋| 145/150 [00:22<00:00,  7.44it/s]
 98%|█████████▊| 147/150 [00:23<00:00,  6.74it/s]
 99%|█████████▉| 149/150 [00:23<00:00,  5.36it/s]
100%|██████████| 150/150 [00:23<00:00,  6.31it/s]
2025-05-21 23:16:43,165 - modnet - INFO - Loss per individual: ind0:557.152   ind1:43.546   ind2:42.848   ind3:42.195   ind4:571.381   ind5:564.902   ind6:40.813   ind7:43.381   ind8:48.676   ind9:43.819   ind10:547.474   ind11:50.066   ind12:41.052   ind13:557.152   ind14:42.804   ind15:40.635   ind16:564.902   ind17:52.530   ind18:41.378   ind19:53.996   ind20:43.062   ind21:564.902   ind22:41.003   ind23:40.865   ind24:41.821   ind25:43.197   ind26:42.283   ind27:41.908   ind28:41.210   ind29:77.340
2025-05-21 23:16:43,179 - modnet - INFO - Generation 11 best loss: 39.2841
2025-05-21 23:16:43,179 - modnet - INFO - Generation 11 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.25, 'fraction2': 0.5, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.01, 'batch_size': 16, 'n_feat': 600, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:16:43,179 - modnet - INFO - === Generation 12 ===

  0%|          | 0/150 [00:00<?, ?it/s]
  1%|          | 1/150 [00:01<03:24,  1.37s/it]
  1%|▏         | 2/150 [00:01<01:37,  1.51it/s]
  3%|▎         | 4/150 [00:01<00:43,  3.32it/s]
  4%|▍         | 6/150 [00:01<00:27,  5.19it/s]
  5%|▌         | 8/150 [00:02<00:21,  6.55it/s]
  7%|▋         | 10/150 [00:02<00:24,  5.82it/s]
  7%|▋         | 11/150 [00:04<01:06,  2.08it/s]
  8%|▊         | 12/150 [00:04<00:55,  2.48it/s]
  9%|▉         | 14/150 [00:04<00:36,  3.71it/s]
 10%|█         | 15/150 [00:04<00:32,  4.17it/s]
 11%|█▏        | 17/150 [00:04<00:22,  5.87it/s]
 13%|█▎        | 19/150 [00:04<00:18,  7.03it/s]
 14%|█▍        | 21/150 [00:06<00:57,  2.23it/s]
 15%|█▌        | 23/150 [00:07<00:43,  2.91it/s]
 16%|█▌        | 24/150 [00:07<00:43,  2.87it/s]
 17%|█▋        | 26/150 [00:07<00:33,  3.69it/s]
 18%|█▊        | 27/150 [00:08<00:35,  3.51it/s]
 20%|██        | 30/150 [00:08<00:22,  5.34it/s]
 21%|██        | 31/150 [00:08<00:23,  4.97it/s]
 21%|██▏       | 32/150 [00:08<00:23,  5.04it/s]
 22%|██▏       | 33/150 [00:09<00:39,  2.98it/s]
 23%|██▎       | 35/150 [00:10<00:33,  3.48it/s]
 25%|██▍       | 37/150 [00:10<00:23,  4.81it/s]
 25%|██▌       | 38/150 [00:10<00:20,  5.37it/s]
 27%|██▋       | 40/150 [00:10<00:15,  7.23it/s]
 28%|██▊       | 42/150 [00:10<00:11,  9.17it/s]
 29%|██▉       | 44/150 [00:10<00:10,  9.96it/s]
 31%|███       | 46/150 [00:10<00:08, 11.82it/s]
 32%|███▏      | 48/150 [00:10<00:08, 12.41it/s]
 33%|███▎      | 50/150 [00:11<00:08, 11.67it/s]
 35%|███▍      | 52/150 [00:11<00:07, 12.98it/s]
 36%|███▌      | 54/150 [00:11<00:07, 12.87it/s]
 37%|███▋      | 56/150 [00:11<00:06, 13.67it/s]
 39%|███▊      | 58/150 [00:11<00:10,  9.09it/s]
 41%|████      | 61/150 [00:12<00:07, 11.55it/s]
 42%|████▏     | 63/150 [00:12<00:07, 12.08it/s]
 43%|████▎     | 65/150 [00:13<00:22,  3.75it/s]
 45%|████▍     | 67/150 [00:14<00:20,  4.04it/s]
 45%|████▌     | 68/150 [00:14<00:18,  4.33it/s]
 47%|████▋     | 70/150 [00:14<00:15,  5.24it/s]
 47%|████▋     | 71/150 [00:14<00:15,  5.11it/s]
 48%|████▊     | 72/150 [00:14<00:15,  4.90it/s]
 49%|████▉     | 74/150 [00:14<00:11,  6.77it/s]
 51%|█████     | 76/150 [00:15<00:10,  7.29it/s]
 51%|█████▏    | 77/150 [00:15<00:09,  7.58it/s]
 53%|█████▎    | 79/150 [00:15<00:12,  5.80it/s]
 53%|█████▎    | 80/150 [00:16<00:13,  5.26it/s]
 55%|█████▍    | 82/150 [00:16<00:10,  6.18it/s]
 55%|█████▌    | 83/150 [00:16<00:15,  4.21it/s]
 57%|█████▋    | 85/150 [00:17<00:11,  5.56it/s]
 57%|█████▋    | 86/150 [00:17<00:14,  4.28it/s]
 58%|█████▊    | 87/150 [00:17<00:17,  3.56it/s]
 59%|█████▉    | 89/150 [00:18<00:16,  3.76it/s]
 60%|██████    | 90/150 [00:18<00:15,  3.87it/s]
 61%|██████▏   | 92/150 [00:19<00:14,  3.96it/s]
 62%|██████▏   | 93/150 [00:19<00:13,  4.28it/s]
 63%|██████▎   | 94/150 [00:19<00:14,  3.84it/s]
 65%|██████▌   | 98/150 [00:19<00:06,  7.69it/s]
 67%|██████▋   | 100/150 [00:20<00:08,  6.22it/s]
 67%|██████▋   | 101/150 [00:20<00:08,  5.83it/s]
 69%|██████▊   | 103/150 [00:20<00:06,  7.22it/s]
 69%|██████▉   | 104/150 [00:20<00:06,  6.86it/s]
 71%|███████▏  | 107/150 [00:20<00:04,  8.68it/s]
 72%|███████▏  | 108/150 [00:21<00:05,  8.34it/s]
 74%|███████▍  | 111/150 [00:21<00:03, 10.05it/s]
 75%|███████▌  | 113/150 [00:21<00:03,  9.70it/s]
 77%|███████▋  | 116/150 [00:21<00:03, 10.54it/s]
 79%|███████▉  | 119/150 [00:21<00:02, 12.24it/s]
 81%|████████▏ | 122/150 [00:22<00:01, 14.69it/s]
 83%|████████▎ | 124/150 [00:22<00:01, 15.23it/s]
 84%|████████▍ | 126/150 [00:22<00:02,  8.23it/s]
 85%|████████▌ | 128/150 [00:22<00:02,  8.90it/s]
 87%|████████▋ | 130/150 [00:23<00:02,  8.18it/s]
 88%|████████▊ | 132/150 [00:23<00:01,  9.68it/s]
 89%|████████▉ | 134/150 [00:23<00:01, 10.85it/s]
 91%|█████████ | 136/150 [00:23<00:01,  9.18it/s]
 92%|█████████▏| 138/150 [00:24<00:01,  9.02it/s]
 93%|█████████▎| 140/150 [00:25<00:02,  4.15it/s]
 94%|█████████▍| 141/150 [00:25<00:02,  3.76it/s]
 95%|█████████▍| 142/150 [00:25<00:02,  3.41it/s]
 95%|█████████▌| 143/150 [00:26<00:02,  3.03it/s]
 96%|█████████▌| 144/150 [00:26<00:01,  3.26it/s]
 97%|█████████▋| 145/150 [00:27<00:02,  2.41it/s]
 99%|█████████▊| 148/150 [00:27<00:00,  3.26it/s]
 99%|█████████▉| 149/150 [00:29<00:00,  1.54it/s]
100%|██████████| 150/150 [00:35<00:00,  1.83s/it]
100%|██████████| 150/150 [00:35<00:00,  4.17it/s]
2025-05-21 23:17:19,895 - modnet - INFO - Loss per individual: ind0:570.413   ind1:564.902   ind2:58.297   ind3:43.618   ind4:41.453   ind5:51.126   ind6:41.162   ind7:557.152   ind8:42.982   ind9:48.592   ind10:44.096   ind11:568.932   ind12:50.920   ind13:43.934   ind14:41.989   ind15:46.208   ind16:44.168   ind17:557.152   ind18:564.902   ind19:40.694   ind20:43.903   ind21:44.379   ind22:41.784   ind23:43.502   ind24:41.392   ind25:420.384   ind26:41.817   ind27:42.736   ind28:45.741   ind29:41.848
2025-05-21 23:17:19,914 - modnet - INFO - Generation 12 best loss: 39.2841
2025-05-21 23:17:19,914 - modnet - INFO - Generation 12 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.25, 'fraction2': 0.5, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.01, 'batch_size': 16, 'n_feat': 600, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:17:19,914 - modnet - INFO - === Generation 13 ===

  0%|          | 0/150 [00:00<?, ?it/s]
  1%|          | 1/150 [00:01<04:09,  1.68s/it]
  1%|▏         | 2/150 [00:01<01:51,  1.33it/s]
  3%|▎         | 4/150 [00:01<00:48,  3.04it/s]
  4%|▍         | 6/150 [00:02<00:47,  3.01it/s]
  5%|▌         | 8/150 [00:02<00:32,  4.42it/s]
  7%|▋         | 10/150 [00:02<00:26,  5.34it/s]
  7%|▋         | 11/150 [00:04<00:51,  2.70it/s]
  8%|▊         | 12/150 [00:05<01:31,  1.50it/s]
  9%|▊         | 13/150 [00:06<01:19,  1.71it/s]
  9%|▉         | 14/150 [00:06<01:18,  1.74it/s]
 11%|█         | 16/150 [00:07<00:56,  2.39it/s]
 12%|█▏        | 18/150 [00:07<00:38,  3.46it/s]
 13%|█▎        | 19/150 [00:07<00:33,  3.91it/s]
 13%|█▎        | 20/150 [00:07<00:29,  4.35it/s]
 14%|█▍        | 21/150 [00:07<00:34,  3.78it/s]
 15%|█▌        | 23/150 [00:08<00:26,  4.75it/s]
 17%|█▋        | 25/150 [00:08<00:28,  4.33it/s]
 17%|█▋        | 26/150 [00:08<00:26,  4.72it/s]
 19%|█▉        | 29/150 [00:08<00:15,  7.80it/s]
 21%|██        | 31/150 [00:08<00:12,  9.31it/s]
 22%|██▏       | 33/150 [00:09<00:12,  9.20it/s]
 23%|██▎       | 35/150 [00:09<00:10, 10.76it/s]
 25%|██▍       | 37/150 [00:09<00:16,  6.96it/s]
 26%|██▌       | 39/150 [00:09<00:12,  8.57it/s]
 27%|██▋       | 41/150 [00:10<00:16,  6.42it/s]
 29%|██▊       | 43/150 [00:10<00:15,  7.01it/s]
 30%|███       | 45/150 [00:10<00:13,  8.02it/s]
 31%|███▏      | 47/150 [00:11<00:12,  8.55it/s]
 33%|███▎      | 50/150 [00:11<00:09, 10.82it/s]
 35%|███▍      | 52/150 [00:11<00:15,  6.17it/s]
 37%|███▋      | 55/150 [00:12<00:11,  8.26it/s]
 38%|███▊      | 57/150 [00:12<00:09,  9.31it/s]
 39%|███▉      | 59/150 [00:12<00:09,  9.90it/s]
 41%|████      | 61/150 [00:12<00:08, 10.55it/s]
 42%|████▏     | 63/150 [00:12<00:07, 10.93it/s]
 43%|████▎     | 65/150 [00:12<00:07, 11.65it/s]
 45%|████▍     | 67/150 [00:12<00:06, 12.43it/s]
 46%|████▌     | 69/150 [00:13<00:06, 12.78it/s]
 47%|████▋     | 71/150 [00:13<00:06, 12.24it/s]
 49%|████▊     | 73/150 [00:13<00:06, 12.70it/s]
 50%|█████     | 75/150 [00:13<00:05, 13.16it/s]
 51%|█████▏    | 77/150 [00:13<00:05, 12.92it/s]
 53%|█████▎    | 80/150 [00:13<00:04, 14.62it/s]
 55%|█████▌    | 83/150 [00:14<00:04, 15.79it/s]
 57%|█████▋    | 85/150 [00:14<00:03, 16.51it/s]
 58%|█████▊    | 87/150 [00:14<00:07,  8.70it/s]
 59%|█████▉    | 89/150 [00:15<00:11,  5.41it/s]
 61%|██████▏   | 92/150 [00:15<00:07,  7.66it/s]
 63%|██████▎   | 95/150 [00:15<00:06,  9.02it/s]
 65%|██████▍   | 97/150 [00:15<00:05,  8.92it/s]
 66%|██████▌   | 99/150 [00:16<00:06,  7.91it/s]
 67%|██████▋   | 101/150 [00:16<00:09,  5.44it/s]
 68%|██████▊   | 102/150 [00:17<00:09,  5.18it/s]
 69%|██████▊   | 103/150 [00:17<00:08,  5.56it/s]
 70%|███████   | 105/150 [00:18<00:10,  4.15it/s]
 71%|███████▏  | 107/150 [00:18<00:07,  5.52it/s]
 72%|███████▏  | 108/150 [00:19<00:13,  3.02it/s]
 74%|███████▍  | 111/150 [00:19<00:07,  4.94it/s]
 77%|███████▋  | 115/150 [00:19<00:04,  7.44it/s]
 78%|███████▊  | 117/150 [00:19<00:04,  6.67it/s]
 79%|███████▉  | 119/150 [00:20<00:04,  7.08it/s]
 81%|████████▏ | 122/150 [00:20<00:04,  6.47it/s]
 82%|████████▏ | 123/150 [00:20<00:03,  6.80it/s]
 83%|████████▎ | 124/150 [00:21<00:04,  5.79it/s]
 83%|████████▎ | 125/150 [00:21<00:05,  4.98it/s]
 85%|████████▍ | 127/150 [00:21<00:04,  5.02it/s]
 85%|████████▌ | 128/150 [00:21<00:04,  5.09it/s]
 87%|████████▋ | 130/150 [00:22<00:03,  6.40it/s]
 87%|████████▋ | 131/150 [00:22<00:04,  4.25it/s]
 89%|████████▊ | 133/150 [00:23<00:04,  3.90it/s]
 89%|████████▉ | 134/150 [00:23<00:04,  3.86it/s]
 90%|█████████ | 135/150 [00:23<00:03,  4.22it/s]
 91%|█████████ | 136/150 [00:24<00:04,  2.97it/s]
 91%|█████████▏| 137/150 [00:25<00:05,  2.17it/s]
 92%|█████████▏| 138/150 [00:25<00:04,  2.73it/s]
 93%|█████████▎| 139/150 [00:25<00:03,  2.79it/s]
 93%|█████████▎| 140/150 [00:26<00:03,  2.68it/s]
 94%|█████████▍| 141/150 [00:26<00:03,  2.32it/s]
 95%|█████████▍| 142/150 [00:27<00:03,  2.10it/s]
 95%|█████████▌| 143/150 [00:28<00:05,  1.28it/s]
 96%|█████████▌| 144/150 [00:29<00:04,  1.42it/s]
 97%|█████████▋| 145/150 [00:29<00:02,  1.88it/s]
 97%|█████████▋| 146/150 [00:30<00:02,  1.52it/s]
 98%|█████████▊| 147/150 [00:30<00:01,  1.72it/s]
 99%|█████████▊| 148/150 [00:30<00:00,  2.12it/s]
 99%|█████████▉| 149/150 [00:31<00:00,  2.37it/s]
100%|██████████| 150/150 [00:36<00:00,  1.84s/it]
100%|██████████| 150/150 [00:36<00:00,  4.13it/s]
2025-05-21 23:17:56,853 - modnet - INFO - Loss per individual: ind0:48.561   ind1:492.586   ind2:44.262   ind3:572.156   ind4:44.295   ind5:44.676   ind6:63.485   ind7:43.111   ind8:39.903   ind9:90.567   ind10:43.501   ind11:41.569   ind12:43.704   ind13:45.421   ind14:45.153   ind15:39.356   ind16:44.673   ind17:557.152   ind18:570.792   ind19:45.424   ind20:58.904   ind21:557.152   ind22:567.962   ind23:42.623   ind24:47.010   ind25:48.644   ind26:564.902   ind27:41.219   ind28:42.966   ind29:40.237
2025-05-21 23:17:56,880 - modnet - INFO - Generation 13 best loss: 39.2841
2025-05-21 23:17:56,880 - modnet - INFO - Generation 13 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.25, 'fraction2': 0.5, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.01, 'batch_size': 16, 'n_feat': 600, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:17:56,880 - modnet - INFO - === Generation 14 ===

  0%|          | 0/150 [00:00<?, ?it/s]
  1%|          | 1/150 [00:02<06:04,  2.45s/it]
  1%|▏         | 2/150 [00:02<02:37,  1.07s/it]
  2%|▏         | 3/150 [00:02<01:32,  1.59it/s]
  3%|▎         | 4/150 [00:02<01:12,  2.01it/s]
  3%|▎         | 5/150 [00:03<00:58,  2.46it/s]
  4%|▍         | 6/150 [00:05<02:21,  1.02it/s]
  5%|▌         | 8/150 [00:05<01:16,  1.86it/s]
  6%|▌         | 9/150 [00:05<01:00,  2.33it/s]
  7%|▋         | 10/150 [00:05<00:55,  2.52it/s]
  8%|▊         | 12/150 [00:06<00:34,  4.01it/s]
  9%|▊         | 13/150 [00:06<00:36,  3.78it/s]
 10%|█         | 15/150 [00:06<00:28,  4.68it/s]
 11%|█         | 16/150 [00:07<00:41,  3.19it/s]
 11%|█▏        | 17/150 [00:07<00:34,  3.82it/s]
 13%|█▎        | 19/150 [00:07<00:23,  5.49it/s]
 13%|█▎        | 20/150 [00:07<00:29,  4.34it/s]
 15%|█▍        | 22/150 [00:08<00:24,  5.24it/s]
 16%|█▌        | 24/150 [00:08<00:19,  6.61it/s]
 17%|█▋        | 26/150 [00:08<00:15,  7.81it/s]
 18%|█▊        | 27/150 [00:08<00:18,  6.82it/s]
 19%|█▊        | 28/150 [00:08<00:17,  6.86it/s]
 20%|██        | 30/150 [00:08<00:13,  8.80it/s]
 21%|██▏       | 32/150 [00:09<00:11, 10.15it/s]
 23%|██▎       | 34/150 [00:09<00:13,  8.56it/s]
 23%|██▎       | 35/150 [00:09<00:14,  8.10it/s]
 25%|██▍       | 37/150 [00:09<00:11,  9.68it/s]
 26%|██▌       | 39/150 [00:09<00:12,  8.76it/s]
 27%|██▋       | 40/150 [00:10<00:12,  8.67it/s]
 29%|██▊       | 43/150 [00:10<00:14,  7.53it/s]
 30%|███       | 45/150 [00:11<00:22,  4.76it/s]
 32%|███▏      | 48/150 [00:11<00:14,  7.00it/s]
 33%|███▎      | 50/150 [00:11<00:11,  8.35it/s]
 35%|███▍      | 52/150 [00:11<00:11,  8.30it/s]
 36%|███▌      | 54/150 [00:12<00:13,  7.38it/s]
 37%|███▋      | 56/150 [00:12<00:19,  4.74it/s]
 39%|███▊      | 58/150 [00:13<00:18,  5.05it/s]
 40%|████      | 60/150 [00:13<00:15,  5.77it/s]
 41%|████▏     | 62/150 [00:13<00:12,  7.18it/s]
 43%|████▎     | 64/150 [00:13<00:10,  8.25it/s]
 44%|████▍     | 66/150 [00:14<00:11,  7.36it/s]
 45%|████▍     | 67/150 [00:14<00:12,  6.91it/s]
 46%|████▌     | 69/150 [00:14<00:09,  8.73it/s]
 47%|████▋     | 71/150 [00:14<00:08,  9.45it/s]
 49%|████▊     | 73/150 [00:14<00:10,  7.59it/s]
 50%|█████     | 75/150 [00:15<00:08,  8.44it/s]
 51%|█████▏    | 77/150 [00:15<00:10,  6.68it/s]
 53%|█████▎    | 79/150 [00:15<00:09,  7.17it/s]
 53%|█████▎    | 80/150 [00:16<00:10,  6.60it/s]
 55%|█████▍    | 82/150 [00:16<00:08,  7.96it/s]
 55%|█████▌    | 83/150 [00:16<00:08,  7.58it/s]
 57%|█████▋    | 85/150 [00:16<00:06,  9.62it/s]
 58%|█████▊    | 87/150 [00:16<00:07,  8.62it/s]
 59%|█████▉    | 89/150 [00:16<00:07,  8.32it/s]
 61%|██████    | 91/150 [00:17<00:06,  9.67it/s]
 63%|██████▎   | 95/150 [00:17<00:04, 11.94it/s]
 65%|██████▍   | 97/150 [00:17<00:04, 11.40it/s]
 66%|██████▌   | 99/150 [00:18<00:06,  8.05it/s]
 67%|██████▋   | 100/150 [00:18<00:06,  8.27it/s]
 68%|██████▊   | 102/150 [00:18<00:07,  6.41it/s]
 69%|██████▉   | 104/150 [00:18<00:06,  7.07it/s]
 71%|███████   | 106/150 [00:18<00:05,  8.29it/s]
 71%|███████▏  | 107/150 [00:19<00:05,  8.00it/s]
 73%|███████▎  | 109/150 [00:19<00:05,  7.39it/s]
 73%|███████▎  | 110/150 [00:19<00:05,  7.12it/s]
 74%|███████▍  | 111/150 [00:19<00:06,  6.03it/s]
 75%|███████▍  | 112/150 [00:20<00:06,  5.57it/s]
 76%|███████▌  | 114/150 [00:20<00:05,  7.08it/s]
 77%|███████▋  | 116/150 [00:20<00:04,  8.27it/s]
 78%|███████▊  | 117/150 [00:20<00:04,  6.74it/s]
 79%|███████▊  | 118/150 [00:20<00:05,  5.64it/s]
 79%|███████▉  | 119/150 [00:21<00:05,  5.88it/s]
 80%|████████  | 120/150 [00:21<00:07,  3.76it/s]
 81%|████████  | 121/150 [00:21<00:07,  3.64it/s]
 82%|████████▏ | 123/150 [00:22<00:04,  5.54it/s]
 83%|████████▎ | 125/150 [00:22<00:03,  7.34it/s]
 85%|████████▌ | 128/150 [00:22<00:02,  7.88it/s]
 87%|████████▋ | 130/150 [00:22<00:02,  9.61it/s]
 88%|████████▊ | 132/150 [00:22<00:02,  8.17it/s]
 90%|█████████ | 135/150 [00:23<00:01,  9.25it/s]
 91%|█████████▏| 137/150 [00:23<00:01,  7.02it/s]
 92%|█████████▏| 138/150 [00:23<00:01,  6.52it/s]
 93%|█████████▎| 140/150 [00:24<00:02,  4.72it/s]
 95%|█████████▍| 142/150 [00:25<00:01,  4.50it/s]
 96%|█████████▌| 144/150 [00:25<00:01,  3.43it/s]
 97%|█████████▋| 145/150 [00:26<00:01,  3.50it/s]
 98%|█████████▊| 147/150 [00:26<00:00,  4.20it/s]
 99%|█████████▊| 148/150 [00:26<00:00,  3.60it/s]
100%|██████████| 150/150 [00:30<00:00,  1.33it/s]
100%|██████████| 150/150 [00:30<00:00,  4.98it/s]
2025-05-21 23:18:27,599 - modnet - INFO - Loss per individual: ind0:39.938   ind1:47.451   ind2:557.152   ind3:48.199   ind4:61.582   ind5:43.236   ind6:53.762   ind7:41.075   ind8:41.308   ind9:44.756   ind10:45.084   ind11:41.823   ind12:570.413   ind13:44.041   ind14:41.969   ind15:557.152   ind16:44.890   ind17:43.939   ind18:45.309   ind19:42.758   ind20:42.534   ind21:45.853   ind22:557.152   ind23:44.423   ind24:40.188   ind25:42.125   ind26:44.263   ind27:570.792   ind28:44.302   ind29:59.368
2025-05-21 23:18:27,626 - modnet - INFO - Generation 14 best loss: 39.2841
2025-05-21 23:18:27,626 - modnet - INFO - Generation 14 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.25, 'fraction2': 0.5, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.01, 'batch_size': 16, 'n_feat': 600, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:18:27,626 - modnet - INFO - === Generation 15 ===
2025-05-21 23:18:27,626 - modnet - INFO - Increase repeats 5->8, re-evaluating parents

  0%|          | 0/240 [00:00<?, ?it/s]
  0%|          | 1/240 [00:04<16:47,  4.21s/it]
  1%|          | 2/240 [00:05<09:59,  2.52s/it]
  1%|▏         | 3/240 [00:06<06:29,  1.64s/it]
  2%|▏         | 5/240 [00:06<03:39,  1.07it/s]
  3%|▎         | 7/240 [00:07<02:09,  1.80it/s]
  4%|▍         | 9/240 [00:07<01:47,  2.16it/s]
  4%|▍         | 10/240 [00:08<02:10,  1.76it/s]
  5%|▌         | 12/240 [00:08<01:30,  2.51it/s]
  6%|▌         | 14/240 [00:09<01:07,  3.35it/s]
  6%|▋         | 15/240 [00:09<01:05,  3.42it/s]
  7%|▋         | 16/240 [00:09<00:56,  3.93it/s]
  7%|▋         | 17/240 [00:09<00:52,  4.24it/s]
  8%|▊         | 18/240 [00:10<01:04,  3.42it/s]
  9%|▉         | 21/240 [00:10<00:40,  5.43it/s]
 10%|█         | 24/240 [00:10<00:29,  7.39it/s]
 11%|█         | 26/240 [00:10<00:29,  7.28it/s]
 12%|█▏        | 28/240 [00:11<00:26,  8.12it/s]
 12%|█▎        | 30/240 [00:11<00:30,  6.92it/s]
 13%|█▎        | 32/240 [00:11<00:26,  7.89it/s]
 15%|█▍        | 35/240 [00:11<00:24,  8.47it/s]
 15%|█▌        | 37/240 [00:12<00:28,  7.23it/s]
 17%|█▋        | 40/240 [00:13<00:41,  4.86it/s]
 18%|█▊        | 43/240 [00:13<00:30,  6.55it/s]
 19%|█▉        | 45/240 [00:13<00:25,  7.53it/s]
 20%|█▉        | 47/240 [00:13<00:23,  8.38it/s]
 20%|██        | 49/240 [00:14<00:28,  6.78it/s]
 21%|██        | 50/240 [00:14<00:31,  6.07it/s]
 22%|██▏       | 52/240 [00:14<00:33,  5.58it/s]
 22%|██▎       | 54/240 [00:15<00:28,  6.49it/s]
 23%|██▎       | 55/240 [00:15<00:29,  6.32it/s]
 23%|██▎       | 56/240 [00:15<00:30,  6.01it/s]
 24%|██▍       | 58/240 [00:15<00:32,  5.56it/s]
 25%|██▌       | 60/240 [00:16<00:25,  7.08it/s]
 25%|██▌       | 61/240 [00:16<00:27,  6.42it/s]
 26%|██▋       | 63/240 [00:16<00:28,  6.28it/s]
 27%|██▋       | 64/240 [00:16<00:26,  6.68it/s]
 28%|██▊       | 66/240 [00:17<00:54,  3.22it/s]
 28%|██▊       | 67/240 [00:18<00:51,  3.38it/s]
 28%|██▊       | 68/240 [00:18<00:57,  3.01it/s]
 29%|██▉       | 69/240 [00:18<00:47,  3.63it/s]
 29%|██▉       | 70/240 [00:18<00:39,  4.33it/s]
 30%|██▉       | 71/240 [00:18<00:38,  4.38it/s]
 30%|███       | 72/240 [00:19<00:42,  3.91it/s]
 31%|███▏      | 75/240 [00:19<00:23,  6.92it/s]
 32%|███▏      | 76/240 [00:19<00:32,  5.09it/s]
 32%|███▎      | 78/240 [00:20<00:28,  5.59it/s]
 33%|███▎      | 80/240 [00:20<00:23,  6.75it/s]
 34%|███▍      | 82/240 [00:20<00:21,  7.22it/s]
 35%|███▌      | 84/240 [00:20<00:22,  6.97it/s]
 35%|███▌      | 85/240 [00:21<00:21,  7.06it/s]
 37%|███▋      | 88/240 [00:21<00:18,  8.42it/s]
 38%|███▊      | 90/240 [00:21<00:21,  7.10it/s]
 38%|███▊      | 91/240 [00:22<00:27,  5.39it/s]
 38%|███▊      | 92/240 [00:22<00:26,  5.65it/s]
 40%|███▉      | 95/240 [00:22<00:18,  7.72it/s]
 40%|████      | 96/240 [00:22<00:18,  7.59it/s]
 41%|████      | 98/240 [00:22<00:14,  9.51it/s]
 42%|████▏     | 100/240 [00:22<00:15,  9.10it/s]
 42%|████▎     | 102/240 [00:23<00:16,  8.27it/s]
 43%|████▎     | 103/240 [00:23<00:16,  8.41it/s]
 44%|████▍     | 105/240 [00:23<00:19,  6.95it/s]
 44%|████▍     | 106/240 [00:23<00:20,  6.44it/s]
 45%|████▌     | 108/240 [00:24<00:18,  7.12it/s]
 45%|████▌     | 109/240 [00:24<00:18,  7.01it/s]
 46%|████▌     | 110/240 [00:24<00:17,  7.45it/s]
 47%|████▋     | 112/240 [00:25<00:33,  3.83it/s]
 48%|████▊     | 115/240 [00:25<00:21,  5.90it/s]
 48%|████▊     | 116/240 [00:25<00:22,  5.43it/s]
 49%|████▉     | 118/240 [00:26<00:20,  5.84it/s]
 50%|████▉     | 119/240 [00:26<00:26,  4.51it/s]
 50%|█████     | 120/240 [00:27<00:38,  3.09it/s]
 51%|█████     | 122/240 [00:27<00:28,  4.12it/s]
 52%|█████▏    | 124/240 [00:27<00:20,  5.72it/s]
 52%|█████▎    | 126/240 [00:27<00:20,  5.60it/s]
 53%|█████▎    | 128/240 [00:28<00:16,  6.96it/s]
 54%|█████▍    | 130/240 [00:28<00:14,  7.43it/s]
 55%|█████▌    | 132/240 [00:28<00:12,  8.67it/s]
 56%|█████▌    | 134/240 [00:28<00:10,  9.73it/s]
 57%|█████▋    | 136/240 [00:28<00:10, 10.13it/s]
 57%|█████▊    | 138/240 [00:29<00:16,  6.04it/s]
 58%|█████▊    | 139/240 [00:29<00:15,  6.38it/s]
 58%|█████▊    | 140/240 [00:29<00:15,  6.28it/s]
 59%|█████▉    | 142/240 [00:30<00:19,  5.15it/s]
 60%|██████    | 144/240 [00:30<00:16,  5.65it/s]
 61%|██████    | 146/240 [00:30<00:16,  5.62it/s]
 62%|██████▏   | 148/240 [00:30<00:13,  6.57it/s]
 62%|██████▎   | 150/240 [00:31<00:18,  4.97it/s]
 63%|██████▎   | 151/240 [00:31<00:19,  4.60it/s]
 64%|██████▍   | 153/240 [00:32<00:14,  5.94it/s]
 64%|██████▍   | 154/240 [00:32<00:18,  4.69it/s]
 65%|██████▌   | 156/240 [00:32<00:13,  6.22it/s]
 66%|██████▋   | 159/240 [00:32<00:09,  8.10it/s]
 67%|██████▋   | 161/240 [00:33<00:09,  8.52it/s]
 68%|██████▊   | 163/240 [00:33<00:11,  6.56it/s]
 68%|██████▊   | 164/240 [00:33<00:12,  6.10it/s]
 69%|██████▉   | 165/240 [00:33<00:11,  6.38it/s]
 69%|██████▉   | 166/240 [00:34<00:12,  5.98it/s]
 70%|███████   | 168/240 [00:34<00:10,  6.67it/s]
 71%|███████   | 170/240 [00:34<00:09,  7.68it/s]
 71%|███████▏  | 171/240 [00:34<00:11,  5.93it/s]
 72%|███████▏  | 172/240 [00:34<00:12,  5.61it/s]
 72%|███████▏  | 173/240 [00:35<00:11,  5.71it/s]
 73%|███████▎  | 175/240 [00:35<00:09,  7.04it/s]
 73%|███████▎  | 176/240 [00:35<00:09,  6.50it/s]
 74%|███████▍  | 177/240 [00:35<00:10,  5.78it/s]
 74%|███████▍  | 178/240 [00:35<00:10,  5.93it/s]
 75%|███████▍  | 179/240 [00:36<00:10,  5.95it/s]
 75%|███████▌  | 180/240 [00:36<00:11,  5.24it/s]
 75%|███████▌  | 181/240 [00:37<00:31,  1.90it/s]
 77%|███████▋  | 184/240 [00:38<00:20,  2.75it/s]
 78%|███████▊  | 187/240 [00:38<00:12,  4.15it/s]
 78%|███████▊  | 188/240 [00:38<00:12,  4.12it/s]
 79%|███████▉  | 190/240 [00:39<00:10,  4.82it/s]
 80%|███████▉  | 191/240 [00:39<00:09,  5.30it/s]
 81%|████████  | 194/240 [00:39<00:06,  6.57it/s]
 82%|████████▏ | 197/240 [00:39<00:04,  8.88it/s]
 83%|████████▎ | 199/240 [00:39<00:04,  9.19it/s]
 84%|████████▍ | 202/240 [00:40<00:04,  8.98it/s]
 85%|████████▌ | 204/240 [00:40<00:03,  9.91it/s]
 86%|████████▋ | 207/240 [00:40<00:02, 11.43it/s]
 88%|████████▊ | 210/240 [00:40<00:02, 11.76it/s]
 88%|████████▊ | 212/240 [00:41<00:02, 11.99it/s]
 89%|████████▉ | 214/240 [00:41<00:01, 13.34it/s]
 90%|█████████ | 217/240 [00:41<00:01, 14.43it/s]
 91%|█████████▏| 219/240 [00:41<00:02, 10.43it/s]
 92%|█████████▏| 221/240 [00:42<00:02,  6.93it/s]
 93%|█████████▎| 223/240 [00:42<00:02,  6.50it/s]
 94%|█████████▍| 225/240 [00:42<00:02,  7.17it/s]
 95%|█████████▌| 228/240 [00:42<00:01,  9.56it/s]
 96%|█████████▌| 230/240 [00:43<00:01,  9.36it/s]
 97%|█████████▋| 232/240 [00:43<00:01,  6.96it/s]
 98%|█████████▊| 234/240 [00:44<00:01,  4.29it/s]
 98%|█████████▊| 235/240 [00:44<00:01,  4.14it/s]
 98%|█████████▊| 236/240 [00:45<00:00,  4.34it/s]
 99%|█████████▉| 238/240 [00:45<00:00,  4.98it/s]
100%|██████████| 240/240 [00:47<00:00,  2.26it/s]
100%|██████████| 240/240 [00:47<00:00,  5.09it/s]
2025-05-21 23:19:15,590 - modnet - INFO - Loss per individual: ind0:42.490   ind1:45.044   ind2:42.633   ind3:43.940   ind4:44.356   ind5:42.859   ind6:42.541   ind7:43.083   ind8:43.157   ind9:42.592   ind10:43.054   ind11:42.026   ind12:42.773   ind13:45.246   ind14:44.206   ind15:43.688   ind16:42.404   ind17:43.727   ind18:44.023   ind19:43.274   ind20:42.926   ind21:44.837   ind22:44.875   ind23:44.094   ind24:42.931   ind25:41.887   ind26:43.282   ind27:44.343   ind28:42.597   ind29:41.620

  0%|          | 0/240 [00:00<?, ?it/s]
  0%|          | 1/240 [00:03<14:34,  3.66s/it]
  1%|          | 2/240 [00:04<08:00,  2.02s/it]
  2%|▏         | 4/240 [00:04<03:16,  1.20it/s]
  2%|▎         | 6/240 [00:04<01:48,  2.15it/s]
  3%|▎         | 8/240 [00:05<01:13,  3.16it/s]
  4%|▍         | 9/240 [00:05<01:03,  3.62it/s]
  5%|▍         | 11/240 [00:05<00:44,  5.14it/s]
  5%|▌         | 13/240 [00:06<01:11,  3.17it/s]
  6%|▋         | 15/240 [00:06<00:53,  4.21it/s]
  7%|▋         | 17/240 [00:06<00:40,  5.55it/s]
  8%|▊         | 19/240 [00:07<00:44,  4.98it/s]
  8%|▊         | 20/240 [00:07<00:59,  3.67it/s]
  9%|▉         | 21/240 [00:07<00:54,  4.04it/s]
 10%|▉         | 23/240 [00:08<00:42,  5.14it/s]
 11%|█         | 26/240 [00:08<00:34,  6.24it/s]
 12%|█▏        | 28/240 [00:08<00:28,  7.52it/s]
 12%|█▎        | 30/240 [00:08<00:23,  8.77it/s]
 13%|█▎        | 32/240 [00:08<00:19, 10.44it/s]
 14%|█▍        | 34/240 [00:09<00:20, 10.06it/s]
 15%|█▌        | 36/240 [00:09<00:18, 11.26it/s]
 16%|█▌        | 38/240 [00:09<00:25,  8.00it/s]
 17%|█▋        | 40/240 [00:10<00:38,  5.13it/s]
 18%|█▊        | 42/240 [00:10<00:35,  5.55it/s]
 18%|█▊        | 43/240 [00:10<00:36,  5.44it/s]
 19%|█▉        | 45/240 [00:11<00:29,  6.71it/s]
 20%|█▉        | 47/240 [00:11<00:23,  8.35it/s]
 20%|██        | 49/240 [00:11<00:27,  6.84it/s]
 21%|██        | 50/240 [00:11<00:35,  5.41it/s]
 22%|██▏       | 53/240 [00:12<00:30,  6.08it/s]
 22%|██▎       | 54/240 [00:12<00:33,  5.49it/s]
 23%|██▎       | 55/240 [00:12<00:33,  5.44it/s]
 24%|██▍       | 57/240 [00:12<00:26,  6.98it/s]
 24%|██▍       | 58/240 [00:13<00:36,  4.98it/s]
 25%|██▍       | 59/240 [00:13<00:38,  4.72it/s]
 25%|██▌       | 61/240 [00:13<00:28,  6.36it/s]
 26%|██▌       | 62/240 [00:13<00:32,  5.55it/s]
 27%|██▋       | 64/240 [00:14<00:38,  4.57it/s]
 27%|██▋       | 65/240 [00:14<00:33,  5.17it/s]
 28%|██▊       | 67/240 [00:14<00:24,  7.07it/s]
 29%|██▉       | 69/240 [00:15<00:23,  7.37it/s]
 29%|██▉       | 70/240 [00:15<00:36,  4.70it/s]
 30%|███       | 72/240 [00:15<00:26,  6.46it/s]
 31%|███       | 74/240 [00:16<00:28,  5.88it/s]
 31%|███▏      | 75/240 [00:16<00:27,  6.05it/s]
 32%|███▏      | 76/240 [00:16<00:27,  6.03it/s]
 33%|███▎      | 79/240 [00:16<00:23,  6.86it/s]
 33%|███▎      | 80/240 [00:16<00:22,  7.12it/s]
 34%|███▍      | 81/240 [00:17<00:29,  5.40it/s]
 34%|███▍      | 82/240 [00:17<00:28,  5.60it/s]
 35%|███▌      | 84/240 [00:17<00:21,  7.30it/s]
 36%|███▌      | 86/240 [00:17<00:16,  9.21it/s]
 37%|███▋      | 88/240 [00:17<00:19,  7.72it/s]
 38%|███▊      | 90/240 [00:18<00:24,  6.18it/s]
 38%|███▊      | 91/240 [00:18<00:22,  6.61it/s]
 39%|███▉      | 93/240 [00:18<00:17,  8.23it/s]
 40%|███▉      | 95/240 [00:18<00:15,  9.64it/s]
 40%|████      | 97/240 [00:19<00:15,  8.96it/s]
 41%|████▏     | 99/240 [00:19<00:15,  9.10it/s]
 42%|████▏     | 101/240 [00:19<00:13, 10.29it/s]
 43%|████▎     | 104/240 [00:19<00:11, 12.09it/s]
 44%|████▍     | 106/240 [00:19<00:14,  9.05it/s]
 45%|████▌     | 108/240 [00:20<00:22,  5.78it/s]
 45%|████▌     | 109/240 [00:20<00:25,  5.22it/s]
 46%|████▋     | 111/240 [00:21<00:19,  6.69it/s]
 47%|████▋     | 112/240 [00:21<00:19,  6.58it/s]
 48%|████▊     | 114/240 [00:21<00:14,  8.55it/s]
 48%|████▊     | 116/240 [00:21<00:13,  9.15it/s]
 49%|████▉     | 118/240 [00:21<00:16,  7.55it/s]
 50%|████▉     | 119/240 [00:22<00:19,  6.11it/s]
 50%|█████     | 120/240 [00:22<00:24,  4.94it/s]
 51%|█████     | 122/240 [00:22<00:19,  6.10it/s]
 52%|█████▏    | 124/240 [00:22<00:15,  7.48it/s]
 52%|█████▎    | 126/240 [00:23<00:16,  7.00it/s]
 53%|█████▎    | 128/240 [00:23<00:16,  6.80it/s]
 54%|█████▍    | 129/240 [00:23<00:18,  5.96it/s]
 54%|█████▍    | 130/240 [00:24<00:28,  3.89it/s]
 55%|█████▌    | 132/240 [00:24<00:20,  5.32it/s]
 55%|█████▌    | 133/240 [00:24<00:21,  5.00it/s]
 56%|█████▋    | 135/240 [00:24<00:16,  6.40it/s]
 57%|█████▋    | 137/240 [00:25<00:13,  7.58it/s]
 58%|█████▊    | 140/240 [00:26<00:21,  4.58it/s]
 59%|█████▉    | 142/240 [00:26<00:17,  5.60it/s]
 60%|██████    | 144/240 [00:26<00:13,  7.04it/s]
 61%|██████    | 146/240 [00:26<00:11,  7.97it/s]
 62%|██████▏   | 148/240 [00:26<00:10,  8.73it/s]
 62%|██████▎   | 150/240 [00:26<00:09,  9.60it/s]
 63%|██████▎   | 152/240 [00:27<00:08,  9.86it/s]
 65%|██████▍   | 155/240 [00:27<00:07, 10.89it/s]
 65%|██████▌   | 157/240 [00:27<00:07, 10.92it/s]
 66%|██████▋   | 159/240 [00:27<00:06, 12.16it/s]
 67%|██████▋   | 161/240 [00:27<00:07, 10.73it/s]
 68%|██████▊   | 163/240 [00:28<00:07, 10.17it/s]
 69%|██████▉   | 165/240 [00:28<00:07, 10.48it/s]
 70%|██████▉   | 167/240 [00:28<00:07,  9.14it/s]
 70%|███████   | 169/240 [00:28<00:07,  9.64it/s]
 71%|███████▏  | 171/240 [00:28<00:06, 10.36it/s]
 72%|███████▏  | 173/240 [00:29<00:10,  6.22it/s]
 73%|███████▎  | 175/240 [00:29<00:09,  7.03it/s]
 73%|███████▎  | 176/240 [00:29<00:08,  7.25it/s]
 74%|███████▍  | 178/240 [00:30<00:08,  7.32it/s]
 75%|███████▌  | 180/240 [00:30<00:06,  9.08it/s]
 76%|███████▌  | 182/240 [00:30<00:06,  9.43it/s]
 77%|███████▋  | 184/240 [00:31<00:09,  5.63it/s]
 77%|███████▋  | 185/240 [00:31<00:10,  5.08it/s]
 78%|███████▊  | 186/240 [00:31<00:09,  5.60it/s]
 78%|███████▊  | 187/240 [00:31<00:11,  4.61it/s]
 78%|███████▊  | 188/240 [00:32<00:13,  3.98it/s]
 79%|███████▉  | 190/240 [00:32<00:10,  4.88it/s]
 80%|███████▉  | 191/240 [00:32<00:12,  3.96it/s]
 80%|████████  | 193/240 [00:32<00:09,  5.21it/s]
 81%|████████▏ | 195/240 [00:33<00:06,  7.09it/s]
 82%|████████▏ | 197/240 [00:33<00:09,  4.62it/s]
 83%|████████▎ | 199/240 [00:34<00:07,  5.66it/s]
 83%|████████▎ | 200/240 [00:34<00:07,  5.51it/s]
 84%|████████▍ | 201/240 [00:34<00:06,  5.94it/s]
 84%|████████▍ | 202/240 [00:34<00:06,  5.71it/s]
 85%|████████▍ | 203/240 [00:34<00:06,  5.88it/s]
 85%|████████▌ | 204/240 [00:34<00:06,  5.20it/s]
 86%|████████▌ | 206/240 [00:35<00:04,  7.49it/s]
 86%|████████▋ | 207/240 [00:35<00:04,  7.43it/s]
 87%|████████▋ | 209/240 [00:35<00:03,  9.27it/s]
 88%|████████▊ | 211/240 [00:35<00:03,  7.53it/s]
 90%|████████▉ | 215/240 [00:35<00:02, 12.04it/s]
 90%|█████████ | 217/240 [00:35<00:01, 12.46it/s]
 92%|█████████▏| 220/240 [00:36<00:01, 12.16it/s]
 92%|█████████▎| 222/240 [00:36<00:01, 10.80it/s]
 93%|█████████▎| 224/240 [00:36<00:01,  9.88it/s]
 94%|█████████▍| 226/240 [00:36<00:01,  9.96it/s]
 95%|█████████▌| 228/240 [00:37<00:01,  9.19it/s]
 95%|█████████▌| 229/240 [00:37<00:01,  7.21it/s]
 96%|█████████▌| 230/240 [00:37<00:01,  5.22it/s]
 97%|█████████▋| 232/240 [00:38<00:01,  4.89it/s]
 98%|█████████▊| 234/240 [00:38<00:01,  5.72it/s]
 98%|█████████▊| 235/240 [00:39<00:01,  3.98it/s]
 98%|█████████▊| 236/240 [00:39<00:00,  4.13it/s]
 99%|█████████▉| 238/240 [00:40<00:00,  3.00it/s]
100%|█████████▉| 239/240 [00:40<00:00,  3.25it/s]
100%|██████████| 240/240 [00:40<00:00,  3.11it/s]
100%|██████████| 240/240 [00:40<00:00,  5.87it/s]
2025-05-21 23:19:57,779 - modnet - INFO - Loss per individual: ind0:75.241   ind1:43.754   ind2:70.064   ind3:43.339   ind4:44.665   ind5:572.353   ind6:43.569   ind7:45.752   ind8:48.200   ind9:59.554   ind10:44.149   ind11:45.153   ind12:44.396   ind13:68.925   ind14:44.115   ind15:42.505   ind16:52.983   ind17:42.583   ind18:45.717   ind19:43.474   ind20:43.076   ind21:45.679   ind22:43.934   ind23:46.219   ind24:580.104   ind25:44.157   ind26:41.610   ind27:45.711   ind28:45.859   ind29:48.468
2025-05-21 23:19:58,040 - modnet - INFO - Generation 15 best loss: 41.6098
2025-05-21 23:19:58,040 - modnet - INFO - Generation 15 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.5, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 600, 'n_layers': 3, 'layer_mults': None}
2025-05-21 23:19:58,040 - modnet - INFO - === Generation 16 ===

  0%|          | 0/240 [00:00<?, ?it/s]
  0%|          | 1/240 [00:03<13:48,  3.47s/it]
  1%|▏         | 3/240 [00:03<03:45,  1.05it/s]
  2%|▏         | 4/240 [00:03<02:35,  1.51it/s]
  2%|▎         | 6/240 [00:03<01:28,  2.63it/s]
  3%|▎         | 7/240 [00:04<01:11,  3.25it/s]
  3%|▎         | 8/240 [00:04<01:04,  3.58it/s]
  4%|▍         | 9/240 [00:05<02:13,  1.73it/s]
  5%|▍         | 11/240 [00:05<01:33,  2.44it/s]
  5%|▌         | 12/240 [00:06<01:19,  2.88it/s]
  5%|▌         | 13/240 [00:06<01:21,  2.79it/s]
  6%|▌         | 14/240 [00:09<03:43,  1.01it/s]
  6%|▋         | 15/240 [00:09<03:09,  1.19it/s]
  7%|▋         | 16/240 [00:09<02:30,  1.48it/s]
  8%|▊         | 18/240 [00:10<01:28,  2.52it/s]
  8%|▊         | 20/240 [00:10<01:08,  3.20it/s]
  9%|▉         | 21/240 [00:10<00:59,  3.69it/s]
  9%|▉         | 22/240 [00:10<00:57,  3.80it/s]
 10%|▉         | 23/240 [00:11<01:13,  2.96it/s]
 10%|█         | 24/240 [00:11<01:00,  3.57it/s]
 11%|█▏        | 27/240 [00:11<00:39,  5.46it/s]
 12%|█▏        | 28/240 [00:12<00:45,  4.71it/s]
 12%|█▎        | 30/240 [00:12<00:32,  6.43it/s]
 13%|█▎        | 32/240 [00:12<00:25,  8.11it/s]
 14%|█▍        | 34/240 [00:12<00:21,  9.63it/s]
 15%|█▌        | 36/240 [00:12<00:23,  8.83it/s]
 16%|█▋        | 39/240 [00:12<00:17, 11.70it/s]
 18%|█▊        | 42/240 [00:12<00:14, 13.24it/s]
 18%|█▊        | 44/240 [00:13<00:18, 10.69it/s]
 19%|█▉        | 46/240 [00:13<00:17, 11.10it/s]
 20%|██        | 48/240 [00:14<00:31,  6.06it/s]
 21%|██        | 50/240 [00:14<00:29,  6.52it/s]
 22%|██▏       | 53/240 [00:14<00:20,  9.08it/s]
 23%|██▎       | 55/240 [00:14<00:19,  9.56it/s]
 24%|██▍       | 57/240 [00:15<00:26,  6.91it/s]
 25%|██▍       | 59/240 [00:15<00:22,  7.88it/s]
 25%|██▌       | 61/240 [00:15<00:25,  6.91it/s]
 26%|██▋       | 63/240 [00:15<00:22,  7.94it/s]
 27%|██▋       | 65/240 [00:16<00:28,  6.24it/s]
 28%|██▊       | 66/240 [00:17<00:50,  3.41it/s]
 28%|██▊       | 67/240 [00:17<00:54,  3.19it/s]
 29%|██▉       | 69/240 [00:17<00:40,  4.26it/s]
 29%|██▉       | 70/240 [00:18<00:40,  4.24it/s]
 30%|██▉       | 71/240 [00:18<00:36,  4.67it/s]
 30%|███       | 73/240 [00:18<00:28,  5.96it/s]
 31%|███       | 74/240 [00:18<00:25,  6.53it/s]
 32%|███▏      | 76/240 [00:18<00:22,  7.31it/s]
 32%|███▏      | 77/240 [00:19<00:45,  3.56it/s]
 33%|███▎      | 80/240 [00:19<00:26,  5.98it/s]
 34%|███▍      | 82/240 [00:20<00:29,  5.36it/s]
 35%|███▌      | 84/240 [00:20<00:23,  6.67it/s]
 36%|███▌      | 86/240 [00:20<00:21,  7.01it/s]
 37%|███▋      | 88/240 [00:20<00:18,  8.26it/s]
 38%|███▊      | 90/240 [00:20<00:15,  9.86it/s]
 38%|███▊      | 92/240 [00:21<00:20,  7.20it/s]
 39%|███▉      | 94/240 [00:21<00:17,  8.13it/s]
 40%|████      | 96/240 [00:21<00:15,  9.21it/s]
 41%|████      | 98/240 [00:21<00:13, 10.22it/s]
 42%|████▏     | 100/240 [00:22<00:16,  8.70it/s]
 42%|████▎     | 102/240 [00:22<00:18,  7.66it/s]
 43%|████▎     | 103/240 [00:22<00:20,  6.62it/s]
 44%|████▍     | 105/240 [00:22<00:17,  7.80it/s]
 45%|████▍     | 107/240 [00:22<00:14,  9.40it/s]
 46%|████▌     | 110/240 [00:23<00:10, 12.55it/s]
 47%|████▋     | 112/240 [00:23<00:11, 11.26it/s]
 48%|████▊     | 115/240 [00:23<00:09, 13.32it/s]
 49%|████▉     | 118/240 [00:23<00:07, 15.88it/s]
 50%|█████     | 120/240 [00:23<00:08, 14.76it/s]
 51%|█████     | 122/240 [00:24<00:10, 10.75it/s]
 52%|█████▏    | 125/240 [00:24<00:10, 10.79it/s]
 53%|█████▎    | 127/240 [00:24<00:13,  8.54it/s]
 54%|█████▍    | 129/240 [00:26<00:31,  3.56it/s]
 54%|█████▍    | 130/240 [00:26<00:32,  3.41it/s]
 55%|█████▌    | 132/240 [00:27<00:32,  3.29it/s]
 56%|█████▌    | 134/240 [00:27<00:31,  3.42it/s]
 56%|█████▋    | 135/240 [00:28<00:31,  3.34it/s]
 57%|█████▋    | 136/240 [00:28<00:36,  2.86it/s]
 57%|█████▊    | 138/240 [00:29<00:34,  3.00it/s]
 58%|█████▊    | 140/240 [00:29<00:24,  4.05it/s]
 59%|█████▉    | 142/240 [00:29<00:18,  5.27it/s]
 60%|█████▉    | 143/240 [00:29<00:16,  5.78it/s]
 60%|██████    | 145/240 [00:30<00:17,  5.30it/s]
 62%|██████▏   | 148/240 [00:30<00:11,  7.74it/s]
 62%|██████▎   | 150/240 [00:30<00:11,  7.97it/s]
 63%|██████▎   | 152/240 [00:30<00:11,  7.34it/s]
 64%|██████▍   | 153/240 [00:30<00:11,  7.29it/s]
 64%|██████▍   | 154/240 [00:31<00:18,  4.68it/s]
 65%|██████▍   | 155/240 [00:31<00:17,  4.82it/s]
 65%|██████▌   | 156/240 [00:31<00:17,  4.93it/s]
 65%|██████▌   | 157/240 [00:31<00:16,  5.11it/s]
 66%|██████▋   | 159/240 [00:32<00:12,  6.59it/s]
 67%|██████▋   | 160/240 [00:32<00:11,  7.03it/s]
 68%|██████▊   | 162/240 [00:32<00:09,  8.20it/s]
 68%|██████▊   | 163/240 [00:32<00:11,  6.94it/s]
 69%|██████▉   | 165/240 [00:32<00:08,  8.39it/s]
 70%|██████▉   | 167/240 [00:32<00:07, 10.23it/s]
 70%|███████   | 169/240 [00:33<00:06, 11.65it/s]
 71%|███████▏  | 171/240 [00:33<00:05, 12.49it/s]
 72%|███████▏  | 173/240 [00:33<00:05, 13.31it/s]
 73%|███████▎  | 175/240 [00:33<00:07,  8.14it/s]
 74%|███████▍  | 177/240 [00:34<00:10,  6.08it/s]
 75%|███████▍  | 179/240 [00:34<00:08,  7.60it/s]
 75%|███████▌  | 181/240 [00:34<00:07,  7.95it/s]
 76%|███████▋  | 183/240 [00:34<00:06,  9.38it/s]
 78%|███████▊  | 187/240 [00:35<00:04, 11.56it/s]
 79%|███████▉  | 189/240 [00:35<00:04, 12.18it/s]
 80%|███████▉  | 191/240 [00:35<00:04, 10.58it/s]
 80%|████████  | 193/240 [00:35<00:05,  8.72it/s]
 81%|████████▏ | 195/240 [00:36<00:05,  7.91it/s]
 82%|████████▏ | 196/240 [00:36<00:06,  7.02it/s]
 82%|████████▏ | 197/240 [00:36<00:06,  6.48it/s]
 83%|████████▎ | 199/240 [00:36<00:05,  7.45it/s]
 84%|████████▍ | 201/240 [00:36<00:04,  8.71it/s]
 85%|████████▍ | 203/240 [00:36<00:03, 10.55it/s]
 85%|████████▌ | 205/240 [00:37<00:04,  7.67it/s]
 87%|████████▋ | 208/240 [00:37<00:03,  9.88it/s]
 88%|████████▊ | 210/240 [00:37<00:03,  9.98it/s]
 88%|████████▊ | 212/240 [00:37<00:02, 10.58it/s]
 89%|████████▉ | 214/240 [00:39<00:06,  4.26it/s]
 90%|████████▉ | 215/240 [00:39<00:05,  4.57it/s]
 91%|█████████▏| 219/240 [00:39<00:02,  7.51it/s]
 92%|█████████▏| 221/240 [00:39<00:02,  8.24it/s]
 93%|█████████▎| 223/240 [00:39<00:01,  9.47it/s]
 94%|█████████▍| 225/240 [00:39<00:01,  9.35it/s]
 95%|█████████▍| 227/240 [00:40<00:01,  7.96it/s]
 95%|█████████▌| 229/240 [00:40<00:01,  7.15it/s]
 96%|█████████▌| 230/240 [00:40<00:01,  6.51it/s]
 97%|█████████▋| 232/240 [00:41<00:01,  6.68it/s]
 98%|█████████▊| 234/240 [00:41<00:00,  7.20it/s]
 98%|█████████▊| 235/240 [00:41<00:01,  4.91it/s]
 99%|█████████▉| 237/240 [00:42<00:00,  5.86it/s]
 99%|█████████▉| 238/240 [00:42<00:00,  3.48it/s]
100%|█████████▉| 239/240 [00:44<00:00,  1.59it/s]
100%|██████████| 240/240 [00:45<00:00,  1.45it/s]
100%|██████████| 240/240 [00:45<00:00,  5.27it/s]
2025-05-21 23:20:44,443 - modnet - INFO - Loss per individual: ind0:43.459   ind1:41.918   ind2:42.409   ind3:44.252   ind4:58.796   ind5:585.994   ind6:45.948   ind7:54.124   ind8:43.778   ind9:41.962   ind10:586.889   ind11:52.625   ind12:44.742   ind13:56.786   ind14:45.150   ind15:47.305   ind16:47.239   ind17:42.529   ind18:585.615   ind19:45.155   ind20:45.397   ind21:42.371   ind22:50.081   ind23:42.390   ind24:45.368   ind25:82.249   ind26:42.101   ind27:53.624   ind28:587.772   ind29:45.366
2025-05-21 23:20:44,464 - modnet - INFO - Generation 16 best loss: 41.6098
2025-05-21 23:20:44,465 - modnet - INFO - Generation 16 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.5, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 600, 'n_layers': 3, 'layer_mults': None}
2025-05-21 23:20:44,466 - modnet - INFO - === Generation 17 ===

  0%|          | 0/240 [00:00<?, ?it/s]
  0%|          | 1/240 [00:03<15:06,  3.79s/it]
  1%|          | 2/240 [00:03<06:33,  1.65s/it]
  2%|▏         | 4/240 [00:04<02:37,  1.50it/s]
  2%|▎         | 6/240 [00:04<01:28,  2.64it/s]
  3%|▎         | 8/240 [00:04<01:21,  2.84it/s]
  4%|▍         | 9/240 [00:06<02:13,  1.73it/s]
  4%|▍         | 10/240 [00:06<01:56,  1.98it/s]
  5%|▍         | 11/240 [00:06<01:50,  2.07it/s]
  5%|▌         | 12/240 [00:07<01:40,  2.26it/s]
  5%|▌         | 13/240 [00:07<01:58,  1.92it/s]
  6%|▌         | 14/240 [00:08<01:33,  2.41it/s]
  7%|▋         | 16/240 [00:08<01:03,  3.54it/s]
  8%|▊         | 18/240 [00:08<00:47,  4.67it/s]
  8%|▊         | 20/240 [00:09<00:55,  3.96it/s]
  9%|▉         | 22/240 [00:09<00:41,  5.22it/s]
 10%|█         | 24/240 [00:09<00:35,  6.09it/s]
 10%|█         | 25/240 [00:10<00:58,  3.70it/s]
 11%|█▏        | 27/240 [00:10<00:41,  5.14it/s]
 12%|█▏        | 28/240 [00:10<00:44,  4.82it/s]
 12%|█▏        | 29/240 [00:10<00:45,  4.63it/s]
 13%|█▎        | 31/240 [00:11<00:35,  5.89it/s]
 13%|█▎        | 32/240 [00:11<00:47,  4.37it/s]
 14%|█▍        | 33/240 [00:11<00:41,  5.01it/s]
 15%|█▍        | 35/240 [00:11<00:30,  6.67it/s]
 15%|█▌        | 37/240 [00:12<00:38,  5.33it/s]
 16%|█▌        | 38/240 [00:12<00:34,  5.89it/s]
 16%|█▋        | 39/240 [00:12<00:31,  6.39it/s]
 17%|█▋        | 40/240 [00:13<00:54,  3.66it/s]
 17%|█▋        | 41/240 [00:13<00:49,  4.04it/s]
 18%|█▊        | 42/240 [00:13<00:52,  3.75it/s]
 18%|█▊        | 43/240 [00:13<00:46,  4.26it/s]
 19%|█▉        | 45/240 [00:14<00:39,  4.99it/s]
 19%|█▉        | 46/240 [00:14<00:45,  4.31it/s]
 20%|██        | 48/240 [00:14<00:39,  4.86it/s]
 21%|██        | 50/240 [00:14<00:30,  6.17it/s]
 21%|██▏       | 51/240 [00:15<00:42,  4.48it/s]
 22%|██▏       | 53/240 [00:15<00:43,  4.28it/s]
 22%|██▎       | 54/240 [00:16<00:39,  4.71it/s]
 23%|██▎       | 55/240 [00:16<00:36,  5.08it/s]
 24%|██▍       | 57/240 [00:16<00:26,  6.89it/s]
 25%|██▌       | 60/240 [00:16<00:17, 10.32it/s]
 26%|██▋       | 63/240 [00:16<00:19,  9.19it/s]
 27%|██▋       | 65/240 [00:17<00:23,  7.32it/s]
 28%|██▊       | 68/240 [00:17<00:22,  7.50it/s]
 29%|██▉       | 69/240 [00:17<00:27,  6.12it/s]
 29%|██▉       | 70/240 [00:18<00:30,  5.55it/s]
 30%|██▉       | 71/240 [00:18<00:38,  4.43it/s]
 30%|███       | 73/240 [00:18<00:31,  5.25it/s]
 31%|███▏      | 75/240 [00:19<00:31,  5.18it/s]
 32%|███▏      | 77/240 [00:19<00:34,  4.78it/s]
 33%|███▎      | 79/240 [00:19<00:26,  6.01it/s]
 33%|███▎      | 80/240 [00:20<00:33,  4.75it/s]
 34%|███▍      | 81/240 [00:20<00:30,  5.28it/s]
 34%|███▍      | 82/240 [00:20<00:27,  5.85it/s]
 35%|███▌      | 84/240 [00:20<00:20,  7.63it/s]
 35%|███▌      | 85/240 [00:21<00:36,  4.30it/s]
 36%|███▌      | 86/240 [00:21<00:35,  4.28it/s]
 36%|███▋      | 87/240 [00:21<00:30,  4.94it/s]
 37%|███▋      | 88/240 [00:21<00:33,  4.54it/s]
 38%|███▊      | 91/240 [00:22<00:19,  7.51it/s]
 39%|███▉      | 93/240 [00:22<00:27,  5.34it/s]
 39%|███▉      | 94/240 [00:22<00:25,  5.77it/s]
 40%|████      | 96/240 [00:22<00:20,  6.99it/s]
 40%|████      | 97/240 [00:23<00:22,  6.40it/s]
 41%|████      | 98/240 [00:23<00:22,  6.27it/s]
 41%|████▏     | 99/240 [00:23<00:26,  5.38it/s]
 42%|████▏     | 101/240 [00:23<00:21,  6.49it/s]
 42%|████▎     | 102/240 [00:23<00:20,  6.79it/s]
 44%|████▍     | 105/240 [00:24<00:13,  9.73it/s]
 45%|████▍     | 107/240 [00:24<00:11, 11.24it/s]
 45%|████▌     | 109/240 [00:24<00:13,  9.87it/s]
 46%|████▋     | 111/240 [00:26<00:40,  3.18it/s]
 47%|████▋     | 112/240 [00:26<00:36,  3.52it/s]
 48%|████▊     | 114/240 [00:26<00:38,  3.26it/s]
 48%|████▊     | 115/240 [00:27<00:35,  3.55it/s]
 49%|████▉     | 117/240 [00:27<00:25,  4.83it/s]
 49%|████▉     | 118/240 [00:27<00:23,  5.23it/s]
 50%|████▉     | 119/240 [00:27<00:30,  3.97it/s]
 50%|█████     | 121/240 [00:27<00:22,  5.40it/s]
 51%|█████     | 122/240 [00:28<00:22,  5.28it/s]
 51%|█████▏    | 123/240 [00:28<00:19,  5.87it/s]
 52%|█████▏    | 124/240 [00:29<00:41,  2.77it/s]
 53%|█████▎    | 127/240 [00:29<00:22,  5.03it/s]
 54%|█████▍    | 129/240 [00:29<00:20,  5.53it/s]
 54%|█████▍    | 130/240 [00:30<00:24,  4.46it/s]
 55%|█████▍    | 131/240 [00:30<00:22,  4.84it/s]
 55%|█████▌    | 132/240 [00:30<00:22,  4.82it/s]
 56%|█████▌    | 134/240 [00:30<00:17,  5.93it/s]
 57%|█████▋    | 136/240 [00:31<00:18,  5.50it/s]
 57%|█████▋    | 137/240 [00:31<00:18,  5.59it/s]
 58%|█████▊    | 139/240 [00:31<00:13,  7.57it/s]
 59%|█████▉    | 141/240 [00:31<00:13,  7.48it/s]
 60%|█████▉    | 143/240 [00:31<00:12,  7.68it/s]
 60%|██████    | 144/240 [00:32<00:14,  6.65it/s]
 60%|██████    | 145/240 [00:32<00:13,  7.00it/s]
 61%|██████    | 146/240 [00:32<00:20,  4.68it/s]
 61%|██████▏   | 147/240 [00:32<00:23,  4.02it/s]
 62%|██████▏   | 149/240 [00:33<00:27,  3.27it/s]
 63%|██████▎   | 151/240 [00:33<00:21,  4.22it/s]
 64%|██████▍   | 153/240 [00:34<00:15,  5.66it/s]
 64%|██████▍   | 154/240 [00:34<00:15,  5.71it/s]
 65%|██████▌   | 156/240 [00:34<00:11,  7.35it/s]
 65%|██████▌   | 157/240 [00:34<00:15,  5.37it/s]
 66%|██████▌   | 158/240 [00:34<00:14,  5.82it/s]
 66%|██████▋   | 159/240 [00:35<00:20,  3.97it/s]
 67%|██████▋   | 161/240 [00:35<00:17,  4.50it/s]
 68%|██████▊   | 163/240 [00:36<00:14,  5.21it/s]
 68%|██████▊   | 164/240 [00:36<00:14,  5.20it/s]
 69%|██████▉   | 165/240 [00:36<00:17,  4.32it/s]
 70%|██████▉   | 167/240 [00:36<00:13,  5.53it/s]
 70%|███████   | 169/240 [00:36<00:09,  7.28it/s]
 72%|███████▏  | 172/240 [00:37<00:07,  9.39it/s]
 72%|███████▎  | 174/240 [00:37<00:08,  7.70it/s]
 73%|███████▎  | 176/240 [00:37<00:09,  6.88it/s]
 74%|███████▍  | 178/240 [00:38<00:09,  6.60it/s]
 75%|███████▍  | 179/240 [00:38<00:11,  5.38it/s]
 75%|███████▌  | 180/240 [00:38<00:11,  5.22it/s]
 76%|███████▌  | 182/240 [00:39<00:09,  6.05it/s]
 77%|███████▋  | 185/240 [00:39<00:06,  7.95it/s]
 78%|███████▊  | 186/240 [00:39<00:07,  6.97it/s]
 78%|███████▊  | 187/240 [00:39<00:07,  7.01it/s]
 79%|███████▉  | 189/240 [00:39<00:05,  9.09it/s]
 80%|████████  | 192/240 [00:39<00:04, 11.28it/s]
 81%|████████  | 194/240 [00:40<00:04,  9.56it/s]
 82%|████████▏ | 196/240 [00:41<00:08,  4.92it/s]
 82%|████████▏ | 197/240 [00:41<00:08,  5.30it/s]
 82%|████████▎ | 198/240 [00:41<00:07,  5.55it/s]
 83%|████████▎ | 200/240 [00:41<00:07,  5.06it/s]
 85%|████████▍ | 203/240 [00:41<00:04,  7.47it/s]
 85%|████████▌ | 205/240 [00:42<00:04,  8.20it/s]
 86%|████████▋ | 207/240 [00:42<00:03,  8.26it/s]
 88%|████████▊ | 210/240 [00:42<00:02, 10.74it/s]
 88%|████████▊ | 212/240 [00:42<00:03,  8.46it/s]
 89%|████████▉ | 214/240 [00:43<00:02,  8.74it/s]
 90%|█████████ | 216/240 [00:43<00:02, 10.12it/s]
 91%|█████████ | 218/240 [00:43<00:02,  8.22it/s]
 92%|█████████▎| 222/240 [00:43<00:01, 12.21it/s]
 93%|█████████▎| 224/240 [00:43<00:01, 12.52it/s]
 95%|█████████▌| 228/240 [00:44<00:00, 13.60it/s]
 96%|█████████▋| 231/240 [00:44<00:00, 15.68it/s]
 97%|█████████▋| 233/240 [00:44<00:00, 14.09it/s]
 98%|█████████▊| 236/240 [00:44<00:00, 15.73it/s]
 99%|█████████▉| 238/240 [00:45<00:00,  4.85it/s]
100%|██████████| 240/240 [00:49<00:00,  1.70it/s]
100%|██████████| 240/240 [00:49<00:00,  4.86it/s]
2025-05-21 23:21:34,548 - modnet - INFO - Loss per individual: ind0:44.195   ind1:42.478   ind2:43.371   ind3:580.104   ind4:44.151   ind5:41.422   ind6:47.533   ind7:45.201   ind8:44.141   ind9:49.770   ind10:44.545   ind11:43.192   ind12:44.977   ind13:42.950   ind14:43.608   ind15:42.154   ind16:45.328   ind17:43.430   ind18:46.257   ind19:58.535   ind20:53.717   ind21:43.468   ind22:43.271   ind23:43.282   ind24:50.400   ind25:43.160   ind26:48.077   ind27:43.779   ind28:43.424   ind29:43.906
2025-05-21 23:21:34,566 - modnet - INFO - Generation 17 best loss: 41.4220
2025-05-21 23:21:34,566 - modnet - INFO - Generation 17 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.5, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 540, 'n_layers': 3, 'layer_mults': None}
2025-05-21 23:21:34,566 - modnet - INFO - === Generation 18 ===

  0%|          | 0/240 [00:00<?, ?it/s]
  0%|          | 1/240 [00:04<19:08,  4.81s/it]
  1%|          | 2/240 [00:05<08:32,  2.15s/it]
  2%|▏         | 4/240 [00:06<04:19,  1.10s/it]
  2%|▏         | 5/240 [00:07<04:17,  1.10s/it]
  2%|▎         | 6/240 [00:07<03:13,  1.21it/s]
  3%|▎         | 7/240 [00:07<02:26,  1.59it/s]
  3%|▎         | 8/240 [00:07<02:04,  1.86it/s]
  4%|▍         | 9/240 [00:08<01:40,  2.30it/s]
  4%|▍         | 10/240 [00:08<01:22,  2.80it/s]
  5%|▌         | 12/240 [00:08<00:56,  4.05it/s]
  5%|▌         | 13/240 [00:08<00:48,  4.66it/s]
  6%|▋         | 15/240 [00:09<00:49,  4.54it/s]
  7%|▋         | 16/240 [00:09<00:53,  4.17it/s]
  7%|▋         | 17/240 [00:09<00:50,  4.39it/s]
  8%|▊         | 18/240 [00:09<00:44,  5.04it/s]
  8%|▊         | 20/240 [00:10<00:56,  3.89it/s]
 10%|▉         | 23/240 [00:10<00:41,  5.22it/s]
 10%|█         | 25/240 [00:10<00:35,  5.99it/s]
 12%|█▏        | 28/240 [00:11<00:27,  7.62it/s]
 12%|█▎        | 30/240 [00:11<00:25,  8.39it/s]
 13%|█▎        | 31/240 [00:11<00:27,  7.49it/s]
 13%|█▎        | 32/240 [00:12<00:55,  3.78it/s]
 14%|█▍        | 34/240 [00:12<00:39,  5.20it/s]
 15%|█▍        | 35/240 [00:12<00:37,  5.41it/s]
 15%|█▌        | 37/240 [00:12<00:28,  7.19it/s]
 16%|█▋        | 39/240 [00:12<00:22,  9.02it/s]
 17%|█▋        | 41/240 [00:13<00:26,  7.51it/s]
 18%|█▊        | 43/240 [00:13<00:22,  8.80it/s]
 19%|█▉        | 45/240 [00:13<00:19,  9.80it/s]
 20%|█▉        | 47/240 [00:13<00:26,  7.39it/s]
 20%|██        | 48/240 [00:14<00:26,  7.16it/s]
 21%|██        | 50/240 [00:14<00:21,  8.70it/s]
 22%|██▏       | 52/240 [00:14<00:22,  8.37it/s]
 22%|██▏       | 53/240 [00:14<00:34,  5.39it/s]
 22%|██▎       | 54/240 [00:15<00:33,  5.47it/s]
 23%|██▎       | 56/240 [00:16<00:56,  3.28it/s]
 24%|██▍       | 58/240 [00:16<00:41,  4.38it/s]
 25%|██▌       | 60/240 [00:16<00:32,  5.58it/s]
 26%|██▌       | 62/240 [00:16<00:29,  6.06it/s]
 26%|██▋       | 63/240 [00:16<00:32,  5.46it/s]
 27%|██▋       | 64/240 [00:17<00:30,  5.73it/s]
 27%|██▋       | 65/240 [00:17<00:32,  5.46it/s]
 28%|██▊       | 67/240 [00:17<00:27,  6.25it/s]
 28%|██▊       | 68/240 [00:17<00:34,  5.00it/s]
 29%|██▉       | 69/240 [00:18<00:31,  5.49it/s]
 30%|██▉       | 71/240 [00:18<00:30,  5.46it/s]
 30%|███       | 72/240 [00:18<00:28,  5.93it/s]
 30%|███       | 73/240 [00:18<00:25,  6.48it/s]
 31%|███       | 74/240 [00:18<00:25,  6.62it/s]
 32%|███▏      | 76/240 [00:18<00:19,  8.56it/s]
 32%|███▏      | 77/240 [00:19<00:27,  5.82it/s]
 32%|███▎      | 78/240 [00:19<00:24,  6.49it/s]
 33%|███▎      | 79/240 [00:19<00:22,  7.02it/s]
 34%|███▍      | 81/240 [00:20<00:31,  5.03it/s]
 34%|███▍      | 82/240 [00:20<00:29,  5.38it/s]
 35%|███▍      | 83/240 [00:20<00:45,  3.49it/s]
 35%|███▌      | 85/240 [00:21<00:38,  4.03it/s]
 36%|███▌      | 86/240 [00:21<00:35,  4.34it/s]
 37%|███▋      | 89/240 [00:21<00:27,  5.57it/s]
 38%|███▊      | 90/240 [00:22<00:32,  4.61it/s]
 38%|███▊      | 91/240 [00:22<00:32,  4.56it/s]
 39%|███▉      | 93/240 [00:22<00:28,  5.21it/s]
 40%|███▉      | 95/240 [00:22<00:26,  5.58it/s]
 40%|████      | 97/240 [00:23<00:34,  4.15it/s]
 41%|████      | 98/240 [00:23<00:35,  3.99it/s]
 41%|████▏     | 99/240 [00:24<00:32,  4.29it/s]
 42%|████▏     | 100/240 [00:24<00:34,  4.07it/s]
 42%|████▎     | 102/240 [00:24<00:28,  4.90it/s]
 43%|████▎     | 103/240 [00:25<00:33,  4.07it/s]
 43%|████▎     | 104/240 [00:25<00:28,  4.71it/s]
 44%|████▍     | 105/240 [00:25<00:26,  5.07it/s]
 44%|████▍     | 106/240 [00:25<00:23,  5.74it/s]
 45%|████▍     | 107/240 [00:26<00:53,  2.50it/s]
 45%|████▌     | 108/240 [00:26<00:42,  3.14it/s]
 46%|████▌     | 110/240 [00:26<00:28,  4.60it/s]
 46%|████▋     | 111/240 [00:26<00:27,  4.61it/s]
 47%|████▋     | 113/240 [00:27<00:22,  5.67it/s]
 48%|████▊     | 115/240 [00:27<00:16,  7.70it/s]
 49%|████▉     | 117/240 [00:27<00:15,  8.15it/s]
 50%|████▉     | 119/240 [00:27<00:12,  9.83it/s]
 51%|█████     | 122/240 [00:27<00:11, 10.06it/s]
 52%|█████▏    | 124/240 [00:28<00:17,  6.81it/s]
 52%|█████▎    | 126/240 [00:28<00:14,  7.94it/s]
 53%|█████▎    | 128/240 [00:28<00:13,  8.30it/s]
 55%|█████▍    | 131/240 [00:28<00:10, 10.68it/s]
 55%|█████▌    | 133/240 [00:29<00:09, 11.41it/s]
 56%|█████▋    | 135/240 [00:29<00:08, 12.88it/s]
 57%|█████▋    | 137/240 [00:29<00:09, 11.03it/s]
 58%|█████▊    | 139/240 [00:30<00:19,  5.06it/s]
 58%|█████▊    | 140/240 [00:30<00:18,  5.42it/s]
 59%|█████▉    | 141/240 [00:30<00:16,  5.94it/s]
 60%|██████    | 144/240 [00:31<00:18,  5.20it/s]
 61%|██████▏   | 147/240 [00:31<00:15,  6.20it/s]
 62%|██████▏   | 148/240 [00:31<00:15,  5.94it/s]
 62%|██████▏   | 149/240 [00:31<00:14,  6.24it/s]
 62%|██████▎   | 150/240 [00:32<00:18,  4.93it/s]
 63%|██████▎   | 151/240 [00:32<00:16,  5.43it/s]
 63%|██████▎   | 152/240 [00:32<00:14,  5.98it/s]
 64%|██████▍   | 154/240 [00:33<00:17,  4.91it/s]
 65%|██████▌   | 156/240 [00:33<00:15,  5.53it/s]
 66%|██████▌   | 158/240 [00:33<00:11,  7.07it/s]
 66%|██████▋   | 159/240 [00:33<00:11,  7.01it/s]
 67%|██████▋   | 161/240 [00:33<00:12,  6.35it/s]
 68%|██████▊   | 162/240 [00:34<00:11,  6.68it/s]
 68%|██████▊   | 164/240 [00:34<00:12,  6.17it/s]
 69%|██████▉   | 166/240 [00:34<00:10,  7.18it/s]
 70%|███████   | 168/240 [00:34<00:09,  7.35it/s]
 71%|███████   | 170/240 [00:35<00:10,  6.36it/s]
 72%|███████▏  | 172/240 [00:35<00:09,  7.18it/s]
 72%|███████▏  | 173/240 [00:35<00:11,  5.85it/s]
 73%|███████▎  | 175/240 [00:35<00:08,  7.26it/s]
 74%|███████▍  | 178/240 [00:36<00:07,  8.50it/s]
 75%|███████▌  | 180/240 [00:36<00:06,  9.14it/s]
 75%|███████▌  | 181/240 [00:36<00:08,  7.15it/s]
 76%|███████▌  | 182/240 [00:36<00:08,  6.62it/s]
 76%|███████▋  | 183/240 [00:37<00:08,  6.82it/s]
 78%|███████▊  | 186/240 [00:37<00:05,  9.36it/s]
 78%|███████▊  | 188/240 [00:37<00:07,  7.22it/s]
 79%|███████▉  | 190/240 [00:37<00:06,  7.48it/s]
 80%|████████  | 193/240 [00:38<00:05,  9.18it/s]
 81%|████████▏ | 195/240 [00:38<00:04, 10.02it/s]
 82%|████████▏ | 197/240 [00:38<00:04,  9.85it/s]
 83%|████████▎ | 199/240 [00:38<00:03, 11.33it/s]
 84%|████████▍ | 202/240 [00:38<00:03, 10.50it/s]
 85%|████████▌ | 204/240 [00:39<00:03,  9.41it/s]
 86%|████████▌ | 206/240 [00:39<00:05,  6.68it/s]
 87%|████████▋ | 208/240 [00:39<00:04,  7.20it/s]
 87%|████████▋ | 209/240 [00:40<00:04,  6.22it/s]
 88%|████████▊ | 210/240 [00:40<00:04,  6.08it/s]
 88%|████████▊ | 212/240 [00:40<00:03,  7.39it/s]
 89%|████████▉ | 213/240 [00:40<00:04,  6.21it/s]
 90%|████████▉ | 215/240 [00:40<00:03,  7.50it/s]
 90%|█████████ | 217/240 [00:41<00:02,  9.54it/s]
 91%|█████████▏| 219/240 [00:41<00:01, 10.74it/s]
 92%|█████████▎| 222/240 [00:41<00:02,  8.07it/s]
 93%|█████████▎| 224/240 [00:42<00:02,  7.68it/s]
 94%|█████████▍| 225/240 [00:42<00:01,  7.65it/s]
 95%|█████████▍| 227/240 [00:42<00:01,  8.73it/s]
 95%|█████████▌| 228/240 [00:42<00:01,  6.81it/s]
 95%|█████████▌| 229/240 [00:42<00:02,  5.30it/s]
 97%|█████████▋| 232/240 [00:43<00:01,  7.23it/s]
 97%|█████████▋| 233/240 [00:43<00:01,  5.72it/s]
 98%|█████████▊| 234/240 [00:43<00:01,  4.29it/s]
 98%|█████████▊| 235/240 [00:44<00:01,  3.37it/s]
 99%|█████████▉| 237/240 [00:45<00:00,  3.17it/s]
 99%|█████████▉| 238/240 [00:45<00:00,  3.73it/s]
100%|█████████▉| 239/240 [00:47<00:00,  1.21it/s]
100%|██████████| 240/240 [00:48<00:00,  1.22it/s]
100%|██████████| 240/240 [00:48<00:00,  4.93it/s]
2025-05-21 23:22:23,931 - modnet - INFO - Loss per individual: ind0:50.053   ind1:43.085   ind2:44.608   ind3:42.786   ind4:44.720   ind5:43.355   ind6:44.235   ind7:42.084   ind8:42.976   ind9:63.273   ind10:44.461   ind11:42.798   ind12:43.799   ind13:45.544   ind14:43.236   ind15:42.251   ind16:44.667   ind17:43.234   ind18:44.266   ind19:45.821   ind20:43.185   ind21:507.792   ind22:45.575   ind23:44.738   ind24:160.350   ind25:44.364   ind26:60.669   ind27:92.631   ind28:46.349   ind29:45.612
2025-05-21 23:22:23,941 - modnet - INFO - Generation 18 best loss: 41.4220
2025-05-21 23:22:23,941 - modnet - INFO - Generation 18 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.5, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 540, 'n_layers': 3, 'layer_mults': None}
2025-05-21 23:22:23,941 - modnet - INFO - === Generation 19 ===

  0%|          | 0/240 [00:00<?, ?it/s]
  0%|          | 1/240 [00:05<20:47,  5.22s/it]
  1%|          | 2/240 [00:05<10:14,  2.58s/it]
  1%|▏         | 3/240 [00:06<07:10,  1.82s/it]
  2%|▏         | 4/240 [00:07<04:51,  1.23s/it]
  2%|▏         | 5/240 [00:07<03:21,  1.17it/s]
  2%|▎         | 6/240 [00:07<02:51,  1.36it/s]
  3%|▎         | 7/240 [00:08<02:48,  1.38it/s]
  3%|▎         | 8/240 [00:08<02:05,  1.85it/s]
  4%|▍         | 10/240 [00:08<01:11,  3.23it/s]
  5%|▌         | 12/240 [00:09<00:58,  3.92it/s]
  6%|▌         | 14/240 [00:09<00:46,  4.88it/s]
  7%|▋         | 16/240 [00:09<00:40,  5.55it/s]
  7%|▋         | 17/240 [00:09<00:38,  5.84it/s]
  8%|▊         | 20/240 [00:10<00:31,  7.04it/s]
  9%|▉         | 22/240 [00:10<00:29,  7.30it/s]
 10%|█         | 24/240 [00:10<00:28,  7.45it/s]
 10%|█         | 25/240 [00:10<00:32,  6.72it/s]
 11%|█         | 26/240 [00:11<00:35,  5.96it/s]
 11%|█▏        | 27/240 [00:11<00:49,  4.27it/s]
 12%|█▏        | 29/240 [00:11<00:37,  5.60it/s]
 12%|█▎        | 30/240 [00:12<00:40,  5.17it/s]
 13%|█▎        | 32/240 [00:12<00:41,  5.02it/s]
 15%|█▍        | 35/240 [00:12<00:26,  7.65it/s]
 15%|█▌        | 37/240 [00:13<00:42,  4.77it/s]
 16%|█▌        | 38/240 [00:13<00:38,  5.18it/s]
 16%|█▋        | 39/240 [00:13<00:46,  4.34it/s]
 17%|█▋        | 40/240 [00:14<00:54,  3.68it/s]
 18%|█▊        | 42/240 [00:14<00:39,  5.04it/s]
 18%|█▊        | 43/240 [00:14<00:40,  4.87it/s]
 19%|█▉        | 45/240 [00:14<00:31,  6.18it/s]
 20%|█▉        | 47/240 [00:15<00:25,  7.64it/s]
 20%|██        | 49/240 [00:15<00:25,  7.42it/s]
 21%|██        | 50/240 [00:15<00:30,  6.19it/s]
 21%|██▏       | 51/240 [00:15<00:28,  6.54it/s]
 22%|██▏       | 52/240 [00:15<00:27,  6.75it/s]
 22%|██▏       | 53/240 [00:16<00:41,  4.49it/s]
 22%|██▎       | 54/240 [00:16<00:56,  3.31it/s]
 23%|██▎       | 56/240 [00:16<00:37,  4.89it/s]
 24%|██▍       | 57/240 [00:17<00:53,  3.43it/s]
 25%|██▍       | 59/240 [00:17<00:35,  5.13it/s]
 25%|██▌       | 61/240 [00:17<00:25,  7.00it/s]
 26%|██▋       | 63/240 [00:17<00:24,  7.10it/s]
 27%|██▋       | 65/240 [00:18<00:44,  3.97it/s]
 28%|██▊       | 66/240 [00:19<00:44,  3.95it/s]
 28%|██▊       | 68/240 [00:19<00:51,  3.35it/s]
 29%|██▉       | 70/240 [00:20<00:47,  3.61it/s]
 30%|██▉       | 71/240 [00:20<00:46,  3.61it/s]
 30%|███       | 72/240 [00:21<00:46,  3.58it/s]
 31%|███       | 74/240 [00:21<00:33,  4.98it/s]
 32%|███▏      | 76/240 [00:21<00:26,  6.20it/s]
 32%|███▏      | 77/240 [00:21<00:31,  5.19it/s]
 32%|███▎      | 78/240 [00:21<00:35,  4.60it/s]
 33%|███▎      | 79/240 [00:22<00:48,  3.34it/s]
 33%|███▎      | 80/240 [00:22<00:39,  4.02it/s]
 34%|███▍      | 82/240 [00:22<00:33,  4.73it/s]
 35%|███▍      | 83/240 [00:23<00:31,  4.91it/s]
 35%|███▌      | 84/240 [00:23<00:39,  3.99it/s]
 35%|███▌      | 85/240 [00:23<00:32,  4.73it/s]
 36%|███▋      | 87/240 [00:23<00:22,  6.90it/s]
 37%|███▋      | 89/240 [00:23<00:21,  7.15it/s]
 38%|███▊      | 91/240 [00:24<00:18,  7.89it/s]
 39%|███▉      | 93/240 [00:24<00:25,  5.86it/s]
 40%|███▉      | 95/240 [00:24<00:20,  7.24it/s]
 40%|████      | 97/240 [00:24<00:16,  8.62it/s]
 41%|████▏     | 99/240 [00:25<00:20,  6.83it/s]
 42%|████▏     | 101/240 [00:25<00:23,  5.79it/s]
 43%|████▎     | 103/240 [00:26<00:20,  6.69it/s]
 43%|████▎     | 104/240 [00:26<00:21,  6.46it/s]
 44%|████▍     | 105/240 [00:27<00:44,  3.04it/s]
 44%|████▍     | 106/240 [00:27<00:41,  3.21it/s]
 45%|████▌     | 108/240 [00:27<00:32,  4.00it/s]
 45%|████▌     | 109/240 [00:28<00:37,  3.48it/s]
 46%|████▌     | 110/240 [00:28<00:37,  3.50it/s]
 46%|████▋     | 111/240 [00:28<00:40,  3.18it/s]
 47%|████▋     | 113/240 [00:29<00:27,  4.64it/s]
 48%|████▊     | 115/240 [00:29<00:27,  4.55it/s]
 49%|████▉     | 118/240 [00:29<00:21,  5.65it/s]
 50%|████▉     | 119/240 [00:30<00:34,  3.46it/s]
 50%|█████     | 120/240 [00:30<00:34,  3.48it/s]
 51%|█████     | 122/240 [00:31<00:30,  3.91it/s]
 51%|█████▏    | 123/240 [00:31<00:30,  3.79it/s]
 52%|█████▏    | 125/240 [00:31<00:22,  5.17it/s]
 53%|█████▎    | 127/240 [00:32<00:18,  6.15it/s]
 53%|█████▎    | 128/240 [00:32<00:30,  3.66it/s]
 54%|█████▍    | 129/240 [00:32<00:28,  3.90it/s]
 55%|█████▍    | 131/240 [00:33<00:20,  5.42it/s]
 55%|█████▌    | 133/240 [00:33<00:15,  7.03it/s]
 56%|█████▋    | 135/240 [00:33<00:17,  5.84it/s]
 57%|█████▋    | 136/240 [00:33<00:19,  5.28it/s]
 57%|█████▋    | 137/240 [00:34<00:28,  3.60it/s]
 57%|█████▊    | 138/240 [00:34<00:28,  3.58it/s]
 58%|█████▊    | 140/240 [00:35<00:24,  4.10it/s]
 59%|█████▉    | 141/240 [00:35<00:25,  3.95it/s]
 59%|█████▉    | 142/240 [00:35<00:22,  4.33it/s]
 60%|█████▉    | 143/240 [00:35<00:24,  3.96it/s]
 60%|██████    | 145/240 [00:36<00:24,  3.88it/s]
 61%|██████    | 146/240 [00:36<00:27,  3.40it/s]
 62%|██████▏   | 149/240 [00:37<00:21,  4.23it/s]
 62%|██████▎   | 150/240 [00:37<00:21,  4.26it/s]
 63%|██████▎   | 151/240 [00:38<00:38,  2.29it/s]
 64%|██████▍   | 154/240 [00:39<00:21,  3.98it/s]
 65%|██████▌   | 157/240 [00:39<00:13,  5.96it/s]
 67%|██████▋   | 160/240 [00:39<00:10,  7.91it/s]
 68%|██████▊   | 162/240 [00:39<00:08,  8.79it/s]
 69%|██████▉   | 165/240 [00:39<00:06, 11.11it/s]
 70%|██████▉   | 167/240 [00:39<00:07,  9.69it/s]
 70%|███████   | 169/240 [00:40<00:11,  6.44it/s]
 72%|███████▏  | 172/240 [00:40<00:08,  8.41it/s]
 72%|███████▎  | 174/240 [00:41<00:09,  6.95it/s]
 73%|███████▎  | 176/240 [00:41<00:09,  6.99it/s]
 74%|███████▍  | 177/240 [00:41<00:08,  7.01it/s]
 74%|███████▍  | 178/240 [00:41<00:11,  5.50it/s]
 75%|███████▍  | 179/240 [00:42<00:11,  5.17it/s]
 75%|███████▌  | 180/240 [00:42<00:15,  3.84it/s]
 76%|███████▋  | 183/240 [00:42<00:11,  5.07it/s]
 77%|███████▋  | 185/240 [00:43<00:10,  5.28it/s]
 78%|███████▊  | 186/240 [00:43<00:11,  4.69it/s]
 78%|███████▊  | 187/240 [00:44<00:13,  3.86it/s]
 78%|███████▊  | 188/240 [00:44<00:14,  3.69it/s]
 79%|███████▉  | 189/240 [00:45<00:22,  2.24it/s]
 80%|███████▉  | 191/240 [00:45<00:18,  2.58it/s]
 80%|████████  | 193/240 [00:46<00:12,  3.76it/s]
 81%|████████  | 194/240 [00:46<00:13,  3.44it/s]
 81%|████████▏ | 195/240 [00:46<00:12,  3.48it/s]
 82%|████████▏ | 196/240 [00:47<00:12,  3.43it/s]
 82%|████████▏ | 197/240 [00:47<00:14,  2.88it/s]
 82%|████████▎ | 198/240 [00:47<00:13,  3.18it/s]
 83%|████████▎ | 199/240 [00:47<00:10,  3.88it/s]
 84%|████████▍ | 202/240 [00:48<00:05,  6.41it/s]
 85%|████████▍ | 203/240 [00:48<00:06,  6.13it/s]
 86%|████████▌ | 206/240 [00:48<00:03,  9.55it/s]
 87%|████████▋ | 209/240 [00:48<00:02, 12.70it/s]
 88%|████████▊ | 211/240 [00:48<00:02, 12.58it/s]
 89%|████████▉ | 213/240 [00:48<00:02, 11.06it/s]
 90%|████████▉ | 215/240 [00:49<00:02,  8.66it/s]
 90%|█████████ | 217/240 [00:49<00:04,  5.67it/s]
 92%|█████████▏| 220/240 [00:50<00:02,  7.49it/s]
 93%|█████████▎| 223/240 [00:50<00:01, 10.06it/s]
 94%|█████████▍| 225/240 [00:50<00:01,  8.78it/s]
 95%|█████████▍| 227/240 [00:51<00:02,  5.08it/s]
 95%|█████████▌| 228/240 [00:51<00:02,  4.26it/s]
 95%|█████████▌| 229/240 [00:52<00:02,  4.09it/s]
 96%|█████████▌| 230/240 [00:52<00:02,  4.60it/s]
 96%|█████████▋| 231/240 [00:52<00:01,  4.76it/s]
 97%|█████████▋| 232/240 [00:52<00:02,  3.89it/s]
 97%|█████████▋| 233/240 [00:54<00:04,  1.60it/s]
 98%|█████████▊| 234/240 [00:57<00:06,  1.16s/it]
 98%|█████████▊| 235/240 [00:57<00:04,  1.01it/s]
 98%|█████████▊| 236/240 [00:58<00:03,  1.19it/s]
 99%|█████████▉| 237/240 [01:01<00:04,  1.48s/it]
 99%|█████████▉| 238/240 [01:02<00:02,  1.49s/it]
100%|█████████▉| 239/240 [01:03<00:01,  1.39s/it]
100%|██████████| 240/240 [01:05<00:00,  1.36s/it]
100%|██████████| 240/240 [01:05<00:00,  3.69it/s]
2025-05-21 23:23:29,688 - modnet - INFO - Loss per individual: ind0:51.124   ind1:43.257   ind2:42.691   ind3:52.527   ind4:44.831   ind5:43.996   ind6:43.461   ind7:43.571   ind8:44.001   ind9:44.793   ind10:43.212   ind11:45.855   ind12:43.873   ind13:52.232   ind14:44.493   ind15:42.362   ind16:43.320   ind17:49.682   ind18:48.180   ind19:53.716   ind20:41.787   ind21:44.471   ind22:42.805   ind23:44.780   ind24:43.300   ind25:50.886   ind26:42.520   ind27:43.514   ind28:44.276   ind29:52.319
2025-05-21 23:23:29,707 - modnet - INFO - Generation 19 best loss: 41.4220
2025-05-21 23:23:29,707 - modnet - INFO - Generation 19 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.5, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 540, 'n_layers': 3, 'layer_mults': None}
2025-05-21 23:23:29,707 - modnet - INFO - === Generation 20 ===

  0%|          | 0/240 [00:00<?, ?it/s]
  0%|          | 1/240 [00:04<16:13,  4.07s/it]
  1%|▏         | 3/240 [00:04<04:21,  1.10s/it]
  2%|▏         | 5/240 [00:04<02:13,  1.76it/s]
  3%|▎         | 8/240 [00:04<01:18,  2.95it/s]
  4%|▍         | 10/240 [00:06<02:06,  1.81it/s]
  5%|▌         | 12/240 [00:06<01:34,  2.40it/s]
  5%|▌         | 13/240 [00:07<01:26,  2.61it/s]
  6%|▌         | 14/240 [00:07<01:13,  3.07it/s]
  6%|▋         | 15/240 [00:08<02:10,  1.72it/s]
  7%|▋         | 16/240 [00:08<01:46,  2.09it/s]
  7%|▋         | 17/240 [00:09<01:31,  2.43it/s]
  8%|▊         | 18/240 [00:09<01:34,  2.35it/s]
  8%|▊         | 19/240 [00:09<01:15,  2.95it/s]
  9%|▉         | 21/240 [00:09<00:56,  3.89it/s]
 10%|▉         | 23/240 [00:10<00:40,  5.32it/s]
 10%|█         | 25/240 [00:10<00:33,  6.33it/s]
 11%|█         | 26/240 [00:10<00:31,  6.74it/s]
 12%|█▏        | 28/240 [00:10<00:35,  5.92it/s]
 12%|█▏        | 29/240 [00:11<00:35,  5.89it/s]
 13%|█▎        | 31/240 [00:11<00:28,  7.35it/s]
 14%|█▍        | 33/240 [00:11<00:27,  7.64it/s]
 15%|█▍        | 35/240 [00:12<00:44,  4.60it/s]
 15%|█▌        | 36/240 [00:12<00:40,  5.01it/s]
 16%|█▌        | 38/240 [00:12<00:32,  6.17it/s]
 17%|█▋        | 40/240 [00:13<00:36,  5.42it/s]
 18%|█▊        | 42/240 [00:13<00:32,  6.03it/s]
 18%|█▊        | 43/240 [00:14<00:57,  3.44it/s]
 19%|█▉        | 45/240 [00:14<00:44,  4.38it/s]
 19%|█▉        | 46/240 [00:14<00:39,  4.88it/s]
 20%|█▉        | 47/240 [00:14<00:39,  4.92it/s]
 20%|██        | 48/240 [00:14<00:37,  5.11it/s]
 21%|██        | 50/240 [00:14<00:26,  7.08it/s]
 21%|██▏       | 51/240 [00:15<00:37,  5.01it/s]
 22%|██▏       | 52/240 [00:15<00:40,  4.69it/s]
 22%|██▏       | 53/240 [00:15<00:38,  4.90it/s]
 22%|██▎       | 54/240 [00:16<00:47,  3.89it/s]
 23%|██▎       | 56/240 [00:16<00:35,  5.13it/s]
 24%|██▍       | 58/240 [00:16<00:27,  6.56it/s]
 25%|██▍       | 59/240 [00:16<00:32,  5.65it/s]
 25%|██▌       | 60/240 [00:17<00:43,  4.10it/s]
 25%|██▌       | 61/240 [00:17<00:38,  4.60it/s]
 26%|██▌       | 62/240 [00:17<00:33,  5.29it/s]
 27%|██▋       | 64/240 [00:17<00:26,  6.69it/s]
 28%|██▊       | 67/240 [00:17<00:17,  9.97it/s]
 29%|██▉       | 70/240 [00:18<00:14, 12.12it/s]
 30%|███       | 72/240 [00:18<00:24,  6.74it/s]
 31%|███       | 74/240 [00:18<00:23,  7.18it/s]
 32%|███▏      | 76/240 [00:19<00:19,  8.50it/s]
 32%|███▎      | 78/240 [00:19<00:25,  6.40it/s]
 33%|███▎      | 80/240 [00:19<00:20,  7.99it/s]
 34%|███▍      | 82/240 [00:20<00:29,  5.34it/s]
 35%|███▍      | 83/240 [00:20<00:30,  5.13it/s]
 35%|███▌      | 84/240 [00:20<00:37,  4.17it/s]
 35%|███▌      | 85/240 [00:21<00:54,  2.86it/s]
 36%|███▌      | 86/240 [00:21<00:50,  3.05it/s]
 37%|███▋      | 88/240 [00:22<00:35,  4.25it/s]
 37%|███▋      | 89/240 [00:22<00:36,  4.19it/s]
 38%|███▊      | 91/240 [00:22<00:32,  4.60it/s]
 38%|███▊      | 92/240 [00:22<00:30,  4.92it/s]
 39%|███▉      | 93/240 [00:23<00:28,  5.08it/s]
 40%|███▉      | 95/240 [00:23<00:23,  6.19it/s]
 40%|████      | 96/240 [00:23<00:31,  4.52it/s]
 40%|████      | 97/240 [00:23<00:29,  4.85it/s]
 41%|████      | 98/240 [00:24<00:36,  3.89it/s]
 41%|████▏     | 99/240 [00:24<00:32,  4.33it/s]
 42%|████▏     | 100/240 [00:24<00:32,  4.25it/s]
 42%|████▏     | 101/240 [00:25<00:37,  3.69it/s]
 42%|████▎     | 102/240 [00:25<00:34,  3.99it/s]
 43%|████▎     | 104/240 [00:25<00:22,  5.95it/s]
 44%|████▍     | 105/240 [00:26<01:00,  2.23it/s]
 45%|████▍     | 107/240 [00:26<00:38,  3.45it/s]
 45%|████▌     | 108/240 [00:26<00:33,  3.90it/s]
 46%|████▌     | 110/240 [00:27<00:22,  5.70it/s]
 47%|████▋     | 112/240 [00:27<00:18,  6.94it/s]
 48%|████▊     | 115/240 [00:27<00:13,  9.28it/s]
 49%|████▉     | 117/240 [00:27<00:13,  9.42it/s]
 50%|████▉     | 119/240 [00:27<00:10, 11.02it/s]
 50%|█████     | 121/240 [00:27<00:09, 12.25it/s]
 51%|█████▏    | 123/240 [00:27<00:08, 13.71it/s]
 52%|█████▏    | 125/240 [00:28<00:08, 13.63it/s]
 53%|█████▎    | 127/240 [00:28<00:12,  8.94it/s]
 54%|█████▍    | 129/240 [00:28<00:14,  7.50it/s]
 55%|█████▍    | 131/240 [00:29<00:22,  4.78it/s]
 56%|█████▌    | 134/240 [00:29<00:16,  6.62it/s]
 57%|█████▋    | 136/240 [00:30<00:14,  7.00it/s]
 57%|█████▊    | 138/240 [00:30<00:16,  6.20it/s]
 58%|█████▊    | 139/240 [00:30<00:17,  5.65it/s]
 58%|█████▊    | 140/240 [00:31<00:19,  5.23it/s]
 59%|█████▉    | 142/240 [00:31<00:15,  6.29it/s]
 60%|██████    | 144/240 [00:31<00:15,  6.33it/s]
 60%|██████    | 145/240 [00:32<00:20,  4.61it/s]
 61%|██████    | 146/240 [00:32<00:19,  4.80it/s]
 61%|██████▏   | 147/240 [00:32<00:20,  4.44it/s]
 62%|██████▏   | 148/240 [00:32<00:27,  3.39it/s]
 62%|██████▏   | 149/240 [00:33<00:29,  3.12it/s]
 62%|██████▎   | 150/240 [00:33<00:23,  3.81it/s]
 63%|██████▎   | 152/240 [00:33<00:16,  5.32it/s]
 65%|██████▍   | 155/240 [00:33<00:10,  8.14it/s]
 65%|██████▌   | 157/240 [00:34<00:12,  6.60it/s]
 66%|██████▌   | 158/240 [00:34<00:18,  4.40it/s]
 66%|██████▋   | 159/240 [00:35<00:21,  3.76it/s]
 67%|██████▋   | 160/240 [00:35<00:19,  4.13it/s]
 67%|██████▋   | 161/240 [00:35<00:23,  3.29it/s]
 68%|██████▊   | 162/240 [00:36<00:20,  3.83it/s]
 68%|██████▊   | 164/240 [00:36<00:14,  5.15it/s]
 69%|██████▉   | 165/240 [00:36<00:14,  5.30it/s]
 69%|██████▉   | 166/240 [00:36<00:12,  5.73it/s]
 70%|██████▉   | 167/240 [00:36<00:16,  4.30it/s]
 70%|███████   | 169/240 [00:37<00:14,  4.89it/s]
 71%|███████   | 170/240 [00:37<00:20,  3.48it/s]
 72%|███████▏  | 172/240 [00:37<00:13,  4.99it/s]
 72%|███████▎  | 174/240 [00:38<00:09,  6.80it/s]
 73%|███████▎  | 176/240 [00:38<00:14,  4.32it/s]
 74%|███████▍  | 177/240 [00:38<00:13,  4.83it/s]
 74%|███████▍  | 178/240 [00:39<00:13,  4.76it/s]
 75%|███████▌  | 180/240 [00:39<00:11,  5.23it/s]
 76%|███████▋  | 183/240 [00:39<00:06,  8.23it/s]
 77%|███████▋  | 185/240 [00:39<00:07,  7.49it/s]
 78%|███████▊  | 187/240 [00:40<00:07,  7.30it/s]
 79%|███████▉  | 189/240 [00:40<00:06,  7.40it/s]
 79%|███████▉  | 190/240 [00:40<00:08,  6.13it/s]
 80%|████████  | 192/240 [00:40<00:06,  7.52it/s]
 81%|████████▏ | 195/240 [00:41<00:05,  8.67it/s]
 82%|████████▏ | 197/240 [00:41<00:04,  9.88it/s]
 83%|████████▎ | 199/240 [00:41<00:04,  8.46it/s]
 84%|████████▍ | 201/240 [00:41<00:04,  8.98it/s]
 85%|████████▍ | 203/240 [00:42<00:05,  6.61it/s]
 85%|████████▌ | 204/240 [00:42<00:06,  5.88it/s]
 85%|████████▌ | 205/240 [00:43<00:08,  4.14it/s]
 86%|████████▌ | 206/240 [00:43<00:09,  3.70it/s]
 86%|████████▋ | 207/240 [00:43<00:08,  3.71it/s]
 87%|████████▋ | 208/240 [00:44<00:10,  3.18it/s]
 87%|████████▋ | 209/240 [00:44<00:09,  3.40it/s]
 88%|████████▊ | 211/240 [00:44<00:05,  5.29it/s]
 89%|████████▉ | 213/240 [00:44<00:04,  6.29it/s]
 90%|████████▉ | 215/240 [00:44<00:03,  7.12it/s]
 90%|█████████ | 216/240 [00:45<00:03,  6.76it/s]
 91%|█████████▏| 219/240 [00:45<00:02,  9.01it/s]
 92%|█████████▏| 220/240 [00:45<00:02,  8.71it/s]
 92%|█████████▎| 222/240 [00:45<00:01,  9.75it/s]
 93%|█████████▎| 224/240 [00:46<00:03,  4.61it/s]
 94%|█████████▍| 225/240 [00:46<00:03,  4.89it/s]
 95%|█████████▍| 227/240 [00:46<00:02,  5.89it/s]
 95%|█████████▌| 228/240 [00:47<00:03,  4.00it/s]
 95%|█████████▌| 229/240 [00:47<00:02,  4.32it/s]
 96%|█████████▌| 230/240 [00:48<00:04,  2.11it/s]
 96%|█████████▋| 231/240 [00:49<00:03,  2.60it/s]
 97%|█████████▋| 232/240 [00:49<00:02,  3.10it/s]
 97%|█████████▋| 233/240 [00:49<00:03,  2.18it/s]
 98%|█████████▊| 235/240 [00:50<00:01,  3.33it/s]
 98%|█████████▊| 236/240 [00:51<00:01,  2.29it/s]
 99%|█████████▉| 238/240 [00:51<00:00,  2.38it/s]
100%|█████████▉| 239/240 [00:52<00:00,  2.37it/s]
100%|██████████| 240/240 [00:55<00:00,  1.02s/it]
100%|██████████| 240/240 [00:55<00:00,  4.35it/s]
2025-05-21 23:24:25,515 - modnet - INFO - Loss per individual: ind0:45.780   ind1:42.275   ind2:45.658   ind3:44.909   ind4:45.355   ind5:585.615   ind6:44.874   ind7:46.819   ind8:43.760   ind9:43.643   ind10:43.773   ind11:45.930   ind12:43.303   ind13:45.253   ind14:44.253   ind15:43.476   ind16:47.931   ind17:141.581   ind18:44.160   ind19:46.017   ind20:91.589   ind21:45.323   ind22:42.853   ind23:87.196   ind24:45.102   ind25:43.075   ind26:47.092   ind27:41.985   ind28:45.051   ind29:45.223
2025-05-21 23:24:25,561 - modnet - INFO - Generation 20 best loss: 41.4220
2025-05-21 23:24:25,561 - modnet - INFO - Generation 20 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.5, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 540, 'n_layers': 3, 'layer_mults': None}
2025-05-21 23:24:25,562 - modnet - INFO - === Generation 21 ===

  0%|          | 0/240 [00:00<?, ?it/s]
  0%|          | 1/240 [00:05<20:32,  5.16s/it]
  1%|          | 2/240 [00:05<09:00,  2.27s/it]
  2%|▏         | 5/240 [00:05<02:42,  1.44it/s]
  2%|▎         | 6/240 [00:05<02:07,  1.84it/s]
  3%|▎         | 8/240 [00:05<01:19,  2.93it/s]
  4%|▍         | 10/240 [00:06<01:11,  3.21it/s]
  5%|▍         | 11/240 [00:06<01:11,  3.20it/s]
  5%|▌         | 12/240 [00:06<01:00,  3.74it/s]
  5%|▌         | 13/240 [00:06<00:52,  4.29it/s]
  6%|▋         | 15/240 [00:07<00:39,  5.72it/s]
  7%|▋         | 17/240 [00:07<00:58,  3.83it/s]
  8%|▊         | 18/240 [00:08<01:27,  2.53it/s]
  8%|▊         | 19/240 [00:08<01:13,  3.00it/s]
  9%|▉         | 21/240 [00:09<00:50,  4.37it/s]
 10%|█         | 24/240 [00:09<00:40,  5.35it/s]
 10%|█         | 25/240 [00:09<00:43,  4.94it/s]
 11%|█▏        | 27/240 [00:10<00:36,  5.79it/s]
 12%|█▏        | 29/240 [00:10<00:47,  4.45it/s]
 12%|█▎        | 30/240 [00:10<00:50,  4.15it/s]
 13%|█▎        | 31/240 [00:11<00:56,  3.69it/s]
 13%|█▎        | 32/240 [00:11<01:00,  3.43it/s]
 14%|█▍        | 33/240 [00:11<00:58,  3.53it/s]
 15%|█▍        | 35/240 [00:12<00:50,  4.09it/s]
 15%|█▌        | 36/240 [00:12<00:43,  4.71it/s]
 15%|█▌        | 37/240 [00:12<00:44,  4.58it/s]
 16%|█▌        | 38/240 [00:12<00:37,  5.32it/s]
 16%|█▋        | 39/240 [00:12<00:36,  5.46it/s]
 17%|█▋        | 41/240 [00:13<00:29,  6.75it/s]
 18%|█▊        | 42/240 [00:13<00:32,  6.13it/s]
 18%|█▊        | 43/240 [00:13<00:43,  4.58it/s]
 19%|█▉        | 45/240 [00:14<00:34,  5.68it/s]
 20%|██        | 48/240 [00:14<00:39,  4.87it/s]
 20%|██        | 49/240 [00:14<00:41,  4.64it/s]
 21%|██        | 50/240 [00:16<01:14,  2.56it/s]
 22%|██▏       | 52/240 [00:16<00:57,  3.27it/s]
 22%|██▏       | 53/240 [00:16<00:52,  3.57it/s]
 22%|██▎       | 54/240 [00:16<00:50,  3.67it/s]
 23%|██▎       | 56/240 [00:17<00:38,  4.73it/s]
 24%|██▍       | 58/240 [00:17<00:33,  5.46it/s]
 25%|██▍       | 59/240 [00:17<00:40,  4.46it/s]
 25%|██▌       | 60/240 [00:17<00:42,  4.21it/s]
 26%|██▌       | 62/240 [00:18<00:44,  4.03it/s]
 27%|██▋       | 64/240 [00:18<00:32,  5.40it/s]
 27%|██▋       | 65/240 [00:18<00:31,  5.54it/s]
 28%|██▊       | 66/240 [00:19<00:37,  4.63it/s]
 28%|██▊       | 68/240 [00:20<01:20,  2.13it/s]
 29%|██▉       | 69/240 [00:20<01:06,  2.56it/s]
 30%|██▉       | 71/240 [00:21<00:43,  3.85it/s]
 30%|███       | 73/240 [00:21<00:33,  4.93it/s]
 31%|███▏      | 75/240 [00:21<00:26,  6.21it/s]
 32%|███▏      | 77/240 [00:21<00:30,  5.34it/s]
 32%|███▎      | 78/240 [00:22<00:27,  5.83it/s]
 33%|███▎      | 80/240 [00:22<00:22,  7.27it/s]
 34%|███▍      | 82/240 [00:22<00:26,  6.02it/s]
 35%|███▍      | 83/240 [00:23<00:33,  4.63it/s]
 35%|███▌      | 84/240 [00:23<00:39,  3.98it/s]
 35%|███▌      | 85/240 [00:23<00:42,  3.66it/s]
 36%|███▌      | 86/240 [00:24<00:45,  3.36it/s]
 36%|███▋      | 87/240 [00:24<00:41,  3.67it/s]
 37%|███▋      | 89/240 [00:24<00:28,  5.36it/s]
 38%|███▊      | 91/240 [00:24<00:21,  7.06it/s]
 39%|███▉      | 93/240 [00:24<00:17,  8.30it/s]
 40%|███▉      | 95/240 [00:24<00:15,  9.51it/s]
 40%|████      | 97/240 [00:25<00:15,  9.36it/s]
 41%|████▏     | 99/240 [00:25<00:13, 10.63it/s]
 42%|████▎     | 102/240 [00:25<00:10, 13.12it/s]
 43%|████▎     | 104/240 [00:25<00:11, 11.60it/s]
 44%|████▍     | 106/240 [00:25<00:13, 10.12it/s]
 45%|████▌     | 108/240 [00:26<00:19,  6.78it/s]
 46%|████▋     | 111/240 [00:26<00:14,  8.98it/s]
 47%|████▋     | 113/240 [00:26<00:14,  8.87it/s]
 48%|████▊     | 115/240 [00:27<00:21,  5.95it/s]
 49%|████▉     | 117/240 [00:27<00:17,  6.97it/s]
 49%|████▉     | 118/240 [00:27<00:18,  6.72it/s]
 50%|████▉     | 119/240 [00:27<00:17,  6.96it/s]
 50%|█████     | 121/240 [00:28<00:14,  8.25it/s]
 51%|█████▏    | 123/240 [00:28<00:11,  9.80it/s]
 52%|█████▏    | 125/240 [00:28<00:10, 11.21it/s]
 53%|█████▎    | 127/240 [00:29<00:24,  4.67it/s]
 53%|█████▎    | 128/240 [00:30<00:39,  2.81it/s]
 55%|█████▍    | 131/240 [00:30<00:27,  3.92it/s]
 55%|█████▌    | 133/240 [00:30<00:21,  5.07it/s]
 56%|█████▋    | 135/240 [00:31<00:29,  3.60it/s]
 57%|█████▋    | 136/240 [00:32<00:36,  2.83it/s]
 57%|█████▋    | 137/240 [00:32<00:33,  3.11it/s]
 57%|█████▊    | 138/240 [00:32<00:29,  3.49it/s]
 58%|█████▊    | 139/240 [00:33<00:26,  3.77it/s]
 58%|█████▊    | 140/240 [00:33<00:25,  3.94it/s]
 59%|█████▉    | 141/240 [00:33<00:23,  4.21it/s]
 60%|█████▉    | 143/240 [00:33<00:18,  5.38it/s]
 60%|██████    | 144/240 [00:33<00:18,  5.27it/s]
 60%|██████    | 145/240 [00:34<00:18,  5.01it/s]
 61%|██████▏   | 147/240 [00:34<00:15,  5.82it/s]
 62%|██████▏   | 148/240 [00:34<00:23,  3.97it/s]
 62%|██████▎   | 150/240 [00:35<00:20,  4.45it/s]
 63%|██████▎   | 152/240 [00:35<00:15,  5.59it/s]
 64%|██████▍   | 153/240 [00:35<00:19,  4.47it/s]
 64%|██████▍   | 154/240 [00:36<00:22,  3.91it/s]
 65%|██████▍   | 155/240 [00:36<00:18,  4.54it/s]
 65%|██████▌   | 157/240 [00:36<00:19,  4.35it/s]
 66%|██████▌   | 158/240 [00:36<00:16,  4.88it/s]
 66%|██████▋   | 159/240 [00:37<00:18,  4.48it/s]
 67%|██████▋   | 161/240 [00:37<00:12,  6.57it/s]
 68%|██████▊   | 162/240 [00:37<00:15,  5.09it/s]
 68%|██████▊   | 163/240 [00:37<00:15,  5.11it/s]
 69%|██████▉   | 165/240 [00:38<00:14,  5.14it/s]
 70%|██████▉   | 167/240 [00:38<00:12,  5.73it/s]
 70%|███████   | 168/240 [00:38<00:16,  4.28it/s]
 70%|███████   | 169/240 [00:39<00:14,  4.83it/s]
 71%|███████▏  | 171/240 [00:39<00:11,  6.10it/s]
 72%|███████▏  | 172/240 [00:39<00:11,  5.85it/s]
 72%|███████▏  | 173/240 [00:39<00:12,  5.31it/s]
 72%|███████▎  | 174/240 [00:40<00:17,  3.82it/s]
 74%|███████▍  | 177/240 [00:40<00:09,  6.78it/s]
 75%|███████▍  | 179/240 [00:40<00:07,  8.01it/s]
 75%|███████▌  | 181/240 [00:40<00:08,  7.26it/s]
 76%|███████▌  | 182/240 [00:41<00:10,  5.36it/s]
 76%|███████▋  | 183/240 [00:41<00:11,  4.93it/s]
 78%|███████▊  | 186/240 [00:41<00:08,  6.45it/s]
 78%|███████▊  | 187/240 [00:42<00:10,  5.18it/s]
 78%|███████▊  | 188/240 [00:42<00:15,  3.39it/s]
 79%|███████▉  | 189/240 [00:42<00:13,  3.84it/s]
 79%|███████▉  | 190/240 [00:43<00:11,  4.50it/s]
 80%|████████  | 192/240 [00:43<00:08,  5.74it/s]
 81%|████████  | 194/240 [00:43<00:06,  7.32it/s]
 82%|████████▏ | 196/240 [00:43<00:06,  6.76it/s]
 82%|████████▎ | 198/240 [00:43<00:04,  8.46it/s]
 83%|████████▎ | 200/240 [00:44<00:04,  8.60it/s]
 84%|████████▍ | 202/240 [00:44<00:04,  8.29it/s]
 85%|████████▌ | 204/240 [00:44<00:03,  9.95it/s]
 86%|████████▌ | 206/240 [00:44<00:03, 10.32it/s]
 87%|████████▋ | 208/240 [00:45<00:04,  6.77it/s]
 87%|████████▋ | 209/240 [00:45<00:04,  7.04it/s]
 88%|████████▊ | 211/240 [00:45<00:03,  8.41it/s]
 89%|████████▉ | 213/240 [00:45<00:03,  8.54it/s]
 90%|████████▉ | 215/240 [00:45<00:03,  8.33it/s]
 91%|█████████ | 218/240 [00:46<00:02,  7.35it/s]
 91%|█████████▏| 219/240 [00:46<00:03,  6.60it/s]
 92%|█████████▏| 221/240 [00:47<00:03,  5.81it/s]
 93%|█████████▎| 224/240 [00:47<00:02,  7.42it/s]
 94%|█████████▍| 226/240 [00:47<00:01,  7.47it/s]
 95%|█████████▌| 228/240 [00:47<00:01,  8.51it/s]
 95%|█████████▌| 229/240 [00:47<00:01,  8.02it/s]
 96%|█████████▌| 230/240 [00:48<00:01,  7.66it/s]
 97%|█████████▋| 232/240 [00:48<00:01,  7.16it/s]
 97%|█████████▋| 233/240 [00:48<00:01,  5.99it/s]
 98%|█████████▊| 234/240 [00:48<00:01,  4.89it/s]
 98%|█████████▊| 235/240 [00:49<00:01,  4.67it/s]
 98%|█████████▊| 236/240 [00:49<00:00,  4.02it/s]
 99%|█████████▉| 238/240 [00:50<00:00,  3.03it/s]
100%|█████████▉| 239/240 [00:50<00:00,  2.95it/s]
100%|██████████| 240/240 [00:51<00:00,  2.10it/s]
100%|██████████| 240/240 [00:51<00:00,  4.64it/s]
2025-05-21 23:25:17,913 - modnet - INFO - Loss per individual: ind0:46.161   ind1:43.590   ind2:44.537   ind3:41.753   ind4:47.227   ind5:52.747   ind6:43.285   ind7:585.615   ind8:46.567   ind9:47.332   ind10:47.909   ind11:43.003   ind12:48.035   ind13:43.107   ind14:44.677   ind15:43.087   ind16:46.960   ind17:572.353   ind18:44.568   ind19:44.691   ind20:43.044   ind21:44.276   ind22:44.201   ind23:43.033   ind24:43.953   ind25:47.562   ind26:46.698   ind27:46.789   ind28:43.234   ind29:45.429
2025-05-21 23:25:17,945 - modnet - INFO - Generation 21 best loss: 41.4220
2025-05-21 23:25:17,945 - modnet - INFO - Generation 21 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.5, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 540, 'n_layers': 3, 'layer_mults': None}
2025-05-21 23:25:17,946 - modnet - INFO - === Generation 22 ===

  0%|          | 0/240 [00:00<?, ?it/s]
  0%|          | 1/240 [00:04<17:54,  4.49s/it]
  1%|          | 2/240 [00:05<09:27,  2.39s/it]
  1%|▏         | 3/240 [00:06<07:03,  1.79s/it]
  2%|▏         | 4/240 [00:06<04:28,  1.14s/it]
  2%|▏         | 5/240 [00:06<03:10,  1.23it/s]
  3%|▎         | 7/240 [00:07<01:42,  2.28it/s]
  4%|▍         | 9/240 [00:07<01:17,  2.97it/s]
  4%|▍         | 10/240 [00:07<01:09,  3.33it/s]
  5%|▍         | 11/240 [00:07<00:59,  3.83it/s]
  5%|▌         | 12/240 [00:08<01:45,  2.17it/s]
  5%|▌         | 13/240 [00:08<01:23,  2.73it/s]
  6%|▌         | 14/240 [00:08<01:07,  3.33it/s]
  7%|▋         | 16/240 [00:09<00:51,  4.37it/s]
  7%|▋         | 17/240 [00:09<01:05,  3.42it/s]
  8%|▊         | 19/240 [00:10<00:58,  3.80it/s]
  9%|▉         | 21/240 [00:10<00:42,  5.20it/s]
 10%|▉         | 23/240 [00:11<00:54,  3.96it/s]
 10%|█         | 24/240 [00:11<00:48,  4.48it/s]
 11%|█         | 26/240 [00:11<00:44,  4.77it/s]
 11%|█▏        | 27/240 [00:11<00:41,  5.18it/s]
 12%|█▏        | 29/240 [00:11<00:31,  6.77it/s]
 12%|█▎        | 30/240 [00:12<00:35,  5.85it/s]
 13%|█▎        | 31/240 [00:12<00:37,  5.57it/s]
 13%|█▎        | 32/240 [00:12<00:48,  4.28it/s]
 14%|█▍        | 34/240 [00:12<00:35,  5.81it/s]
 15%|█▍        | 35/240 [00:13<00:36,  5.69it/s]
 15%|█▌        | 37/240 [00:13<00:30,  6.59it/s]
 16%|█▌        | 38/240 [00:13<00:37,  5.35it/s]
 17%|█▋        | 40/240 [00:13<00:27,  7.28it/s]
 17%|█▋        | 41/240 [00:14<00:48,  4.07it/s]
 18%|█▊        | 42/240 [00:14<00:50,  3.93it/s]
 18%|█▊        | 43/240 [00:14<00:44,  4.45it/s]
 18%|█▊        | 44/240 [00:14<00:42,  4.65it/s]
 19%|█▉        | 45/240 [00:15<00:37,  5.22it/s]
 20%|█▉        | 47/240 [00:15<00:26,  7.37it/s]
 20%|██        | 49/240 [00:15<00:20,  9.40it/s]
 22%|██▏       | 52/240 [00:15<00:15, 11.96it/s]
 22%|██▎       | 54/240 [00:15<00:17, 10.58it/s]
 23%|██▎       | 56/240 [00:15<00:19,  9.50it/s]
 24%|██▍       | 58/240 [00:17<00:56,  3.20it/s]
 25%|██▌       | 61/240 [00:17<00:37,  4.80it/s]
 26%|██▋       | 63/240 [00:17<00:29,  5.98it/s]
 28%|██▊       | 66/240 [00:17<00:21,  8.03it/s]
 28%|██▊       | 68/240 [00:18<00:18,  9.06it/s]
 29%|██▉       | 70/240 [00:18<00:16, 10.16it/s]
 30%|███       | 72/240 [00:18<00:22,  7.36it/s]
 31%|███       | 74/240 [00:19<00:23,  7.19it/s]
 32%|███▏      | 76/240 [00:19<00:28,  5.69it/s]
 32%|███▏      | 77/240 [00:19<00:35,  4.57it/s]
 32%|███▎      | 78/240 [00:20<00:33,  4.83it/s]
 34%|███▍      | 81/240 [00:20<00:21,  7.32it/s]
 35%|███▍      | 83/240 [00:20<00:18,  8.43it/s]
 35%|███▌      | 85/240 [00:21<00:35,  4.32it/s]
 36%|███▌      | 86/240 [00:21<00:33,  4.57it/s]
 37%|███▋      | 88/240 [00:21<00:28,  5.36it/s]
 37%|███▋      | 89/240 [00:22<00:28,  5.31it/s]
 38%|███▊      | 90/240 [00:22<00:26,  5.63it/s]
 38%|███▊      | 91/240 [00:22<00:26,  5.63it/s]
 38%|███▊      | 92/240 [00:23<00:57,  2.58it/s]
 39%|███▉      | 94/240 [00:23<00:46,  3.11it/s]
 40%|███▉      | 95/240 [00:24<00:43,  3.30it/s]
 40%|████      | 97/240 [00:24<00:31,  4.58it/s]
 41%|████      | 98/240 [00:24<00:27,  5.14it/s]
 42%|████▏     | 100/240 [00:24<00:30,  4.66it/s]
 42%|████▏     | 101/240 [00:25<00:31,  4.41it/s]
 42%|████▎     | 102/240 [00:25<00:31,  4.37it/s]
 43%|████▎     | 103/240 [00:25<00:27,  4.99it/s]
 43%|████▎     | 104/240 [00:25<00:28,  4.71it/s]
 44%|████▍     | 105/240 [00:25<00:28,  4.80it/s]
 44%|████▍     | 106/240 [00:26<00:46,  2.90it/s]
 45%|████▌     | 109/240 [00:26<00:24,  5.31it/s]
 46%|████▋     | 111/240 [00:27<00:23,  5.56it/s]
 47%|████▋     | 113/240 [00:27<00:19,  6.43it/s]
 48%|████▊     | 114/240 [00:27<00:19,  6.54it/s]
 48%|████▊     | 115/240 [00:27<00:18,  6.80it/s]
 49%|████▉     | 117/240 [00:27<00:13,  8.86it/s]
 50%|████▉     | 119/240 [00:27<00:12, 10.03it/s]
 50%|█████     | 121/240 [00:28<00:11, 10.80it/s]
 51%|█████▏    | 123/240 [00:28<00:09, 12.08it/s]
 52%|█████▏    | 125/240 [00:28<00:11, 10.24it/s]
 53%|█████▎    | 127/240 [00:28<00:09, 12.04it/s]
 54%|█████▍    | 129/240 [00:28<00:12,  8.99it/s]
 55%|█████▍    | 131/240 [00:29<00:17,  6.06it/s]
 55%|█████▌    | 132/240 [00:29<00:20,  5.37it/s]
 55%|█████▌    | 133/240 [00:30<00:25,  4.16it/s]
 56%|█████▋    | 135/240 [00:30<00:19,  5.29it/s]
 57%|█████▋    | 137/240 [00:30<00:15,  6.50it/s]
 58%|█████▊    | 140/240 [00:30<00:12,  8.08it/s]
 59%|█████▉    | 141/240 [00:30<00:12,  7.66it/s]
 59%|█████▉    | 142/240 [00:31<00:14,  6.99it/s]
 60%|█████▉    | 143/240 [00:31<00:18,  5.17it/s]
 60%|██████    | 145/240 [00:31<00:14,  6.58it/s]
 61%|██████    | 146/240 [00:32<00:20,  4.67it/s]
 61%|██████▏   | 147/240 [00:32<00:26,  3.54it/s]
 62%|██████▏   | 148/240 [00:32<00:27,  3.35it/s]
 62%|██████▏   | 149/240 [00:33<00:23,  3.80it/s]
 63%|██████▎   | 151/240 [00:33<00:15,  5.71it/s]
 63%|██████▎   | 152/240 [00:33<00:16,  5.37it/s]
 64%|██████▍   | 154/240 [00:33<00:11,  7.31it/s]
 65%|██████▍   | 155/240 [00:33<00:12,  6.61it/s]
 65%|██████▌   | 157/240 [00:33<00:10,  8.00it/s]
 66%|██████▋   | 159/240 [00:34<00:08,  9.38it/s]
 67%|██████▋   | 161/240 [00:34<00:10,  7.64it/s]
 68%|██████▊   | 162/240 [00:34<00:09,  7.82it/s]
 69%|██████▉   | 165/240 [00:34<00:07, 10.65it/s]
 70%|██████▉   | 167/240 [00:35<00:11,  6.61it/s]
 70%|███████   | 169/240 [00:35<00:09,  7.32it/s]
 71%|███████   | 170/240 [00:35<00:10,  6.62it/s]
 71%|███████▏  | 171/240 [00:36<00:16,  4.18it/s]
 72%|███████▏  | 173/240 [00:36<00:16,  4.10it/s]
 72%|███████▎  | 174/240 [00:37<00:15,  4.23it/s]
 74%|███████▍  | 178/240 [00:37<00:08,  7.22it/s]
 75%|███████▌  | 181/240 [00:37<00:05,  9.84it/s]
 76%|███████▋  | 183/240 [00:37<00:06,  9.01it/s]
 77%|███████▋  | 185/240 [00:39<00:15,  3.57it/s]
 78%|███████▊  | 186/240 [00:39<00:13,  3.90it/s]
 78%|███████▊  | 187/240 [00:39<00:12,  4.20it/s]
 79%|███████▉  | 189/240 [00:39<00:11,  4.54it/s]
 79%|███████▉  | 190/240 [00:40<00:11,  4.30it/s]
 80%|███████▉  | 191/240 [00:40<00:12,  3.92it/s]
 80%|████████  | 193/240 [00:40<00:09,  5.16it/s]
 82%|████████▏ | 196/240 [00:40<00:06,  6.79it/s]
 82%|████████▏ | 197/240 [00:41<00:06,  6.43it/s]
 82%|████████▎ | 198/240 [00:41<00:07,  5.98it/s]
 83%|████████▎ | 199/240 [00:41<00:06,  6.50it/s]
 83%|████████▎ | 200/240 [00:41<00:06,  5.75it/s]
 84%|████████▍ | 201/240 [00:42<00:10,  3.56it/s]
 85%|████████▍ | 203/240 [00:42<00:07,  4.87it/s]
 86%|████████▌ | 206/240 [00:42<00:04,  8.09it/s]
 87%|████████▋ | 208/240 [00:42<00:04,  6.45it/s]
 88%|████████▊ | 210/240 [00:43<00:04,  7.12it/s]
 88%|████████▊ | 212/240 [00:43<00:04,  6.91it/s]
 89%|████████▉ | 213/240 [00:43<00:03,  6.94it/s]
 89%|████████▉ | 214/240 [00:43<00:04,  5.76it/s]
 90%|█████████ | 216/240 [00:44<00:03,  7.71it/s]
 91%|█████████ | 218/240 [00:44<00:03,  6.97it/s]
 92%|█████████▏| 220/240 [00:44<00:03,  6.15it/s]
 92%|█████████▏| 221/240 [00:44<00:03,  6.01it/s]
 92%|█████████▎| 222/240 [00:45<00:03,  5.48it/s]
 94%|█████████▍| 225/240 [00:45<00:02,  7.02it/s]
 94%|█████████▍| 226/240 [00:45<00:01,  7.07it/s]
 95%|█████████▍| 227/240 [00:46<00:03,  3.92it/s]
 95%|█████████▌| 228/240 [00:46<00:03,  3.56it/s]
 95%|█████████▌| 229/240 [00:46<00:02,  3.84it/s]
 96%|█████████▌| 230/240 [00:47<00:03,  3.26it/s]
 96%|█████████▋| 231/240 [00:47<00:02,  3.97it/s]
 97%|█████████▋| 232/240 [00:48<00:03,  2.58it/s]
 97%|█████████▋| 233/240 [00:48<00:02,  2.91it/s]
 98%|█████████▊| 234/240 [00:48<00:02,  2.65it/s]
 98%|█████████▊| 235/240 [00:49<00:01,  3.25it/s]
 99%|█████████▉| 237/240 [00:49<00:00,  3.31it/s]
 99%|█████████▉| 238/240 [00:49<00:00,  3.78it/s]
100%|██████████| 240/240 [00:50<00:00,  4.54it/s]
100%|██████████| 240/240 [00:50<00:00,  4.79it/s]
2025-05-21 23:26:08,653 - modnet - INFO - Loss per individual: ind0:52.478   ind1:44.053   ind2:43.726   ind3:42.898   ind4:43.922   ind5:45.268   ind6:50.192   ind7:45.454   ind8:43.822   ind9:61.320   ind10:49.248   ind11:45.245   ind12:585.615   ind13:44.116   ind14:43.535   ind15:44.680   ind16:46.636   ind17:45.510   ind18:42.633   ind19:45.884   ind20:585.615   ind21:48.514   ind22:46.997   ind23:43.932   ind24:42.868   ind25:583.164   ind26:47.322   ind27:45.602   ind28:45.662   ind29:44.115
2025-05-21 23:26:08,673 - modnet - INFO - Generation 22 best loss: 41.4220
2025-05-21 23:26:08,673 - modnet - INFO - Generation 22 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.5, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 540, 'n_layers': 3, 'layer_mults': None}
2025-05-21 23:26:08,673 - modnet - INFO - === Generation 23 ===

  0%|          | 0/240 [00:00<?, ?it/s]
  0%|          | 1/240 [00:06<26:38,  6.69s/it]
  1%|          | 2/240 [00:07<12:06,  3.05s/it]
  1%|▏         | 3/240 [00:07<07:25,  1.88s/it]
  2%|▏         | 4/240 [00:07<04:38,  1.18s/it]
  2%|▎         | 6/240 [00:08<02:24,  1.62it/s]
  4%|▍         | 9/240 [00:08<01:13,  3.16it/s]
  5%|▍         | 11/240 [00:08<01:11,  3.22it/s]
  5%|▌         | 13/240 [00:09<00:58,  3.85it/s]
  6%|▋         | 15/240 [00:09<00:43,  5.12it/s]
  7%|▋         | 17/240 [00:09<00:47,  4.68it/s]
  8%|▊         | 18/240 [00:09<00:47,  4.71it/s]
  8%|▊         | 19/240 [00:10<00:42,  5.16it/s]
  8%|▊         | 20/240 [00:10<00:45,  4.83it/s]
  9%|▉         | 21/240 [00:10<00:43,  5.06it/s]
  9%|▉         | 22/240 [00:10<00:54,  4.03it/s]
 10%|▉         | 23/240 [00:11<01:08,  3.16it/s]
 10%|█         | 24/240 [00:11<01:04,  3.32it/s]
 11%|█         | 26/240 [00:11<00:49,  4.30it/s]
 12%|█▏        | 28/240 [00:12<00:35,  5.90it/s]
 12%|█▏        | 29/240 [00:12<00:42,  4.95it/s]
 13%|█▎        | 32/240 [00:12<00:29,  6.96it/s]
 14%|█▍        | 34/240 [00:12<00:23,  8.62it/s]
 15%|█▌        | 36/240 [00:12<00:21,  9.59it/s]
 16%|█▌        | 38/240 [00:13<00:19, 10.44it/s]
 17%|█▋        | 40/240 [00:13<00:18, 10.53it/s]
 18%|█▊        | 42/240 [00:13<00:25,  7.82it/s]
 18%|█▊        | 43/240 [00:13<00:29,  6.63it/s]
 18%|█▊        | 44/240 [00:14<00:36,  5.34it/s]
 19%|█▉        | 46/240 [00:14<00:27,  7.06it/s]
 20%|█▉        | 47/240 [00:14<00:41,  4.69it/s]
 20%|██        | 48/240 [00:15<00:48,  3.95it/s]
 20%|██        | 49/240 [00:15<00:45,  4.21it/s]
 21%|██▏       | 51/240 [00:15<00:37,  5.01it/s]
 22%|██▏       | 52/240 [00:15<00:38,  4.90it/s]
 22%|██▎       | 54/240 [00:16<00:36,  5.10it/s]
 23%|██▎       | 55/240 [00:16<00:41,  4.47it/s]
 23%|██▎       | 56/240 [00:16<00:45,  4.01it/s]
 24%|██▍       | 58/240 [00:17<00:33,  5.46it/s]
 25%|██▌       | 61/240 [00:17<00:25,  6.96it/s]
 26%|██▋       | 63/240 [00:17<00:23,  7.57it/s]
 28%|██▊       | 66/240 [00:17<00:18,  9.23it/s]
 28%|██▊       | 68/240 [00:17<00:16, 10.39it/s]
 29%|██▉       | 70/240 [00:19<00:50,  3.34it/s]
 30%|███       | 72/240 [00:19<00:39,  4.27it/s]
 31%|███       | 74/240 [00:19<00:31,  5.25it/s]
 32%|███▏      | 76/240 [00:19<00:24,  6.68it/s]
 32%|███▎      | 78/240 [00:20<00:20,  7.87it/s]
 33%|███▎      | 80/240 [00:20<00:17,  8.97it/s]
 34%|███▍      | 82/240 [00:20<00:17,  9.21it/s]
 35%|███▌      | 85/240 [00:20<00:14, 10.94it/s]
 36%|███▋      | 87/240 [00:20<00:13, 11.25it/s]
 37%|███▋      | 89/240 [00:21<00:21,  7.10it/s]
 38%|███▊      | 91/240 [00:21<00:23,  6.26it/s]
 38%|███▊      | 92/240 [00:22<00:29,  5.02it/s]
 39%|███▉      | 94/240 [00:22<00:24,  5.99it/s]
 40%|███▉      | 95/240 [00:22<00:23,  6.18it/s]
 40%|████      | 96/240 [00:22<00:21,  6.67it/s]
 41%|████      | 98/240 [00:22<00:16,  8.64it/s]
 42%|████▏     | 100/240 [00:23<00:17,  7.80it/s]
 42%|████▏     | 101/240 [00:23<00:22,  6.21it/s]
 42%|████▎     | 102/240 [00:23<00:32,  4.26it/s]
 43%|████▎     | 103/240 [00:24<00:34,  3.96it/s]
 44%|████▍     | 105/240 [00:24<00:31,  4.27it/s]
 44%|████▍     | 106/240 [00:25<00:41,  3.27it/s]
 45%|████▍     | 107/240 [00:25<00:37,  3.53it/s]
 45%|████▌     | 109/240 [00:26<00:48,  2.72it/s]
 46%|████▌     | 110/240 [00:26<00:48,  2.67it/s]
 46%|████▋     | 111/240 [00:26<00:39,  3.25it/s]
 47%|████▋     | 113/240 [00:27<00:28,  4.48it/s]
 48%|████▊     | 115/240 [00:27<00:22,  5.53it/s]
 48%|████▊     | 116/240 [00:27<00:22,  5.55it/s]
 49%|████▉     | 117/240 [00:27<00:23,  5.23it/s]
 49%|████▉     | 118/240 [00:27<00:22,  5.33it/s]
 50%|█████     | 120/240 [00:27<00:17,  7.00it/s]
 50%|█████     | 121/240 [00:28<00:17,  6.90it/s]
 51%|█████     | 122/240 [00:28<00:17,  6.79it/s]
 51%|█████▏    | 123/240 [00:28<00:19,  5.98it/s]
 52%|█████▏    | 125/240 [00:28<00:19,  5.85it/s]
 52%|█████▎    | 126/240 [00:29<00:20,  5.44it/s]
 53%|█████▎    | 128/240 [00:29<00:18,  5.95it/s]
 54%|█████▍    | 129/240 [00:29<00:17,  6.40it/s]
 54%|█████▍    | 130/240 [00:29<00:19,  5.58it/s]
 55%|█████▍    | 131/240 [00:30<00:26,  4.12it/s]
 55%|█████▌    | 133/240 [00:30<00:17,  6.15it/s]
 56%|█████▌    | 134/240 [00:30<00:28,  3.69it/s]
 56%|█████▋    | 135/240 [00:31<00:26,  4.03it/s]
 57%|█████▋    | 136/240 [00:31<00:35,  2.95it/s]
 57%|█████▊    | 138/240 [00:31<00:23,  4.42it/s]
 58%|█████▊    | 140/240 [00:31<00:16,  5.89it/s]
 59%|█████▉    | 142/240 [00:32<00:14,  6.67it/s]
 60%|█████▉    | 143/240 [00:32<00:21,  4.47it/s]
 60%|██████    | 144/240 [00:32<00:20,  4.74it/s]
 60%|██████    | 145/240 [00:33<00:22,  4.17it/s]
 61%|██████    | 146/240 [00:33<00:24,  3.78it/s]
 62%|██████▏   | 148/240 [00:33<00:20,  4.47it/s]
 62%|██████▎   | 150/240 [00:34<00:14,  6.27it/s]
 63%|██████▎   | 151/240 [00:34<00:18,  4.79it/s]
 63%|██████▎   | 152/240 [00:34<00:20,  4.37it/s]
 64%|██████▍   | 154/240 [00:34<00:13,  6.22it/s]
 65%|██████▌   | 157/240 [00:35<00:11,  7.31it/s]
 66%|██████▋   | 159/240 [00:35<00:10,  7.74it/s]
 67%|██████▋   | 160/240 [00:36<00:19,  4.16it/s]
 68%|██████▊   | 162/240 [00:36<00:16,  4.87it/s]
 68%|██████▊   | 164/240 [00:36<00:12,  6.12it/s]
 70%|██████▉   | 167/240 [00:37<00:12,  5.84it/s]
 70%|███████   | 169/240 [00:37<00:09,  7.19it/s]
 71%|███████▏  | 171/240 [00:37<00:09,  6.95it/s]
 72%|███████▏  | 172/240 [00:38<00:14,  4.73it/s]
 72%|███████▎  | 174/240 [00:38<00:11,  5.61it/s]
 73%|███████▎  | 175/240 [00:38<00:14,  4.57it/s]
 73%|███████▎  | 176/240 [00:38<00:14,  4.32it/s]
 74%|███████▍  | 178/240 [00:39<00:10,  6.08it/s]
 75%|███████▍  | 179/240 [00:39<00:09,  6.63it/s]
 75%|███████▌  | 180/240 [00:39<00:09,  6.15it/s]
 76%|███████▌  | 182/240 [00:39<00:08,  6.58it/s]
 76%|███████▋  | 183/240 [00:39<00:09,  5.99it/s]
 77%|███████▋  | 184/240 [00:40<00:16,  3.38it/s]
 77%|███████▋  | 185/240 [00:40<00:16,  3.26it/s]
 78%|███████▊  | 187/240 [00:41<00:11,  4.78it/s]
 78%|███████▊  | 188/240 [00:41<00:13,  3.85it/s]
 79%|███████▉  | 189/240 [00:42<00:20,  2.45it/s]
 80%|████████  | 192/240 [00:42<00:10,  4.56it/s]
 81%|████████▏ | 195/240 [00:43<00:10,  4.31it/s]
 82%|████████▎ | 198/240 [00:43<00:06,  6.11it/s]
 83%|████████▎ | 200/240 [00:44<00:08,  4.75it/s]
 84%|████████▍ | 201/240 [00:44<00:09,  4.27it/s]
 84%|████████▍ | 202/240 [00:44<00:10,  3.54it/s]
 85%|████████▍ | 203/240 [00:45<00:10,  3.68it/s]
 86%|████████▌ | 206/240 [00:45<00:05,  6.01it/s]
 86%|████████▋ | 207/240 [00:45<00:05,  6.13it/s]
 87%|████████▋ | 209/240 [00:45<00:05,  5.87it/s]
 88%|████████▊ | 211/240 [00:46<00:05,  5.68it/s]
 89%|████████▉ | 214/240 [00:46<00:03,  8.41it/s]
 90%|█████████ | 216/240 [00:46<00:03,  7.40it/s]
 91%|█████████ | 218/240 [00:47<00:03,  6.47it/s]
 91%|█████████▏| 219/240 [00:47<00:03,  5.58it/s]
 92%|█████████▏| 220/240 [00:47<00:03,  5.20it/s]
 92%|█████████▏| 221/240 [00:47<00:04,  4.47it/s]
 92%|█████████▎| 222/240 [00:48<00:04,  3.84it/s]
 94%|█████████▍| 225/240 [00:48<00:02,  6.28it/s]
 94%|█████████▍| 226/240 [00:48<00:02,  5.63it/s]
 95%|█████████▌| 228/240 [00:48<00:01,  6.60it/s]
 95%|█████████▌| 229/240 [00:49<00:01,  6.72it/s]
 97%|█████████▋| 232/240 [00:49<00:01,  5.93it/s]
 98%|█████████▊| 234/240 [00:49<00:00,  6.99it/s]
 98%|█████████▊| 235/240 [00:51<00:01,  2.53it/s]
 98%|█████████▊| 236/240 [00:51<00:01,  2.91it/s]
 99%|█████████▉| 238/240 [00:52<00:00,  2.40it/s]
100%|█████████▉| 239/240 [00:52<00:00,  2.58it/s]
100%|██████████| 240/240 [00:53<00:00,  2.23it/s]
100%|██████████| 240/240 [00:53<00:00,  4.48it/s]
2025-05-21 23:27:02,852 - modnet - INFO - Loss per individual: ind0:44.596   ind1:53.409   ind2:44.697   ind3:42.545   ind4:42.742   ind5:44.946   ind6:44.662   ind7:42.831   ind8:47.185   ind9:43.110   ind10:45.997   ind11:76.088   ind12:48.118   ind13:49.366   ind14:507.792   ind15:50.753   ind16:50.443   ind17:42.531   ind18:75.572   ind19:49.490   ind20:43.114   ind21:46.070   ind22:44.187   ind23:44.496   ind24:43.558   ind25:43.042   ind26:46.435   ind27:45.206   ind28:44.607   ind29:44.573
2025-05-21 23:27:02,869 - modnet - INFO - Generation 23 best loss: 41.4220
2025-05-21 23:27:02,869 - modnet - INFO - Generation 23 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.5, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 540, 'n_layers': 3, 'layer_mults': None}
2025-05-21 23:27:02,870 - modnet - INFO - Early stopping in final phase at generation 23
2025-05-21 23:27:02,870 - modnet - INFO - Starting grid search for neuron multipliers...
2025-05-21 23:27:02,876 - modnet - INFO - Neuron tuning will use a 3-fold CV on a subset of 1012 samples.
2025-05-21 23:27:02,884 - modnet - INFO - Trying 125 multiplier configurations for 3 layers.
2025-05-21 23:27:02,898 - modnet - INFO - Evaluating 375 tasks for neuron multiplier tuning.

Neuron Multiplier Tuning:   0%|          | 0/375 [00:00<?, ?it/s]
Neuron Multiplier Tuning:   0%|          | 1/375 [00:07<49:09,  7.89s/it]
Neuron Multiplier Tuning:   1%|          | 2/375 [00:08<21:33,  3.47s/it]
Neuron Multiplier Tuning:   1%|          | 3/375 [00:09<14:23,  2.32s/it]
Neuron Multiplier Tuning:   1%|          | 4/375 [00:09<08:59,  1.45s/it]
Neuron Multiplier Tuning:   1%|▏         | 5/375 [00:09<06:25,  1.04s/it]
Neuron Multiplier Tuning:   2%|▏         | 6/375 [00:09<04:29,  1.37it/s]
Neuron Multiplier Tuning:   2%|▏         | 7/375 [00:10<04:03,  1.51it/s]
Neuron Multiplier Tuning:   2%|▏         | 8/375 [00:10<03:10,  1.93it/s]
Neuron Multiplier Tuning:   3%|▎         | 10/375 [00:10<01:54,  3.18it/s]
Neuron Multiplier Tuning:   3%|▎         | 12/375 [00:11<01:33,  3.87it/s]
Neuron Multiplier Tuning:   4%|▎         | 14/375 [00:11<01:37,  3.69it/s]
Neuron Multiplier Tuning:   5%|▍         | 17/375 [00:11<01:02,  5.69it/s]
Neuron Multiplier Tuning:   5%|▌         | 19/375 [00:11<00:50,  7.07it/s]
Neuron Multiplier Tuning:   6%|▌         | 21/375 [00:12<00:45,  7.70it/s]
Neuron Multiplier Tuning:   6%|▌         | 23/375 [00:12<00:38,  9.25it/s]
Neuron Multiplier Tuning:   7%|▋         | 25/375 [00:12<00:46,  7.50it/s]
Neuron Multiplier Tuning:   8%|▊         | 29/375 [00:12<00:29, 11.68it/s]
Neuron Multiplier Tuning:   8%|▊         | 31/375 [00:12<00:32, 10.66it/s]
Neuron Multiplier Tuning:   9%|▉         | 33/375 [00:13<00:32, 10.61it/s]
Neuron Multiplier Tuning:   9%|▉         | 35/375 [00:13<00:28, 11.90it/s]
Neuron Multiplier Tuning:  10%|▉         | 37/375 [00:13<00:37,  9.03it/s]
Neuron Multiplier Tuning:  10%|█         | 39/375 [00:13<00:31, 10.58it/s]
Neuron Multiplier Tuning:  11%|█▏        | 43/375 [00:13<00:21, 15.49it/s]
Neuron Multiplier Tuning:  12%|█▏        | 46/375 [00:14<00:25, 12.76it/s]
Neuron Multiplier Tuning:  13%|█▎        | 48/375 [00:14<00:25, 12.92it/s]
Neuron Multiplier Tuning:  13%|█▎        | 50/375 [00:14<00:41,  7.76it/s]
Neuron Multiplier Tuning:  14%|█▍        | 52/375 [00:15<00:40,  7.90it/s]
Neuron Multiplier Tuning:  14%|█▍        | 54/375 [00:15<00:38,  8.33it/s]
Neuron Multiplier Tuning:  15%|█▍        | 56/375 [00:16<01:17,  4.11it/s]
Neuron Multiplier Tuning:  15%|█▌        | 57/375 [00:16<01:21,  3.90it/s]
Neuron Multiplier Tuning:  15%|█▌        | 58/375 [00:16<01:16,  4.16it/s]
Neuron Multiplier Tuning:  16%|█▌        | 59/375 [00:17<01:17,  4.10it/s]
Neuron Multiplier Tuning:  16%|█▌        | 60/375 [00:17<01:07,  4.65it/s]
Neuron Multiplier Tuning:  16%|█▋        | 61/375 [00:17<01:06,  4.70it/s]
Neuron Multiplier Tuning:  17%|█▋        | 62/375 [00:18<02:03,  2.54it/s]
Neuron Multiplier Tuning:  17%|█▋        | 63/375 [00:18<01:41,  3.06it/s]
Neuron Multiplier Tuning:  17%|█▋        | 64/375 [00:18<01:34,  3.31it/s]
Neuron Multiplier Tuning:  17%|█▋        | 65/375 [00:19<01:26,  3.60it/s]
Neuron Multiplier Tuning:  18%|█▊        | 66/375 [00:19<01:40,  3.08it/s]
Neuron Multiplier Tuning:  18%|█▊        | 68/375 [00:20<01:47,  2.85it/s]
Neuron Multiplier Tuning:  18%|█▊        | 69/375 [00:20<01:29,  3.42it/s]
Neuron Multiplier Tuning:  19%|█▊        | 70/375 [00:20<01:18,  3.91it/s]
Neuron Multiplier Tuning:  19%|█▉        | 72/375 [00:20<01:09,  4.39it/s]
Neuron Multiplier Tuning:  19%|█▉        | 73/375 [00:20<01:00,  4.99it/s]
Neuron Multiplier Tuning:  20%|█▉        | 74/375 [00:21<01:07,  4.49it/s]
Neuron Multiplier Tuning:  20%|██        | 75/375 [00:21<01:01,  4.84it/s]
Neuron Multiplier Tuning:  20%|██        | 76/375 [00:22<01:42,  2.92it/s]
Neuron Multiplier Tuning:  21%|██        | 78/375 [00:22<01:13,  4.05it/s]
Neuron Multiplier Tuning:  21%|██▏       | 80/375 [00:22<01:04,  4.57it/s]
Neuron Multiplier Tuning:  22%|██▏       | 81/375 [00:22<01:02,  4.67it/s]
Neuron Multiplier Tuning:  22%|██▏       | 83/375 [00:23<00:45,  6.45it/s]
Neuron Multiplier Tuning:  23%|██▎       | 85/375 [00:23<00:37,  7.79it/s]
Neuron Multiplier Tuning:  23%|██▎       | 87/375 [00:23<00:39,  7.22it/s]
Neuron Multiplier Tuning:  23%|██▎       | 88/375 [00:23<00:40,  7.16it/s]
Neuron Multiplier Tuning:  24%|██▎       | 89/375 [00:23<00:43,  6.58it/s]
Neuron Multiplier Tuning:  24%|██▍       | 91/375 [00:24<00:40,  7.04it/s]
Neuron Multiplier Tuning:  25%|██▍       | 93/375 [00:24<00:36,  7.75it/s]
Neuron Multiplier Tuning:  25%|██▌       | 94/375 [00:24<00:34,  8.11it/s]
Neuron Multiplier Tuning:  25%|██▌       | 95/375 [00:24<00:35,  7.98it/s]
Neuron Multiplier Tuning:  26%|██▌       | 98/375 [00:24<00:27, 10.19it/s]
Neuron Multiplier Tuning:  27%|██▋       | 100/375 [00:24<00:24, 11.04it/s]
Neuron Multiplier Tuning:  27%|██▋       | 102/375 [00:25<00:40,  6.71it/s]
Neuron Multiplier Tuning:  27%|██▋       | 103/375 [00:25<00:51,  5.28it/s]
Neuron Multiplier Tuning:  28%|██▊       | 104/375 [00:25<00:48,  5.60it/s]
Neuron Multiplier Tuning:  28%|██▊       | 105/375 [00:26<00:47,  5.67it/s]
Neuron Multiplier Tuning:  28%|██▊       | 106/375 [00:26<00:42,  6.32it/s]
Neuron Multiplier Tuning:  29%|██▉       | 108/375 [00:26<00:40,  6.59it/s]
Neuron Multiplier Tuning:  29%|██▉       | 110/375 [00:27<00:52,  5.09it/s]
Neuron Multiplier Tuning:  30%|███       | 113/375 [00:27<00:40,  6.46it/s]
Neuron Multiplier Tuning:  31%|███       | 116/375 [00:27<00:31,  8.30it/s]
Neuron Multiplier Tuning:  31%|███▏      | 118/375 [00:27<00:32,  7.95it/s]
Neuron Multiplier Tuning:  32%|███▏      | 119/375 [00:28<00:35,  7.20it/s]
Neuron Multiplier Tuning:  32%|███▏      | 120/375 [00:28<00:42,  5.99it/s]
Neuron Multiplier Tuning:  33%|███▎      | 123/375 [00:28<00:43,  5.83it/s]
Neuron Multiplier Tuning:  34%|███▎      | 126/375 [00:29<00:59,  4.21it/s]
Neuron Multiplier Tuning:  34%|███▍      | 127/375 [00:30<00:58,  4.23it/s]
Neuron Multiplier Tuning:  34%|███▍      | 129/375 [00:30<00:52,  4.67it/s]
Neuron Multiplier Tuning:  35%|███▍      | 131/375 [00:30<00:41,  5.91it/s]
Neuron Multiplier Tuning:  35%|███▌      | 133/375 [00:31<00:56,  4.28it/s]
Neuron Multiplier Tuning:  36%|███▌      | 134/375 [00:31<00:53,  4.49it/s]
Neuron Multiplier Tuning:  36%|███▌      | 135/375 [00:31<01:03,  3.78it/s]
Neuron Multiplier Tuning:  36%|███▋      | 136/375 [00:32<00:57,  4.17it/s]
Neuron Multiplier Tuning:  37%|███▋      | 137/375 [00:32<00:49,  4.80it/s]
Neuron Multiplier Tuning:  37%|███▋      | 138/375 [00:32<00:45,  5.24it/s]
Neuron Multiplier Tuning:  37%|███▋      | 139/375 [00:32<00:56,  4.20it/s]
Neuron Multiplier Tuning:  37%|███▋      | 140/375 [00:33<00:59,  3.95it/s]
Neuron Multiplier Tuning:  38%|███▊      | 142/375 [00:33<00:41,  5.61it/s]
Neuron Multiplier Tuning:  38%|███▊      | 144/375 [00:33<00:48,  4.73it/s]
Neuron Multiplier Tuning:  39%|███▊      | 145/375 [00:33<00:44,  5.19it/s]
Neuron Multiplier Tuning:  39%|███▉      | 147/375 [00:34<00:34,  6.60it/s]
Neuron Multiplier Tuning:  39%|███▉      | 148/375 [00:34<00:35,  6.42it/s]
Neuron Multiplier Tuning:  40%|████      | 150/375 [00:34<00:34,  6.52it/s]
Neuron Multiplier Tuning:  40%|████      | 151/375 [00:34<00:33,  6.73it/s]
Neuron Multiplier Tuning:  41%|████      | 152/375 [00:34<00:37,  5.88it/s]
Neuron Multiplier Tuning:  41%|████      | 154/375 [00:35<00:30,  7.23it/s]
Neuron Multiplier Tuning:  41%|████▏     | 155/375 [00:35<00:31,  7.03it/s]
Neuron Multiplier Tuning:  42%|████▏     | 156/375 [00:35<00:32,  6.73it/s]
Neuron Multiplier Tuning:  42%|████▏     | 157/375 [00:35<00:35,  6.07it/s]
Neuron Multiplier Tuning:  42%|████▏     | 158/375 [00:35<00:34,  6.29it/s]
Neuron Multiplier Tuning:  43%|████▎     | 161/375 [00:35<00:23,  9.19it/s]
Neuron Multiplier Tuning:  43%|████▎     | 163/375 [00:36<00:26,  8.01it/s]
Neuron Multiplier Tuning:  44%|████▎     | 164/375 [00:36<00:33,  6.25it/s]
Neuron Multiplier Tuning:  44%|████▍     | 165/375 [00:36<00:36,  5.78it/s]
Neuron Multiplier Tuning:  44%|████▍     | 166/375 [00:36<00:34,  6.09it/s]
Neuron Multiplier Tuning:  45%|████▍     | 167/375 [00:37<00:42,  4.89it/s]
Neuron Multiplier Tuning:  45%|████▌     | 169/375 [00:37<00:33,  6.16it/s]
Neuron Multiplier Tuning:  46%|████▌     | 172/375 [00:38<00:38,  5.28it/s]
Neuron Multiplier Tuning:  46%|████▌     | 173/375 [00:38<00:43,  4.63it/s]
Neuron Multiplier Tuning:  47%|████▋     | 175/375 [00:38<00:36,  5.55it/s]
Neuron Multiplier Tuning:  47%|████▋     | 176/375 [00:38<00:33,  5.87it/s]
Neuron Multiplier Tuning:  47%|████▋     | 177/375 [00:39<00:40,  4.93it/s]
Neuron Multiplier Tuning:  47%|████▋     | 178/375 [00:39<00:43,  4.51it/s]
Neuron Multiplier Tuning:  48%|████▊     | 179/375 [00:39<00:48,  4.01it/s]
Neuron Multiplier Tuning:  48%|████▊     | 181/375 [00:40<00:54,  3.55it/s]
Neuron Multiplier Tuning:  49%|████▊     | 182/375 [00:40<00:46,  4.18it/s]
Neuron Multiplier Tuning:  49%|████▉     | 185/375 [00:40<00:28,  6.62it/s]
Neuron Multiplier Tuning:  50%|████▉     | 186/375 [00:40<00:28,  6.57it/s]
Neuron Multiplier Tuning:  50%|█████     | 188/375 [00:41<00:28,  6.64it/s]
Neuron Multiplier Tuning:  50%|█████     | 189/375 [00:41<00:35,  5.24it/s]
Neuron Multiplier Tuning:  51%|█████     | 190/375 [00:41<00:36,  5.06it/s]
Neuron Multiplier Tuning:  51%|█████     | 192/375 [00:42<00:37,  4.89it/s]
Neuron Multiplier Tuning:  52%|█████▏    | 195/375 [00:42<00:29,  6.19it/s]
Neuron Multiplier Tuning:  52%|█████▏    | 196/375 [00:42<00:33,  5.31it/s]
Neuron Multiplier Tuning:  53%|█████▎    | 197/375 [00:42<00:32,  5.55it/s]
Neuron Multiplier Tuning:  53%|█████▎    | 199/375 [00:43<00:26,  6.59it/s]
Neuron Multiplier Tuning:  53%|█████▎    | 200/375 [00:43<00:35,  4.90it/s]
Neuron Multiplier Tuning:  54%|█████▍    | 202/375 [00:44<00:43,  4.00it/s]
Neuron Multiplier Tuning:  54%|█████▍    | 203/375 [00:44<00:40,  4.23it/s]
Neuron Multiplier Tuning:  55%|█████▍    | 205/375 [00:44<00:31,  5.47it/s]
Neuron Multiplier Tuning:  55%|█████▌    | 207/375 [00:44<00:29,  5.73it/s]
Neuron Multiplier Tuning:  55%|█████▌    | 208/375 [00:45<00:31,  5.30it/s]
Neuron Multiplier Tuning:  56%|█████▌    | 210/375 [00:45<00:23,  6.89it/s]
Neuron Multiplier Tuning:  56%|█████▋    | 211/375 [00:45<00:23,  6.87it/s]
Neuron Multiplier Tuning:  57%|█████▋    | 212/375 [00:45<00:25,  6.32it/s]
Neuron Multiplier Tuning:  57%|█████▋    | 213/375 [00:46<00:43,  3.73it/s]
Neuron Multiplier Tuning:  57%|█████▋    | 214/375 [00:47<01:28,  1.82it/s]
Neuron Multiplier Tuning:  60%|██████    | 225/375 [00:47<00:18,  8.02it/s]
Neuron Multiplier Tuning:  61%|██████    | 227/375 [00:47<00:17,  8.45it/s]
Neuron Multiplier Tuning:  61%|██████    | 229/375 [00:48<00:27,  5.24it/s]
Neuron Multiplier Tuning:  62%|██████▏   | 231/375 [00:49<00:28,  5.14it/s]
Neuron Multiplier Tuning:  62%|██████▏   | 233/375 [00:49<00:25,  5.55it/s]
Neuron Multiplier Tuning:  62%|██████▏   | 234/375 [00:50<00:31,  4.42it/s]
Neuron Multiplier Tuning:  63%|██████▎   | 235/375 [00:50<00:30,  4.63it/s]
Neuron Multiplier Tuning:  63%|██████▎   | 237/375 [00:50<00:26,  5.27it/s]
Neuron Multiplier Tuning:  64%|██████▎   | 239/375 [00:50<00:21,  6.24it/s]
Neuron Multiplier Tuning:  64%|██████▍   | 241/375 [00:50<00:17,  7.59it/s]
Neuron Multiplier Tuning:  65%|██████▍   | 243/375 [00:50<00:14,  9.06it/s]
Neuron Multiplier Tuning:  65%|██████▌   | 245/375 [00:51<00:15,  8.40it/s]
Neuron Multiplier Tuning:  66%|██████▌   | 247/375 [00:51<00:19,  6.47it/s]
Neuron Multiplier Tuning:  66%|██████▌   | 248/375 [00:52<00:30,  4.14it/s]
Neuron Multiplier Tuning:  66%|██████▋   | 249/375 [00:52<00:30,  4.07it/s]
Neuron Multiplier Tuning:  67%|██████▋   | 250/375 [00:52<00:31,  3.94it/s]
Neuron Multiplier Tuning:  67%|██████▋   | 251/375 [00:53<00:32,  3.83it/s]
Neuron Multiplier Tuning:  67%|██████▋   | 252/375 [00:53<00:30,  4.09it/s]
Neuron Multiplier Tuning:  67%|██████▋   | 253/375 [00:53<00:27,  4.42it/s]
Neuron Multiplier Tuning:  68%|██████▊   | 254/375 [00:53<00:24,  4.96it/s]
Neuron Multiplier Tuning:  68%|██████▊   | 256/375 [00:53<00:18,  6.27it/s]
Neuron Multiplier Tuning:  69%|██████▊   | 257/375 [00:54<00:17,  6.59it/s]
Neuron Multiplier Tuning:  69%|██████▉   | 260/375 [00:54<00:12,  9.03it/s]
Neuron Multiplier Tuning:  70%|██████▉   | 262/375 [00:54<00:11, 10.11it/s]
Neuron Multiplier Tuning:  70%|███████   | 264/375 [00:54<00:12,  9.13it/s]
Neuron Multiplier Tuning:  71%|███████   | 265/375 [00:54<00:12,  9.07it/s]
Neuron Multiplier Tuning:  71%|███████   | 266/375 [00:55<00:19,  5.70it/s]
Neuron Multiplier Tuning:  71%|███████   | 267/375 [00:55<00:17,  6.00it/s]
Neuron Multiplier Tuning:  71%|███████▏  | 268/375 [00:55<00:26,  4.11it/s]
Neuron Multiplier Tuning:  72%|███████▏  | 270/375 [00:56<00:21,  4.96it/s]
Neuron Multiplier Tuning:  72%|███████▏  | 271/375 [00:56<00:21,  4.86it/s]
Neuron Multiplier Tuning:  73%|███████▎  | 273/375 [00:56<00:15,  6.50it/s]
Neuron Multiplier Tuning:  73%|███████▎  | 275/375 [00:56<00:17,  5.71it/s]
Neuron Multiplier Tuning:  74%|███████▍  | 277/375 [00:57<00:13,  7.54it/s]
Neuron Multiplier Tuning:  74%|███████▍  | 279/375 [00:57<00:15,  6.26it/s]
Neuron Multiplier Tuning:  75%|███████▍  | 280/375 [00:58<00:25,  3.76it/s]
Neuron Multiplier Tuning:  75%|███████▍  | 281/375 [00:58<00:23,  3.95it/s]
Neuron Multiplier Tuning:  75%|███████▌  | 282/375 [00:58<00:23,  3.93it/s]
Neuron Multiplier Tuning:  75%|███████▌  | 283/375 [00:58<00:22,  4.02it/s]
Neuron Multiplier Tuning:  76%|███████▌  | 284/375 [00:59<00:24,  3.72it/s]
Neuron Multiplier Tuning:  76%|███████▋  | 286/375 [00:59<00:17,  5.18it/s]
Neuron Multiplier Tuning:  77%|███████▋  | 287/375 [00:59<00:18,  4.70it/s]
Neuron Multiplier Tuning:  77%|███████▋  | 288/375 [01:00<00:22,  3.94it/s]
Neuron Multiplier Tuning:  77%|███████▋  | 289/375 [01:00<00:36,  2.35it/s]
Neuron Multiplier Tuning:  77%|███████▋  | 290/375 [01:01<00:29,  2.93it/s]
Neuron Multiplier Tuning:  78%|███████▊  | 293/375 [01:01<00:18,  4.52it/s]
Neuron Multiplier Tuning:  78%|███████▊  | 294/375 [01:01<00:16,  5.03it/s]
Neuron Multiplier Tuning:  79%|███████▉  | 296/375 [01:01<00:16,  4.81it/s]
Neuron Multiplier Tuning:  79%|███████▉  | 297/375 [01:02<00:14,  5.31it/s]
Neuron Multiplier Tuning:  80%|███████▉  | 299/375 [01:02<00:11,  6.58it/s]
Neuron Multiplier Tuning:  80%|████████  | 300/375 [01:02<00:14,  5.09it/s]
Neuron Multiplier Tuning:  81%|████████  | 302/375 [01:02<00:11,  6.25it/s]
Neuron Multiplier Tuning:  81%|████████  | 303/375 [01:03<00:17,  4.11it/s]
Neuron Multiplier Tuning:  81%|████████▏ | 305/375 [01:03<00:12,  5.53it/s]
Neuron Multiplier Tuning:  82%|████████▏ | 306/375 [01:03<00:11,  5.82it/s]
Neuron Multiplier Tuning:  82%|████████▏ | 308/375 [01:03<00:11,  6.06it/s]
Neuron Multiplier Tuning:  83%|████████▎ | 310/375 [01:04<00:09,  7.08it/s]
Neuron Multiplier Tuning:  83%|████████▎ | 311/375 [01:04<00:09,  6.63it/s]
Neuron Multiplier Tuning:  83%|████████▎ | 313/375 [01:04<00:08,  7.50it/s]
Neuron Multiplier Tuning:  84%|████████▍ | 315/375 [01:05<00:12,  4.77it/s]
Neuron Multiplier Tuning:  85%|████████▍ | 318/375 [01:05<00:07,  7.18it/s]
Neuron Multiplier Tuning:  85%|████████▌ | 320/375 [01:06<00:10,  5.02it/s]
Neuron Multiplier Tuning:  86%|████████▌ | 322/375 [01:06<00:10,  5.04it/s]
Neuron Multiplier Tuning:  86%|████████▌ | 323/375 [01:06<00:10,  4.86it/s]
Neuron Multiplier Tuning:  86%|████████▋ | 324/375 [01:06<00:09,  5.35it/s]
Neuron Multiplier Tuning:  87%|████████▋ | 325/375 [01:07<00:09,  5.48it/s]
Neuron Multiplier Tuning:  87%|████████▋ | 327/375 [01:07<00:07,  6.84it/s]
Neuron Multiplier Tuning:  88%|████████▊ | 330/375 [01:07<00:04,  9.25it/s]
Neuron Multiplier Tuning:  89%|████████▊ | 332/375 [01:07<00:06,  6.83it/s]
Neuron Multiplier Tuning:  89%|████████▉ | 333/375 [01:08<00:06,  6.75it/s]
Neuron Multiplier Tuning:  89%|████████▉ | 335/375 [01:08<00:04,  8.30it/s]
Neuron Multiplier Tuning:  90%|████████▉ | 337/375 [01:08<00:05,  7.24it/s]
Neuron Multiplier Tuning:  90%|█████████ | 339/375 [01:08<00:04,  8.19it/s]
Neuron Multiplier Tuning:  91%|█████████ | 340/375 [01:08<00:04,  7.86it/s]
Neuron Multiplier Tuning:  92%|█████████▏| 344/375 [01:09<00:04,  7.23it/s]
Neuron Multiplier Tuning:  93%|█████████▎| 347/375 [01:09<00:02,  9.37it/s]
Neuron Multiplier Tuning:  93%|█████████▎| 349/375 [01:09<00:02,  8.79it/s]
Neuron Multiplier Tuning:  94%|█████████▎| 351/375 [01:10<00:04,  5.99it/s]
Neuron Multiplier Tuning:  94%|█████████▍| 352/375 [01:10<00:03,  6.12it/s]
Neuron Multiplier Tuning:  94%|█████████▍| 353/375 [01:10<00:03,  6.20it/s]
Neuron Multiplier Tuning:  95%|█████████▍| 355/375 [01:10<00:02,  7.10it/s]
Neuron Multiplier Tuning:  95%|█████████▍| 356/375 [01:11<00:05,  3.69it/s]
Neuron Multiplier Tuning:  96%|█████████▌| 359/375 [01:11<00:02,  5.96it/s]
Neuron Multiplier Tuning:  96%|█████████▋| 361/375 [01:12<00:02,  6.92it/s]
Neuron Multiplier Tuning:  97%|█████████▋| 363/375 [01:12<00:01,  7.07it/s]
Neuron Multiplier Tuning:  97%|█████████▋| 364/375 [01:12<00:01,  6.12it/s]
Neuron Multiplier Tuning:  97%|█████████▋| 365/375 [01:12<00:01,  6.13it/s]
Neuron Multiplier Tuning:  98%|█████████▊| 368/375 [01:12<00:00,  8.97it/s]
Neuron Multiplier Tuning:  99%|█████████▊| 370/375 [01:13<00:01,  4.49it/s]
Neuron Multiplier Tuning:  99%|█████████▉| 372/375 [01:14<00:00,  5.02it/s]
Neuron Multiplier Tuning:  99%|█████████▉| 373/375 [01:14<00:00,  5.45it/s]
Neuron Multiplier Tuning: 100%|█████████▉| 374/375 [01:15<00:00,  2.83it/s]
Neuron Multiplier Tuning: 100%|██████████| 375/375 [01:15<00:00,  2.85it/s]
Neuron Multiplier Tuning: 100%|██████████| 375/375 [01:15<00:00,  4.96it/s]
2025-05-21 23:28:18,595 - modnet - INFO - Neuron tuning: Best multipliers [0.8, 1.2, 1.1] with avg loss 42.7209
2025-05-21 23:28:23,023 - modnet - INFO - Final best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 96, 'fraction1': 0.5, 'fraction2': 0.75, 'fraction3': 1, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.0031622776601683794, 'batch_size': 16, 'n_feat': 540, 'n_layers': 3, 'layer_mults': [0.8, 1.2, 1.1]}
2025-05-21 23:28:23,024 - modnet - INFO - Final tuning loss: 42.7209
2025-05-21 23:28:25,001 - modnet - INFO - Model successfully saved as results/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_best_model_fold_1.pkl!
Saved best model for fold 1 to results/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_best_model_fold_1.pkl
2025-05-21 23:28:25.043350: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:28:25.055923: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
[Fold 1] MAE = 43.734 , MedianAE = 24.968 , MAPE = 7.318, √MSE = 82.499 
[Fold 1] MaxAE = 657.131 , slope = 0.95, R = 0.99
Fold 1 metrics saved.
Saved fold 1 complete results to results/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_fold_1_results.pkl
Training fold 2 ...
Processing fold 2 ...
2025-05-21 23:28:28,868 - modnet - INFO - Targets:
2025-05-21 23:28:28,868 - modnet - INFO - 1) target: regression
2025-05-21 23:28:56.280267: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.280273: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.280330: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.280341: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.285398: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.289593: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.294718: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.295597: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.296463: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.297892: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.303762: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.303802: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.308115: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.309048: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.309874: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.310133: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.310270: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.310623: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.312243: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.312373: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.312414: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.312495: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.312797: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.313260: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.313289: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.313573: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.313755: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.313852: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.313879: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.313940: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.314002: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.370449: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.377703: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.411672: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.493059: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.544800: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.602201: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.674393: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.714046: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.759841: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.822567: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.873922: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56,876 - modnet - INFO - Multiprocessing on 64 cores. Total of 128 cores available.
2025-05-21 23:28:56,876 - modnet - INFO - === Generation 0 ===
Population initialized with 60 individuals.
2025-05-21 23:28:56,880 - modnet - INFO - Generating 3 splits of 80/20  for Generation 0
2025-05-21 23:28:56.949031: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:56.997136: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:57.112177: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:57.123442: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:57.169822: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:57.236817: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:57.353388: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:57.446903: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:57.560123: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0

  0%|          | 0/180 [00:00<?, ?it/s]2025-05-21 23:28:57.604571: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:57.637753: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:57.667240: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:57.738211: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:57.822497: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:57.828213: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:57.914064: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:58.080995: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:58.091894: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:58.109492: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:58.210937: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:58.410095: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:28:58.619705: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-21 23:29:00.834824: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:00.835349: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:00.835381: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:00.835415: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:00.835691: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:00.876461: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:00.876969: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:00.877001: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:00.877036: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:00.877323: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:00.947815: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:00.948381: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:00.948413: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:00.948453: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:00.948800: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:00.992492: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:00.993013: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:00.993044: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:00.993081: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:00.993383: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.042757: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.043243: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.043273: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.043308: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.043698: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.090879: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.094200: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.094644: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.094679: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.094714: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.094984: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.103870: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.133277: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.146868: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.154333: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.154815: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.154852: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.154888: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.155166: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.196782: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.197275: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.197302: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.197338: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.197602: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.226160: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.239927: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.249487: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.249931: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.249959: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.249993: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.250236: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.256152: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.269871: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.291791: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.295706: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.296158: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.296187: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.296219: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.296599: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.304864: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.339964: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.343939: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.344413: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.344444: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.344478: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.344771: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.353849: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.395999: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.396531: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.396559: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.396780: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.397543: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.407473: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.420863: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.435787: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.436273: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.436301: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.436334: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.436596: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.456975: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.469876: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.482197: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.482684: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.482721: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.482764: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.483030: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.503485: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.516863: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.527843: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.528309: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.528552: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.528587: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.528854: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.541721: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.554864: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.571430: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.571931: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.571960: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.571995: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.572261: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.599887: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.601548: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.617507: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.618007: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.618038: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.618073: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.618312: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.644466: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.657863: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.663474: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.664197: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.664233: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.664268: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.664527: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.692110: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.704874: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.708577: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.709065: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.709095: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.709130: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.709379: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.737910: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.750868: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.755914: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.756416: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.756449: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.756487: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.756741: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.780251: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.782298: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.801648: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.802206: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.802241: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.802277: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.802585: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.843185: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.843735: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.843769: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.843805: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.844043: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.850707: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.863867: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.890563: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.891135: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.891165: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.891202: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.891484: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.891820: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.893493: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.933671: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.934945: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.935440: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.935471: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.935504: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.935767: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.946865: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.961289: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:01.963096: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:01.978185: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:01.978702: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:01.978741: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:01.978777: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:01.979169: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.010318: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.012154: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.030083: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.030588: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.030803: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.030842: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.031083: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.054772: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.057307: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.084935: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.085490: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.085522: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.085560: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.085831: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.088716: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.090490: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.132810: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.133339: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.133368: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.133401: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.133643: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.183341: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.183668: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.183870: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.184072: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.184112: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.184371: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.185961: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.191384: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.193160: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.230340: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.230869: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.230898: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.230936: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.231206: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.232473: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.234316: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.274421: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.277338: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.277889: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.277920: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.277957: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.278221: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.287862: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.325706: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.326271: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.326300: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.326337: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.326606: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.366976: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.376455: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.378170: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.379013: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.379623: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.379658: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.379698: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.380018: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.380882: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.414676: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.415256: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.415288: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.415325: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.415812: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.419776: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.421476: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.461864: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.462420: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.462454: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.462488: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.462765: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.469725: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.482862: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.509859: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.511859: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.518303: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.518850: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.518880: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.518920: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.519173: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.563949: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.576865: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.587073: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.587724: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.587970: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.588026: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.588342: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.619743: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.632871: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.637610: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.638162: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.638196: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.638230: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.638477: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.644490: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.657861: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.693800: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.694448: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.694483: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.694527: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.694877: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.694982: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.708865: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.746620: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.748555: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.750771: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.751430: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.751468: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.751510: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.751846: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.835815: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.837019: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.837063: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.837109: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.837574: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.854282: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.854841: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.854874: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.854912: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.855158: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.873485: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.879219: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.883108: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.886871: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.927069: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.927713: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.927764: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.927806: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.928117: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.958370: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:02.972873: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:02.994949: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:02.995617: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:02.995656: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:02.995702: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:02.996054: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.048275: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.050261: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.053741: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.054337: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.054368: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.054623: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.054896: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.107740: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.110227: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.110830: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.110868: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.110905: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.111176: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.120865: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.163318: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.163935: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.163974: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.164012: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.164296: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.190703: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.205872: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.216587: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.217211: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.217468: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.217507: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.217823: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.236973: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.239453: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.271795: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.273703: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.279237: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.279878: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.279914: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.279951: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.280237: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.339814: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.339982: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.340627: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.340664: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.340705: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.341029: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.353897: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.370441: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.383856: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.399019: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.399961: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.399998: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.400038: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.400365: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.413014: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.415074: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.459169: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.459871: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.459927: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.459973: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.460446: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.486245: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.500887: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.503052: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.503659: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.503690: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.503740: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.504002: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.514366: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.516415: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.565030: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.565649: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.565686: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.565727: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.566000: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.605579: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.610291: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.610924: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.610955: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.610992: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.611244: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.619854: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.656255: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.658203: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.668536: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.669217: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.669253: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.669579: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.670175: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.718939: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.719593: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.719630: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.719667: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.719956: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.729927: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.732009: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.758727: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.760567: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.777513: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.778139: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.778172: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.778210: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.778498: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.820679: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.821320: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.821586: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.821627: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.821709: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.821906: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.823518: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.865917: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.867613: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.874652: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.875286: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.875321: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.875358: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.875785: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.930391: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.931022: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.931053: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.931094: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.931356: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.934562: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.936526: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.973606: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:03.975715: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:03.982654: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:03.983819: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:03.983855: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:03.983895: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:03.984278: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:04.033921: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:04.035987: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:04.057460: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:04.058174: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:04.058216: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:04.058258: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:04.058542: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:04.097142: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:04.099217: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:04.119224: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:04.119929: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 23:29:04.119966: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 23:29:04.120005: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns266.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 23:29:04.120327: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 23:29:04.157532: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:04.161449: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:04.234147: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:04.236394: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:04.296326: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:04.298567: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:04.409823: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:04.412258: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz
2025-05-21 23:29:04.426543: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-21 23:29:04.428703: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445315000 Hz

  1%|          | 1/180 [00:08<25:25,  8.52s/it]
  1%|          | 2/180 [00:08<11:12,  3.78s/it]
  2%|▏         | 3/180 [00:09<07:11,  2.44s/it]
  3%|▎         | 5/180 [00:10<03:21,  1.15s/it]
  3%|▎         | 6/180 [00:10<02:27,  1.18it/s]
  4%|▍         | 8/180 [00:10<01:25,  2.01it/s]
  5%|▌         | 9/180 [00:10<01:08,  2.49it/s]
  6%|▌         | 10/180 [00:10<00:56,  3.02it/s]
  7%|▋         | 12/180 [00:10<00:37,  4.51it/s]
  8%|▊         | 14/180 [00:10<00:28,  5.89it/s]
  9%|▉         | 16/180 [00:11<00:21,  7.70it/s]
 10%|█         | 18/180 [00:11<00:22,  7.24it/s]
 12%|█▏        | 21/180 [00:11<00:17,  9.18it/s]
 13%|█▎        | 23/180 [00:11<00:18,  8.37it/s]
 14%|█▍        | 25/180 [00:11<00:15,  9.71it/s]
 15%|█▌        | 27/180 [00:12<00:15, 10.06it/s]
 16%|█▌        | 29/180 [00:12<00:14, 10.49it/s]
 17%|█▋        | 31/180 [00:12<00:12, 12.10it/s]
 18%|█▊        | 33/180 [00:12<00:12, 11.97it/s]
 19%|█▉        | 35/180 [00:13<00:23,  6.25it/s]
 21%|██        | 37/180 [00:13<00:21,  6.54it/s]
 21%|██        | 38/180 [00:13<00:21,  6.62it/s]
 22%|██▏       | 39/180 [00:13<00:21,  6.68it/s]
 23%|██▎       | 41/180 [00:14<00:24,  5.73it/s]
 24%|██▍       | 44/180 [00:14<00:16,  8.18it/s]
 26%|██▌       | 47/180 [00:14<00:14,  9.29it/s]
 27%|██▋       | 49/180 [00:15<00:15,  8.28it/s]
 28%|██▊       | 50/180 [00:15<00:15,  8.16it/s]
 29%|██▉       | 52/180 [00:15<00:15,  8.51it/s]
 29%|██▉       | 53/180 [00:15<00:14,  8.68it/s]
 30%|███       | 54/180 [00:15<00:14,  8.66it/s]
 31%|███       | 55/180 [00:15<00:18,  6.94it/s]
 31%|███       | 56/180 [00:16<00:24,  5.03it/s]
 33%|███▎      | 59/180 [00:16<00:16,  7.53it/s]
 34%|███▍      | 61/180 [00:16<00:13,  8.76it/s]
 34%|███▍      | 62/180 [00:16<00:15,  7.80it/s]
 35%|███▌      | 63/180 [00:17<00:18,  6.34it/s]
 36%|███▌      | 64/180 [00:17<00:24,  4.80it/s]
 37%|███▋      | 66/180 [00:17<00:18,  6.06it/s]
 37%|███▋      | 67/180 [00:17<00:17,  6.32it/s]
 38%|███▊      | 68/180 [00:17<00:20,  5.44it/s]
 39%|███▉      | 71/180 [00:18<00:12,  8.79it/s]
 41%|████      | 73/180 [00:18<00:16,  6.68it/s]
 42%|████▏     | 75/180 [00:18<00:15,  6.70it/s]
 42%|████▏     | 76/180 [00:19<00:15,  6.57it/s]
 43%|████▎     | 77/180 [00:19<00:17,  5.95it/s]
 44%|████▍     | 79/180 [00:19<00:17,  5.88it/s]
 44%|████▍     | 80/180 [00:19<00:15,  6.35it/s]
 45%|████▌     | 81/180 [00:19<00:17,  5.52it/s]
 46%|████▌     | 82/180 [00:20<00:16,  5.83it/s]
 47%|████▋     | 84/180 [00:20<00:16,  5.93it/s]
 47%|████▋     | 85/180 [00:20<00:16,  5.84it/s]
 48%|████▊     | 87/180 [00:20<00:12,  7.28it/s]
 49%|████▉     | 88/180 [00:21<00:15,  5.99it/s]
 49%|████▉     | 89/180 [00:21<00:24,  3.72it/s]
 50%|█████     | 90/180 [00:22<00:33,  2.70it/s]
 51%|█████     | 91/180 [00:22<00:35,  2.49it/s]
 51%|█████     | 92/180 [00:23<00:34,  2.56it/s]
 52%|█████▏    | 94/180 [00:23<00:20,  4.13it/s]
 53%|█████▎    | 96/180 [00:23<00:16,  4.99it/s]
 54%|█████▍    | 97/180 [00:24<00:27,  3.00it/s]
 54%|█████▍    | 98/180 [00:24<00:23,  3.51it/s]
 55%|█████▌    | 99/180 [00:24<00:21,  3.75it/s]
 56%|█████▌    | 100/180 [00:24<00:19,  4.08it/s]
 57%|█████▋    | 102/180 [00:25<00:13,  5.66it/s]
 57%|█████▋    | 103/180 [00:25<00:13,  5.82it/s]
 58%|█████▊    | 104/180 [00:25<00:20,  3.67it/s]
 59%|█████▉    | 106/180 [00:25<00:14,  5.22it/s]
 60%|██████    | 108/180 [00:26<00:10,  7.13it/s]
 61%|██████    | 110/180 [00:26<00:09,  7.44it/s]
 62%|██████▏   | 112/180 [00:26<00:09,  7.43it/s]
 63%|██████▎   | 113/180 [00:26<00:09,  7.19it/s]
 63%|██████▎   | 114/180 [00:26<00:10,  6.18it/s]
 64%|██████▍   | 116/180 [00:27<00:10,  6.06it/s]
 65%|██████▌   | 117/180 [00:28<00:19,  3.19it/s]
 66%|██████▌   | 119/180 [00:28<00:21,  2.88it/s]
 67%|██████▋   | 121/180 [00:29<00:16,  3.64it/s]
 68%|██████▊   | 122/180 [00:29<00:17,  3.29it/s]
 68%|██████▊   | 123/180 [00:29<00:16,  3.47it/s]
 69%|██████▉   | 124/180 [00:30<00:14,  3.85it/s]
 69%|██████▉   | 125/180 [00:30<00:22,  2.44it/s]
 70%|███████   | 126/180 [00:31<00:19,  2.73it/s]
 71%|███████   | 128/180 [00:31<00:13,  3.81it/s]
 73%|███████▎  | 131/180 [00:31<00:07,  6.42it/s]
 74%|███████▍  | 133/180 [00:34<00:24,  1.91it/s]
 77%|███████▋  | 139/180 [00:34<00:09,  4.28it/s]
 81%|████████  | 145/180 [00:34<00:04,  7.35it/s]
 84%|████████▍ | 151/180 [00:34<00:02, 11.15it/s]
 87%|████████▋ | 156/180 [00:34<00:01, 14.76it/s]
 89%|████████▉ | 161/180 [00:35<00:01,  9.67it/s]
 92%|█████████▏| 165/180 [00:37<00:02,  5.18it/s]
 93%|█████████▎| 168/180 [00:37<00:02,  5.81it/s]
 94%|█████████▍| 170/180 [00:38<00:01,  5.24it/s]
 96%|█████████▌| 172/180 [00:38<00:01,  4.54it/s]
 97%|█████████▋| 175/180 [00:39<00:01,  4.41it/s]
 98%|█████████▊| 176/180 [00:39<00:00,  4.55it/s]
 98%|█████████▊| 177/180 [00:40<00:00,  3.02it/s]
 99%|█████████▉| 178/180 [00:42<00:01,  1.99it/s]
 99%|█████████▉| 179/180 [00:47<00:01,  1.43s/it]
100%|██████████| 180/180 [00:50<00:00,  1.80s/it]
100%|██████████| 180/180 [00:50<00:00,  3.59it/s]
2025-05-21 23:29:47,804 - modnet - INFO - Loss per individual: ind0:58.020   ind1:67.190   ind2:63.469   ind3:83.399   ind4:76.812   ind5:93.076   ind6:63.504   ind7:120.883   ind8:146.396   ind9:58.499   ind10:83.812   ind11:49.853   ind12:56.755   ind13:66.599   ind14:61.310   ind15:59.541   ind16:65.654   ind17:59.380   ind18:108.301   ind19:335.023   ind20:113.777   ind21:77.174   ind22:85.899   ind23:105.671   ind24:66.373   ind25:72.932   ind26:76.459   ind27:50.893   ind28:60.362   ind29:50.384   ind30:48.468   ind31:50.974   ind32:59.748   ind33:48.875   ind34:63.915   ind35:68.895   ind36:53.741   ind37:47.652   ind38:48.911   ind39:56.246   ind40:54.706   ind41:66.684   ind42:80.063   ind43:66.046   ind44:58.239   ind45:47.874   ind46:51.154   ind47:60.924   ind48:47.756   ind49:52.881   ind50:48.580   ind51:60.531   ind52:153.886   ind53:52.167   ind54:58.283   ind55:53.544   ind56:63.267   ind57:68.579   ind58:51.473   ind59:58.656
2025-05-21 23:29:47,817 - modnet - INFO - Generation 0 best loss:  47.6521
2025-05-21 23:29:47,818 - modnet - INFO - Generation 0 best genes: {'act': 'leaky_relu', 'loss': 'mae', 'dropout': 0.2, 'n_neurons_first_layer': 256, 'fraction1': 0.25, 'fraction2': 0.25, 'fraction3': 1, 'fraction4': 0.5, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 720, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:29:47,818 - modnet - INFO - === Generation 1 ===

  0%|          | 0/90 [00:00<?, ?it/s]
  1%|          | 1/90 [00:04<07:19,  4.94s/it]
  3%|▎         | 3/90 [00:05<01:54,  1.32s/it]
  6%|▌         | 5/90 [00:05<01:03,  1.35it/s]
  8%|▊         | 7/90 [00:06<00:54,  1.53it/s]
  9%|▉         | 8/90 [00:07<00:51,  1.58it/s]
 10%|█         | 9/90 [00:08<01:14,  1.09it/s]
 11%|█         | 10/90 [00:09<01:04,  1.24it/s]
 12%|█▏        | 11/90 [00:09<00:49,  1.60it/s]
 13%|█▎        | 12/90 [00:09<00:38,  2.03it/s]
 14%|█▍        | 13/90 [00:10<00:38,  2.03it/s]
 16%|█▌        | 14/90 [00:10<00:29,  2.56it/s]
 17%|█▋        | 15/90 [00:10<00:27,  2.73it/s]
 18%|█▊        | 16/90 [00:10<00:26,  2.84it/s]
 20%|██        | 18/90 [00:12<00:34,  2.09it/s]
 21%|██        | 19/90 [00:12<00:28,  2.49it/s]
 23%|██▎       | 21/90 [00:12<00:18,  3.80it/s]
 24%|██▍       | 22/90 [00:12<00:18,  3.63it/s]
 26%|██▌       | 23/90 [00:13<00:21,  3.16it/s]
 27%|██▋       | 24/90 [00:13<00:21,  3.02it/s]
 28%|██▊       | 25/90 [00:14<00:33,  1.93it/s]
 29%|██▉       | 26/90 [00:14<00:29,  2.14it/s]
 30%|███       | 27/90 [00:15<00:24,  2.62it/s]
 31%|███       | 28/90 [00:15<00:19,  3.10it/s]
 32%|███▏      | 29/90 [00:15<00:20,  3.03it/s]
 33%|███▎      | 30/90 [00:15<00:16,  3.65it/s]
 34%|███▍      | 31/90 [00:16<00:28,  2.05it/s]
 36%|███▌      | 32/90 [00:17<00:23,  2.43it/s]
 37%|███▋      | 33/90 [00:17<00:22,  2.50it/s]
 39%|███▉      | 35/90 [00:17<00:16,  3.39it/s]
 40%|████      | 36/90 [00:17<00:14,  3.70it/s]
 41%|████      | 37/90 [00:18<00:23,  2.30it/s]
 43%|████▎     | 39/90 [00:18<00:14,  3.51it/s]
 44%|████▍     | 40/90 [00:19<00:17,  2.78it/s]
 46%|████▌     | 41/90 [00:19<00:16,  2.95it/s]
 47%|████▋     | 42/90 [00:19<00:13,  3.57it/s]
 49%|████▉     | 44/90 [00:20<00:09,  4.96it/s]
 50%|█████     | 45/90 [00:20<00:08,  5.11it/s]
 51%|█████     | 46/90 [00:20<00:07,  5.70it/s]
 52%|█████▏    | 47/90 [00:20<00:07,  5.71it/s]
 53%|█████▎    | 48/90 [00:20<00:07,  5.30it/s]
 54%|█████▍    | 49/90 [00:21<00:18,  2.22it/s]
 56%|█████▌    | 50/90 [00:22<00:16,  2.50it/s]
 58%|█████▊    | 52/90 [00:22<00:09,  4.03it/s]
 59%|█████▉    | 53/90 [00:22<00:09,  4.06it/s]
 62%|██████▏   | 56/90 [00:22<00:05,  6.26it/s]
 63%|██████▎   | 57/90 [00:23<00:05,  6.29it/s]
 64%|██████▍   | 58/90 [00:23<00:04,  6.64it/s]
 67%|██████▋   | 60/90 [00:23<00:03,  8.37it/s]
 69%|██████▉   | 62/90 [00:23<00:03,  7.96it/s]
 72%|███████▏  | 65/90 [00:23<00:02, 11.06it/s]
 74%|███████▍  | 67/90 [00:24<00:02,  8.87it/s]
 77%|███████▋  | 69/90 [00:24<00:02,  9.54it/s]
 79%|███████▉  | 71/90 [00:24<00:02,  6.60it/s]
 80%|████████  | 72/90 [00:25<00:05,  3.43it/s]
 82%|████████▏ | 74/90 [00:25<00:03,  4.01it/s]
 83%|████████▎ | 75/90 [00:26<00:04,  3.29it/s]
 84%|████████▍ | 76/90 [00:26<00:04,  3.32it/s]
 86%|████████▌ | 77/90 [00:26<00:03,  3.82it/s]
 88%|████████▊ | 79/90 [00:27<00:03,  3.41it/s]
 89%|████████▉ | 80/90 [00:28<00:03,  2.81it/s]
 90%|█████████ | 81/90 [00:29<00:04,  1.97it/s]
 91%|█████████ | 82/90 [00:29<00:03,  2.37it/s]
 92%|█████████▏| 83/90 [00:29<00:02,  2.57it/s]
 93%|█████████▎| 84/90 [00:30<00:03,  1.98it/s]
 96%|█████████▌| 86/90 [00:32<00:03,  1.32it/s]
 97%|█████████▋| 87/90 [00:33<00:02,  1.15it/s]
 98%|█████████▊| 88/90 [00:34<00:01,  1.30it/s]
 99%|█████████▉| 89/90 [00:34<00:00,  1.43it/s]
100%|██████████| 90/90 [00:35<00:00,  1.39it/s]
100%|██████████| 90/90 [00:35<00:00,  2.53it/s]
2025-05-21 23:30:23,686 - modnet - INFO - Loss per individual: ind0:49.676   ind1:60.498   ind2:49.652   ind3:52.645   ind4:78.044   ind5:54.739   ind6:50.056   ind7:49.020   ind8:63.079   ind9:48.720   ind10:58.820   ind11:63.249   ind12:60.223   ind13:60.233   ind14:48.824   ind15:69.183   ind16:57.969   ind17:578.194   ind18:59.597   ind19:62.441   ind20:578.194   ind21:47.262   ind22:44.897   ind23:47.678   ind24:50.709   ind25:58.272   ind26:48.089   ind27:58.603   ind28:57.340   ind29:56.409
2025-05-21 23:30:23,692 - modnet - INFO - Generation 1 best loss: 44.8973
2025-05-21 23:30:23,692 - modnet - INFO - Generation 1 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.2, 'n_neurons_first_layer': 256, 'fraction1': 0.25, 'fraction2': 1, 'fraction3': 1, 'fraction4': 0.5, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 470, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:30:23,692 - modnet - INFO - === Generation 2 ===

  0%|          | 0/90 [00:00<?, ?it/s]
  1%|          | 1/90 [00:04<06:37,  4.47s/it]
  3%|▎         | 3/90 [00:04<01:45,  1.21s/it]
  4%|▍         | 4/90 [00:04<01:12,  1.18it/s]
  6%|▌         | 5/90 [00:09<03:05,  2.18s/it]
  7%|▋         | 6/90 [00:10<02:19,  1.66s/it]
  8%|▊         | 7/90 [00:12<02:27,  1.78s/it]
  9%|▉         | 8/90 [00:12<01:47,  1.32s/it]
 11%|█         | 10/90 [00:12<00:58,  1.37it/s]
 12%|█▏        | 11/90 [00:12<00:45,  1.73it/s]
 13%|█▎        | 12/90 [00:13<00:42,  1.84it/s]
 14%|█▍        | 13/90 [00:13<00:42,  1.80it/s]
 16%|█▌        | 14/90 [00:14<00:51,  1.47it/s]
 17%|█▋        | 15/90 [00:15<00:42,  1.75it/s]
 18%|█▊        | 16/90 [00:15<00:36,  2.03it/s]
 19%|█▉        | 17/90 [00:15<00:30,  2.41it/s]
 21%|██        | 19/90 [00:15<00:18,  3.80it/s]
 23%|██▎       | 21/90 [00:15<00:12,  5.36it/s]
 24%|██▍       | 22/90 [00:16<00:12,  5.46it/s]
 26%|██▌       | 23/90 [00:17<00:24,  2.79it/s]
 27%|██▋       | 24/90 [00:17<00:24,  2.74it/s]
 28%|██▊       | 25/90 [00:17<00:24,  2.68it/s]
 29%|██▉       | 26/90 [00:17<00:19,  3.35it/s]
 30%|███       | 27/90 [00:18<00:19,  3.17it/s]
 31%|███       | 28/90 [00:18<00:21,  2.83it/s]
 32%|███▏      | 29/90 [00:18<00:17,  3.44it/s]
 33%|███▎      | 30/90 [00:19<00:15,  3.95it/s]
 34%|███▍      | 31/90 [00:19<00:12,  4.79it/s]
 37%|███▋      | 33/90 [00:19<00:12,  4.56it/s]
 38%|███▊      | 34/90 [00:19<00:11,  4.74it/s]
 41%|████      | 37/90 [00:19<00:06,  7.93it/s]
 43%|████▎     | 39/90 [00:20<00:08,  6.09it/s]
 46%|████▌     | 41/90 [00:20<00:06,  7.38it/s]
 48%|████▊     | 43/90 [00:20<00:07,  6.27it/s]
 50%|█████     | 45/90 [00:21<00:06,  7.45it/s]
 51%|█████     | 46/90 [00:21<00:06,  7.26it/s]
 53%|█████▎    | 48/90 [00:22<00:08,  4.70it/s]
 54%|█████▍    | 49/90 [00:22<00:08,  4.63it/s]
 56%|█████▌    | 50/90 [00:22<00:07,  5.12it/s]
 57%|█████▋    | 51/90 [00:22<00:06,  5.69it/s]
 59%|█████▉    | 53/90 [00:22<00:07,  4.97it/s]
 60%|██████    | 54/90 [00:23<00:06,  5.52it/s]
 62%|██████▏   | 56/90 [00:23<00:04,  7.12it/s]
 63%|██████▎   | 57/90 [00:23<00:05,  5.94it/s]
 64%|██████▍   | 58/90 [00:23<00:06,  4.85it/s]
 66%|██████▌   | 59/90 [00:23<00:06,  5.13it/s]
 67%|██████▋   | 60/90 [00:24<00:06,  4.80it/s]
 68%|██████▊   | 61/90 [00:24<00:05,  5.57it/s]
 70%|███████   | 63/90 [00:25<00:08,  3.25it/s]
 73%|███████▎  | 66/90 [00:25<00:04,  5.45it/s]
 76%|███████▌  | 68/90 [00:25<00:03,  6.02it/s]
 77%|███████▋  | 69/90 [00:25<00:03,  6.12it/s]
 78%|███████▊  | 70/90 [00:26<00:04,  4.86it/s]
 79%|███████▉  | 71/90 [00:26<00:03,  5.24it/s]
 80%|████████  | 72/90 [00:26<00:03,  4.85it/s]
 81%|████████  | 73/90 [00:26<00:03,  4.32it/s]
 83%|████████▎ | 75/90 [00:27<00:02,  5.32it/s]
 84%|████████▍ | 76/90 [00:27<00:02,  5.39it/s]
 86%|████████▌ | 77/90 [00:27<00:02,  5.63it/s]
 87%|████████▋ | 78/90 [00:27<00:02,  4.68it/s]
 88%|████████▊ | 79/90 [00:27<00:02,  5.46it/s]
 89%|████████▉ | 80/90 [00:28<00:03,  3.30it/s]
 90%|█████████ | 81/90 [00:28<00:03,  2.88it/s]
 91%|█████████ | 82/90 [00:30<00:04,  1.68it/s]
 92%|█████████▏| 83/90 [00:32<00:06,  1.05it/s]
 93%|█████████▎| 84/90 [00:32<00:05,  1.09it/s]
 94%|█████████▍| 85/90 [00:33<00:03,  1.33it/s]
 96%|█████████▌| 86/90 [00:34<00:04,  1.05s/it]
 97%|█████████▋| 87/90 [00:35<00:02,  1.24it/s]
 98%|█████████▊| 88/90 [00:35<00:01,  1.25it/s]
 99%|█████████▉| 89/90 [00:39<00:01,  1.60s/it]
100%|██████████| 90/90 [00:43<00:00,  2.25s/it]
100%|██████████| 90/90 [00:43<00:00,  2.08it/s]
2025-05-21 23:31:07,142 - modnet - INFO - Loss per individual: ind0:59.178   ind1:53.499   ind2:68.789   ind3:44.933   ind4:63.048   ind5:44.169   ind6:43.845   ind7:73.263   ind8:45.821   ind9:46.412   ind10:53.183   ind11:578.194   ind12:55.324   ind13:53.804   ind14:45.112   ind15:77.686   ind16:82.107   ind17:58.966   ind18:61.688   ind19:65.030   ind20:45.999   ind21:45.082   ind22:51.436   ind23:577.419   ind24:68.861   ind25:53.795   ind26:50.161   ind27:577.419   ind28:49.681   ind29:52.887
2025-05-21 23:31:07,156 - modnet - INFO - Generation 2 best loss: 43.8455
2025-05-21 23:31:07,156 - modnet - INFO - Generation 2 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 256, 'fraction1': 0.25, 'fraction2': 1, 'fraction3': 0.25, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 610, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:31:07,156 - modnet - INFO - === Generation 3 ===

  0%|          | 0/90 [00:00<?, ?it/s]
  1%|          | 1/90 [00:07<10:46,  7.27s/it]
  2%|▏         | 2/90 [00:08<05:31,  3.76s/it]
  3%|▎         | 3/90 [00:09<03:45,  2.59s/it]
  4%|▍         | 4/90 [00:10<02:28,  1.72s/it]
  6%|▌         | 5/90 [00:11<02:09,  1.53s/it]
  7%|▋         | 6/90 [00:11<01:28,  1.06s/it]
  8%|▊         | 7/90 [00:11<01:03,  1.31it/s]
  9%|▉         | 8/90 [00:12<01:00,  1.37it/s]
 10%|█         | 9/90 [00:12<00:47,  1.69it/s]
 11%|█         | 10/90 [00:12<00:35,  2.23it/s]
 13%|█▎        | 12/90 [00:13<00:27,  2.80it/s]
 14%|█▍        | 13/90 [00:14<00:38,  1.98it/s]
 16%|█▌        | 14/90 [00:14<00:34,  2.23it/s]
 17%|█▋        | 15/90 [00:14<00:30,  2.46it/s]
 18%|█▊        | 16/90 [00:15<00:34,  2.17it/s]
 19%|█▉        | 17/90 [00:15<00:30,  2.42it/s]
 21%|██        | 19/90 [00:15<00:19,  3.70it/s]
 22%|██▏       | 20/90 [00:16<00:20,  3.49it/s]
 24%|██▍       | 22/90 [00:16<00:14,  4.60it/s]
 26%|██▌       | 23/90 [00:16<00:15,  4.42it/s]
 27%|██▋       | 24/90 [00:16<00:13,  4.83it/s]
 28%|██▊       | 25/90 [00:17<00:17,  3.71it/s]
 29%|██▉       | 26/90 [00:17<00:14,  4.45it/s]
 30%|███       | 27/90 [00:17<00:13,  4.83it/s]
 32%|███▏      | 29/90 [00:17<00:08,  6.85it/s]
 33%|███▎      | 30/90 [00:17<00:09,  6.35it/s]
 34%|███▍      | 31/90 [00:18<00:09,  5.93it/s]
 36%|███▌      | 32/90 [00:18<00:08,  6.65it/s]
 37%|███▋      | 33/90 [00:18<00:08,  6.70it/s]
 38%|███▊      | 34/90 [00:18<00:08,  6.93it/s]
 39%|███▉      | 35/90 [00:18<00:09,  5.62it/s]
 40%|████      | 36/90 [00:19<00:12,  4.38it/s]
 41%|████      | 37/90 [00:19<00:11,  4.46it/s]
 42%|████▏     | 38/90 [00:19<00:17,  2.91it/s]
 43%|████▎     | 39/90 [00:20<00:21,  2.40it/s]
 46%|████▌     | 41/90 [00:20<00:14,  3.27it/s]
 49%|████▉     | 44/90 [00:21<00:09,  4.75it/s]
 50%|█████     | 45/90 [00:21<00:11,  3.84it/s]
 51%|█████     | 46/90 [00:21<00:11,  3.97it/s]
 53%|█████▎    | 48/90 [00:22<00:07,  5.28it/s]
 56%|█████▌    | 50/90 [00:22<00:07,  5.64it/s]
 57%|█████▋    | 51/90 [00:23<00:13,  2.82it/s]
 58%|█████▊    | 52/90 [00:24<00:16,  2.27it/s]
 63%|██████▎   | 57/90 [00:24<00:06,  5.38it/s]
 66%|██████▌   | 59/90 [00:24<00:04,  6.41it/s]
 68%|██████▊   | 61/90 [00:24<00:05,  5.27it/s]
 70%|███████   | 63/90 [00:26<00:07,  3.46it/s]
 71%|███████   | 64/90 [00:26<00:09,  2.70it/s]
 73%|███████▎  | 66/90 [00:27<00:07,  3.12it/s]
 74%|███████▍  | 67/90 [00:27<00:08,  2.76it/s]
 76%|███████▌  | 68/90 [00:29<00:11,  1.83it/s]
 77%|███████▋  | 69/90 [00:29<00:09,  2.11it/s]
 79%|███████▉  | 71/90 [00:29<00:05,  3.28it/s]
 80%|████████  | 72/90 [00:29<00:05,  3.56it/s]
 82%|████████▏ | 74/90 [00:29<00:03,  4.11it/s]
 83%|████████▎ | 75/90 [00:30<00:05,  2.70it/s]
 84%|████████▍ | 76/90 [00:31<00:04,  2.98it/s]
 86%|████████▌ | 77/90 [00:31<00:03,  3.44it/s]
 88%|████████▊ | 79/90 [00:31<00:03,  3.55it/s]
 90%|█████████ | 81/90 [00:33<00:03,  2.36it/s]
 91%|█████████ | 82/90 [00:35<00:06,  1.19it/s]
 92%|█████████▏| 83/90 [00:35<00:05,  1.38it/s]
 93%|█████████▎| 84/90 [00:36<00:04,  1.47it/s]
 94%|█████████▍| 85/90 [00:36<00:02,  1.89it/s]
 96%|█████████▌| 86/90 [00:36<00:01,  2.02it/s]
 97%|█████████▋| 87/90 [00:37<00:01,  2.44it/s]
 98%|█████████▊| 88/90 [00:37<00:00,  2.07it/s]
 99%|█████████▉| 89/90 [00:40<00:01,  1.09s/it]
100%|██████████| 90/90 [00:41<00:00,  1.13s/it]
100%|██████████| 90/90 [00:41<00:00,  2.17it/s]
2025-05-21 23:31:49,001 - modnet - INFO - Loss per individual: ind0:64.990   ind1:55.651   ind2:59.882   ind3:45.133   ind4:50.691   ind5:49.106   ind6:43.631   ind7:43.089   ind8:46.235   ind9:48.832   ind10:54.843   ind11:42.218   ind12:43.611   ind13:63.109   ind14:44.536   ind15:55.731   ind16:56.264   ind17:49.610   ind18:46.965   ind19:46.860   ind20:49.023   ind21:43.408   ind22:48.128   ind23:55.618   ind24:45.761   ind25:55.309   ind26:50.164   ind27:48.127   ind28:47.412   ind29:54.730
2025-05-21 23:31:49,010 - modnet - INFO - Generation 3 best loss: 42.2184
2025-05-21 23:31:49,010 - modnet - INFO - Generation 3 best genes: {'act': 'elu', 'loss': 'mae', 'dropout': 0.0, 'n_neurons_first_layer': 384, 'fraction1': 1.0, 'fraction2': 0.5, 'fraction3': 0.25, 'fraction4': 1, 'xscale': 'minmax', 'lr': 0.001, 'batch_size': 16, 'n_feat': 610, 'n_layers': 2, 'layer_mults': None}
2025-05-21 23:31:49,010 - modnet - INFO - === Generation 4 ===

  0%|          | 0/90 [00:00<?, ?it/s]slurmstepd: error: *** JOB 8648077 ON cns266 CANCELLED AT 2025-05-21T23:31:53 ***

Resources Used

Total Memory used                        - MEM              : 39GiB
Total CPU Time                           - CPU_Time         : 23:32:16
Execution Time                           - Wall_Time        : 00:22:04
total programme cpu time                 - Total_CPU        : 12:53:01
Total_CPU / CPU_Time  (%)                - ETA              : 54%
Number of alloc CPU                      - NCPUS            : 64
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 64
Mobilized Resources x Execution Time     - R_Wall_Time      : 23:32:16
CPU_Time / R_Wall_Time (%)               - ALPHA            : 100%
Energy (Joules)                                             : 433361

