Job started on Fri May 16 00:04:20 CEST 2025
Running on node(s): cnm029
2025-05-16 00:04:37.528678: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 64 jobs
Preparing nested CV run for task 'matbench_steels_matminer_roost_lmp'
Found 1 matching files for matbench_steels_matminer_roost_lmp
2025-05-16 00:04:53,856 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff9af1fed00> object, created with modnet version 0.1.13
Preparing fold 1 ...
2025-05-16 00:04:53,882 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff9af1fee80> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_steels_matminer_roost_lmp_train_moddata_f1
2025-05-16 00:04:53,909 - modnet - INFO - Data successfully saved as folds/matbench_steels_matminer_roost_lmp_train_moddata_f1!
Preparing fold 2 ...
2025-05-16 00:04:53,934 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff9af004e80> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_steels_matminer_roost_lmp_train_moddata_f2
2025-05-16 00:04:53,963 - modnet - INFO - Data successfully saved as folds/matbench_steels_matminer_roost_lmp_train_moddata_f2!
Preparing fold 3 ...
2025-05-16 00:04:53,991 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff9ae395160> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_steels_matminer_roost_lmp_train_moddata_f3
2025-05-16 00:04:54,125 - modnet - INFO - Data successfully saved as folds/matbench_steels_matminer_roost_lmp_train_moddata_f3!
Preparing fold 4 ...
2025-05-16 00:04:54,147 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff9aed1f0d0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_steels_matminer_roost_lmp_train_moddata_f4
2025-05-16 00:04:54,179 - modnet - INFO - Data successfully saved as folds/matbench_steels_matminer_roost_lmp_train_moddata_f4!
Preparing fold 5 ...
2025-05-16 00:04:54,213 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff9aefd08e0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_steels_matminer_roost_lmp_train_moddata_f5
2025-05-16 00:04:54,238 - modnet - INFO - Data successfully saved as folds/matbench_steels_matminer_roost_lmp_train_moddata_f5!
Training fold 1 ...
Processing fold 1 ...
2025-05-16 00:04:54.251910: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 00:04:54.252349: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 00:04:54.252375: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 00:04:54.252405: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm029.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 00:04:54.252631: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 00:04:54,347 - modnet - INFO - Targets:
2025-05-16 00:04:54,347 - modnet - INFO - 1) target: regression
2025-05-16 00:04:55,384 - modnet - INFO - Multiprocessing on 64 cores. Total of 128 cores available.
2025-05-16 00:04:55,384 - modnet - INFO - Generation number 0

  0%|          | 0/250 [00:00<?, ?it/s]2025-05-16 00:04:55.687117: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.732824: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.732822: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.744715: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.758109: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.781681: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.815420: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.836274: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.837703: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.853051: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.853051: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.884179: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.910014: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.924901: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.929445: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.936250: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.965353: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.969239: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.993138: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:55.993576: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.038601: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.039185: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.057766: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.069726: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.073432: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.082425: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.108212: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.119390: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.119391: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.126427: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.153381: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.189078: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.229742: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.229744: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.259935: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.272850: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.272847: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.313786: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.313874: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.368473: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.381313: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.422383: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.435414: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.444214: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.458611: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.486247: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.517617: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.537115: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.558029: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.601960: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.604719: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.620940: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.624708: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.672017: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.680862: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.680864: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.740833: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.749484: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.751354: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.759946: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.804113: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.806484: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.821405: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:56.976643: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-16 00:04:59.085605: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 00:04:59.086197: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 00:04:59.086231: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 00:04:59.086268: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm029.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 00:04:59.086600: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 00:04:59.128859: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-16 00:04:59.129392: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-16 00:04:59.129423: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-16 00:04:59.129461: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm029.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-16 00:04:59.129751: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set

  0%|          | 0/250 [00:03<?, ?it/s]
multiprocessing.pool.RemoteTraceback: 
"""
Traceback (most recent call last):
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/multiprocessing/pool.py", line 125, in worker
    result = (True, func(*args, **kwds))
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 581, in _map_evaluate_individual
    return _evaluate_individual(**kwargs)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 593, in _evaluate_individual
    individual.evaluate(train_data, val_data, fast=fast)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 257, in evaluate
    model = MODNetModel(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/models/vanilla.py", line 127, in __init__
    self.model = self.build_model(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/models/vanilla.py", line 208, in build_model
    for li in range(num_layers[3]):
IndexError: list index out of range
"""

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "run_benchmark.py", line 863, in <module>
    results = benchmark(data, settings, n_jobs=n_jobs, fast=fast)
  File "run_benchmark.py", line 140, in benchmark
    return matbench_benchmark(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 143, in matbench_benchmark
    fold_results.append(train_fold(fold, *args, **model_kwargs))
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 287, in train_fold
    model = ga.run(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 508, in run
    val_loss, models, individuals = self.function_fitness(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 456, in function_fitness
    for res in tqdm.tqdm(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tqdm/std.py", line 1181, in __iter__
    for obj in iterable:
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/multiprocessing/pool.py", line 868, in next
    raise value
IndexError: list index out of range
Job finished on Fri May 16 00:05:00 CEST 2025

Resources Used

Total Memory used                        - MEM              : 0B
Total CPU Time                           - CPU_Time         : 00:42:40
Execution Time                           - Wall_Time        : 00:00:40
total programme cpu time                 - Total_CPU        : 04:40.901
Total_CPU / CPU_Time  (%)                - ETA              : 10%
Number of alloc CPU                      - NCPUS            : 64
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 64
Mobilized Resources x Execution Time     - R_Wall_Time      : 00:42:40
CPU_Time / R_Wall_Time (%)               - ALPHA            : 100%
Energy (Joules)                                             : 20626

