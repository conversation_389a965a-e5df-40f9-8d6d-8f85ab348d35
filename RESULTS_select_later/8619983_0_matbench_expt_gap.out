Job started on Tue May 20 15:20:16 CEST 2025
Running on node(s): cnm027
2025-05-20 15:20:19.085504: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 32 jobs
Preparing nested CV run for task 'matbench_expt_gap_matminer_sisso_roost_lmp'
Found 1 matching files for matbench_expt_gap_matminer_sisso_roost_lmp
2025-05-20 15:20:21,841 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f11343bdf10> object, created with modnet version 0.1.13
GA settings:
    Population size: 50
    Number of generations: 20
    Early stopping: 8
    Refit: False
Preparing fold 1 ...
2025-05-20 15:20:21,903 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f11343bd880> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_expt_gap_matminer_sisso_roost_lmp_train_moddata_f1
2025-05-20 15:20:21,938 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_matminer_sisso_roost_lmp_train_moddata_f1!
Preparing fold 2 ...
2025-05-20 15:20:21,983 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f1132d81040> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_expt_gap_matminer_sisso_roost_lmp_train_moddata_f2
2025-05-20 15:20:22,164 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_matminer_sisso_roost_lmp_train_moddata_f2!
Preparing fold 3 ...
2025-05-20 15:20:22,217 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f11330020d0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_expt_gap_matminer_sisso_roost_lmp_train_moddata_f3
2025-05-20 15:20:22,258 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_matminer_sisso_roost_lmp_train_moddata_f3!
Preparing fold 4 ...
2025-05-20 15:20:22,306 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f1132e01550> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_expt_gap_matminer_sisso_roost_lmp_train_moddata_f4
2025-05-20 15:20:22,345 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_matminer_sisso_roost_lmp_train_moddata_f4!
Preparing fold 5 ...
2025-05-20 15:20:22,399 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f11318adf40> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_expt_gap_matminer_sisso_roost_lmp_train_moddata_f5
2025-05-20 15:20:22,442 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_matminer_sisso_roost_lmp_train_moddata_f5!
Training fold 1 ...
Processing fold 1 ...
2025-05-20 15:20:22.450955: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-20 15:20:22.451380: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/8.4.1.50-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-20 15:20:22.451408: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-20 15:20:22.451439: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm027.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-20 15:20:22.451679: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-20 15:20:22,553 - modnet - INFO - Targets:
2025-05-20 15:20:22,553 - modnet - INFO - 1) target: regression
/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py:353: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples,), for example using ravel().
  rf_model.fit(self.train_data.df_featurized, self.train_data.df_targets)
2025-05-20 15:21:54,526 - modnet - INFO - Multiprocessing on 32 cores. Total of 128 cores available.
2025-05-20 15:21:54,527 - modnet - INFO - Generation number 0
Population initialized with 100 individuals.
2025-05-20 15:21:54,533 - modnet - INFO - Initialized generation 0 with population size: 100

  0%|          | 0/500 [00:00<?, ?it/s]
  0%|          | 1/500 [00:20<2:47:30, 20.14s/it]
  0%|          | 2/500 [00:22<1:18:28,  9.45s/it]
  1%|          | 3/500 [00:22<45:03,  5.44s/it]  
  1%|          | 5/500 [00:22<19:53,  2.41s/it]
  1%|          | 6/500 [00:23<15:30,  1.88s/it]
  1%|▏         | 7/500 [00:24<12:17,  1.50s/it]
  2%|▏         | 8/500 [00:24<10:03,  1.23s/it]
  2%|▏         | 9/500 [00:25<09:14,  1.13s/it]
  2%|▏         | 10/500 [00:25<07:07,  1.15it/s]
  2%|▏         | 11/500 [00:26<07:16,  1.12it/s]
  2%|▏         | 12/500 [00:26<05:23,  1.51it/s]
  3%|▎         | 13/500 [00:27<04:20,  1.87it/s]
  3%|▎         | 14/500 [00:28<05:10,  1.57it/s]
  3%|▎         | 15/500 [00:28<04:26,  1.82it/s]
  3%|▎         | 17/500 [00:28<02:50,  2.83it/s]
  4%|▎         | 18/500 [00:29<03:25,  2.35it/s]
  4%|▍         | 19/500 [00:29<03:37,  2.21it/s]
  4%|▍         | 20/500 [00:29<03:04,  2.60it/s]
  4%|▍         | 21/500 [00:30<02:39,  3.00it/s]
  4%|▍         | 22/500 [00:30<03:09,  2.52it/s]
  5%|▍         | 23/500 [00:31<03:09,  2.52it/s]
  5%|▍         | 24/500 [00:31<03:52,  2.05it/s]
  5%|▌         | 25/500 [00:32<04:06,  1.92it/s]
  5%|▌         | 26/500 [00:32<03:17,  2.41it/s]
  5%|▌         | 27/500 [00:32<02:57,  2.67it/s]
  6%|▌         | 28/500 [00:33<03:38,  2.16it/s]
  6%|▌         | 29/500 [00:33<03:35,  2.19it/s]
  6%|▌         | 30/500 [00:34<02:46,  2.83it/s]
  6%|▌         | 31/500 [00:34<02:11,  3.56it/s]
  6%|▋         | 32/500 [00:34<03:08,  2.48it/s]
  7%|▋         | 33/500 [00:36<04:58,  1.57it/s]
  7%|▋         | 34/500 [00:36<04:02,  1.92it/s]
  7%|▋         | 35/500 [00:37<04:40,  1.66it/s]
  7%|▋         | 36/500 [00:37<03:54,  1.98it/s]
  7%|▋         | 37/500 [00:39<07:01,  1.10it/s]
  8%|▊         | 38/500 [00:39<05:35,  1.38it/s]
  8%|▊         | 39/500 [00:41<07:36,  1.01it/s]
  8%|▊         | 40/500 [00:41<06:18,  1.21it/s]
  8%|▊         | 41/500 [00:42<05:24,  1.41it/s]
  8%|▊         | 42/500 [00:42<04:34,  1.67it/s]
  9%|▉         | 44/500 [00:42<02:50,  2.67it/s]
  9%|▉         | 46/500 [00:43<02:41,  2.82it/s]
 10%|▉         | 48/500 [00:43<02:00,  3.76it/s]
 10%|▉         | 49/500 [00:43<01:48,  4.16it/s]
 10%|█         | 50/500 [00:43<01:41,  4.45it/s]
 10%|█         | 51/500 [00:43<01:34,  4.77it/s]
 10%|█         | 52/500 [00:44<02:37,  2.84it/s]
 11%|█         | 53/500 [00:45<02:47,  2.68it/s]
 11%|█         | 54/500 [00:45<02:56,  2.53it/s]
 11%|█         | 55/500 [00:45<02:21,  3.15it/s]
 11%|█         | 56/500 [00:46<02:42,  2.73it/s]
 12%|█▏        | 58/500 [00:46<01:45,  4.17it/s]
 12%|█▏        | 59/500 [00:46<01:31,  4.80it/s]
 12%|█▏        | 60/500 [00:46<01:38,  4.48it/s]
 12%|█▏        | 61/500 [00:47<02:40,  2.74it/s]
 12%|█▏        | 62/500 [00:47<02:51,  2.56it/s]
 13%|█▎        | 63/500 [00:48<03:11,  2.28it/s]
 13%|█▎        | 64/500 [00:48<02:32,  2.86it/s]
 13%|█▎        | 65/500 [00:48<02:09,  3.36it/s]
 13%|█▎        | 66/500 [00:48<01:50,  3.93it/s]
 13%|█▎        | 67/500 [00:49<01:39,  4.37it/s]
 14%|█▎        | 68/500 [00:49<02:22,  3.03it/s]
 14%|█▍        | 69/500 [00:49<02:11,  3.28it/s]
 14%|█▍        | 70/500 [00:50<03:02,  2.35it/s]
 14%|█▍        | 71/500 [00:51<02:52,  2.49it/s]
 14%|█▍        | 72/500 [00:51<03:23,  2.10it/s]
 15%|█▍        | 74/500 [00:52<03:20,  2.12it/s]
 15%|█▌        | 75/500 [00:53<03:34,  1.98it/s]
 15%|█▌        | 76/500 [00:54<04:32,  1.56it/s]
 15%|█▌        | 77/500 [00:54<03:53,  1.81it/s]
 16%|█▌        | 79/500 [00:55<03:54,  1.80it/s]
 16%|█▌        | 80/500 [00:55<03:14,  2.16it/s]
 16%|█▌        | 81/500 [00:56<02:44,  2.54it/s]
 16%|█▋        | 82/500 [00:56<02:18,  3.02it/s]
 17%|█▋        | 83/500 [00:56<02:08,  3.25it/s]
 17%|█▋        | 85/500 [00:56<01:38,  4.22it/s]
 17%|█▋        | 86/500 [00:56<01:25,  4.87it/s]
 18%|█▊        | 88/500 [00:57<01:07,  6.10it/s]
 18%|█▊        | 90/500 [00:57<01:03,  6.43it/s]
 18%|█▊        | 91/500 [00:57<01:02,  6.50it/s]
 19%|█▊        | 93/500 [00:58<01:25,  4.76it/s]
 19%|█▉        | 94/500 [00:58<01:21,  4.99it/s]
 19%|█▉        | 95/500 [01:00<04:13,  1.60it/s]
 19%|█▉        | 96/500 [01:01<05:58,  1.13it/s]
 19%|█▉        | 97/500 [01:02<04:35,  1.46it/s]
 20%|█▉        | 98/500 [01:02<04:01,  1.66it/s]
 20%|█▉        | 99/500 [01:02<03:37,  1.85it/s]
 20%|██        | 100/500 [01:02<02:53,  2.31it/s]
 20%|██        | 102/500 [01:04<03:27,  1.92it/s]
 21%|██        | 103/500 [01:04<03:45,  1.76it/s]
 21%|██        | 104/500 [01:05<03:35,  1.84it/s]
 21%|██        | 105/500 [01:05<03:25,  1.93it/s]
 21%|██        | 106/500 [01:06<03:11,  2.06it/s]
 21%|██▏       | 107/500 [01:06<02:51,  2.29it/s]
 22%|██▏       | 108/500 [01:07<04:22,  1.49it/s]
 22%|██▏       | 109/500 [01:08<03:49,  1.71it/s]
 22%|██▏       | 110/500 [01:08<03:44,  1.74it/s]
 22%|██▏       | 111/500 [01:09<03:23,  1.91it/s]
 22%|██▏       | 112/500 [01:09<03:25,  1.89it/s]
 23%|██▎       | 113/500 [01:10<04:10,  1.55it/s]
 23%|██▎       | 114/500 [01:11<03:41,  1.74it/s]
 23%|██▎       | 116/500 [01:12<03:22,  1.89it/s]
 23%|██▎       | 117/500 [01:12<02:45,  2.31it/s]
 24%|██▍       | 119/500 [01:12<01:49,  3.48it/s]
 24%|██▍       | 120/500 [01:12<01:34,  4.04it/s]
 24%|██▍       | 121/500 [01:13<03:05,  2.04it/s]
 24%|██▍       | 122/500 [01:13<02:41,  2.34it/s]
 25%|██▍       | 123/500 [01:14<03:25,  1.83it/s]
 25%|██▍       | 124/500 [01:15<03:46,  1.66it/s]
 25%|██▌       | 125/500 [01:15<03:28,  1.80it/s]
 25%|██▌       | 126/500 [01:16<02:44,  2.27it/s]
 26%|██▌       | 128/500 [01:16<02:15,  2.74it/s]
 26%|██▌       | 129/500 [01:16<01:53,  3.28it/s]
 26%|██▌       | 130/500 [01:16<01:34,  3.90it/s]
 26%|██▌       | 131/500 [01:17<02:35,  2.38it/s]
 26%|██▋       | 132/500 [01:19<04:11,  1.46it/s]
 27%|██▋       | 133/500 [01:20<04:44,  1.29it/s]
 27%|██▋       | 135/500 [01:21<03:59,  1.52it/s]
 27%|██▋       | 136/500 [01:22<05:25,  1.12it/s]
 27%|██▋       | 137/500 [01:23<04:27,  1.36it/s]
 28%|██▊       | 138/500 [01:24<04:55,  1.23it/s]
 28%|██▊       | 139/500 [01:24<04:22,  1.37it/s]
 28%|██▊       | 140/500 [01:24<03:18,  1.81it/s]
 28%|██▊       | 141/500 [01:25<03:35,  1.67it/s]
 28%|██▊       | 142/500 [01:25<03:12,  1.86it/s]
 29%|██▊       | 143/500 [01:25<02:33,  2.33it/s]
 29%|██▉       | 144/500 [01:26<02:58,  1.99it/s]
 29%|██▉       | 146/500 [01:27<02:11,  2.69it/s]
 29%|██▉       | 147/500 [01:27<02:10,  2.71it/s]
 30%|██▉       | 148/500 [01:27<01:50,  3.20it/s]
 30%|██▉       | 149/500 [01:28<02:05,  2.79it/s]
 30%|███       | 151/500 [01:28<01:19,  4.41it/s]
 30%|███       | 152/500 [01:28<01:31,  3.79it/s]
 31%|███       | 153/500 [01:29<01:46,  3.27it/s]
 31%|███       | 154/500 [01:29<02:14,  2.58it/s]
 31%|███       | 155/500 [01:30<02:13,  2.58it/s]
 31%|███       | 156/500 [01:30<03:06,  1.84it/s]
 31%|███▏      | 157/500 [01:31<02:22,  2.40it/s]
 32%|███▏      | 158/500 [01:31<02:00,  2.83it/s]
 32%|███▏      | 159/500 [01:31<01:38,  3.46it/s]
 32%|███▏      | 160/500 [01:31<01:21,  4.19it/s]
 32%|███▏      | 161/500 [01:31<01:11,  4.71it/s]
 32%|███▏      | 162/500 [01:31<01:19,  4.26it/s]
 33%|███▎      | 163/500 [01:32<01:30,  3.74it/s]
 33%|███▎      | 164/500 [01:32<01:17,  4.32it/s]
 33%|███▎      | 165/500 [01:33<02:02,  2.73it/s]
 33%|███▎      | 166/500 [01:33<02:44,  2.03it/s]
 33%|███▎      | 167/500 [01:34<02:51,  1.94it/s]
 34%|███▎      | 168/500 [01:35<03:12,  1.73it/s]
 34%|███▍      | 169/500 [01:35<02:40,  2.06it/s]
 34%|███▍      | 170/500 [01:35<02:18,  2.39it/s]
 34%|███▍      | 171/500 [01:35<01:56,  2.83it/s]
 34%|███▍      | 172/500 [01:36<01:55,  2.84it/s]
 35%|███▍      | 174/500 [01:36<01:16,  4.27it/s]
 35%|███▌      | 175/500 [01:36<01:23,  3.89it/s]
 35%|███▌      | 176/500 [01:37<01:24,  3.82it/s]
 35%|███▌      | 177/500 [01:37<01:21,  3.95it/s]
 36%|███▌      | 178/500 [01:37<01:10,  4.60it/s]
 36%|███▌      | 179/500 [01:37<01:16,  4.21it/s]
 36%|███▌      | 180/500 [01:37<01:16,  4.19it/s]
 36%|███▌      | 181/500 [01:38<01:36,  3.32it/s]
 36%|███▋      | 182/500 [01:39<02:09,  2.46it/s]
 37%|███▋      | 183/500 [01:39<02:09,  2.46it/s]
 37%|███▋      | 184/500 [01:41<04:27,  1.18it/s]
 37%|███▋      | 185/500 [01:42<04:16,  1.23it/s]
 37%|███▋      | 186/500 [01:42<03:54,  1.34it/s]
 37%|███▋      | 187/500 [01:44<05:01,  1.04it/s]
 38%|███▊      | 188/500 [01:44<04:39,  1.12it/s]
 38%|███▊      | 189/500 [01:45<03:32,  1.47it/s]
 38%|███▊      | 190/500 [01:46<04:40,  1.11it/s]
 38%|███▊      | 191/500 [01:46<03:38,  1.42it/s]
 38%|███▊      | 192/500 [01:48<05:20,  1.04s/it]
 39%|███▊      | 193/500 [01:49<05:40,  1.11s/it]
 39%|███▉      | 194/500 [01:49<04:09,  1.23it/s]
 39%|███▉      | 195/500 [01:50<03:16,  1.55it/s]
 39%|███▉      | 196/500 [01:51<04:19,  1.17it/s]
 39%|███▉      | 197/500 [01:51<03:17,  1.54it/s]
 40%|███▉      | 198/500 [01:52<02:50,  1.77it/s]
 40%|███▉      | 199/500 [01:52<02:59,  1.68it/s]
 40%|████      | 200/500 [01:53<03:09,  1.59it/s]
 40%|████      | 201/500 [01:54<04:22,  1.14it/s]
 40%|████      | 202/500 [01:55<04:05,  1.21it/s]
 41%|████      | 203/500 [01:56<03:24,  1.45it/s]
 41%|████      | 204/500 [01:56<03:06,  1.59it/s]
 41%|████      | 205/500 [01:56<02:23,  2.06it/s]
 41%|████      | 206/500 [01:57<02:28,  1.98it/s]
 41%|████▏     | 207/500 [01:58<03:12,  1.52it/s]
 42%|████▏     | 208/500 [01:58<02:39,  1.83it/s]
 42%|████▏     | 210/500 [01:59<01:59,  2.43it/s]
 42%|████▏     | 211/500 [01:59<02:38,  1.82it/s]
 42%|████▏     | 212/500 [02:00<02:43,  1.76it/s]
 43%|████▎     | 213/500 [02:01<03:07,  1.53it/s]
 43%|████▎     | 214/500 [02:01<02:43,  1.75it/s]
 43%|████▎     | 215/500 [02:02<02:22,  2.00it/s]
 43%|████▎     | 216/500 [02:02<02:27,  1.92it/s]
 43%|████▎     | 217/500 [02:03<02:54,  1.63it/s]
 44%|████▎     | 218/500 [02:04<02:38,  1.78it/s]
 44%|████▍     | 219/500 [02:04<02:56,  1.60it/s]
 44%|████▍     | 220/500 [02:04<02:18,  2.02it/s]
 44%|████▍     | 222/500 [02:05<02:04,  2.23it/s]
 45%|████▍     | 223/500 [02:06<02:04,  2.22it/s]
 45%|████▍     | 224/500 [02:07<03:10,  1.45it/s]
 45%|████▌     | 226/500 [02:07<01:56,  2.36it/s]
 45%|████▌     | 227/500 [02:07<01:43,  2.64it/s]
 46%|████▌     | 228/500 [02:08<01:32,  2.93it/s]
 46%|████▌     | 229/500 [02:09<02:33,  1.77it/s]
 46%|████▌     | 230/500 [02:09<02:06,  2.13it/s]
 46%|████▌     | 231/500 [02:09<01:54,  2.34it/s]slurmstepd: error: *** JOB 8619983 ON cnm027 CANCELLED AT 2025-05-20T15:24:04 ***

Resources Used

Total Memory used                        - MEM              : 15GiB
Total CPU Time                           - CPU_Time         : 02:01:36
Execution Time                           - Wall_Time        : 00:03:48
total programme cpu time                 - Total_CPU        : 55:30.743
Total_CPU / CPU_Time  (%)                - ETA              : 45%
Number of alloc CPU                      - NCPUS            : 32
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 48
Mobilized Resources x Execution Time     - R_Wall_Time      : 03:02:24
CPU_Time / R_Wall_Time (%)               - ALPHA            : 66%
Energy (Joules)                                             : 31818

