Job started on Wed May 14 16:07:27 CEST 2025
Running on node(s): cnm025
2025-05-14 16:07:29.066652: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 32 jobs
Preparing nested CV run for task 'matbench_glass'
Found 1 matching files for matbench_glass
They are ['./precomputed/matbench_glass_matminer_featurizedMM2020Struct.pkl.gz']
2025-05-14 16:07:31,436 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fbb58fe9940> object, created with modnet version 0.1.13
Preparing fold 1 ...
2025-05-14 16:07:31,445 - modnet - INFO - Loading cross NMI from 'Features_cross' file.
2025-05-14 16:07:31,521 - modnet - WARNING - Feature mismatch between precomputed `Features_cross` and `df_featurized`. Missing columns: {'<PERSON><PERSON><PERSON>|Mi<PERSON><PERSON>_deltaH_amor', '<PERSON><PERSON>ma|Mi<PERSON><PERSON>_deltaH_ss_min'}
2025-05-14 16:07:31,525 - modnet - INFO - Starting target 1/1: target ...
2025-05-14 16:07:31,525 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 16:07:44,229 - modnet - INFO - Computing optimal features...
2025-05-14 16:07:47,584 - modnet - INFO - Selected 50/216 features...
2025-05-14 16:07:50,157 - modnet - INFO - Selected 100/216 features...
2025-05-14 16:07:51,857 - modnet - INFO - Selected 150/216 features...
2025-05-14 16:07:52,676 - modnet - INFO - Selected 200/216 features...
2025-05-14 16:07:52,747 - modnet - INFO - Done with target 1/1: target.
2025-05-14 16:07:52,747 - modnet - INFO - Merging all features...
2025-05-14 16:07:52,747 - modnet - INFO - Done.
2025-05-14 16:07:52,779 - modnet - INFO - Data successfully saved as folds/matbench_glass_train_moddata_f1!
2025-05-14 16:07:52,801 - modnet - INFO - Data successfully saved as folds/matbench_glass_test_moddata_f1!
Preparing fold 2 ...
2025-05-14 16:07:52,806 - modnet - INFO - Loading cross NMI from 'Features_cross' file.
2025-05-14 16:07:52,837 - modnet - WARNING - Feature mismatch between precomputed `Features_cross` and `df_featurized`. Missing columns: {'Miedema|Miedema_deltaH_amor', 'Miedema|Miedema_deltaH_ss_min'}
2025-05-14 16:07:52,841 - modnet - INFO - Starting target 1/1: target ...
2025-05-14 16:07:52,841 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 16:08:05,512 - modnet - INFO - Computing optimal features...
2025-05-14 16:08:08,848 - modnet - INFO - Selected 50/217 features...
2025-05-14 16:08:11,416 - modnet - INFO - Selected 100/217 features...
2025-05-14 16:08:13,120 - modnet - INFO - Selected 150/217 features...
2025-05-14 16:08:13,951 - modnet - INFO - Selected 200/217 features...
2025-05-14 16:08:14,028 - modnet - INFO - Done with target 1/1: target.
2025-05-14 16:08:14,029 - modnet - INFO - Merging all features...
2025-05-14 16:08:14,029 - modnet - INFO - Done.
2025-05-14 16:08:14,059 - modnet - INFO - Data successfully saved as folds/matbench_glass_train_moddata_f2!
2025-05-14 16:08:14,081 - modnet - INFO - Data successfully saved as folds/matbench_glass_test_moddata_f2!
Preparing fold 3 ...
2025-05-14 16:08:14,086 - modnet - INFO - Loading cross NMI from 'Features_cross' file.
2025-05-14 16:08:14,116 - modnet - WARNING - Feature mismatch between precomputed `Features_cross` and `df_featurized`. Missing columns: {'Miedema|Miedema_deltaH_amor', 'Miedema|Miedema_deltaH_ss_min'}
2025-05-14 16:08:14,119 - modnet - INFO - Starting target 1/1: target ...
2025-05-14 16:08:14,119 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 16:08:26,653 - modnet - INFO - Computing optimal features...
2025-05-14 16:08:29,946 - modnet - INFO - Selected 50/215 features...
2025-05-14 16:08:32,467 - modnet - INFO - Selected 100/215 features...
2025-05-14 16:08:34,131 - modnet - INFO - Selected 150/215 features...
2025-05-14 16:08:34,925 - modnet - INFO - Selected 200/215 features...
2025-05-14 16:08:34,987 - modnet - INFO - Done with target 1/1: target.
2025-05-14 16:08:34,987 - modnet - INFO - Merging all features...
2025-05-14 16:08:34,988 - modnet - INFO - Done.
2025-05-14 16:08:35,118 - modnet - INFO - Data successfully saved as folds/matbench_glass_train_moddata_f3!
2025-05-14 16:08:35,143 - modnet - INFO - Data successfully saved as folds/matbench_glass_test_moddata_f3!
Preparing fold 4 ...
2025-05-14 16:08:35,148 - modnet - INFO - Loading cross NMI from 'Features_cross' file.
2025-05-14 16:08:35,178 - modnet - WARNING - Feature mismatch between precomputed `Features_cross` and `df_featurized`. Missing columns: {'Miedema|Miedema_deltaH_amor', 'Miedema|Miedema_deltaH_ss_min'}
2025-05-14 16:08:35,181 - modnet - INFO - Starting target 1/1: target ...
2025-05-14 16:08:35,181 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 16:08:47,875 - modnet - INFO - Computing optimal features...
2025-05-14 16:08:51,248 - modnet - INFO - Selected 50/218 features...
2025-05-14 16:08:53,854 - modnet - INFO - Selected 100/218 features...
2025-05-14 16:08:55,591 - modnet - INFO - Selected 150/218 features...
2025-05-14 16:08:56,446 - modnet - INFO - Selected 200/218 features...
2025-05-14 16:08:56,532 - modnet - INFO - Done with target 1/1: target.
2025-05-14 16:08:56,532 - modnet - INFO - Merging all features...
2025-05-14 16:08:56,532 - modnet - INFO - Done.
2025-05-14 16:08:56,563 - modnet - INFO - Data successfully saved as folds/matbench_glass_train_moddata_f4!
2025-05-14 16:08:56,584 - modnet - INFO - Data successfully saved as folds/matbench_glass_test_moddata_f4!
Preparing fold 5 ...
2025-05-14 16:08:56,589 - modnet - INFO - Loading cross NMI from 'Features_cross' file.
2025-05-14 16:08:56,619 - modnet - WARNING - Feature mismatch between precomputed `Features_cross` and `df_featurized`. Missing columns: {'Miedema|Miedema_deltaH_amor', 'Miedema|Miedema_deltaH_ss_min'}
2025-05-14 16:08:56,623 - modnet - INFO - Starting target 1/1: target ...
2025-05-14 16:08:56,623 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 16:09:09,322 - modnet - INFO - Computing optimal features...
2025-05-14 16:09:12,669 - modnet - INFO - Selected 50/218 features...
2025-05-14 16:09:15,250 - modnet - INFO - Selected 100/218 features...
2025-05-14 16:09:16,972 - modnet - INFO - Selected 150/218 features...
2025-05-14 16:09:17,821 - modnet - INFO - Selected 200/218 features...
2025-05-14 16:09:17,906 - modnet - INFO - Done with target 1/1: target.
2025-05-14 16:09:17,906 - modnet - INFO - Merging all features...
2025-05-14 16:09:17,906 - modnet - INFO - Done.
2025-05-14 16:09:17,936 - modnet - INFO - Data successfully saved as folds/matbench_glass_train_moddata_f5!
2025-05-14 16:09:17,958 - modnet - INFO - Data successfully saved as folds/matbench_glass_test_moddata_f5!
Training fold 1 ...
Processing fold 1 ...
2025-05-14 16:09:17.965043: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:17.965457: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:17.965484: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:17.965522: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:17.965807: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:18,064 - modnet - INFO - Targets:
2025-05-14 16:09:18,064 - modnet - INFO - 1)target: classification
2025-05-14 16:09:18,499 - modnet - INFO - Multiprocessing on 32 cores. Total of 128 cores available.
2025-05-14 16:09:18,499 - modnet - INFO - Generation number 0

  0%|          | 0/250 [00:00<?, ?it/s]2025-05-14 16:09:19.313891: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.363012: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.365746: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.365748: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.368523: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.389804: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.403814: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.417408: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.435575: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.444460: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.458099: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.467197: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.507540: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.507535: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.515571: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.539215: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.560975: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.585305: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.591827: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.592304: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.592303: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.655531: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.655534: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.662617: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.662617: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.678794: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.715621: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.739956: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.739957: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.744140: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.781544: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:19.809960: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 16:09:22.284808: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.285276: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:22.285308: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:22.285341: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:22.285683: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.369656: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.370117: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:22.370152: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:22.370186: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:22.370492: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.419534: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.420005: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:22.420035: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:22.420068: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:22.420384: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.510632: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.511596: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:22.511627: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:22.511660: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:22.512002: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.527748: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.528173: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:22.528200: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:22.528234: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:22.528707: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.576039: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.576423: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:22.576451: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:22.576482: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:22.576803: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.628830: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.629242: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:22.629271: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:22.629302: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:22.629613: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.669418: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:22.674691: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:22.678490: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.678881: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:22.678908: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:22.678939: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:22.679186: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.682656: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:22.687638: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:22.688622: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:22.701650: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:22.730478: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.730851: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:22.730877: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:22.730909: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:22.731175: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.787929: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.788927: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:22.788968: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:22.789004: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:22.789516: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.812375: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:22.826671: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:22.833927: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:22.840701: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:22.843434: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.844330: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:22.844357: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:22.844388: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:22.844744: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.847663: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:22.853643: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:22.881553: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.881965: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:22.881992: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:22.882022: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:22.882318: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.913199: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:22.926217: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.926663: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:22.926696: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:22.926728: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:22.927062: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.927637: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:22.938194: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:22.951654: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:22.969557: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.970006: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:22.970042: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:22.970076: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:22.970393: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:22.993080: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.006627: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.022422: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.022879: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.022907: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.022940: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.023199: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.085384: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.086021: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.086051: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.086085: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.086588: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.086684: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.100650: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.114548: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.114983: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.115009: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.115042: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.115347: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.152067: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.164852: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.165275: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.165302: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.165332: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.165636: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.165649: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.176872: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.189667: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.205794: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.207742: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.243202: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.243631: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.243660: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.243691: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.243974: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.253375: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.266624: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.298058: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.298567: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.298605: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.298639: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.298985: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.311188: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.312944: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.381597: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.384546: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.385302: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.385331: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.385362: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.385653: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.394654: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.406856: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.420662: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.422825: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.423297: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.423324: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.423358: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.423648: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.425601: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.427226: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.440817: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.441249: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.441276: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.441308: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.441557: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.490121: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.490625: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.490654: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.490689: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.490980: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.529118: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.537325: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.537812: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.537846: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.537880: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.538199: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.542660: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.580956: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.581485: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.581544: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.581588: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.581936: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.586343: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.599666: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.637488: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.637958: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.637986: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.638020: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.638322: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.677990: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.679244: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.679730: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.679757: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.679794: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.680083: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.691646: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.714273: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.715982: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.725062: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.726798: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.741316: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.741863: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.741893: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.741928: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.742337: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.767277: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.768893: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.802041: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.802521: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.802554: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.802587: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.802871: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.828948: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.830683: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.856996: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.857537: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.857595: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.857640: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.857971: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.872918: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.874722: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.907048: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.908769: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:23.927766: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.928386: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 16:09:23.928422: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 16:09:23.928457: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 16:09:23.928842: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 16:09:23.943865: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:23.945440: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:24.045898: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:24.048022: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:24.062593: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:24.064231: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:24.137652: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:24.139489: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:09:24.221798: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:09:24.223902: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz

  0%|          | 1/250 [00:19<1:20:38, 19.43s/it]
  1%|          | 2/250 [00:20<35:18,  8.54s/it]  
  1%|          | 3/250 [00:20<19:23,  4.71s/it]
  2%|▏         | 4/250 [00:21<12:41,  3.09s/it]
  2%|▏         | 5/250 [00:21<08:21,  2.05s/it]
  3%|▎         | 7/250 [00:21<04:42,  1.16s/it]
  3%|▎         | 8/250 [00:22<03:37,  1.12it/s]
  4%|▎         | 9/250 [00:23<04:01,  1.00s/it]
  4%|▍         | 10/250 [00:23<03:21,  1.19it/s]
  4%|▍         | 11/250 [00:24<03:26,  1.16it/s]
  5%|▍         | 12/250 [00:24<02:40,  1.48it/s]
  5%|▌         | 13/250 [00:25<02:01,  1.95it/s]
  6%|▌         | 14/250 [00:26<02:44,  1.44it/s]
  6%|▌         | 15/250 [00:26<02:43,  1.43it/s]
  6%|▋         | 16/250 [00:27<02:29,  1.57it/s]
  7%|▋         | 17/250 [00:28<02:33,  1.52it/s]
  7%|▋         | 18/250 [00:28<02:02,  1.90it/s]
  8%|▊         | 19/250 [00:32<06:25,  1.67s/it]
  8%|▊         | 20/250 [00:33<05:31,  1.44s/it]
  8%|▊         | 21/250 [00:34<04:35,  1.20s/it]
  9%|▉         | 22/250 [00:35<04:42,  1.24s/it]
  9%|▉         | 23/250 [00:36<04:10,  1.10s/it]
 10%|▉         | 24/250 [00:36<03:16,  1.15it/s]
 10%|█         | 25/250 [00:37<03:17,  1.14it/s]
 10%|█         | 26/250 [00:37<02:26,  1.53it/s]
 11%|█         | 27/250 [00:38<02:16,  1.63it/s]
 11%|█         | 28/250 [00:40<04:11,  1.13s/it]
 12%|█▏        | 29/250 [00:40<03:09,  1.16it/s]
 12%|█▏        | 31/250 [00:41<02:07,  1.72it/s]
 13%|█▎        | 32/250 [00:42<03:09,  1.15it/s]
 13%|█▎        | 33/250 [00:44<03:45,  1.04s/it]
 14%|█▎        | 34/250 [00:45<03:25,  1.05it/s]
 14%|█▍        | 35/250 [00:45<03:10,  1.13it/s]
 15%|█▍        | 37/250 [00:47<02:37,  1.36it/s]
 15%|█▌        | 38/250 [00:47<02:11,  1.61it/s]
 16%|█▌        | 39/250 [00:47<02:06,  1.67it/s]
 16%|█▌        | 40/250 [00:48<01:43,  2.03it/s]
 16%|█▋        | 41/250 [00:48<01:46,  1.96it/s]
 17%|█▋        | 42/250 [00:49<01:49,  1.89it/s]
 17%|█▋        | 43/250 [00:49<01:43,  2.01it/s]
 18%|█▊        | 45/250 [00:49<01:05,  3.12it/s]
 18%|█▊        | 46/250 [00:50<01:10,  2.89it/s]
 19%|█▉        | 47/250 [00:50<01:01,  3.31it/s]
 19%|█▉        | 48/250 [00:51<01:22,  2.45it/s]
 20%|█▉        | 49/250 [00:51<01:10,  2.85it/s]
 20%|██        | 50/250 [00:51<01:03,  3.16it/s]
 20%|██        | 51/250 [00:52<01:14,  2.67it/s]
 21%|██        | 52/250 [00:53<01:55,  1.72it/s]
 21%|██        | 53/250 [00:54<02:49,  1.16it/s]
 22%|██▏       | 54/250 [00:54<02:13,  1.47it/s]
 22%|██▏       | 55/250 [00:55<02:11,  1.48it/s]
 23%|██▎       | 57/250 [00:55<01:27,  2.21it/s]
 23%|██▎       | 58/250 [00:57<02:12,  1.45it/s]
 24%|██▎       | 59/250 [00:59<03:29,  1.09s/it]
 24%|██▍       | 60/250 [01:01<03:56,  1.24s/it]
 24%|██▍       | 61/250 [01:01<03:10,  1.01s/it]
 25%|██▌       | 63/250 [01:01<01:49,  1.71it/s]
 26%|██▌       | 64/250 [01:02<01:48,  1.72it/s]
 26%|██▌       | 65/250 [01:02<01:35,  1.95it/s]
 26%|██▋       | 66/250 [01:03<01:43,  1.78it/s]
 27%|██▋       | 68/250 [01:04<01:34,  1.93it/s]
 28%|██▊       | 69/250 [01:06<02:32,  1.19it/s]
 28%|██▊       | 70/250 [01:07<03:14,  1.08s/it]
 28%|██▊       | 71/250 [01:08<02:39,  1.12it/s]
 29%|██▉       | 72/250 [01:08<02:12,  1.34it/s]
 29%|██▉       | 73/250 [01:08<01:49,  1.62it/s]
 30%|██▉       | 74/250 [01:09<01:26,  2.03it/s]
 30%|███       | 75/250 [01:09<01:34,  1.84it/s]
 30%|███       | 76/250 [01:10<01:21,  2.13it/s]
 31%|███       | 77/250 [01:10<01:27,  1.99it/s]
 31%|███       | 78/250 [01:11<01:22,  2.10it/s]
 32%|███▏      | 79/250 [01:11<01:09,  2.47it/s]
 32%|███▏      | 80/250 [01:13<02:22,  1.20it/s]
 32%|███▏      | 81/250 [01:13<02:01,  1.39it/s]
 33%|███▎      | 82/250 [01:15<02:39,  1.05it/s]
 33%|███▎      | 83/250 [01:15<02:08,  1.30it/s]
 34%|███▎      | 84/250 [01:16<02:11,  1.26it/s]
 34%|███▍      | 85/250 [01:16<01:37,  1.69it/s]
 34%|███▍      | 86/250 [01:16<01:17,  2.12it/s]
 35%|███▍      | 87/250 [01:16<01:04,  2.54it/s]
 35%|███▌      | 88/250 [01:17<01:16,  2.11it/s]
 36%|███▌      | 89/250 [01:19<02:06,  1.27it/s]
 36%|███▌      | 90/250 [01:19<01:34,  1.70it/s]
 36%|███▋      | 91/250 [01:20<02:09,  1.22it/s]
 37%|███▋      | 93/250 [01:20<01:21,  1.93it/s]
 38%|███▊      | 94/250 [01:21<01:28,  1.77it/s]
 38%|███▊      | 96/250 [01:21<00:54,  2.84it/s]
 39%|███▉      | 97/250 [01:22<01:24,  1.81it/s]
 39%|███▉      | 98/250 [01:24<01:46,  1.43it/s]
 40%|███▉      | 99/250 [01:24<01:35,  1.59it/s]
 40%|████      | 100/250 [01:24<01:16,  1.97it/s]
 41%|████      | 102/250 [01:25<01:13,  2.03it/s]
 41%|████      | 103/250 [01:27<02:04,  1.18it/s]
 42%|████▏     | 104/250 [01:27<01:37,  1.50it/s]
 42%|████▏     | 106/250 [01:27<00:59,  2.44it/s]
 43%|████▎     | 107/250 [01:28<00:57,  2.47it/s]
 43%|████▎     | 108/250 [01:28<00:47,  3.00it/s]
 44%|████▎     | 109/250 [01:29<01:09,  2.02it/s]
 44%|████▍     | 110/250 [01:29<00:56,  2.46it/s]
 44%|████▍     | 111/250 [01:29<00:58,  2.38it/s]
 45%|████▍     | 112/250 [01:30<01:23,  1.65it/s]
 45%|████▌     | 113/250 [01:31<01:09,  1.98it/s]
 46%|████▌     | 114/250 [01:32<01:40,  1.35it/s]
 46%|████▌     | 115/250 [01:32<01:16,  1.75it/s]
 46%|████▋     | 116/250 [01:33<01:38,  1.36it/s]
 47%|████▋     | 117/250 [01:34<01:54,  1.16it/s]
 48%|████▊     | 119/250 [01:35<01:14,  1.75it/s]
 48%|████▊     | 120/250 [01:38<02:49,  1.30s/it]
 48%|████▊     | 121/250 [01:39<02:08,  1.00it/s]
 49%|████▉     | 122/250 [01:40<02:10,  1.02s/it]
 49%|████▉     | 123/250 [01:40<01:52,  1.13it/s]
 50%|████▉     | 124/250 [01:40<01:25,  1.47it/s]
 50%|█████     | 125/250 [01:42<02:01,  1.03it/s]
 51%|█████     | 127/250 [01:42<01:12,  1.69it/s]
 52%|█████▏    | 129/250 [01:44<01:13,  1.64it/s]
 52%|█████▏    | 130/250 [01:44<01:05,  1.83it/s]
 52%|█████▏    | 131/250 [01:45<01:28,  1.35it/s]
 53%|█████▎    | 132/250 [01:46<01:24,  1.40it/s]
 53%|█████▎    | 133/250 [01:55<05:35,  2.87s/it]
 54%|█████▎    | 134/250 [01:57<05:01,  2.60s/it]
 54%|█████▍    | 135/250 [01:58<04:15,  2.22s/it]
 54%|█████▍    | 136/250 [01:59<03:23,  1.78s/it]
 55%|█████▍    | 137/250 [02:02<04:29,  2.38s/it]
 55%|█████▌    | 138/250 [02:03<03:19,  1.78s/it]
 56%|█████▌    | 139/250 [02:03<02:37,  1.42s/it]
 56%|█████▌    | 140/250 [02:06<03:14,  1.76s/it]
 56%|█████▋    | 141/250 [02:06<02:23,  1.32s/it]
 57%|█████▋    | 142/250 [02:07<01:55,  1.07s/it]
 57%|█████▋    | 143/250 [02:08<01:54,  1.07s/it]
 58%|█████▊    | 144/250 [02:10<02:29,  1.41s/it]
 58%|█████▊    | 145/250 [02:11<02:08,  1.23s/it]
 58%|█████▊    | 146/250 [02:12<02:21,  1.36s/it]
 59%|█████▉    | 147/250 [02:13<01:42,  1.00it/s]
 59%|█████▉    | 148/250 [02:14<02:03,  1.21s/it]
 60%|█████▉    | 149/250 [02:19<03:37,  2.15s/it]
 60%|██████    | 150/250 [02:19<02:34,  1.54s/it]
 60%|██████    | 151/250 [02:19<01:55,  1.16s/it]
 61%|██████    | 152/250 [02:20<01:36,  1.01it/s]
 61%|██████    | 153/250 [02:20<01:26,  1.13it/s]
 62%|██████▏   | 154/250 [02:20<01:02,  1.54it/s]
 62%|██████▏   | 155/250 [02:21<00:49,  1.93it/s]
 62%|██████▏   | 156/250 [02:21<00:46,  2.01it/s]
 63%|██████▎   | 157/250 [02:21<00:35,  2.60it/s]
 63%|██████▎   | 158/250 [02:21<00:28,  3.18it/s]
 64%|██████▎   | 159/250 [02:22<00:46,  1.95it/s]
 64%|██████▍   | 161/250 [02:23<00:46,  1.93it/s]
 65%|██████▍   | 162/250 [02:24<00:40,  2.17it/s]
 65%|██████▌   | 163/250 [02:24<00:38,  2.27it/s]
 66%|██████▌   | 164/250 [02:24<00:38,  2.22it/s]
 66%|██████▌   | 165/250 [02:26<01:04,  1.32it/s]
 66%|██████▋   | 166/250 [02:27<01:00,  1.39it/s]
 67%|██████▋   | 167/250 [02:28<01:09,  1.19it/s]
 67%|██████▋   | 168/250 [02:29<01:12,  1.13it/s]
 68%|██████▊   | 169/250 [02:30<01:27,  1.08s/it]
 68%|██████▊   | 170/250 [02:31<01:11,  1.11it/s]
 68%|██████▊   | 171/250 [02:31<00:52,  1.50it/s]
 69%|██████▉   | 172/250 [02:32<01:06,  1.17it/s]
 69%|██████▉   | 173/250 [02:32<00:50,  1.52it/s]
 70%|██████▉   | 174/250 [02:35<01:45,  1.39s/it]
 70%|███████   | 175/250 [02:37<01:36,  1.28s/it]
 70%|███████   | 176/250 [02:37<01:17,  1.04s/it]
 71%|███████   | 178/250 [02:40<01:25,  1.19s/it]
 72%|███████▏  | 179/250 [02:41<01:28,  1.25s/it]
 72%|███████▏  | 180/250 [02:43<01:42,  1.46s/it]
 72%|███████▏  | 181/250 [02:44<01:23,  1.21s/it]
 73%|███████▎  | 182/250 [02:46<01:39,  1.47s/it]
 73%|███████▎  | 183/250 [02:47<01:37,  1.45s/it]
 74%|███████▎  | 184/250 [02:47<01:09,  1.06s/it]
 74%|███████▍  | 185/250 [02:48<01:03,  1.03it/s]
 74%|███████▍  | 186/250 [02:49<01:00,  1.07it/s]
 75%|███████▍  | 187/250 [02:49<00:49,  1.28it/s]
 75%|███████▌  | 188/250 [02:50<00:40,  1.53it/s]
 76%|███████▌  | 189/250 [02:50<00:39,  1.55it/s]
 76%|███████▌  | 190/250 [02:50<00:29,  2.06it/s]
 76%|███████▋  | 191/250 [02:51<00:26,  2.25it/s]
 77%|███████▋  | 192/250 [02:51<00:20,  2.81it/s]
 77%|███████▋  | 193/250 [02:53<00:43,  1.32it/s]
 78%|███████▊  | 194/250 [02:54<00:58,  1.04s/it]
 78%|███████▊  | 195/250 [02:55<00:49,  1.11it/s]
 78%|███████▊  | 196/250 [02:55<00:36,  1.47it/s]
 79%|███████▉  | 198/250 [02:56<00:32,  1.59it/s]
 80%|███████▉  | 199/250 [02:57<00:30,  1.65it/s]
 80%|████████  | 200/250 [02:58<00:33,  1.51it/s]
 80%|████████  | 201/250 [02:59<00:40,  1.22it/s]
 81%|████████  | 202/250 [03:01<00:52,  1.09s/it]
 81%|████████  | 203/250 [03:02<01:00,  1.29s/it]
 82%|████████▏ | 204/250 [03:07<01:37,  2.12s/it]
 82%|████████▏ | 205/250 [03:08<01:19,  1.78s/it]
 82%|████████▏ | 206/250 [03:08<01:06,  1.50s/it]
 83%|████████▎ | 207/250 [03:09<00:48,  1.13s/it]
 84%|████████▎ | 209/250 [03:10<00:42,  1.04s/it]
 84%|████████▍ | 210/250 [03:11<00:41,  1.03s/it]
 84%|████████▍ | 211/250 [03:12<00:34,  1.15it/s]
 85%|████████▌ | 213/250 [03:13<00:29,  1.25it/s]
 86%|████████▌ | 214/250 [03:14<00:23,  1.51it/s]
 86%|████████▌ | 215/250 [03:14<00:18,  1.91it/s]
 87%|████████▋ | 217/250 [03:14<00:13,  2.40it/s]
 87%|████████▋ | 218/250 [03:16<00:24,  1.29it/s]
 88%|████████▊ | 219/250 [03:18<00:32,  1.05s/it]
 88%|████████▊ | 220/250 [03:19<00:29,  1.01it/s]
 88%|████████▊ | 221/250 [03:23<00:50,  1.73s/it]
 89%|████████▉ | 222/250 [03:23<00:36,  1.31s/it]
 89%|████████▉ | 223/250 [03:23<00:29,  1.10s/it]
 90%|████████▉ | 224/250 [03:25<00:31,  1.21s/it]
 90%|█████████ | 225/250 [03:27<00:38,  1.56s/it]
 90%|█████████ | 226/250 [03:28<00:28,  1.20s/it]
 91%|█████████ | 227/250 [03:30<00:37,  1.62s/it]
 91%|█████████ | 228/250 [03:34<00:47,  2.17s/it]
 92%|█████████▏| 229/250 [03:34<00:35,  1.68s/it]
 92%|█████████▏| 230/250 [03:35<00:26,  1.30s/it]
 92%|█████████▏| 231/250 [03:36<00:27,  1.44s/it]
 93%|█████████▎| 232/250 [03:37<00:19,  1.08s/it]
 93%|█████████▎| 233/250 [03:38<00:22,  1.30s/it]
 94%|█████████▎| 234/250 [03:39<00:18,  1.15s/it]
 94%|█████████▍| 235/250 [03:41<00:19,  1.27s/it]
 94%|█████████▍| 236/250 [03:43<00:23,  1.66s/it]
 95%|█████████▍| 237/250 [03:48<00:33,  2.56s/it]
 95%|█████████▌| 238/250 [03:50<00:29,  2.45s/it]
 96%|█████████▌| 239/250 [03:53<00:28,  2.59s/it]
 96%|█████████▌| 240/250 [03:53<00:18,  1.85s/it]
 96%|█████████▋| 241/250 [03:53<00:12,  1.34s/it]
 97%|█████████▋| 242/250 [03:54<00:09,  1.18s/it]
 97%|█████████▋| 243/250 [03:55<00:08,  1.16s/it]
 98%|█████████▊| 244/250 [03:57<00:07,  1.19s/it]
 98%|█████████▊| 245/250 [03:57<00:04,  1.02it/s]
 98%|█████████▊| 246/250 [04:00<00:06,  1.60s/it]
 99%|█████████▉| 247/250 [04:03<00:05,  1.86s/it]
 99%|█████████▉| 248/250 [04:05<00:03,  1.88s/it]
100%|█████████▉| 249/250 [04:05<00:01,  1.40s/it]
100%|██████████| 250/250 [04:26<00:00,  7.38s/it]
100%|██████████| 250/250 [04:26<00:00,  1.07s/it]
2025-05-14 16:13:45,192 - modnet - INFO - Loss per individual: ind 0: -0.914 	ind 1: -0.878 	ind 2: -0.904 	ind 3: -0.896 	ind 4: -0.907 	ind 5: -0.901 	ind 6: -0.876 	ind 7: -0.839 	ind 8: -0.789 	ind 9: -0.901 	ind 10: -0.832 	ind 11: -0.510 	ind 12: -0.495 	ind 13: -0.910 	ind 14: -0.911 	ind 15: -0.736 	ind 16: -0.495 	ind 17: -0.906 	ind 18: -0.902 	ind 19: -0.503 	ind 20: -0.910 	ind 21: -0.896 	ind 22: -0.570 	ind 23: -0.501 	ind 24: -0.903 	ind 25: -0.500 	ind 26: -0.908 	ind 27: -0.914 	ind 28: -0.503 	ind 29: -0.886 	ind 30: -0.885 	ind 31: -0.900 	ind 32: -0.895 	ind 33: -0.689 	ind 34: -0.895 	ind 35: -0.728 	ind 36: -0.534 	ind 37: -0.908 	ind 38: -0.897 	ind 39: -0.905 	ind 40: -0.911 	ind 41: -0.906 	ind 42: -0.895 	ind 43: -0.472 	ind 44: -0.915 	ind 45: -0.915 	ind 46: -0.898 	ind 47: -0.497 	ind 48: -0.901 	ind 49: -0.900 	
2025-05-14 16:13:45,193 - modnet - INFO - Generation number 1

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:02<12:23,  2.99s/it]
  1%|          | 2/250 [00:03<05:44,  1.39s/it]
  1%|          | 3/250 [00:03<03:19,  1.24it/s]
  2%|▏         | 4/250 [00:03<02:43,  1.50it/s]
  2%|▏         | 5/250 [00:03<01:58,  2.07it/s]
  2%|▏         | 6/250 [00:04<01:31,  2.66it/s]
  3%|▎         | 8/250 [00:05<02:03,  1.96it/s]
  4%|▎         | 9/250 [00:05<01:40,  2.40it/s]
  4%|▍         | 11/250 [00:05<01:03,  3.74it/s]
  5%|▍         | 12/250 [00:05<01:03,  3.75it/s]
  6%|▌         | 14/250 [00:06<00:50,  4.63it/s]
  6%|▌         | 15/250 [00:07<01:19,  2.96it/s]
  7%|▋         | 17/250 [00:07<00:53,  4.35it/s]
  7%|▋         | 18/250 [00:07<00:47,  4.89it/s]
  8%|▊         | 19/250 [00:07<00:47,  4.83it/s]
  8%|▊         | 20/250 [00:07<01:02,  3.70it/s]
  8%|▊         | 21/250 [00:08<01:09,  3.28it/s]
  9%|▉         | 23/250 [00:09<01:37,  2.32it/s]
 10%|▉         | 24/250 [00:09<01:27,  2.58it/s]
 10%|█         | 25/250 [00:10<01:39,  2.26it/s]
 11%|█         | 27/250 [00:10<01:16,  2.93it/s]
 12%|█▏        | 29/250 [00:10<00:55,  4.02it/s]
 12%|█▏        | 30/250 [00:11<00:52,  4.19it/s]
 12%|█▏        | 31/250 [00:11<01:01,  3.57it/s]
 13%|█▎        | 32/250 [00:12<01:47,  2.02it/s]
 13%|█▎        | 33/250 [00:12<01:25,  2.55it/s]
 14%|█▍        | 35/250 [00:13<01:23,  2.58it/s]
 15%|█▌        | 38/250 [00:14<01:03,  3.32it/s]
 16%|█▌        | 39/250 [00:14<01:01,  3.45it/s]
 16%|█▌        | 40/250 [00:14<00:52,  3.99it/s]
 17%|█▋        | 42/250 [00:14<00:38,  5.39it/s]
 17%|█▋        | 43/250 [00:15<00:42,  4.90it/s]
 18%|█▊        | 44/250 [00:15<00:39,  5.26it/s]
 18%|█▊        | 45/250 [00:15<00:41,  4.88it/s]
 19%|█▉        | 47/250 [00:15<00:30,  6.70it/s]
 20%|█▉        | 49/250 [00:15<00:22,  8.88it/s]
 20%|██        | 51/250 [00:16<00:30,  6.60it/s]
 21%|██        | 52/250 [00:16<00:29,  6.81it/s]
 21%|██        | 53/250 [00:16<00:31,  6.29it/s]
 22%|██▏       | 55/250 [00:16<00:23,  8.28it/s]
 23%|██▎       | 57/250 [00:17<00:42,  4.58it/s]
 23%|██▎       | 58/250 [00:17<00:41,  4.63it/s]
 24%|██▎       | 59/250 [00:18<01:18,  2.45it/s]
 24%|██▍       | 60/250 [00:19<01:23,  2.27it/s]
 24%|██▍       | 61/250 [00:19<01:17,  2.43it/s]
 25%|██▍       | 62/250 [00:19<01:06,  2.84it/s]
 26%|██▌       | 64/250 [00:19<00:46,  3.99it/s]
 26%|██▌       | 65/250 [00:20<00:59,  3.11it/s]
 26%|██▋       | 66/250 [00:20<00:54,  3.35it/s]
 27%|██▋       | 67/250 [00:20<00:47,  3.86it/s]
 28%|██▊       | 69/250 [00:21<00:39,  4.53it/s]
 28%|██▊       | 70/250 [00:21<00:37,  4.81it/s]
 28%|██▊       | 71/250 [00:21<00:38,  4.68it/s]
 29%|██▉       | 73/250 [00:21<00:28,  6.18it/s]
 30%|██▉       | 74/250 [00:21<00:28,  6.14it/s]
 30%|███       | 75/250 [00:22<00:31,  5.59it/s]
 30%|███       | 76/250 [00:24<02:00,  1.44it/s]
 31%|███       | 78/250 [00:24<01:16,  2.26it/s]
 32%|███▏      | 79/250 [00:24<01:12,  2.36it/s]
 32%|███▏      | 81/250 [00:25<00:49,  3.44it/s]
 33%|███▎      | 83/250 [00:25<00:34,  4.81it/s]
 34%|███▎      | 84/250 [00:25<00:31,  5.24it/s]
 34%|███▍      | 86/250 [00:25<00:24,  6.57it/s]
 35%|███▍      | 87/250 [00:25<00:37,  4.38it/s]
 35%|███▌      | 88/250 [00:26<00:53,  3.00it/s]
 36%|███▌      | 89/250 [00:26<00:50,  3.22it/s]
 36%|███▌      | 90/250 [00:27<00:41,  3.87it/s]
 36%|███▋      | 91/250 [00:28<01:19,  2.00it/s]
 37%|███▋      | 92/250 [00:28<01:29,  1.76it/s]
 37%|███▋      | 93/250 [00:29<01:15,  2.09it/s]
 38%|███▊      | 95/250 [00:29<00:48,  3.21it/s]
 39%|███▉      | 97/250 [00:29<00:33,  4.54it/s]
 39%|███▉      | 98/250 [00:29<00:34,  4.38it/s]
 40%|███▉      | 99/250 [00:30<00:44,  3.43it/s]
 40%|████      | 100/250 [00:30<00:46,  3.26it/s]
 40%|████      | 101/250 [00:31<01:10,  2.10it/s]
 41%|████      | 102/250 [00:32<01:19,  1.87it/s]
 41%|████      | 103/250 [00:32<01:08,  2.14it/s]
 42%|████▏     | 104/250 [00:32<00:54,  2.69it/s]
 42%|████▏     | 105/250 [00:32<00:43,  3.33it/s]
 42%|████▏     | 106/250 [00:32<00:36,  3.94it/s]
 43%|████▎     | 107/250 [00:33<00:30,  4.77it/s]
 43%|████▎     | 108/250 [00:33<00:26,  5.38it/s]
 44%|████▎     | 109/250 [00:33<00:36,  3.84it/s]
 44%|████▍     | 110/250 [00:33<00:30,  4.61it/s]
 45%|████▍     | 112/250 [00:34<00:29,  4.69it/s]
 46%|████▌     | 114/250 [00:34<00:24,  5.62it/s]
 46%|████▌     | 115/250 [00:35<00:39,  3.41it/s]
 46%|████▋     | 116/250 [00:35<00:34,  3.91it/s]
 47%|████▋     | 118/250 [00:35<00:26,  5.05it/s]
 48%|████▊     | 119/250 [00:36<00:44,  2.95it/s]
 48%|████▊     | 121/250 [00:37<00:54,  2.37it/s]
 50%|████▉     | 124/250 [00:37<00:39,  3.15it/s]
 50%|█████     | 125/250 [00:38<00:43,  2.89it/s]
 50%|█████     | 126/250 [00:38<00:41,  3.01it/s]
 51%|█████     | 127/250 [00:39<00:40,  3.05it/s]
 51%|█████     | 128/250 [00:39<00:46,  2.64it/s]
 52%|█████▏    | 129/250 [00:39<00:39,  3.09it/s]
 52%|█████▏    | 131/250 [00:39<00:27,  4.28it/s]
 53%|█████▎    | 132/250 [00:41<01:09,  1.70it/s]
 53%|█████▎    | 133/250 [00:42<01:08,  1.70it/s]
 54%|█████▎    | 134/250 [00:42<00:58,  1.99it/s]
 54%|█████▍    | 135/250 [00:43<01:07,  1.70it/s]
 55%|█████▍    | 137/250 [00:43<00:42,  2.67it/s]
 55%|█████▌    | 138/250 [00:43<00:41,  2.70it/s]
 56%|█████▌    | 139/250 [00:44<00:35,  3.14it/s]
 56%|█████▌    | 140/250 [00:44<00:40,  2.71it/s]
 57%|█████▋    | 142/250 [00:45<00:31,  3.43it/s]
 57%|█████▋    | 143/250 [00:45<00:30,  3.55it/s]
 58%|█████▊    | 145/250 [00:45<00:31,  3.30it/s]
 58%|█████▊    | 146/250 [00:46<00:27,  3.83it/s]
 59%|█████▉    | 147/250 [00:46<00:24,  4.15it/s]
 59%|█████▉    | 148/250 [00:46<00:36,  2.81it/s]
 60%|██████    | 151/250 [00:47<00:19,  5.09it/s]
 61%|██████    | 152/250 [00:47<00:17,  5.48it/s]
 61%|██████    | 153/250 [00:47<00:18,  5.11it/s]
 62%|██████▏   | 154/250 [00:47<00:21,  4.52it/s]
 62%|██████▏   | 156/250 [00:47<00:14,  6.37it/s]
 64%|██████▎   | 159/250 [00:48<00:20,  4.48it/s]
 64%|██████▍   | 161/250 [00:49<00:20,  4.35it/s]
 65%|██████▌   | 163/250 [00:50<00:28,  3.04it/s]
 66%|██████▌   | 164/250 [00:50<00:24,  3.47it/s]
 66%|██████▋   | 166/250 [00:50<00:23,  3.61it/s]
 67%|██████▋   | 167/250 [00:51<00:22,  3.65it/s]
 68%|██████▊   | 169/250 [00:51<00:16,  4.87it/s]
 68%|██████▊   | 170/250 [00:51<00:17,  4.66it/s]
 68%|██████▊   | 171/250 [00:51<00:15,  5.25it/s]
 69%|██████▉   | 172/250 [00:51<00:13,  5.60it/s]
 69%|██████▉   | 173/250 [00:52<00:17,  4.45it/s]
 70%|██████▉   | 174/250 [00:52<00:15,  4.79it/s]
 70%|███████   | 176/250 [00:52<00:13,  5.32it/s]
 71%|███████   | 177/250 [00:53<00:25,  2.91it/s]
 71%|███████   | 178/250 [00:53<00:21,  3.32it/s]
 72%|███████▏  | 179/250 [00:54<00:35,  2.00it/s]
 72%|███████▏  | 180/250 [00:56<00:50,  1.39it/s]
 72%|███████▏  | 181/250 [00:56<00:40,  1.70it/s]
 73%|███████▎  | 182/250 [00:57<00:51,  1.31it/s]
 73%|███████▎  | 183/250 [00:57<00:42,  1.59it/s]
 74%|███████▎  | 184/250 [00:58<00:36,  1.81it/s]
 74%|███████▍  | 185/250 [00:59<00:52,  1.23it/s]
 74%|███████▍  | 186/250 [01:01<01:04,  1.01s/it]
 75%|███████▍  | 187/250 [01:01<00:51,  1.23it/s]
 75%|███████▌  | 188/250 [01:02<00:55,  1.12it/s]
 76%|███████▌  | 190/250 [01:03<00:36,  1.65it/s]
 76%|███████▋  | 191/250 [01:03<00:32,  1.82it/s]
 77%|███████▋  | 192/250 [01:04<00:31,  1.83it/s]
 78%|███████▊  | 194/250 [01:04<00:19,  2.89it/s]
 78%|███████▊  | 195/250 [01:04<00:20,  2.64it/s]
 79%|███████▉  | 197/250 [01:04<00:13,  3.92it/s]
 79%|███████▉  | 198/250 [01:04<00:11,  4.35it/s]
 80%|████████  | 200/250 [01:05<00:08,  5.74it/s]
 80%|████████  | 201/250 [01:05<00:12,  4.04it/s]
 81%|████████  | 202/250 [01:06<00:20,  2.31it/s]
 81%|████████  | 203/250 [01:07<00:21,  2.23it/s]
 82%|████████▏ | 204/250 [01:07<00:18,  2.49it/s]
 82%|████████▏ | 206/250 [01:07<00:11,  3.84it/s]
 83%|████████▎ | 207/250 [01:08<00:12,  3.37it/s]
 84%|████████▎ | 209/250 [01:09<00:17,  2.29it/s]
 84%|████████▍ | 211/250 [01:09<00:12,  3.08it/s]
 85%|████████▌ | 213/250 [01:10<00:11,  3.36it/s]
 86%|████████▌ | 214/250 [01:10<00:10,  3.41it/s]
 86%|████████▋ | 216/250 [01:10<00:08,  3.97it/s]
 87%|████████▋ | 217/250 [01:10<00:08,  4.10it/s]
 87%|████████▋ | 218/250 [01:11<00:07,  4.44it/s]
 88%|████████▊ | 219/250 [01:11<00:06,  4.70it/s]
 88%|████████▊ | 220/250 [01:11<00:06,  4.91it/s]
 88%|████████▊ | 221/250 [01:11<00:05,  5.37it/s]
 89%|████████▉ | 223/250 [01:11<00:03,  6.92it/s]
 90%|█████████ | 225/250 [01:12<00:05,  4.98it/s]
 90%|█████████ | 226/250 [01:12<00:05,  4.05it/s]
 91%|█████████ | 227/250 [01:13<00:08,  2.87it/s]
 92%|█████████▏| 229/250 [01:13<00:06,  3.20it/s]
 92%|█████████▏| 230/250 [01:14<00:06,  3.30it/s]
 92%|█████████▏| 231/250 [01:14<00:07,  2.43it/s]
 93%|█████████▎| 232/250 [01:15<00:09,  1.95it/s]
 93%|█████████▎| 233/250 [01:16<00:08,  1.89it/s]
 94%|█████████▎| 234/250 [01:16<00:06,  2.44it/s]
 94%|█████████▍| 235/250 [01:19<00:16,  1.11s/it]
 94%|█████████▍| 236/250 [01:19<00:13,  1.06it/s]
 95%|█████████▍| 237/250 [01:19<00:09,  1.42it/s]
 95%|█████████▌| 238/250 [01:20<00:07,  1.51it/s]
 96%|█████████▌| 240/250 [01:22<00:07,  1.42it/s]
 96%|█████████▋| 241/250 [01:22<00:05,  1.72it/s]
 97%|█████████▋| 242/250 [01:22<00:04,  1.92it/s]
 97%|█████████▋| 243/250 [01:23<00:04,  1.49it/s]
 98%|█████████▊| 244/250 [01:24<00:03,  1.70it/s]
 98%|█████████▊| 246/250 [01:24<00:01,  2.85it/s]
 99%|█████████▉| 247/250 [01:26<00:02,  1.18it/s]
 99%|█████████▉| 248/250 [01:26<00:01,  1.46it/s]
100%|█████████▉| 249/250 [01:32<00:02,  2.08s/it]
100%|██████████| 250/250 [01:45<00:00,  5.02s/it]
100%|██████████| 250/250 [01:45<00:00,  2.37it/s]
2025-05-14 16:15:30,611 - modnet - INFO - Loss per individual: ind 0: -0.498 	ind 1: -0.496 	ind 2: -0.501 	ind 3: -0.504 	ind 4: -0.460 	ind 5: -0.477 	ind 6: -0.540 	ind 7: -0.523 	ind 8: -0.500 	ind 9: -0.808 	ind 10: -0.498 	ind 11: -0.502 	ind 12: -0.500 	ind 13: -0.501 	ind 14: -0.482 	ind 15: -0.504 	ind 16: -0.500 	ind 17: -0.501 	ind 18: -0.499 	ind 19: -0.552 	ind 20: -0.910 	ind 21: -0.495 	ind 22: -0.612 	ind 23: -0.505 	ind 24: -0.521 	ind 25: -0.506 	ind 26: -0.498 	ind 27: -0.533 	ind 28: -0.901 	ind 29: -0.809 	ind 30: -0.503 	ind 31: -0.500 	ind 32: -0.777 	ind 33: -0.499 	ind 34: -0.499 	ind 35: -0.674 	ind 36: -0.502 	ind 37: -0.498 	ind 38: -0.907 	ind 39: -0.823 	ind 40: -0.888 	ind 41: -0.502 	ind 42: -0.497 	ind 43: -0.495 	ind 44: -0.502 	ind 45: -0.501 	ind 46: -0.576 	ind 47: -0.500 	ind 48: -0.490 	ind 49: -0.765 	
2025-05-14 16:15:30,612 - modnet - INFO - Generation number 2

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:05<21:27,  5.17s/it]
  1%|          | 3/250 [00:05<06:19,  1.54s/it]
  2%|▏         | 4/250 [00:06<05:25,  1.33s/it]
  2%|▏         | 5/250 [00:06<04:01,  1.01it/s]
  2%|▏         | 6/250 [00:07<02:57,  1.38it/s]
  3%|▎         | 8/250 [00:07<01:42,  2.37it/s]
  4%|▍         | 10/250 [00:07<01:07,  3.54it/s]
  4%|▍         | 11/250 [00:07<00:58,  4.12it/s]
  5%|▌         | 13/250 [00:07<00:50,  4.66it/s]
  6%|▌         | 15/250 [00:08<00:37,  6.25it/s]
  7%|▋         | 17/250 [00:08<00:43,  5.35it/s]
  8%|▊         | 19/250 [00:08<00:38,  5.94it/s]
  8%|▊         | 21/250 [00:08<00:31,  7.22it/s]
  9%|▉         | 22/250 [00:09<00:31,  7.34it/s]
  9%|▉         | 23/250 [00:09<01:01,  3.72it/s]
 10%|▉         | 24/250 [00:10<01:12,  3.11it/s]
 10%|█         | 25/250 [00:10<01:12,  3.09it/s]
 11%|█         | 27/250 [00:11<01:11,  3.14it/s]
 11%|█         | 28/250 [00:11<01:12,  3.07it/s]
 12%|█▏        | 30/250 [00:11<00:52,  4.17it/s]
 12%|█▏        | 31/250 [00:12<01:04,  3.42it/s]
 13%|█▎        | 32/250 [00:12<01:02,  3.51it/s]
 13%|█▎        | 33/250 [00:12<01:02,  3.47it/s]
 14%|█▍        | 35/250 [00:13<00:45,  4.76it/s]
 15%|█▍        | 37/250 [00:13<00:46,  4.53it/s]
 15%|█▌        | 38/250 [00:14<01:04,  3.30it/s]
 16%|█▌        | 39/250 [00:14<01:15,  2.79it/s]
 16%|█▌        | 40/250 [00:14<01:03,  3.33it/s]
 16%|█▋        | 41/250 [00:14<00:55,  3.80it/s]
 17%|█▋        | 42/250 [00:15<00:47,  4.42it/s]
 17%|█▋        | 43/250 [00:15<00:42,  4.92it/s]
 18%|█▊        | 44/250 [00:15<00:44,  4.61it/s]
 18%|█▊        | 45/250 [00:15<00:44,  4.62it/s]
 18%|█▊        | 46/250 [00:16<01:04,  3.14it/s]
 19%|█▉        | 48/250 [00:16<00:49,  4.08it/s]
 20%|██        | 50/250 [00:16<00:37,  5.34it/s]
 20%|██        | 51/250 [00:17<00:45,  4.38it/s]
 21%|██        | 52/250 [00:17<00:52,  3.74it/s]
 21%|██        | 53/250 [00:17<00:56,  3.50it/s]
 22%|██▏       | 55/250 [00:18<00:40,  4.87it/s]
 23%|██▎       | 57/250 [00:18<00:30,  6.36it/s]
 24%|██▎       | 59/250 [00:18<00:24,  7.66it/s]
 24%|██▍       | 60/250 [00:19<00:44,  4.23it/s]
 24%|██▍       | 61/250 [00:19<00:41,  4.61it/s]
 25%|██▌       | 63/250 [00:19<00:44,  4.19it/s]
 26%|██▌       | 65/250 [00:20<00:38,  4.82it/s]
 26%|██▋       | 66/250 [00:20<00:44,  4.10it/s]
 27%|██▋       | 67/250 [00:21<01:02,  2.91it/s]
 27%|██▋       | 68/250 [00:21<01:13,  2.49it/s]
 28%|██▊       | 70/250 [00:21<00:51,  3.52it/s]
 29%|██▉       | 72/250 [00:22<00:39,  4.56it/s]
 30%|██▉       | 74/250 [00:22<00:36,  4.85it/s]
 30%|███       | 75/250 [00:22<00:32,  5.39it/s]
 30%|███       | 76/250 [00:22<00:36,  4.73it/s]
 31%|███       | 78/250 [00:23<00:28,  6.04it/s]
 32%|███▏      | 80/250 [00:23<00:23,  7.18it/s]
 32%|███▏      | 81/250 [00:23<00:40,  4.21it/s]
 33%|███▎      | 83/250 [00:24<00:36,  4.52it/s]
 34%|███▎      | 84/250 [00:24<00:35,  4.70it/s]
 34%|███▍      | 86/250 [00:24<00:30,  5.41it/s]
 35%|███▍      | 87/250 [00:24<00:29,  5.55it/s]
 36%|███▌      | 89/250 [00:25<00:31,  5.08it/s]
 36%|███▌      | 90/250 [00:25<00:30,  5.21it/s]
 36%|███▋      | 91/250 [00:25<00:30,  5.18it/s]
 37%|███▋      | 92/250 [00:26<00:39,  4.03it/s]
 38%|███▊      | 94/250 [00:26<00:29,  5.24it/s]
 38%|███▊      | 96/250 [00:26<00:21,  7.00it/s]
 40%|███▉      | 99/250 [00:26<00:14, 10.13it/s]
 40%|████      | 101/250 [00:27<00:38,  3.88it/s]
 41%|████      | 102/250 [00:28<00:43,  3.38it/s]
 41%|████      | 103/250 [00:28<00:50,  2.93it/s]
 42%|████▏     | 104/250 [00:29<00:46,  3.17it/s]
 42%|████▏     | 106/250 [00:29<00:30,  4.69it/s]
 43%|████▎     | 107/250 [00:29<00:27,  5.29it/s]
 43%|████▎     | 108/250 [00:29<00:24,  5.79it/s]
 44%|████▍     | 110/250 [00:30<00:42,  3.32it/s]
 44%|████▍     | 111/250 [00:30<00:35,  3.87it/s]
 45%|████▍     | 112/250 [00:30<00:33,  4.10it/s]
 46%|████▌     | 114/250 [00:30<00:23,  5.87it/s]
 46%|████▌     | 115/250 [00:31<00:22,  5.93it/s]
 46%|████▋     | 116/250 [00:31<00:20,  6.42it/s]
 47%|████▋     | 117/250 [00:31<00:20,  6.60it/s]
 48%|████▊     | 119/250 [00:31<00:15,  8.44it/s]
 48%|████▊     | 120/250 [00:31<00:21,  6.12it/s]
 49%|████▉     | 122/250 [00:31<00:16,  7.87it/s]
 49%|████▉     | 123/250 [00:32<00:18,  6.70it/s]
 50%|████▉     | 124/250 [00:32<00:37,  3.40it/s]
 50%|█████     | 125/250 [00:33<00:34,  3.63it/s]
 50%|█████     | 126/250 [00:33<00:47,  2.61it/s]
 51%|█████     | 127/250 [00:34<01:06,  1.85it/s]
 51%|█████     | 128/250 [00:34<00:53,  2.29it/s]
 52%|█████▏    | 129/250 [00:35<00:44,  2.75it/s]
 52%|█████▏    | 131/250 [00:35<00:30,  3.84it/s]
 53%|█████▎    | 132/250 [00:35<00:36,  3.23it/s]
 54%|█████▎    | 134/250 [00:35<00:24,  4.81it/s]
 54%|█████▍    | 135/250 [00:36<00:21,  5.25it/s]
 54%|█████▍    | 136/250 [00:36<00:23,  4.91it/s]
 55%|█████▍    | 137/250 [00:37<00:38,  2.91it/s]
 55%|█████▌    | 138/250 [00:37<00:34,  3.24it/s]
 56%|█████▌    | 139/250 [00:37<00:32,  3.44it/s]
 57%|█████▋    | 142/250 [00:37<00:22,  4.80it/s]
 57%|█████▋    | 143/250 [00:38<00:39,  2.70it/s]
 58%|█████▊    | 145/250 [00:39<00:29,  3.58it/s]
 59%|█████▉    | 147/250 [00:39<00:22,  4.49it/s]
 59%|█████▉    | 148/250 [00:39<00:22,  4.48it/s]
 60%|█████▉    | 149/250 [00:39<00:23,  4.32it/s]
 60%|██████    | 150/250 [00:40<00:20,  4.81it/s]
 60%|██████    | 151/250 [00:40<00:18,  5.47it/s]
 61%|██████    | 152/250 [00:40<00:22,  4.44it/s]
 61%|██████    | 153/250 [00:40<00:19,  4.97it/s]
 62%|██████▏   | 154/250 [00:40<00:23,  4.05it/s]
 62%|██████▏   | 155/250 [00:41<00:39,  2.43it/s]
 63%|██████▎   | 157/250 [00:41<00:24,  3.80it/s]
 63%|██████▎   | 158/250 [00:42<00:26,  3.48it/s]
 64%|██████▍   | 160/250 [00:42<00:23,  3.89it/s]
 65%|██████▍   | 162/250 [00:42<00:16,  5.38it/s]
 66%|██████▌   | 164/250 [00:42<00:12,  7.10it/s]
 66%|██████▋   | 166/250 [00:43<00:17,  4.77it/s]
 67%|██████▋   | 167/250 [00:43<00:15,  5.26it/s]
 68%|██████▊   | 169/250 [00:44<00:15,  5.09it/s]
 68%|██████▊   | 170/250 [00:45<00:33,  2.39it/s]
 68%|██████▊   | 171/250 [00:45<00:27,  2.87it/s]
 69%|██████▉   | 172/250 [00:45<00:27,  2.84it/s]
 70%|██████▉   | 174/250 [00:46<00:21,  3.47it/s]
 70%|███████   | 176/250 [00:46<00:14,  4.97it/s]
 71%|███████   | 178/250 [00:47<00:18,  3.88it/s]
 72%|███████▏  | 180/250 [00:47<00:16,  4.28it/s]
 73%|███████▎  | 182/250 [00:47<00:11,  5.71it/s]
 74%|███████▎  | 184/250 [00:48<00:11,  5.75it/s]
 74%|███████▍  | 185/250 [00:48<00:11,  5.69it/s]
 75%|███████▍  | 187/250 [00:48<00:13,  4.61it/s]
 75%|███████▌  | 188/250 [00:49<00:15,  3.95it/s]
 76%|███████▌  | 189/250 [00:49<00:14,  4.09it/s]
 76%|███████▌  | 190/250 [00:49<00:14,  4.19it/s]
 76%|███████▋  | 191/250 [00:49<00:15,  3.77it/s]
 77%|███████▋  | 193/250 [00:50<00:16,  3.50it/s]
 78%|███████▊  | 194/250 [00:50<00:14,  3.74it/s]
 78%|███████▊  | 196/250 [00:51<00:15,  3.52it/s]
 79%|███████▉  | 198/250 [00:51<00:13,  3.94it/s]
 80%|███████▉  | 199/250 [00:52<00:12,  3.93it/s]
 80%|████████  | 200/250 [00:52<00:12,  4.16it/s]
 80%|████████  | 201/250 [00:52<00:11,  4.42it/s]
 81%|████████  | 202/250 [00:53<00:15,  3.05it/s]
 81%|████████  | 203/250 [00:53<00:17,  2.69it/s]
 82%|████████▏ | 205/250 [00:53<00:11,  3.89it/s]
 82%|████████▏ | 206/250 [00:54<00:12,  3.55it/s]
 83%|████████▎ | 207/250 [00:54<00:10,  3.98it/s]
 84%|████████▎ | 209/250 [00:54<00:10,  3.85it/s]
 84%|████████▍ | 210/250 [00:55<00:09,  4.14it/s]
 85%|████████▍ | 212/250 [00:55<00:06,  5.61it/s]
 85%|████████▌ | 213/250 [00:55<00:08,  4.25it/s]
 86%|████████▌ | 214/250 [00:55<00:09,  3.75it/s]
 86%|████████▋ | 216/250 [00:56<00:08,  4.00it/s]
 87%|████████▋ | 217/250 [00:57<00:13,  2.49it/s]
 88%|████████▊ | 219/250 [00:57<00:08,  3.47it/s]
 88%|████████▊ | 220/250 [00:58<00:09,  3.11it/s]
 88%|████████▊ | 221/250 [00:58<00:08,  3.43it/s]
 89%|████████▉ | 222/250 [00:58<00:07,  4.00it/s]
 89%|████████▉ | 223/250 [00:59<00:09,  2.79it/s]
 90%|████████▉ | 224/250 [00:59<00:07,  3.38it/s]
 90%|█████████ | 225/250 [00:59<00:06,  3.67it/s]
 91%|█████████ | 228/250 [00:59<00:04,  4.87it/s]
 92%|█████████▏| 230/250 [01:00<00:04,  4.22it/s]
 92%|█████████▏| 231/250 [01:00<00:04,  3.94it/s]
 93%|█████████▎| 233/250 [01:00<00:03,  5.02it/s]
 94%|█████████▎| 234/250 [01:02<00:06,  2.52it/s]
 95%|█████████▍| 237/250 [01:02<00:03,  3.28it/s]
 95%|█████████▌| 238/250 [01:02<00:03,  3.71it/s]
 96%|█████████▌| 240/250 [01:02<00:01,  5.05it/s]
 97%|█████████▋| 242/250 [01:05<00:04,  1.78it/s]
 97%|█████████▋| 243/250 [01:08<00:06,  1.06it/s]
 98%|█████████▊| 244/250 [01:08<00:04,  1.31it/s]
 98%|█████████▊| 245/250 [01:08<00:03,  1.31it/s]
 98%|█████████▊| 246/250 [01:11<00:04,  1.09s/it]
 99%|█████████▉| 247/250 [01:11<00:02,  1.01it/s]
 99%|█████████▉| 248/250 [01:14<00:03,  1.53s/it]
100%|█████████▉| 249/250 [01:17<00:01,  1.99s/it]
100%|██████████| 250/250 [01:23<00:00,  3.04s/it]
100%|██████████| 250/250 [01:23<00:00,  2.99it/s]
2025-05-14 16:16:54,179 - modnet - INFO - Loss per individual: ind 0: -0.496 	ind 1: -0.501 	ind 2: -0.501 	ind 3: -0.521 	ind 4: -0.520 	ind 5: -0.500 	ind 6: -0.503 	ind 7: -0.503 	ind 8: -0.497 	ind 9: -0.503 	ind 10: -0.504 	ind 11: -0.586 	ind 12: -0.501 	ind 13: -0.499 	ind 14: -0.506 	ind 15: -0.553 	ind 16: -0.500 	ind 17: -0.502 	ind 18: -0.515 	ind 19: -0.506 	ind 20: -0.501 	ind 21: -0.549 	ind 22: -0.499 	ind 23: -0.506 	ind 24: -0.501 	ind 25: -0.536 	ind 26: -0.501 	ind 27: -0.499 	ind 28: -0.500 	ind 29: -0.499 	ind 30: -0.702 	ind 31: -0.502 	ind 32: -0.501 	ind 33: -0.575 	ind 34: -0.902 	ind 35: -0.497 	ind 36: -0.502 	ind 37: -0.503 	ind 38: -0.501 	ind 39: -0.501 	ind 40: -0.498 	ind 41: -0.497 	ind 42: -0.681 	ind 43: -0.499 	ind 44: -0.499 	ind 45: -0.500 	ind 46: -0.908 	ind 47: -0.501 	ind 48: -0.500 	ind 49: -0.500 	
2025-05-14 16:16:54,180 - modnet - INFO - Generation number 3

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:04<17:22,  4.19s/it]
  1%|          | 2/250 [00:04<07:23,  1.79s/it]
  1%|          | 3/250 [00:04<04:53,  1.19s/it]
  2%|▏         | 4/250 [00:05<03:48,  1.07it/s]
  2%|▏         | 6/250 [00:05<01:56,  2.10it/s]
  3%|▎         | 7/250 [00:06<02:07,  1.90it/s]
  3%|▎         | 8/250 [00:06<01:45,  2.29it/s]
  4%|▎         | 9/250 [00:06<01:29,  2.69it/s]
  4%|▍         | 10/250 [00:06<01:16,  3.12it/s]
  4%|▍         | 11/250 [00:07<02:06,  1.89it/s]
  5%|▍         | 12/250 [00:07<01:35,  2.49it/s]
  5%|▌         | 13/250 [00:08<01:21,  2.92it/s]
  6%|▌         | 15/250 [00:08<01:01,  3.81it/s]
  6%|▋         | 16/250 [00:08<01:08,  3.43it/s]
  7%|▋         | 17/250 [00:08<00:56,  4.10it/s]
  7%|▋         | 18/250 [00:09<01:07,  3.45it/s]
  8%|▊         | 19/250 [00:09<01:00,  3.82it/s]
  8%|▊         | 20/250 [00:09<01:03,  3.60it/s]
  9%|▉         | 22/250 [00:09<00:45,  5.06it/s]
  9%|▉         | 23/250 [00:10<00:53,  4.28it/s]
 10%|▉         | 24/250 [00:10<00:54,  4.17it/s]
 10%|█         | 25/250 [00:10<00:45,  4.91it/s]
 10%|█         | 26/250 [00:11<00:54,  4.10it/s]
 11%|█         | 27/250 [00:11<00:55,  4.02it/s]
 11%|█         | 28/250 [00:11<00:47,  4.68it/s]
 12%|█▏        | 29/250 [00:11<00:44,  5.02it/s]
 12%|█▏        | 30/250 [00:11<00:50,  4.33it/s]
 12%|█▏        | 31/250 [00:12<00:44,  4.88it/s]
 13%|█▎        | 32/250 [00:12<01:15,  2.90it/s]
 14%|█▎        | 34/250 [00:12<00:53,  4.07it/s]
 14%|█▍        | 36/250 [00:14<01:29,  2.40it/s]
 15%|█▌        | 38/250 [00:14<01:02,  3.39it/s]
 16%|█▌        | 39/250 [00:14<00:59,  3.57it/s]
 16%|█▌        | 40/250 [00:15<01:17,  2.70it/s]
 17%|█▋        | 43/250 [00:15<00:45,  4.55it/s]
 18%|█▊        | 44/250 [00:15<00:42,  4.80it/s]
 18%|█▊        | 46/250 [00:15<00:33,  6.05it/s]
 19%|█▉        | 47/250 [00:16<00:46,  4.41it/s]
 20%|█▉        | 49/250 [00:16<00:33,  6.04it/s]
 20%|██        | 50/250 [00:16<00:31,  6.33it/s]
 20%|██        | 51/250 [00:16<00:28,  6.87it/s]
 21%|██        | 53/250 [00:17<00:42,  4.58it/s]
 22%|██▏       | 54/250 [00:18<01:18,  2.50it/s]
 22%|██▏       | 55/250 [00:18<01:05,  2.96it/s]
 22%|██▏       | 56/250 [00:18<01:02,  3.11it/s]
 23%|██▎       | 57/250 [00:19<00:58,  3.31it/s]
 23%|██▎       | 58/250 [00:19<00:53,  3.57it/s]
 24%|██▍       | 60/250 [00:19<00:40,  4.71it/s]
 24%|██▍       | 61/250 [00:19<00:35,  5.39it/s]
 25%|██▍       | 62/250 [00:20<00:49,  3.78it/s]
 26%|██▌       | 64/250 [00:20<00:36,  5.04it/s]
 26%|██▌       | 65/250 [00:21<01:04,  2.88it/s]
 26%|██▋       | 66/250 [00:22<01:49,  1.68it/s]
 27%|██▋       | 67/250 [00:24<02:53,  1.05it/s]
 28%|██▊       | 69/250 [00:25<02:04,  1.45it/s]
 28%|██▊       | 70/250 [00:25<01:46,  1.68it/s]
 28%|██▊       | 71/250 [00:25<01:28,  2.02it/s]
 29%|██▉       | 72/250 [00:25<01:14,  2.39it/s]
 29%|██▉       | 73/250 [00:26<01:23,  2.12it/s]
 30%|███       | 75/250 [00:26<00:51,  3.40it/s]
 30%|███       | 76/250 [00:26<00:43,  4.03it/s]
 31%|███       | 78/250 [00:26<00:32,  5.24it/s]
 32%|███▏      | 79/250 [00:27<00:29,  5.84it/s]
 32%|███▏      | 80/250 [00:27<00:40,  4.21it/s]
 33%|███▎      | 82/250 [00:27<00:29,  5.63it/s]
 33%|███▎      | 83/250 [00:28<00:49,  3.41it/s]
 34%|███▎      | 84/250 [00:28<00:57,  2.89it/s]
 34%|███▍      | 85/250 [00:29<00:54,  3.01it/s]
 34%|███▍      | 86/250 [00:29<00:50,  3.22it/s]
 35%|███▍      | 87/250 [00:30<01:15,  2.15it/s]
 35%|███▌      | 88/250 [00:31<02:03,  1.31it/s]
 36%|███▌      | 89/250 [00:32<01:43,  1.56it/s]
 36%|███▌      | 90/250 [00:32<01:21,  1.97it/s]
 36%|███▋      | 91/250 [00:32<01:03,  2.51it/s]
 37%|███▋      | 93/250 [00:33<00:58,  2.70it/s]
 38%|███▊      | 94/250 [00:33<00:58,  2.69it/s]
 38%|███▊      | 95/250 [00:33<00:59,  2.61it/s]
 38%|███▊      | 96/250 [00:34<00:53,  2.86it/s]
 39%|███▉      | 98/250 [00:34<00:39,  3.88it/s]
 40%|████      | 100/250 [00:34<00:30,  5.00it/s]
 40%|████      | 101/250 [00:34<00:28,  5.23it/s]
 41%|████      | 102/250 [00:35<00:27,  5.48it/s]
 42%|████▏     | 104/250 [00:35<00:23,  6.12it/s]
 42%|████▏     | 105/250 [00:36<00:52,  2.78it/s]
 43%|████▎     | 107/250 [00:36<00:34,  4.17it/s]
 43%|████▎     | 108/250 [00:37<00:44,  3.19it/s]
 44%|████▎     | 109/250 [00:37<00:41,  3.43it/s]
 44%|████▍     | 110/250 [00:37<00:40,  3.48it/s]
 45%|████▍     | 112/250 [00:38<00:41,  3.36it/s]
 45%|████▌     | 113/250 [00:38<00:36,  3.75it/s]
 46%|████▌     | 114/250 [00:38<00:34,  3.95it/s]
 46%|████▌     | 115/250 [00:39<00:44,  3.02it/s]
 46%|████▋     | 116/250 [00:39<00:46,  2.88it/s]
 47%|████▋     | 117/250 [00:39<00:37,  3.58it/s]
 47%|████▋     | 118/250 [00:40<00:44,  2.98it/s]
 48%|████▊     | 119/250 [00:41<01:16,  1.72it/s]
 48%|████▊     | 120/250 [00:41<00:59,  2.20it/s]
 49%|████▉     | 122/250 [00:41<00:41,  3.09it/s]
 49%|████▉     | 123/250 [00:41<00:38,  3.32it/s]
 50%|█████     | 125/250 [00:42<00:31,  3.95it/s]
 50%|█████     | 126/250 [00:42<00:30,  4.09it/s]
 51%|█████     | 127/250 [00:42<00:32,  3.81it/s]
 52%|█████▏    | 129/250 [00:43<00:28,  4.27it/s]
 52%|█████▏    | 130/250 [00:43<00:25,  4.78it/s]
 53%|█████▎    | 132/250 [00:43<00:22,  5.25it/s]
 53%|█████▎    | 133/250 [00:43<00:23,  5.07it/s]
 54%|█████▍    | 135/250 [00:44<00:18,  6.20it/s]
 55%|█████▍    | 137/250 [00:44<00:14,  7.92it/s]
 55%|█████▌    | 138/250 [00:44<00:19,  5.84it/s]
 56%|█████▌    | 139/250 [00:44<00:22,  4.97it/s]
 56%|█████▋    | 141/250 [00:45<00:29,  3.73it/s]
 57%|█████▋    | 142/250 [00:45<00:25,  4.21it/s]
 57%|█████▋    | 143/250 [00:46<00:29,  3.61it/s]
 58%|█████▊    | 144/250 [00:46<00:44,  2.38it/s]
 58%|█████▊    | 145/250 [00:47<00:46,  2.25it/s]
 58%|█████▊    | 146/250 [00:47<00:40,  2.56it/s]
 59%|█████▉    | 147/250 [00:47<00:33,  3.07it/s]
 59%|█████▉    | 148/250 [00:48<00:26,  3.82it/s]
 60%|█████▉    | 149/250 [00:48<00:35,  2.87it/s]
 60%|██████    | 150/250 [00:48<00:32,  3.09it/s]
 60%|██████    | 151/250 [00:49<00:32,  3.08it/s]
 61%|██████    | 153/250 [00:49<00:21,  4.48it/s]
 62%|██████▏   | 154/250 [00:49<00:24,  3.92it/s]
 62%|██████▏   | 155/250 [00:50<00:33,  2.81it/s]
 62%|██████▏   | 156/250 [00:51<00:47,  1.98it/s]
 63%|██████▎   | 157/250 [00:52<00:58,  1.58it/s]
 63%|██████▎   | 158/250 [00:52<00:50,  1.82it/s]
 64%|██████▎   | 159/250 [00:53<00:52,  1.72it/s]
 64%|██████▍   | 160/250 [00:53<00:54,  1.64it/s]
 64%|██████▍   | 161/250 [00:54<00:48,  1.83it/s]
 65%|██████▌   | 163/250 [00:54<00:28,  3.03it/s]
 66%|██████▌   | 164/250 [00:55<00:42,  2.02it/s]
 66%|██████▋   | 166/250 [00:55<00:32,  2.55it/s]
 67%|██████▋   | 167/250 [00:56<00:29,  2.78it/s]
 67%|██████▋   | 168/250 [00:56<00:28,  2.83it/s]
 68%|██████▊   | 169/250 [00:56<00:27,  2.98it/s]
 68%|██████▊   | 170/250 [00:57<00:32,  2.45it/s]
 69%|██████▉   | 172/250 [00:57<00:20,  3.72it/s]
 69%|██████▉   | 173/250 [00:58<00:31,  2.44it/s]
 70%|██████▉   | 174/250 [00:58<00:27,  2.73it/s]
 70%|███████   | 175/250 [00:58<00:23,  3.24it/s]
 70%|███████   | 176/250 [00:58<00:19,  3.84it/s]
 71%|███████   | 178/250 [00:59<00:12,  5.78it/s]
 72%|███████▏  | 180/250 [00:59<00:11,  6.24it/s]
 73%|███████▎  | 182/250 [00:59<00:08,  7.88it/s]
 74%|███████▎  | 184/250 [00:59<00:07,  8.60it/s]
 74%|███████▍  | 186/250 [01:00<00:08,  7.81it/s]
 75%|███████▍  | 187/250 [01:00<00:08,  7.26it/s]
 75%|███████▌  | 188/250 [01:00<00:10,  5.66it/s]
 76%|███████▌  | 189/250 [01:00<00:14,  4.26it/s]
 76%|███████▋  | 191/250 [01:01<00:10,  5.54it/s]
 77%|███████▋  | 192/250 [01:01<00:10,  5.50it/s]
 77%|███████▋  | 193/250 [01:01<00:12,  4.49it/s]
 78%|███████▊  | 194/250 [01:02<00:17,  3.25it/s]
 78%|███████▊  | 196/250 [01:02<00:17,  3.08it/s]
 79%|███████▉  | 198/250 [01:04<00:25,  2.08it/s]
 80%|████████  | 201/250 [01:04<00:14,  3.33it/s]
 81%|████████  | 202/250 [01:04<00:13,  3.59it/s]
 82%|████████▏ | 204/250 [01:05<00:13,  3.44it/s]
 82%|████████▏ | 205/250 [01:05<00:11,  3.80it/s]
 82%|████████▏ | 206/250 [01:05<00:10,  4.22it/s]
 83%|████████▎ | 207/250 [01:06<00:10,  3.98it/s]
 83%|████████▎ | 208/250 [01:06<00:11,  3.65it/s]
 84%|████████▎ | 209/250 [01:06<00:11,  3.51it/s]
 84%|████████▍ | 210/250 [01:06<00:10,  3.66it/s]
 84%|████████▍ | 211/250 [01:07<00:09,  4.27it/s]
 86%|████████▌ | 214/250 [01:07<00:05,  6.42it/s]
 86%|████████▌ | 215/250 [01:07<00:05,  6.30it/s]
 86%|████████▋ | 216/250 [01:08<00:08,  4.24it/s]
 87%|████████▋ | 217/250 [01:08<00:07,  4.65it/s]
 88%|████████▊ | 219/250 [01:08<00:05,  5.78it/s]
 88%|████████▊ | 220/250 [01:08<00:05,  5.04it/s]
 89%|████████▉ | 222/250 [01:08<00:04,  5.68it/s]
 90%|████████▉ | 224/250 [01:09<00:04,  6.01it/s]
 90%|█████████ | 225/250 [01:09<00:04,  5.53it/s]
 90%|█████████ | 226/250 [01:10<00:08,  2.92it/s]
 91%|█████████ | 227/250 [01:10<00:07,  3.22it/s]
 91%|█████████ | 228/250 [01:11<00:07,  2.80it/s]
 92%|█████████▏| 229/250 [01:11<00:07,  2.91it/s]
 92%|█████████▏| 230/250 [01:11<00:05,  3.61it/s]
 93%|█████████▎| 232/250 [01:11<00:03,  5.42it/s]
 94%|█████████▎| 234/250 [01:11<00:02,  5.85it/s]
 94%|█████████▍| 235/250 [01:12<00:02,  6.35it/s]
 94%|█████████▍| 236/250 [01:12<00:02,  5.59it/s]
 95%|█████████▌| 238/250 [01:12<00:01,  7.23it/s]
 96%|█████████▌| 240/250 [01:12<00:01,  8.67it/s]
 97%|█████████▋| 242/250 [01:12<00:01,  7.69it/s]
 97%|█████████▋| 243/250 [01:13<00:01,  5.70it/s]
 98%|█████████▊| 245/250 [01:13<00:00,  6.52it/s]
 98%|█████████▊| 246/250 [01:13<00:00,  6.41it/s]
 99%|█████████▉| 247/250 [01:14<00:00,  4.40it/s]
 99%|█████████▉| 248/250 [01:14<00:00,  4.11it/s]
100%|█████████▉| 249/250 [01:15<00:00,  2.30it/s]
100%|██████████| 250/250 [01:16<00:00,  1.72it/s]
100%|██████████| 250/250 [01:16<00:00,  3.27it/s]
2025-05-14 16:18:10,618 - modnet - INFO - Loss per individual: ind 0: -0.502 	ind 1: -0.497 	ind 2: -0.500 	ind 3: -0.501 	ind 4: -0.500 	ind 5: -0.501 	ind 6: -0.466 	ind 7: -0.504 	ind 8: -0.887 	ind 9: -0.502 	ind 10: -0.494 	ind 11: -0.498 	ind 12: -0.520 	ind 13: -0.500 	ind 14: -0.501 	ind 15: -0.505 	ind 16: -0.529 	ind 17: -0.499 	ind 18: -0.576 	ind 19: -0.498 	ind 20: -0.572 	ind 21: -0.502 	ind 22: -0.499 	ind 23: -0.499 	ind 24: -0.499 	ind 25: -0.506 	ind 26: -0.603 	ind 27: -0.502 	ind 28: -0.499 	ind 29: -0.509 	ind 30: -0.808 	ind 31: -0.500 	ind 32: -0.499 	ind 33: -0.514 	ind 34: -0.500 	ind 35: -0.512 	ind 36: -0.485 	ind 37: -0.501 	ind 38: -0.578 	ind 39: -0.554 	ind 40: -0.479 	ind 41: -0.504 	ind 42: -0.499 	ind 43: -0.503 	ind 44: -0.500 	ind 45: -0.498 	ind 46: -0.502 	ind 47: -0.499 	ind 48: -0.500 	ind 49: -0.504 	
2025-05-14 16:18:10,620 - modnet - INFO - Generation number 4

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:04<17:49,  4.29s/it]
  1%|          | 3/250 [00:05<05:53,  1.43s/it]
  2%|▏         | 5/250 [00:05<02:58,  1.37it/s]
  3%|▎         | 7/250 [00:05<01:47,  2.26it/s]
  4%|▎         | 9/250 [00:06<01:37,  2.47it/s]
  4%|▍         | 10/250 [00:06<02:00,  1.98it/s]
  5%|▍         | 12/250 [00:07<01:21,  2.91it/s]
  6%|▌         | 14/250 [00:07<01:29,  2.63it/s]
  6%|▋         | 16/250 [00:08<01:08,  3.44it/s]
  7%|▋         | 18/250 [00:08<00:50,  4.64it/s]
  8%|▊         | 20/250 [00:08<00:39,  5.83it/s]
  9%|▉         | 22/250 [00:08<00:43,  5.28it/s]
  9%|▉         | 23/250 [00:09<00:41,  5.45it/s]
 10%|▉         | 24/250 [00:09<00:38,  5.83it/s]
 10%|█         | 26/250 [00:09<00:28,  7.78it/s]
 11%|█         | 28/250 [00:10<01:10,  3.14it/s]
 12%|█▏        | 29/250 [00:11<01:12,  3.04it/s]
 12%|█▏        | 31/250 [00:11<00:57,  3.78it/s]
 13%|█▎        | 33/250 [00:12<01:03,  3.39it/s]
 14%|█▍        | 35/250 [00:12<00:47,  4.57it/s]
 15%|█▍        | 37/250 [00:12<00:36,  5.83it/s]
 16%|█▌        | 39/250 [00:12<00:31,  6.60it/s]
 16%|█▋        | 41/250 [00:12<00:29,  6.98it/s]
 17%|█▋        | 43/250 [00:13<00:33,  6.16it/s]
 18%|█▊        | 44/250 [00:13<00:31,  6.55it/s]
 18%|█▊        | 45/250 [00:13<00:29,  6.95it/s]
 19%|█▉        | 47/250 [00:13<00:22,  8.97it/s]
 20%|█▉        | 49/250 [00:13<00:22,  8.87it/s]
 20%|██        | 51/250 [00:14<00:31,  6.23it/s]
 21%|██        | 53/250 [00:14<00:30,  6.46it/s]
 22%|██▏       | 54/250 [00:14<00:29,  6.72it/s]
 22%|██▏       | 55/250 [00:14<00:27,  7.02it/s]
 22%|██▏       | 56/250 [00:15<00:35,  5.53it/s]
 23%|██▎       | 58/250 [00:15<00:24,  7.70it/s]
 24%|██▍       | 60/250 [00:15<00:28,  6.61it/s]
 24%|██▍       | 61/250 [00:15<00:34,  5.50it/s]
 25%|██▌       | 63/250 [00:16<00:26,  7.07it/s]
 26%|██▌       | 64/250 [00:16<00:38,  4.79it/s]
 26%|██▌       | 65/250 [00:17<01:04,  2.88it/s]
 26%|██▋       | 66/250 [00:17<01:02,  2.96it/s]
 27%|██▋       | 67/250 [00:18<01:23,  2.19it/s]
 27%|██▋       | 68/250 [00:18<01:07,  2.72it/s]
 28%|██▊       | 69/250 [00:18<00:59,  3.04it/s]
 29%|██▉       | 72/250 [00:18<00:30,  5.80it/s]
 30%|██▉       | 74/250 [00:19<00:26,  6.75it/s]
 30%|███       | 76/250 [00:19<00:20,  8.44it/s]
 31%|███       | 78/250 [00:19<00:25,  6.76it/s]
 32%|███▏      | 80/250 [00:20<00:30,  5.54it/s]
 33%|███▎      | 82/250 [00:20<00:26,  6.33it/s]
 34%|███▎      | 84/250 [00:20<00:22,  7.46it/s]
 34%|███▍      | 85/250 [00:20<00:29,  5.65it/s]
 34%|███▍      | 86/250 [00:21<00:27,  6.00it/s]
 35%|███▌      | 88/250 [00:21<00:28,  5.78it/s]
 36%|███▌      | 89/250 [00:22<00:43,  3.71it/s]
 37%|███▋      | 92/250 [00:22<00:43,  3.61it/s]
 37%|███▋      | 93/250 [00:23<00:41,  3.74it/s]
 38%|███▊      | 94/250 [00:23<00:37,  4.15it/s]
 38%|███▊      | 95/250 [00:24<01:03,  2.45it/s]
 39%|███▉      | 97/250 [00:24<00:44,  3.44it/s]
 40%|███▉      | 99/250 [00:24<00:30,  4.87it/s]
 40%|████      | 101/250 [00:24<00:24,  6.05it/s]
 41%|████      | 103/250 [00:25<00:30,  4.83it/s]
 42%|████▏     | 104/250 [00:25<00:29,  4.94it/s]
 42%|████▏     | 105/250 [00:25<00:27,  5.26it/s]
 42%|████▏     | 106/250 [00:25<00:31,  4.55it/s]
 43%|████▎     | 107/250 [00:26<00:27,  5.24it/s]
 43%|████▎     | 108/250 [00:26<00:23,  5.98it/s]
 44%|████▎     | 109/250 [00:26<00:33,  4.21it/s]
 44%|████▍     | 110/250 [00:26<00:29,  4.79it/s]
 44%|████▍     | 111/250 [00:28<01:13,  1.89it/s]
 45%|████▍     | 112/250 [00:28<01:23,  1.65it/s]
 45%|████▌     | 113/250 [00:29<01:18,  1.75it/s]
 46%|████▌     | 114/250 [00:29<01:01,  2.20it/s]
 46%|████▌     | 115/250 [00:29<00:48,  2.80it/s]
 46%|████▋     | 116/250 [00:30<00:51,  2.60it/s]
 47%|████▋     | 117/250 [00:30<00:41,  3.17it/s]
 47%|████▋     | 118/250 [00:30<00:41,  3.18it/s]
 48%|████▊     | 119/250 [00:30<00:33,  3.94it/s]
 48%|████▊     | 120/250 [00:31<00:40,  3.19it/s]
 48%|████▊     | 121/250 [00:31<00:38,  3.39it/s]
 49%|████▉     | 122/250 [00:31<00:32,  3.90it/s]
 49%|████▉     | 123/250 [00:31<00:35,  3.56it/s]
 50%|█████     | 125/250 [00:32<00:32,  3.84it/s]
 50%|█████     | 126/250 [00:32<00:29,  4.27it/s]
 51%|█████     | 128/250 [00:32<00:20,  6.09it/s]
 52%|█████▏    | 130/250 [00:33<00:22,  5.37it/s]
 53%|█████▎    | 132/250 [00:33<00:17,  6.79it/s]
 54%|█████▎    | 134/250 [00:33<00:14,  8.05it/s]
 54%|█████▍    | 136/250 [00:33<00:17,  6.67it/s]
 55%|█████▍    | 137/250 [00:34<00:23,  4.88it/s]
 55%|█████▌    | 138/250 [00:34<00:23,  4.68it/s]
 56%|█████▌    | 140/250 [00:34<00:22,  4.94it/s]
 56%|█████▋    | 141/250 [00:35<00:22,  4.89it/s]
 57%|█████▋    | 143/250 [00:35<00:26,  4.02it/s]
 58%|█████▊    | 144/250 [00:35<00:23,  4.50it/s]
 58%|█████▊    | 146/250 [00:35<00:17,  6.02it/s]
 59%|█████▉    | 148/250 [00:36<00:15,  6.61it/s]
 60%|█████▉    | 149/250 [00:37<00:39,  2.57it/s]
 60%|██████    | 150/250 [00:37<00:34,  2.89it/s]
 60%|██████    | 151/250 [00:39<00:56,  1.74it/s]
 61%|██████    | 153/250 [00:39<00:40,  2.40it/s]
 62%|██████▏   | 155/250 [00:39<00:30,  3.15it/s]
 62%|██████▏   | 156/250 [00:39<00:28,  3.35it/s]
 63%|██████▎   | 157/250 [00:40<00:30,  3.01it/s]
 63%|██████▎   | 158/250 [00:40<00:26,  3.44it/s]
 64%|██████▎   | 159/250 [00:40<00:24,  3.70it/s]
 64%|██████▍   | 161/250 [00:40<00:17,  5.22it/s]
 65%|██████▍   | 162/250 [00:41<00:15,  5.77it/s]
 65%|██████▌   | 163/250 [00:41<00:13,  6.22it/s]
 66%|██████▌   | 165/250 [00:41<00:11,  7.31it/s]
 66%|██████▋   | 166/250 [00:41<00:11,  7.54it/s]
 68%|██████▊   | 169/250 [00:41<00:07, 10.17it/s]
 68%|██████▊   | 171/250 [00:41<00:08,  9.87it/s]
 69%|██████▉   | 173/250 [00:43<00:19,  3.96it/s]
 70%|███████   | 175/250 [00:43<00:15,  4.98it/s]
 70%|███████   | 176/250 [00:43<00:13,  5.38it/s]
 71%|███████   | 177/250 [00:43<00:15,  4.71it/s]
 71%|███████   | 178/250 [00:43<00:14,  4.83it/s]
 72%|███████▏  | 179/250 [00:44<00:14,  4.78it/s]
 72%|███████▏  | 181/250 [00:44<00:13,  5.16it/s]
 73%|███████▎  | 182/250 [00:44<00:12,  5.34it/s]
 73%|███████▎  | 183/250 [00:44<00:13,  5.06it/s]
 74%|███████▎  | 184/250 [00:44<00:12,  5.42it/s]
 74%|███████▍  | 185/250 [00:45<00:16,  3.94it/s]
 74%|███████▍  | 186/250 [00:45<00:13,  4.68it/s]
 75%|███████▍  | 187/250 [00:45<00:16,  3.93it/s]
 75%|███████▌  | 188/250 [00:46<00:14,  4.29it/s]
 76%|███████▌  | 189/250 [00:46<00:17,  3.57it/s]
 76%|███████▌  | 190/250 [00:47<00:35,  1.69it/s]
 76%|███████▋  | 191/250 [00:48<00:39,  1.49it/s]
 77%|███████▋  | 193/250 [00:48<00:22,  2.54it/s]
 78%|███████▊  | 194/250 [00:49<00:21,  2.66it/s]
 78%|███████▊  | 195/250 [00:49<00:21,  2.57it/s]
 78%|███████▊  | 196/250 [00:49<00:19,  2.76it/s]
 79%|███████▉  | 197/250 [00:50<00:18,  2.80it/s]
 79%|███████▉  | 198/250 [00:50<00:15,  3.32it/s]
 80%|███████▉  | 199/250 [00:50<00:17,  2.86it/s]
 80%|████████  | 201/250 [00:50<00:11,  4.27it/s]
 81%|████████  | 202/250 [00:51<00:14,  3.35it/s]
 81%|████████  | 203/250 [00:51<00:16,  2.93it/s]
 82%|████████▏ | 204/250 [00:52<00:13,  3.42it/s]
 82%|████████▏ | 205/250 [00:52<00:11,  4.00it/s]
 82%|████████▏ | 206/250 [00:52<00:09,  4.73it/s]
 83%|████████▎ | 208/250 [00:52<00:07,  5.95it/s]
 84%|████████▍ | 210/250 [00:52<00:06,  5.99it/s]
 85%|████████▍ | 212/250 [00:53<00:04,  7.95it/s]
 86%|████████▌ | 214/250 [00:53<00:04,  7.72it/s]
 86%|████████▋ | 216/250 [00:53<00:03,  9.57it/s]
 87%|████████▋ | 218/250 [00:53<00:03,  9.71it/s]
 88%|████████▊ | 220/250 [00:53<00:02, 10.40it/s]
 89%|████████▉ | 222/250 [00:54<00:06,  4.59it/s]
 90%|████████▉ | 224/250 [00:54<00:04,  5.25it/s]
 90%|█████████ | 226/250 [00:55<00:03,  6.19it/s]
 92%|█████████▏| 229/250 [00:55<00:02,  8.88it/s]
 92%|█████████▏| 231/250 [00:55<00:02,  8.11it/s]
 93%|█████████▎| 233/250 [00:56<00:02,  6.15it/s]
 94%|█████████▎| 234/250 [00:56<00:02,  5.68it/s]
 94%|█████████▍| 235/250 [00:56<00:03,  4.73it/s]
 94%|█████████▍| 236/250 [00:56<00:02,  5.26it/s]
 95%|█████████▍| 237/250 [00:56<00:02,  5.69it/s]
 95%|█████████▌| 238/250 [00:57<00:03,  3.21it/s]
 96%|█████████▌| 239/250 [00:58<00:05,  1.92it/s]
 96%|█████████▌| 240/250 [00:59<00:04,  2.13it/s]
 96%|█████████▋| 241/250 [00:59<00:03,  2.47it/s]
 97%|█████████▋| 242/250 [00:59<00:02,  3.06it/s]
 97%|█████████▋| 243/250 [01:00<00:03,  2.25it/s]
 98%|█████████▊| 244/250 [01:01<00:04,  1.48it/s]
 98%|█████████▊| 245/250 [01:01<00:02,  1.79it/s]
 98%|█████████▊| 246/250 [01:02<00:01,  2.11it/s]
 99%|█████████▉| 247/250 [01:02<00:01,  2.01it/s]
 99%|█████████▉| 248/250 [01:02<00:00,  2.15it/s]
100%|█████████▉| 249/250 [01:03<00:00,  2.22it/s]
100%|██████████| 250/250 [01:03<00:00,  3.94it/s]
2025-05-14 16:19:14,121 - modnet - INFO - Loss per individual: ind 0: -0.500 	ind 1: -0.499 	ind 2: -0.477 	ind 3: -0.501 	ind 4: -0.501 	ind 5: -0.592 	ind 6: -0.500 	ind 7: -0.501 	ind 8: -0.503 	ind 9: -0.500 	ind 10: -0.589 	ind 11: -0.503 	ind 12: -0.536 	ind 13: -0.500 	ind 14: -0.501 	ind 15: -0.504 	ind 16: -0.646 	ind 17: -0.515 	ind 18: -0.501 	ind 19: -0.500 	ind 20: -0.494 	ind 21: -0.503 	ind 22: -0.500 	ind 23: -0.501 	ind 24: -0.508 	ind 25: -0.479 	ind 26: -0.499 	ind 27: -0.503 	ind 28: -0.507 	ind 29: -0.529 	ind 30: -0.500 	ind 31: -0.557 	ind 32: -0.501 	ind 33: -0.499 	ind 34: -0.499 	ind 35: -0.499 	ind 36: -0.505 	ind 37: -0.503 	ind 38: -0.549 	ind 39: -0.534 	ind 40: -0.495 	ind 41: -0.496 	ind 42: -0.493 	ind 43: -0.516 	ind 44: -0.503 	ind 45: -0.500 	ind 46: -0.503 	ind 47: -0.501 	ind 48: -0.500 	ind 49: -0.500 	
2025-05-14 16:19:14,123 - modnet - INFO - Generation number 5

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:03<14:31,  3.50s/it]
  1%|          | 2/250 [00:03<06:15,  1.51s/it]
  1%|          | 3/250 [00:03<03:40,  1.12it/s]
  2%|▏         | 5/250 [00:04<01:53,  2.15it/s]
  3%|▎         | 7/250 [00:04<01:10,  3.43it/s]
  3%|▎         | 8/250 [00:04<01:12,  3.32it/s]
  4%|▍         | 10/250 [00:05<01:08,  3.52it/s]
  5%|▍         | 12/250 [00:05<00:48,  4.96it/s]
  6%|▌         | 14/250 [00:05<00:37,  6.26it/s]
  6%|▋         | 16/250 [00:05<00:40,  5.78it/s]
  7%|▋         | 17/250 [00:05<00:38,  6.00it/s]
  7%|▋         | 18/250 [00:06<00:41,  5.56it/s]
  8%|▊         | 19/250 [00:06<00:53,  4.30it/s]
  8%|▊         | 20/250 [00:06<00:46,  4.89it/s]
  8%|▊         | 21/250 [00:06<00:47,  4.78it/s]
  9%|▉         | 22/250 [00:07<01:06,  3.45it/s]
  9%|▉         | 23/250 [00:07<01:18,  2.88it/s]
 10%|▉         | 24/250 [00:07<01:02,  3.61it/s]
 10%|█         | 25/250 [00:08<00:54,  4.10it/s]
 10%|█         | 26/250 [00:08<00:57,  3.93it/s]
 11%|█         | 27/250 [00:08<00:51,  4.34it/s]
 11%|█         | 28/250 [00:08<00:43,  5.09it/s]
 12%|█▏        | 31/250 [00:09<00:38,  5.71it/s]
 13%|█▎        | 32/250 [00:09<00:38,  5.60it/s]
 13%|█▎        | 33/250 [00:10<01:11,  3.04it/s]
 14%|█▍        | 35/250 [00:10<00:51,  4.20it/s]
 14%|█▍        | 36/250 [00:10<00:47,  4.55it/s]
 15%|█▌        | 38/250 [00:10<00:39,  5.43it/s]
 16%|█▌        | 39/250 [00:11<00:42,  4.97it/s]
 16%|█▌        | 40/250 [00:11<00:49,  4.25it/s]
 16%|█▋        | 41/250 [00:11<01:02,  3.35it/s]
 17%|█▋        | 42/250 [00:12<01:16,  2.71it/s]
 17%|█▋        | 43/250 [00:12<01:05,  3.17it/s]
 18%|█▊        | 44/250 [00:12<01:07,  3.03it/s]
 18%|█▊        | 45/250 [00:13<01:23,  2.45it/s]
 19%|█▉        | 47/250 [00:13<00:52,  3.89it/s]
 19%|█▉        | 48/250 [00:13<00:45,  4.45it/s]
 20%|█▉        | 49/250 [00:13<00:39,  5.08it/s]
 20%|██        | 50/250 [00:14<00:44,  4.47it/s]
 20%|██        | 51/250 [00:14<00:54,  3.63it/s]
 21%|██        | 52/250 [00:15<01:14,  2.65it/s]
 22%|██▏       | 54/250 [00:15<00:59,  3.32it/s]
 22%|██▏       | 56/250 [00:15<00:40,  4.84it/s]
 23%|██▎       | 57/250 [00:16<00:38,  5.03it/s]
 23%|██▎       | 58/250 [00:16<00:46,  4.09it/s]
 24%|██▎       | 59/250 [00:16<00:49,  3.85it/s]
 24%|██▍       | 60/250 [00:16<00:49,  3.85it/s]
 24%|██▍       | 61/250 [00:17<01:10,  2.69it/s]
 25%|██▍       | 62/250 [00:17<01:02,  3.00it/s]
 25%|██▌       | 63/250 [00:19<01:48,  1.72it/s]
 26%|██▌       | 64/250 [00:19<01:35,  1.95it/s]
 26%|██▌       | 65/250 [00:19<01:14,  2.49it/s]
 26%|██▋       | 66/250 [00:19<00:58,  3.13it/s]
 27%|██▋       | 67/250 [00:20<01:01,  2.97it/s]
 27%|██▋       | 68/250 [00:20<01:13,  2.46it/s]
 28%|██▊       | 70/250 [00:20<00:48,  3.70it/s]
 29%|██▉       | 72/250 [00:21<00:35,  4.98it/s]
 29%|██▉       | 73/250 [00:21<00:47,  3.69it/s]
 30%|██▉       | 74/250 [00:21<00:46,  3.82it/s]
 30%|███       | 75/250 [00:21<00:40,  4.28it/s]
 30%|███       | 76/250 [00:22<00:45,  3.79it/s]
 31%|███       | 77/250 [00:23<01:12,  2.40it/s]
 32%|███▏      | 79/250 [00:23<00:48,  3.52it/s]
 32%|███▏      | 80/250 [00:23<00:44,  3.79it/s]
 32%|███▏      | 81/250 [00:23<00:46,  3.63it/s]
 33%|███▎      | 82/250 [00:24<00:53,  3.14it/s]
 34%|███▎      | 84/250 [00:24<00:46,  3.60it/s]
 34%|███▍      | 85/250 [00:24<00:40,  4.06it/s]
 34%|███▍      | 86/250 [00:25<00:38,  4.26it/s]
 35%|███▍      | 87/250 [00:26<01:37,  1.67it/s]
 35%|███▌      | 88/250 [00:27<01:26,  1.88it/s]
 36%|███▌      | 89/250 [00:27<01:17,  2.07it/s]
 36%|███▌      | 90/250 [00:27<01:03,  2.53it/s]
 37%|███▋      | 92/250 [00:27<00:48,  3.28it/s]
 38%|███▊      | 94/250 [00:28<00:34,  4.47it/s]
 38%|███▊      | 95/250 [00:28<00:34,  4.48it/s]
 38%|███▊      | 96/250 [00:28<00:32,  4.71it/s]
 39%|███▉      | 97/250 [00:28<00:40,  3.79it/s]
 39%|███▉      | 98/250 [00:29<00:41,  3.63it/s]
 40%|███▉      | 99/250 [00:29<00:35,  4.28it/s]
 40%|████      | 100/250 [00:29<00:42,  3.54it/s]
 40%|████      | 101/250 [00:31<01:31,  1.64it/s]
 41%|████      | 102/250 [00:31<01:13,  2.01it/s]
 41%|████      | 103/250 [00:31<00:58,  2.49it/s]
 42%|████▏     | 104/250 [00:32<01:01,  2.36it/s]
 42%|████▏     | 105/250 [00:32<00:56,  2.59it/s]
 43%|████▎     | 107/250 [00:32<00:35,  4.06it/s]
 44%|████▎     | 109/250 [00:32<00:24,  5.73it/s]
 44%|████▍     | 110/250 [00:33<00:43,  3.19it/s]
 45%|████▍     | 112/250 [00:34<00:40,  3.45it/s]
 45%|████▌     | 113/250 [00:34<00:41,  3.28it/s]
 46%|████▌     | 114/250 [00:35<01:00,  2.25it/s]
 46%|████▋     | 116/250 [00:35<00:39,  3.36it/s]
 47%|████▋     | 118/250 [00:35<00:27,  4.80it/s]
 48%|████▊     | 120/250 [00:35<00:26,  4.98it/s]
 48%|████▊     | 121/250 [00:36<00:23,  5.39it/s]
 49%|████▉     | 122/250 [00:36<00:29,  4.31it/s]
 49%|████▉     | 123/250 [00:36<00:25,  4.96it/s]
 50%|████▉     | 124/250 [00:36<00:23,  5.31it/s]
 50%|█████     | 125/250 [00:36<00:27,  4.58it/s]
 50%|█████     | 126/250 [00:37<00:25,  4.81it/s]
 51%|█████     | 127/250 [00:37<00:22,  5.57it/s]
 52%|█████▏    | 129/250 [00:37<00:18,  6.47it/s]
 52%|█████▏    | 130/250 [00:37<00:18,  6.64it/s]
 52%|█████▏    | 131/250 [00:37<00:17,  6.69it/s]
 53%|█████▎    | 133/250 [00:37<00:14,  8.30it/s]
 54%|█████▎    | 134/250 [00:38<00:27,  4.28it/s]
 54%|█████▍    | 135/250 [00:39<00:56,  2.05it/s]
 55%|█████▍    | 137/250 [00:40<00:46,  2.43it/s]
 55%|█████▌    | 138/250 [00:40<00:48,  2.32it/s]
 56%|█████▌    | 139/250 [00:41<00:47,  2.32it/s]
 56%|█████▌    | 140/250 [00:41<00:41,  2.63it/s]
 56%|█████▋    | 141/250 [00:42<00:45,  2.41it/s]
 57%|█████▋    | 143/250 [00:42<00:29,  3.68it/s]
 58%|█████▊    | 144/250 [00:42<00:26,  4.07it/s]
 58%|█████▊    | 145/250 [00:42<00:24,  4.21it/s]
 58%|█████▊    | 146/250 [00:42<00:23,  4.45it/s]
 59%|█████▉    | 148/250 [00:43<00:18,  5.39it/s]
 60%|██████    | 150/250 [00:43<00:13,  7.27it/s]
 60%|██████    | 151/250 [00:44<00:27,  3.60it/s]
 61%|██████    | 152/250 [00:44<00:29,  3.32it/s]
 62%|██████▏   | 154/250 [00:44<00:20,  4.74it/s]
 62%|██████▏   | 156/250 [00:44<00:16,  5.65it/s]
 63%|██████▎   | 157/250 [00:45<00:18,  5.09it/s]
 63%|██████▎   | 158/250 [00:45<00:17,  5.19it/s]
 64%|██████▎   | 159/250 [00:45<00:18,  5.03it/s]
 64%|██████▍   | 160/250 [00:45<00:17,  5.10it/s]
 64%|██████▍   | 161/250 [00:45<00:15,  5.62it/s]
 65%|██████▍   | 162/250 [00:45<00:13,  6.37it/s]
 65%|██████▌   | 163/250 [00:46<00:33,  2.62it/s]
 66%|██████▌   | 164/250 [00:46<00:26,  3.24it/s]
 66%|██████▋   | 166/250 [00:47<00:19,  4.41it/s]
 67%|██████▋   | 167/250 [00:47<00:18,  4.55it/s]
 67%|██████▋   | 168/250 [00:47<00:16,  4.83it/s]
 68%|██████▊   | 169/250 [00:47<00:14,  5.53it/s]
 68%|██████▊   | 171/250 [00:47<00:10,  7.48it/s]
 69%|██████▉   | 172/250 [00:48<00:13,  5.96it/s]
 70%|███████   | 175/250 [00:48<00:10,  6.95it/s]
 70%|███████   | 176/250 [00:48<00:10,  6.81it/s]
 71%|███████   | 177/250 [00:48<00:13,  5.25it/s]
 72%|███████▏  | 179/250 [00:49<00:10,  6.54it/s]
 72%|███████▏  | 180/250 [00:49<00:10,  6.78it/s]
 73%|███████▎  | 182/250 [00:49<00:09,  7.06it/s]
 74%|███████▎  | 184/250 [00:51<00:26,  2.45it/s]
 74%|███████▍  | 185/250 [00:51<00:23,  2.73it/s]
 74%|███████▍  | 186/250 [00:51<00:21,  3.03it/s]
 75%|███████▌  | 188/250 [00:52<00:21,  2.90it/s]
 76%|███████▌  | 189/250 [00:52<00:18,  3.36it/s]
 76%|███████▌  | 190/250 [00:52<00:15,  3.82it/s]
 76%|███████▋  | 191/250 [00:53<00:23,  2.49it/s]
 77%|███████▋  | 192/250 [00:53<00:19,  2.95it/s]
 78%|███████▊  | 194/250 [00:54<00:14,  3.79it/s]
 78%|███████▊  | 196/250 [00:54<00:10,  5.26it/s]
 79%|███████▉  | 198/250 [00:54<00:07,  6.57it/s]
 80%|███████▉  | 199/250 [00:54<00:07,  6.85it/s]
 80%|████████  | 201/250 [00:54<00:05,  8.96it/s]
 81%|████████  | 203/250 [00:54<00:04, 10.06it/s]
 82%|████████▏ | 205/250 [00:55<00:07,  6.26it/s]
 82%|████████▏ | 206/250 [00:55<00:08,  5.45it/s]
 83%|████████▎ | 207/250 [00:56<00:11,  3.90it/s]
 83%|████████▎ | 208/250 [00:56<00:15,  2.75it/s]
 84%|████████▎ | 209/250 [00:57<00:17,  2.30it/s]
 84%|████████▍ | 210/250 [00:57<00:16,  2.41it/s]
 85%|████████▍ | 212/250 [00:58<00:10,  3.68it/s]
 86%|████████▌ | 215/250 [00:58<00:08,  4.19it/s]
 87%|████████▋ | 217/250 [00:58<00:06,  4.89it/s]
 87%|████████▋ | 218/250 [00:59<00:06,  4.82it/s]
 88%|████████▊ | 219/250 [00:59<00:06,  4.49it/s]
 88%|████████▊ | 220/250 [00:59<00:08,  3.71it/s]
 89%|████████▉ | 223/250 [01:00<00:04,  5.92it/s]
 90%|█████████ | 226/250 [01:00<00:04,  5.54it/s]
 91%|█████████ | 227/250 [01:00<00:04,  5.10it/s]
 92%|█████████▏| 229/250 [01:01<00:03,  6.43it/s]
 92%|█████████▏| 231/250 [01:01<00:02,  8.13it/s]
 93%|█████████▎| 233/250 [01:01<00:01,  9.31it/s]
 94%|█████████▍| 235/250 [01:02<00:02,  5.29it/s]
 95%|█████████▍| 237/250 [01:02<00:02,  5.97it/s]
 95%|█████████▌| 238/250 [01:03<00:03,  3.38it/s]
 96%|█████████▌| 239/250 [01:03<00:02,  3.78it/s]
 96%|█████████▌| 240/250 [01:03<00:02,  3.96it/s]
 96%|█████████▋| 241/250 [01:04<00:02,  3.17it/s]
 97%|█████████▋| 242/250 [01:04<00:02,  2.96it/s]
 97%|█████████▋| 243/250 [01:04<00:02,  2.73it/s]
 98%|█████████▊| 244/250 [01:04<00:01,  3.41it/s]
 98%|█████████▊| 246/250 [01:05<00:01,  3.48it/s]
 99%|█████████▉| 247/250 [01:05<00:00,  3.16it/s]
 99%|█████████▉| 248/250 [01:06<00:00,  3.35it/s]
100%|█████████▉| 249/250 [01:07<00:00,  2.19it/s]
100%|██████████| 250/250 [01:07<00:00,  2.69it/s]
100%|██████████| 250/250 [01:07<00:00,  3.72it/s]
2025-05-14 16:20:21,377 - modnet - INFO - Loss per individual: ind 0: -0.489 	ind 1: -0.500 	ind 2: -0.495 	ind 3: -0.505 	ind 4: -0.501 	ind 5: -0.501 	ind 6: -0.501 	ind 7: -0.503 	ind 8: -0.517 	ind 9: -0.502 	ind 10: -0.499 	ind 11: -0.500 	ind 12: -0.500 	ind 13: -0.502 	ind 14: -0.496 	ind 15: -0.500 	ind 16: -0.500 	ind 17: -0.502 	ind 18: -0.499 	ind 19: -0.493 	ind 20: -0.583 	ind 21: -0.526 	ind 22: -0.501 	ind 23: -0.573 	ind 24: -0.503 	ind 25: -0.532 	ind 26: -0.501 	ind 27: -0.502 	ind 28: -0.504 	ind 29: -0.611 	ind 30: -0.489 	ind 31: -0.500 	ind 32: -0.501 	ind 33: -0.500 	ind 34: -0.487 	ind 35: -0.503 	ind 36: -0.500 	ind 37: -0.497 	ind 38: -0.499 	ind 39: -0.501 	ind 40: -0.504 	ind 41: -0.525 	ind 42: -0.501 	ind 43: -0.496 	ind 44: -0.498 	ind 45: -0.500 	ind 46: -0.499 	ind 47: -0.518 	ind 48: -0.500 	ind 49: -0.503 	
2025-05-14 16:20:21,378 - modnet - INFO - Generation number 6

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:02<10:16,  2.48s/it]
  1%|          | 2/250 [00:02<04:43,  1.14s/it]
  1%|          | 3/250 [00:03<03:23,  1.22it/s]
  2%|▏         | 4/250 [00:03<03:04,  1.34it/s]
  2%|▏         | 5/250 [00:04<02:29,  1.64it/s]
  3%|▎         | 7/250 [00:04<01:36,  2.52it/s]
  4%|▎         | 9/250 [00:04<01:02,  3.84it/s]
  4%|▍         | 11/250 [00:04<00:43,  5.51it/s]
  6%|▌         | 14/250 [00:04<00:29,  8.13it/s]
  6%|▋         | 16/250 [00:05<00:33,  6.97it/s]
  7%|▋         | 18/250 [00:05<00:41,  5.62it/s]
  8%|▊         | 19/250 [00:06<00:46,  4.93it/s]
  8%|▊         | 20/250 [00:06<00:42,  5.46it/s]
  8%|▊         | 21/250 [00:06<00:51,  4.42it/s]
  9%|▉         | 23/250 [00:06<00:46,  4.85it/s]
 10%|▉         | 24/250 [00:07<00:47,  4.71it/s]
 10%|█         | 26/250 [00:07<00:35,  6.39it/s]
 11%|█         | 28/250 [00:07<00:30,  7.38it/s]
 12%|█▏        | 30/250 [00:07<00:24,  8.84it/s]
 13%|█▎        | 32/250 [00:08<00:36,  6.01it/s]
 13%|█▎        | 33/250 [00:08<00:40,  5.40it/s]
 14%|█▎        | 34/250 [00:08<00:41,  5.18it/s]
 14%|█▍        | 35/250 [00:09<01:23,  2.59it/s]
 15%|█▍        | 37/250 [00:10<01:04,  3.29it/s]
 15%|█▌        | 38/250 [00:10<01:01,  3.47it/s]
 16%|█▌        | 39/250 [00:12<02:16,  1.55it/s]
 16%|█▋        | 41/250 [00:12<01:26,  2.43it/s]
 17%|█▋        | 42/250 [00:12<01:28,  2.34it/s]
 18%|█▊        | 44/250 [00:12<00:57,  3.56it/s]
 18%|█▊        | 45/250 [00:13<00:55,  3.72it/s]
 19%|█▉        | 47/250 [00:13<00:39,  5.09it/s]
 19%|█▉        | 48/250 [00:13<00:51,  3.93it/s]
 20%|█▉        | 49/250 [00:14<00:58,  3.46it/s]
 20%|██        | 50/250 [00:14<01:28,  2.26it/s]
 21%|██        | 52/250 [00:15<01:21,  2.43it/s]
 21%|██        | 53/250 [00:15<01:10,  2.78it/s]
 22%|██▏       | 54/250 [00:16<01:04,  3.03it/s]
 22%|██▏       | 55/250 [00:16<01:05,  2.99it/s]
 23%|██▎       | 57/250 [00:16<00:42,  4.51it/s]
 23%|██▎       | 58/250 [00:16<00:47,  4.05it/s]
 24%|██▎       | 59/250 [00:17<00:50,  3.81it/s]
 24%|██▍       | 60/250 [00:17<01:05,  2.89it/s]
 24%|██▍       | 61/250 [00:17<00:55,  3.41it/s]
 25%|██▍       | 62/250 [00:18<01:13,  2.56it/s]
 25%|██▌       | 63/250 [00:20<02:19,  1.34it/s]
 26%|██▌       | 65/250 [00:20<01:22,  2.25it/s]
 27%|██▋       | 67/250 [00:21<01:18,  2.32it/s]
 27%|██▋       | 68/250 [00:21<01:09,  2.64it/s]
 28%|██▊       | 69/250 [00:21<00:56,  3.19it/s]
 28%|██▊       | 70/250 [00:21<00:50,  3.53it/s]
 29%|██▉       | 72/250 [00:21<00:33,  5.33it/s]
 29%|██▉       | 73/250 [00:22<01:06,  2.68it/s]
 30%|███       | 75/250 [00:22<00:43,  4.04it/s]
 31%|███       | 77/250 [00:23<00:32,  5.37it/s]
 32%|███▏      | 79/250 [00:23<00:24,  6.88it/s]
 32%|███▏      | 81/250 [00:23<00:32,  5.15it/s]
 33%|███▎      | 83/250 [00:23<00:26,  6.34it/s]
 34%|███▍      | 85/250 [00:24<00:39,  4.20it/s]
 34%|███▍      | 86/250 [00:25<00:42,  3.90it/s]
 35%|███▍      | 87/250 [00:25<00:38,  4.20it/s]
 35%|███▌      | 88/250 [00:25<00:35,  4.53it/s]
 36%|███▌      | 89/250 [00:26<00:53,  2.99it/s]
 36%|███▋      | 91/250 [00:26<00:42,  3.78it/s]
 37%|███▋      | 92/250 [00:26<00:45,  3.44it/s]
 38%|███▊      | 94/250 [00:26<00:30,  5.06it/s]
 38%|███▊      | 95/250 [00:27<00:44,  3.45it/s]
 39%|███▉      | 97/250 [00:28<01:00,  2.54it/s]
 39%|███▉      | 98/250 [00:28<00:50,  2.99it/s]
 40%|████      | 100/250 [00:29<00:38,  3.93it/s]
 40%|████      | 101/250 [00:29<00:36,  4.07it/s]
 41%|████      | 103/250 [00:29<00:27,  5.35it/s]
 42%|████▏     | 106/250 [00:29<00:17,  8.18it/s]
 43%|████▎     | 108/250 [00:30<00:31,  4.45it/s]
 44%|████▎     | 109/250 [00:30<00:30,  4.69it/s]
 44%|████▍     | 110/250 [00:30<00:31,  4.39it/s]
 45%|████▍     | 112/250 [00:31<00:32,  4.20it/s]
 45%|████▌     | 113/250 [00:31<00:29,  4.61it/s]
 46%|████▌     | 115/250 [00:31<00:24,  5.52it/s]
 47%|████▋     | 117/250 [00:33<00:43,  3.06it/s]
 48%|████▊     | 119/250 [00:33<00:35,  3.74it/s]
 48%|████▊     | 120/250 [00:33<00:40,  3.22it/s]
 48%|████▊     | 121/250 [00:34<00:45,  2.82it/s]
 49%|████▉     | 122/250 [00:34<00:47,  2.67it/s]
 50%|████▉     | 124/250 [00:34<00:31,  4.04it/s]
 50%|█████     | 126/250 [00:35<00:28,  4.31it/s]
 51%|█████     | 127/250 [00:35<00:36,  3.36it/s]
 51%|█████     | 128/250 [00:36<00:49,  2.49it/s]
 52%|█████▏    | 129/250 [00:36<00:39,  3.03it/s]
 52%|█████▏    | 130/250 [00:36<00:32,  3.65it/s]
 52%|█████▏    | 131/250 [00:37<00:32,  3.64it/s]
 53%|█████▎    | 132/250 [00:37<00:45,  2.58it/s]
 53%|█████▎    | 133/250 [00:38<00:43,  2.68it/s]
 54%|█████▎    | 134/250 [00:38<00:41,  2.81it/s]
 54%|█████▍    | 135/250 [00:38<00:35,  3.21it/s]
 54%|█████▍    | 136/250 [00:38<00:28,  4.00it/s]
 55%|█████▍    | 137/250 [00:38<00:24,  4.55it/s]
 55%|█████▌    | 138/250 [00:39<00:21,  5.16it/s]
 56%|█████▌    | 139/250 [00:39<00:18,  5.98it/s]
 56%|█████▌    | 140/250 [00:39<00:17,  6.36it/s]
 56%|█████▋    | 141/250 [00:39<00:16,  6.50it/s]
 57%|█████▋    | 142/250 [00:39<00:15,  6.95it/s]
 58%|█████▊    | 144/250 [00:40<00:26,  3.97it/s]
 58%|█████▊    | 145/250 [00:40<00:23,  4.49it/s]
 59%|█████▉    | 147/250 [00:40<00:24,  4.18it/s]
 59%|█████▉    | 148/250 [00:41<00:26,  3.82it/s]
 60%|██████    | 150/250 [00:41<00:19,  5.13it/s]
 61%|██████    | 152/250 [00:41<00:19,  5.06it/s]
 61%|██████    | 153/250 [00:42<00:22,  4.29it/s]
 62%|██████▏   | 155/250 [00:42<00:17,  5.40it/s]
 62%|██████▏   | 156/250 [00:42<00:20,  4.59it/s]
 63%|██████▎   | 157/250 [00:43<00:25,  3.61it/s]
 64%|██████▎   | 159/250 [00:43<00:23,  3.87it/s]
 64%|██████▍   | 161/250 [00:44<00:21,  4.23it/s]
 65%|██████▍   | 162/250 [00:44<00:22,  4.00it/s]
 65%|██████▌   | 163/250 [00:44<00:20,  4.21it/s]
 66%|██████▌   | 164/250 [00:44<00:19,  4.40it/s]
 66%|██████▌   | 165/250 [00:45<00:17,  4.89it/s]
 67%|██████▋   | 167/250 [00:45<00:12,  6.47it/s]
 67%|██████▋   | 168/250 [00:45<00:13,  6.08it/s]
 68%|██████▊   | 169/250 [00:45<00:16,  4.86it/s]
 68%|██████▊   | 171/250 [00:45<00:11,  6.60it/s]
 69%|██████▉   | 172/250 [00:45<00:11,  6.68it/s]
 70%|██████▉   | 174/250 [00:46<00:09,  7.88it/s]
 70%|███████   | 176/250 [00:46<00:08,  8.39it/s]
 71%|███████   | 178/250 [00:46<00:07,  9.30it/s]
 72%|███████▏  | 179/250 [00:46<00:09,  7.42it/s]
 72%|███████▏  | 181/250 [00:47<00:14,  4.80it/s]
 73%|███████▎  | 182/250 [00:48<00:22,  3.02it/s]
 73%|███████▎  | 183/250 [00:49<00:32,  2.04it/s]
 74%|███████▎  | 184/250 [00:49<00:33,  1.98it/s]
 74%|███████▍  | 186/250 [00:50<00:23,  2.70it/s]
 75%|███████▍  | 187/250 [00:50<00:20,  3.04it/s]
 75%|███████▌  | 188/250 [00:50<00:17,  3.64it/s]
 76%|███████▌  | 190/250 [00:50<00:11,  5.41it/s]
 76%|███████▋  | 191/250 [00:50<00:10,  5.61it/s]
 77%|███████▋  | 192/250 [00:51<00:12,  4.51it/s]
 77%|███████▋  | 193/250 [00:51<00:12,  4.60it/s]
 78%|███████▊  | 195/250 [00:51<00:09,  5.73it/s]
 78%|███████▊  | 196/250 [00:51<00:10,  5.04it/s]
 79%|███████▉  | 197/250 [00:52<00:09,  5.44it/s]
 79%|███████▉  | 198/250 [00:52<00:11,  4.68it/s]
 80%|███████▉  | 199/250 [00:52<00:10,  5.05it/s]
 80%|████████  | 200/250 [00:53<00:16,  3.02it/s]
 80%|████████  | 201/250 [00:53<00:14,  3.34it/s]
 81%|████████  | 202/250 [00:53<00:12,  3.77it/s]
 81%|████████  | 203/250 [00:53<00:11,  3.93it/s]
 82%|████████▏ | 204/250 [00:53<00:09,  4.69it/s]
 82%|████████▏ | 206/250 [00:54<00:07,  6.01it/s]
 83%|████████▎ | 207/250 [00:54<00:09,  4.46it/s]
 83%|████████▎ | 208/250 [00:54<00:08,  4.90it/s]
 84%|████████▎ | 209/250 [00:54<00:08,  5.07it/s]
 84%|████████▍ | 211/250 [00:54<00:05,  6.89it/s]
 85%|████████▌ | 213/250 [00:55<00:04,  8.04it/s]
 86%|████████▌ | 214/250 [00:55<00:04,  7.86it/s]
 86%|████████▋ | 216/250 [00:55<00:04,  8.31it/s]
 87%|████████▋ | 217/250 [00:55<00:04,  7.66it/s]
 87%|████████▋ | 218/250 [00:56<00:05,  5.71it/s]
 88%|████████▊ | 219/250 [00:56<00:05,  5.22it/s]
 88%|████████▊ | 220/250 [00:56<00:09,  3.21it/s]
 89%|████████▉ | 223/250 [00:57<00:04,  5.57it/s]
 90%|████████▉ | 224/250 [00:57<00:04,  5.70it/s]
 90%|█████████ | 226/250 [00:57<00:03,  6.95it/s]
 91%|█████████ | 227/250 [00:57<00:04,  4.92it/s]
 92%|█████████▏| 229/250 [00:58<00:03,  6.13it/s]
 92%|█████████▏| 230/250 [00:58<00:03,  6.13it/s]
 92%|█████████▏| 231/250 [00:58<00:03,  5.08it/s]
 93%|█████████▎| 232/250 [00:58<00:03,  5.05it/s]
 94%|█████████▎| 234/250 [00:58<00:02,  7.11it/s]
 94%|█████████▍| 235/250 [00:59<00:03,  4.88it/s]
 94%|█████████▍| 236/250 [00:59<00:03,  4.61it/s]
 95%|█████████▍| 237/250 [00:59<00:02,  5.10it/s]
 95%|█████████▌| 238/250 [00:59<00:02,  4.84it/s]
 96%|█████████▌| 240/250 [01:00<00:01,  5.16it/s]
 97%|█████████▋| 243/250 [01:00<00:00,  7.48it/s]
 98%|█████████▊| 244/250 [01:01<00:01,  3.99it/s]
 98%|█████████▊| 245/250 [01:01<00:01,  4.57it/s]
 98%|█████████▊| 246/250 [01:01<00:01,  3.60it/s]
 99%|█████████▉| 247/250 [01:01<00:00,  4.21it/s]
 99%|█████████▉| 248/250 [01:02<00:00,  2.54it/s]
100%|██████████| 250/250 [01:04<00:00,  1.45it/s]
100%|██████████| 250/250 [01:04<00:00,  3.85it/s]
2025-05-14 16:21:26,327 - modnet - INFO - Loss per individual: ind 0: -0.500 	ind 1: -0.497 	ind 2: -0.528 	ind 3: -0.503 	ind 4: -0.501 	ind 5: -0.500 	ind 6: -0.496 	ind 7: -0.487 	ind 8: -0.652 	ind 9: -0.497 	ind 10: -0.516 	ind 11: -0.466 	ind 12: -0.508 	ind 13: -0.497 	ind 14: -0.532 	ind 15: -0.499 	ind 16: -0.586 	ind 17: -0.498 	ind 18: -0.502 	ind 19: -0.502 	ind 20: -0.502 	ind 21: -0.458 	ind 22: -0.499 	ind 23: -0.531 	ind 24: -0.501 	ind 25: -0.496 	ind 26: -0.501 	ind 27: -0.614 	ind 28: -0.556 	ind 29: -0.504 	ind 30: -0.512 	ind 31: -0.507 	ind 32: -0.508 	ind 33: -0.501 	ind 34: -0.498 	ind 35: -0.499 	ind 36: -0.501 	ind 37: -0.518 	ind 38: -0.484 	ind 39: -0.493 	ind 40: -0.501 	ind 41: -0.535 	ind 42: -0.497 	ind 43: -0.501 	ind 44: -0.504 	ind 45: -0.505 	ind 46: -0.553 	ind 47: -0.500 	ind 48: -0.501 	ind 49: -0.493 	
2025-05-14 16:21:26,331 - modnet - INFO - Generation number 7

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:02<10:52,  2.62s/it]
  1%|          | 2/250 [00:02<05:06,  1.24s/it]
  1%|          | 3/250 [00:03<03:37,  1.14it/s]
  2%|▏         | 5/250 [00:03<01:50,  2.22it/s]
  2%|▏         | 6/250 [00:04<02:13,  1.82it/s]
  3%|▎         | 7/250 [00:04<01:55,  2.11it/s]
  3%|▎         | 8/250 [00:04<01:40,  2.40it/s]
  4%|▎         | 9/250 [00:05<01:21,  2.97it/s]
  4%|▍         | 10/250 [00:05<01:28,  2.71it/s]
  4%|▍         | 11/250 [00:05<01:17,  3.10it/s]
  5%|▍         | 12/250 [00:06<01:19,  3.00it/s]
  6%|▌         | 14/250 [00:06<00:55,  4.23it/s]
  6%|▌         | 15/250 [00:06<01:03,  3.72it/s]
  7%|▋         | 17/250 [00:06<00:42,  5.48it/s]
  8%|▊         | 19/250 [00:07<00:43,  5.33it/s]
  8%|▊         | 20/250 [00:07<00:50,  4.54it/s]
  8%|▊         | 21/250 [00:07<00:56,  4.08it/s]
  9%|▉         | 23/250 [00:08<00:59,  3.84it/s]
 10%|█         | 25/250 [00:08<00:42,  5.31it/s]
 10%|█         | 26/250 [00:08<00:40,  5.52it/s]
 11%|█         | 27/250 [00:09<00:48,  4.61it/s]
 11%|█         | 28/250 [00:10<01:43,  2.15it/s]
 12%|█▏        | 29/250 [00:10<01:27,  2.51it/s]
 12%|█▏        | 31/250 [00:10<01:03,  3.44it/s]
 13%|█▎        | 33/250 [00:10<00:46,  4.67it/s]
 14%|█▍        | 35/250 [00:11<00:38,  5.56it/s]
 15%|█▍        | 37/250 [00:11<00:32,  6.64it/s]
 16%|█▌        | 39/250 [00:11<00:25,  8.17it/s]
 16%|█▋        | 41/250 [00:11<00:29,  7.10it/s]
 17%|█▋        | 42/250 [00:12<00:28,  7.33it/s]
 17%|█▋        | 43/250 [00:12<00:40,  5.13it/s]
 18%|█▊        | 44/250 [00:12<00:41,  5.02it/s]
 18%|█▊        | 46/250 [00:13<00:54,  3.73it/s]
 19%|█▉        | 47/250 [00:14<01:17,  2.62it/s]
 19%|█▉        | 48/250 [00:14<01:30,  2.24it/s]
 20%|█▉        | 49/250 [00:15<01:21,  2.47it/s]
 20%|██        | 51/250 [00:15<00:56,  3.53it/s]
 21%|██        | 53/250 [00:15<00:45,  4.37it/s]
 22%|██▏       | 54/250 [00:15<00:43,  4.54it/s]
 22%|██▏       | 55/250 [00:16<00:49,  3.92it/s]
 22%|██▏       | 56/250 [00:16<00:53,  3.60it/s]
 23%|██▎       | 57/250 [00:16<00:45,  4.20it/s]
 23%|██▎       | 58/250 [00:16<00:44,  4.29it/s]
 24%|██▎       | 59/250 [00:17<00:41,  4.57it/s]
 24%|██▍       | 60/250 [00:17<00:35,  5.30it/s]
 24%|██▍       | 61/250 [00:18<01:23,  2.27it/s]
 26%|██▌       | 64/250 [00:18<00:42,  4.40it/s]
 26%|██▋       | 66/250 [00:18<00:32,  5.74it/s]
 27%|██▋       | 68/250 [00:18<00:25,  7.12it/s]
 28%|██▊       | 70/250 [00:18<00:20,  8.74it/s]
 29%|██▉       | 72/250 [00:18<00:19,  9.30it/s]
 30%|██▉       | 74/250 [00:19<00:16, 10.48it/s]
 30%|███       | 76/250 [00:19<00:24,  7.09it/s]
 31%|███       | 78/250 [00:20<00:27,  6.24it/s]
 32%|███▏      | 80/250 [00:20<00:30,  5.65it/s]
 33%|███▎      | 83/250 [00:20<00:21,  7.80it/s]
 34%|███▍      | 85/250 [00:21<00:32,  5.07it/s]
 35%|███▍      | 87/250 [00:21<00:26,  6.15it/s]
 35%|███▌      | 88/250 [00:21<00:25,  6.26it/s]
 36%|███▌      | 90/250 [00:22<00:27,  5.78it/s]
 37%|███▋      | 92/250 [00:22<00:27,  5.66it/s]
 37%|███▋      | 93/250 [00:22<00:29,  5.32it/s]
 38%|███▊      | 94/250 [00:22<00:27,  5.72it/s]
 38%|███▊      | 95/250 [00:23<00:31,  4.92it/s]
 39%|███▉      | 97/250 [00:23<00:24,  6.16it/s]
 39%|███▉      | 98/250 [00:23<00:27,  5.48it/s]
 40%|████      | 100/250 [00:24<00:30,  4.89it/s]
 41%|████      | 102/250 [00:24<00:25,  5.81it/s]
 41%|████      | 103/250 [00:24<00:38,  3.85it/s]
 42%|████▏     | 104/250 [00:25<00:34,  4.26it/s]
 42%|████▏     | 105/250 [00:25<00:43,  3.31it/s]
 42%|████▏     | 106/250 [00:25<00:37,  3.87it/s]
 43%|████▎     | 107/250 [00:25<00:31,  4.50it/s]
 43%|████▎     | 108/250 [00:26<00:41,  3.42it/s]
 44%|████▍     | 110/250 [00:26<00:38,  3.62it/s]
 44%|████▍     | 111/250 [00:26<00:34,  3.99it/s]
 45%|████▍     | 112/250 [00:27<00:38,  3.56it/s]
 46%|████▌     | 114/250 [00:27<00:28,  4.82it/s]
 46%|████▌     | 115/250 [00:27<00:31,  4.27it/s]
 46%|████▋     | 116/250 [00:28<00:40,  3.34it/s]
 47%|████▋     | 117/250 [00:28<00:38,  3.46it/s]
 47%|████▋     | 118/250 [00:28<00:32,  4.06it/s]
 48%|████▊     | 119/250 [00:29<00:45,  2.88it/s]
 48%|████▊     | 121/250 [00:29<00:33,  3.85it/s]
 49%|████▉     | 122/250 [00:30<00:38,  3.33it/s]
 49%|████▉     | 123/250 [00:30<00:48,  2.60it/s]
 50%|████▉     | 124/250 [00:31<00:47,  2.63it/s]
 50%|█████     | 125/250 [00:31<01:03,  1.96it/s]
 50%|█████     | 126/250 [00:32<00:57,  2.15it/s]
 51%|█████     | 127/250 [00:32<00:47,  2.58it/s]
 51%|█████     | 128/250 [00:32<00:39,  3.11it/s]
 52%|█████▏    | 129/250 [00:33<00:43,  2.76it/s]
 52%|█████▏    | 130/250 [00:34<01:31,  1.31it/s]
 52%|█████▏    | 131/250 [00:35<01:11,  1.66it/s]
 54%|█████▎    | 134/250 [00:35<00:35,  3.25it/s]
 54%|█████▍    | 135/250 [00:36<00:55,  2.07it/s]
 54%|█████▍    | 136/250 [00:36<00:47,  2.40it/s]
 55%|█████▍    | 137/250 [00:37<00:57,  1.97it/s]
 56%|█████▌    | 139/250 [00:38<00:51,  2.16it/s]
 56%|█████▋    | 141/250 [00:38<00:37,  2.91it/s]
 57%|█████▋    | 142/250 [00:38<00:38,  2.84it/s]
 58%|█████▊    | 144/250 [00:39<00:37,  2.80it/s]
 58%|█████▊    | 146/250 [00:39<00:26,  3.95it/s]
 59%|█████▉    | 147/250 [00:40<00:29,  3.55it/s]
 60%|█████▉    | 149/250 [00:40<00:21,  4.65it/s]
 60%|██████    | 150/250 [00:40<00:20,  4.76it/s]
 60%|██████    | 151/250 [00:40<00:18,  5.30it/s]
 61%|██████    | 152/250 [00:42<00:54,  1.79it/s]
 61%|██████    | 153/250 [00:43<00:58,  1.66it/s]
 62%|██████▏   | 154/250 [00:43<00:54,  1.77it/s]
 62%|██████▏   | 155/250 [00:43<00:47,  2.02it/s]
 62%|██████▏   | 156/250 [00:44<00:42,  2.21it/s]
 63%|██████▎   | 157/250 [00:45<01:03,  1.47it/s]
 63%|██████▎   | 158/250 [00:45<00:49,  1.85it/s]
 64%|██████▎   | 159/250 [00:46<00:48,  1.86it/s]
 64%|██████▍   | 160/250 [00:46<00:38,  2.32it/s]
 65%|██████▍   | 162/250 [00:46<00:23,  3.75it/s]
 66%|██████▌   | 164/250 [00:46<00:15,  5.40it/s]
 66%|██████▋   | 166/250 [00:46<00:11,  7.05it/s]
 67%|██████▋   | 168/250 [00:46<00:09,  8.51it/s]
 68%|██████▊   | 170/250 [00:47<00:09,  8.12it/s]
 69%|██████▉   | 172/250 [00:47<00:10,  7.78it/s]
 69%|██████▉   | 173/250 [00:47<00:10,  7.18it/s]
 70%|██████▉   | 174/250 [00:47<00:11,  6.61it/s]
 70%|███████   | 175/250 [00:48<00:16,  4.47it/s]
 70%|███████   | 176/250 [00:49<00:34,  2.15it/s]
 71%|███████   | 177/250 [00:49<00:31,  2.35it/s]
 71%|███████   | 178/250 [00:50<00:40,  1.76it/s]
 72%|███████▏  | 179/250 [00:50<00:31,  2.28it/s]
 72%|███████▏  | 181/250 [00:50<00:18,  3.72it/s]
 73%|███████▎  | 182/250 [00:51<00:17,  4.00it/s]
 73%|███████▎  | 183/250 [00:51<00:17,  3.85it/s]
 74%|███████▎  | 184/250 [00:51<00:14,  4.61it/s]
 74%|███████▍  | 185/250 [00:52<00:21,  3.00it/s]
 74%|███████▍  | 186/250 [00:52<00:24,  2.57it/s]
 75%|███████▌  | 188/250 [00:52<00:17,  3.59it/s]
 76%|███████▌  | 189/250 [00:53<00:14,  4.20it/s]
 77%|███████▋  | 192/250 [00:53<00:15,  3.69it/s]
 78%|███████▊  | 194/250 [00:54<00:14,  3.95it/s]
 78%|███████▊  | 195/250 [00:54<00:13,  3.97it/s]
 78%|███████▊  | 196/250 [00:55<00:21,  2.50it/s]
 79%|███████▉  | 197/250 [00:55<00:19,  2.75it/s]
 80%|███████▉  | 199/250 [00:56<00:14,  3.41it/s]
 80%|████████  | 201/250 [00:56<00:13,  3.74it/s]
 81%|████████  | 202/250 [00:56<00:13,  3.51it/s]
 82%|████████▏ | 204/250 [00:57<00:11,  3.91it/s]
 82%|████████▏ | 205/250 [00:57<00:14,  3.16it/s]
 82%|████████▏ | 206/250 [00:58<00:15,  2.78it/s]
 83%|████████▎ | 207/250 [00:58<00:13,  3.19it/s]
 83%|████████▎ | 208/250 [00:58<00:11,  3.80it/s]
 84%|████████▎ | 209/250 [00:58<00:09,  4.47it/s]
 84%|████████▍ | 210/250 [00:59<00:10,  3.82it/s]
 84%|████████▍ | 211/250 [00:59<00:08,  4.45it/s]
 85%|████████▌ | 213/250 [00:59<00:05,  6.47it/s]
 86%|████████▌ | 214/250 [01:00<00:09,  3.93it/s]
 86%|████████▋ | 216/250 [01:00<00:05,  5.85it/s]
 87%|████████▋ | 218/250 [01:01<00:10,  3.06it/s]
 88%|████████▊ | 220/250 [01:01<00:08,  3.61it/s]
 88%|████████▊ | 221/250 [01:02<00:08,  3.46it/s]
 89%|████████▉ | 222/250 [01:02<00:09,  2.80it/s]
 89%|████████▉ | 223/250 [01:02<00:09,  2.89it/s]
 90%|████████▉ | 224/250 [01:03<00:11,  2.29it/s]
 90%|█████████ | 225/250 [01:04<00:12,  1.94it/s]
 90%|█████████ | 226/250 [01:04<00:10,  2.37it/s]
 91%|█████████ | 227/250 [01:04<00:07,  2.88it/s]
 91%|█████████ | 228/250 [01:05<00:08,  2.71it/s]
 92%|█████████▏| 229/250 [01:05<00:06,  3.15it/s]
 92%|█████████▏| 230/250 [01:05<00:08,  2.44it/s]
 93%|█████████▎| 233/250 [01:06<00:04,  3.40it/s]
 94%|█████████▎| 234/250 [01:07<00:05,  2.80it/s]
 94%|█████████▍| 235/250 [01:07<00:05,  2.56it/s]
 95%|█████████▍| 237/250 [01:07<00:03,  3.56it/s]
 95%|█████████▌| 238/250 [01:08<00:03,  3.03it/s]
 96%|█████████▌| 239/250 [01:08<00:03,  3.40it/s]
 96%|█████████▋| 241/250 [01:08<00:02,  4.25it/s]
 97%|█████████▋| 242/250 [01:09<00:02,  3.22it/s]
 97%|█████████▋| 243/250 [01:09<00:01,  3.72it/s]
 98%|█████████▊| 244/250 [01:09<00:01,  3.75it/s]
 98%|█████████▊| 245/250 [01:10<00:01,  4.29it/s]
 98%|█████████▊| 246/250 [01:22<00:14,  3.53s/it]
 99%|█████████▉| 247/250 [01:25<00:10,  3.55s/it]
 99%|█████████▉| 248/250 [01:26<00:05,  2.70s/it]
100%|█████████▉| 249/250 [01:31<00:03,  3.29s/it]
100%|██████████| 250/250 [01:32<00:00,  2.71s/it]
100%|██████████| 250/250 [01:32<00:00,  2.71it/s]
2025-05-14 16:22:58,746 - modnet - INFO - Loss per individual: ind 0: -0.519 	ind 1: -0.505 	ind 2: -0.570 	ind 3: -0.502 	ind 4: -0.496 	ind 5: -0.492 	ind 6: -0.513 	ind 7: -0.492 	ind 8: -0.502 	ind 9: -0.496 	ind 10: -0.569 	ind 11: -0.500 	ind 12: -0.500 	ind 13: -0.500 	ind 14: -0.499 	ind 15: -0.622 	ind 16: -0.513 	ind 17: -0.502 	ind 18: -0.499 	ind 19: -0.499 	ind 20: -0.500 	ind 21: -0.608 	ind 22: -0.500 	ind 23: -0.548 	ind 24: -0.579 	ind 25: -0.889 	ind 26: -0.501 	ind 27: -0.501 	ind 28: -0.626 	ind 29: -0.781 	ind 30: -0.912 	ind 31: -0.597 	ind 32: -0.502 	ind 33: -0.499 	ind 34: -0.571 	ind 35: -0.497 	ind 36: -0.502 	ind 37: -0.500 	ind 38: -0.514 	ind 39: -0.501 	ind 40: -0.518 	ind 41: -0.499 	ind 42: -0.500 	ind 43: -0.915 	ind 44: -0.500 	ind 45: -0.501 	ind 46: -0.496 	ind 47: -0.501 	ind 48: -0.500 	ind 49: -0.501 	
2025-05-14 16:22:58,749 - modnet - INFO - Early stopping: same best model for 8 consecutive generations
2025-05-14 16:22:58,749 - modnet - INFO - Early stopping at generation number 7
2025-05-14 16:23:01,399 - modnet - INFO - Model successfully saved as results/matbench_glass_best_model_fold_1.pkl!
Saved best model for fold 1 to results/matbench_glass_best_model_fold_1.pkl
2025-05-14 16:23:01.437422: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 16:23:01.449704: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 16:23:05,905 - modnet - INFO - [Fold 1] ROC-AUC: 0.959185960233971
Traceback (most recent call last):
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/pandas/core/indexes/base.py", line 2898, in get_loc
    return self._engine.get_loc(casted_key)
  File "pandas/_libs/index.pyx", line 70, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/index.pyx", line 101, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 1675, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 1683, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'target'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "run_benchmark.py", line 861, in <module>
    results = benchmark(data, settings, n_jobs=n_jobs, fast=fast)
  File "run_benchmark.py", line 140, in benchmark
    return matbench_benchmark(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 137, in matbench_benchmark
    fold_results.append(train_fold(fold, *args, **model_kwargs))
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 375, in train_fold
    p = predictions[col].values
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/pandas/core/frame.py", line 2906, in __getitem__
    indexer = self.columns.get_loc(key)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/pandas/core/indexes/base.py", line 2900, in get_loc
    raise KeyError(key) from err
KeyError: 'target'
Job finished on Wed May 14 16:23:07 CEST 2025

Resources Used

Total Memory used                        - MEM              : 20GiB
Total CPU Time                           - CPU_Time         : 08:21:52
Execution Time                           - Wall_Time        : 00:15:41
total programme cpu time                 - Total_CPU        : 05:03:45
Total_CPU / CPU_Time  (%)                - ETA              : 60%
Number of alloc CPU                      - NCPUS            : 32
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 48
Mobilized Resources x Execution Time     - R_Wall_Time      : 12:32:48
CPU_Time / R_Wall_Time (%)               - ALPHA            : 66%
Energy (Joules)                                             : 167194

