Job started on Thu May 15 12:23:22 CEST 2025
Running on node(s): cnm028
2025-05-15 12:23:36.900070: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 32 jobs
Preparing nested CV run for task 'matbench_mp_gap'
Found 2 matching files for matbench_mp_gap
Found multiple data files ['./precomputed/matbench_mp_gap_matminer_featurizedMM2020Struct.pkl.gz', './precomputed/matbench_mp_gap_matminer_mvl32_roost_lmp_sisso_featurizedMM2020Struct.pkl.gz'], loading the first ./precomputed/matbench_mp_gap_matminer_featurizedMM2020Struct.pkl.gz
2025-05-15 12:23:48,070 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff0e31b5ee0> object, created with modnet version 0.1.13
Preparing fold 1 ...
2025-05-15 12:23:48,882 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff0e31b5c10> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_mp_gap_train_moddata_f1
2025-05-15 12:23:49,265 - modnet - INFO - Data successfully saved as folds/matbench_mp_gap_train_moddata_f1!
Preparing fold 2 ...
2025-05-15 12:23:50,089 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff0703ffcd0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_mp_gap_train_moddata_f2
2025-05-15 12:23:50,468 - modnet - INFO - Data successfully saved as folds/matbench_mp_gap_train_moddata_f2!
Preparing fold 3 ...
2025-05-15 12:23:51,282 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff0ba8565b0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_mp_gap_train_moddata_f3
2025-05-15 12:23:51,777 - modnet - INFO - Data successfully saved as folds/matbench_mp_gap_train_moddata_f3!
Preparing fold 4 ...
2025-05-15 12:23:52,509 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff070406970> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_mp_gap_train_moddata_f4
2025-05-15 12:23:52,891 - modnet - INFO - Data successfully saved as folds/matbench_mp_gap_train_moddata_f4!
Preparing fold 5 ...
2025-05-15 12:23:53,608 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff07040f1f0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_mp_gap_train_moddata_f5
2025-05-15 12:23:53,983 - modnet - INFO - Data successfully saved as folds/matbench_mp_gap_train_moddata_f5!
Training fold 1 ...
Fold 1 already completed. Loading saved results.
2025-05-15 12:23:54.114181: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:23:54.114630: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:23:54.114655: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:23:54.114692: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:23:54.114925: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:23:55,152 - modnet - INFO - Loaded <modnet.models.ensemble.EnsembleMODNetModel object at 0x7ff0e3a03430> object, created with modnet version 0.1.13
Loaded saved model from results/matbench_mp_gap_best_model_fold_1.pkl
Training fold 2 ...
Fold 2 already completed. Loading saved results.
2025-05-15 12:23:56,350 - modnet - INFO - Loaded <modnet.models.ensemble.EnsembleMODNetModel object at 0x7ff0704bd2b0> object, created with modnet version 0.1.13
Loaded saved model from results/matbench_mp_gap_best_model_fold_2.pkl
Training fold 3 ...
Fold 3 already completed. Loading saved results.
2025-05-15 12:23:57,476 - modnet - INFO - Loaded <modnet.models.ensemble.EnsembleMODNetModel object at 0x7ff09c373f70> object, created with modnet version 0.1.13
Loaded saved model from results/matbench_mp_gap_best_model_fold_3.pkl
Training fold 4 ...
Fold 4 already completed. Loading saved results.
2025-05-15 12:23:58,597 - modnet - INFO - Loaded <modnet.models.ensemble.EnsembleMODNetModel object at 0x7ff09ae59c70> object, created with modnet version 0.1.13
Loaded saved model from results/matbench_mp_gap_best_model_fold_4.pkl
Training fold 5 ...
Processing fold 5 ...
2025-05-15 12:23:58,757 - modnet - INFO - Targets:
2025-05-15 12:23:58,757 - modnet - INFO - 1)target: regression
2025-05-15 12:23:59,753 - modnet - INFO - Multiprocessing on 32 cores. Total of 128 cores available.
2025-05-15 12:23:59,753 - modnet - INFO - Generation number 0

  0%|          | 0/250 [00:00<?, ?it/s]2025-05-15 12:24:03.026815: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.026837: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.027281: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.045219: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.053368: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.053353: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.053457: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.053761: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.053805: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.054347: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.054430: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.054718: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.054766: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.054828: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.054906: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.054914: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.064455: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.067495: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.067496: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.067519: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.067694: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.068317: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.068317: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.068330: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.070269: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.085396: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.094396: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.094396: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.097293: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.112837: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.119151: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:03.148326: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-15 12:24:09.070557: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.070951: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:09.070977: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:09.071009: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:09.071251: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.177992: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.178446: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:09.178471: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:09.178510: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:09.178840: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.278348: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.278810: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:09.278835: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:09.278868: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:09.279125: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.341439: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:09.355292: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:09.389306: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.389736: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:09.389765: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:09.389798: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:09.390036: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.453958: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:09.467279: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:09.499185: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.499615: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:09.499640: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:09.499675: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:09.499927: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.566735: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:09.580268: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:09.595930: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.596382: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:09.596408: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:09.596440: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:09.596662: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.684341: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:09.686172: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:09.709605: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.710128: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:09.710155: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:09.710190: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:09.710495: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.772637: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:09.786307: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:09.801678: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.802159: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:09.802187: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:09.802224: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:09.802482: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.863248: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:09.877263: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:09.910325: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.910804: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:09.910829: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:09.910867: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:09.911126: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:09.961991: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:09.975271: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:10.029360: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.029900: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:10.029926: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:10.029964: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:10.030388: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.047790: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:10.049872: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:10.128335: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.128859: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:10.128885: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:10.128921: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:10.129165: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.171667: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:10.173610: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:10.242787: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.243341: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:10.243367: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:10.243403: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:10.243673: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.284709: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:10.298258: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:10.339403: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.339996: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:10.340029: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:10.340068: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:10.340372: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.400276: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:10.402228: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:10.437377: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.437939: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:10.437970: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:10.438012: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:10.438280: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.500982: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:10.503116: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:10.548186: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.548752: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:10.548778: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:10.548818: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:10.549080: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.600524: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:10.614262: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:10.648089: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.648635: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:10.648662: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:10.648699: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:10.648960: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.702676: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:10.716260: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:10.755178: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.755704: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:10.755733: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:10.755767: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:10.756169: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.825784: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:10.827997: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:10.861978: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.862608: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:10.862639: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:10.862679: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:10.863003: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.921412: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:10.936254: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:10.965746: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:10.966312: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:10.966341: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:10.966376: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:10.966603: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.066697: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:11.068855: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:11.086965: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.087612: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:11.087640: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:11.087680: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:11.087982: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.160419: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:11.162923: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:11.209180: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.209744: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:11.209772: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:11.209807: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:11.210212: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.266226: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:11.280279: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:11.346761: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.347348: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:11.347375: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:11.347415: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:11.347644: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.381485: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:11.395251: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:11.478359: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.478824: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:11.478854: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:11.478887: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:11.479299: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.527055: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:11.529205: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:11.599737: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.600310: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:11.600341: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:11.600378: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:11.600604: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.636700: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:11.650305: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:11.720272: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.720838: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:11.720866: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:11.720904: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:11.722366: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.807498: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:11.809782: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:11.837167: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.837807: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:11.837836: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:11.837878: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:11.838179: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.889662: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:11.903252: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:11.954219: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:11.954820: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:11.954856: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:11.954896: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:11.955174: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:12.043745: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:12.058288: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:12.068078: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:12.068783: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:12.068815: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:12.068856: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:12.069162: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:12.170471: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:12.172455: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:12.185209: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:12.185778: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:12.185808: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:12.185847: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:12.186117: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:12.257015: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:12.258974: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:12.376948: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:12.377684: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:12.377719: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:12.377764: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:12.378147: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:12.389693: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:12.391631: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:12.513479: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:12.516020: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:12.520821: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:12.521508: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:12.521538: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:12.521578: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:12.521949: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:12.718298: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:12.719195: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:24:12.719231: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:24:12.719283: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm028.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:24:12.719693: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:24:12.810189: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:12.813237: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:12.894326: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:12.897138: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
2025-05-15 12:24:13.073770: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 12:24:13.076447: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz

  0%|          | 1/250 [00:16<1:10:29, 16.99s/it]
  1%|          | 2/250 [00:17<30:01,  7.26s/it]  
  1%|          | 3/250 [00:17<16:38,  4.04s/it]
  2%|▏         | 4/250 [00:18<10:56,  2.67s/it]
  2%|▏         | 5/250 [00:18<07:48,  1.91s/it]
  2%|▏         | 6/250 [00:22<10:40,  2.63s/it]
  3%|▎         | 7/250 [00:24<09:49,  2.42s/it]
  3%|▎         | 8/250 [00:25<07:14,  1.80s/it]
  4%|▎         | 9/250 [00:25<05:13,  1.30s/it]
  4%|▍         | 10/250 [00:26<04:51,  1.21s/it]
  4%|▍         | 11/250 [00:26<03:52,  1.03it/s]
  5%|▍         | 12/250 [00:28<04:46,  1.21s/it]
  5%|▌         | 13/250 [00:29<03:58,  1.00s/it]
  6%|▌         | 14/250 [00:33<08:05,  2.06s/it]
  6%|▌         | 15/250 [00:36<08:26,  2.16s/it]
  6%|▋         | 16/250 [00:37<07:05,  1.82s/it]
  7%|▋         | 17/250 [00:38<06:50,  1.76s/it]
  7%|▋         | 18/250 [00:39<05:59,  1.55s/it]
  8%|▊         | 19/250 [00:39<04:22,  1.14s/it]
  8%|▊         | 20/250 [00:40<03:13,  1.19it/s]
  8%|▊         | 21/250 [00:40<02:27,  1.55it/s]
  9%|▉         | 22/250 [00:41<02:56,  1.29it/s]
  9%|▉         | 23/250 [00:41<02:26,  1.54it/s]
 10%|▉         | 24/250 [00:43<04:01,  1.07s/it]
 10%|█         | 25/250 [00:44<03:24,  1.10it/s]
 10%|█         | 26/250 [00:44<02:35,  1.44it/s]
 11%|█         | 27/250 [00:44<02:12,  1.69it/s]
 11%|█         | 28/250 [00:45<01:44,  2.12it/s]
 12%|█▏        | 30/250 [00:46<01:47,  2.05it/s]
 12%|█▏        | 31/250 [00:46<01:27,  2.49it/s]
 13%|█▎        | 32/250 [00:46<01:17,  2.80it/s]
 13%|█▎        | 33/250 [00:46<01:02,  3.44it/s]
 14%|█▎        | 34/250 [00:47<01:41,  2.13it/s]
 14%|█▍        | 35/250 [00:47<01:38,  2.19it/s]
 14%|█▍        | 36/250 [00:50<04:13,  1.18s/it]
 15%|█▍        | 37/250 [00:51<03:14,  1.09it/s]
 16%|█▌        | 39/250 [00:52<02:46,  1.27it/s]
 16%|█▌        | 40/250 [00:52<02:30,  1.39it/s]
 16%|█▋        | 41/250 [00:54<03:22,  1.03it/s]
 17%|█▋        | 42/250 [00:58<05:55,  1.71s/it]
 17%|█▋        | 43/250 [01:01<07:42,  2.24s/it]
 18%|█▊        | 44/250 [01:02<05:40,  1.65s/it]
 18%|█▊        | 45/250 [01:06<08:24,  2.46s/it]
 18%|█▊        | 46/250 [01:07<06:28,  1.90s/it]
 19%|█▉        | 47/250 [01:09<06:58,  2.06s/it]
 19%|█▉        | 48/250 [01:11<07:20,  2.18s/it]
 20%|█▉        | 49/250 [01:13<07:00,  2.09s/it]
 20%|██        | 50/250 [01:14<05:11,  1.56s/it]
 20%|██        | 51/250 [01:14<03:53,  1.17s/it]
 21%|██        | 52/250 [01:16<04:30,  1.36s/it]
 21%|██        | 53/250 [01:18<05:40,  1.73s/it]
 22%|██▏       | 54/250 [01:19<04:57,  1.52s/it]
 22%|██▏       | 55/250 [01:20<03:36,  1.11s/it]
 22%|██▏       | 56/250 [01:20<03:18,  1.02s/it]
 23%|██▎       | 57/250 [01:26<07:34,  2.36s/it]
 23%|██▎       | 58/250 [01:26<05:32,  1.73s/it]
 24%|██▎       | 59/250 [01:26<04:01,  1.27s/it]
 24%|██▍       | 60/250 [01:27<03:15,  1.03s/it]
 24%|██▍       | 61/250 [01:28<03:50,  1.22s/it]
 25%|██▍       | 62/250 [01:29<03:12,  1.02s/it]
 25%|██▌       | 63/250 [01:29<02:29,  1.25it/s]
 26%|██▋       | 66/250 [01:30<01:23,  2.21it/s]
 27%|██▋       | 67/250 [01:31<01:56,  1.57it/s]
 27%|██▋       | 68/250 [01:31<01:42,  1.78it/s]
 28%|██▊       | 69/250 [01:32<01:34,  1.91it/s]
 28%|██▊       | 70/250 [01:35<03:30,  1.17s/it]
 28%|██▊       | 71/250 [01:39<05:56,  1.99s/it]
 29%|██▉       | 72/250 [01:45<09:12,  3.10s/it]
 29%|██▉       | 73/250 [01:45<06:54,  2.34s/it]
 30%|██▉       | 74/250 [01:46<05:09,  1.76s/it]
 30%|███       | 75/250 [01:48<05:48,  1.99s/it]
 30%|███       | 76/250 [01:52<06:50,  2.36s/it]
 31%|███       | 77/250 [01:53<06:14,  2.17s/it]
 31%|███       | 78/250 [01:54<04:47,  1.67s/it]
 32%|███▏      | 79/250 [01:57<06:07,  2.15s/it]
 32%|███▏      | 80/250 [01:58<05:01,  1.77s/it]
 32%|███▏      | 81/250 [01:58<03:40,  1.31s/it]
 33%|███▎      | 82/250 [01:58<02:41,  1.04it/s]
 33%|███▎      | 83/250 [01:59<02:21,  1.18it/s]
 34%|███▎      | 84/250 [02:04<05:45,  2.08s/it]
 34%|███▍      | 85/250 [02:04<04:06,  1.49s/it]
 34%|███▍      | 86/250 [02:04<02:56,  1.08s/it]
 35%|███▍      | 87/250 [02:05<02:45,  1.02s/it]
 35%|███▌      | 88/250 [02:05<02:15,  1.20it/s]
 36%|███▌      | 89/250 [02:06<01:45,  1.52it/s]
 36%|███▌      | 90/250 [02:06<01:44,  1.53it/s]
 36%|███▋      | 91/250 [02:07<01:50,  1.44it/s]
 37%|███▋      | 92/250 [02:09<02:49,  1.07s/it]
 37%|███▋      | 93/250 [02:09<02:14,  1.17it/s]
 38%|███▊      | 94/250 [02:10<01:57,  1.33it/s]
 38%|███▊      | 95/250 [02:11<02:08,  1.21it/s]
 38%|███▊      | 96/250 [02:11<01:40,  1.53it/s]
 39%|███▉      | 97/250 [02:12<01:35,  1.61it/s]
 39%|███▉      | 98/250 [02:12<01:21,  1.85it/s]
 40%|███▉      | 99/250 [02:12<01:03,  2.37it/s]
 40%|████      | 100/250 [02:14<02:11,  1.14it/s]
 40%|████      | 101/250 [02:16<02:49,  1.14s/it]
 41%|████      | 102/250 [02:17<02:46,  1.12s/it]
 41%|████      | 103/250 [02:17<02:08,  1.14it/s]
 42%|████▏     | 104/250 [02:18<02:24,  1.01it/s]
 42%|████▏     | 105/250 [02:19<02:20,  1.04it/s]
 42%|████▏     | 106/250 [02:20<01:56,  1.23it/s]
 43%|████▎     | 107/250 [02:21<02:14,  1.07it/s]
 43%|████▎     | 108/250 [02:22<02:16,  1.04it/s]
 44%|████▎     | 109/250 [02:22<01:42,  1.38it/s]
 44%|████▍     | 111/250 [02:23<01:03,  2.19it/s]
 45%|████▍     | 112/250 [02:23<01:04,  2.14it/s]
 45%|████▌     | 113/250 [02:23<00:53,  2.58it/s]
 46%|████▌     | 114/250 [02:24<00:55,  2.47it/s]
 46%|████▌     | 115/250 [02:25<01:33,  1.45it/s]
 46%|████▋     | 116/250 [02:26<01:53,  1.18it/s]
 47%|████▋     | 117/250 [02:28<02:13,  1.00s/it]
 47%|████▋     | 118/250 [02:28<01:45,  1.26it/s]
 48%|████▊     | 119/250 [02:29<02:07,  1.03it/s]
 48%|████▊     | 120/250 [02:30<01:56,  1.11it/s]
 48%|████▊     | 121/250 [02:32<02:22,  1.10s/it]
 49%|████▉     | 122/250 [02:32<01:54,  1.12it/s]
 49%|████▉     | 123/250 [02:34<02:45,  1.30s/it]
 50%|████▉     | 124/250 [02:35<02:33,  1.22s/it]
 50%|█████     | 125/250 [02:39<03:48,  1.83s/it]
 50%|█████     | 126/250 [02:39<02:57,  1.43s/it]
 51%|█████     | 127/250 [02:41<03:27,  1.69s/it]
 51%|█████     | 128/250 [02:45<04:50,  2.38s/it]
 52%|█████▏    | 129/250 [02:49<05:35,  2.78s/it]
 52%|█████▏    | 130/250 [02:50<04:29,  2.25s/it]
 52%|█████▏    | 131/250 [02:54<05:18,  2.68s/it]
 53%|█████▎    | 132/250 [02:54<03:54,  1.99s/it]
 54%|█████▎    | 134/250 [02:55<02:17,  1.18s/it]
 54%|█████▍    | 135/250 [02:55<02:03,  1.07s/it]
 54%|█████▍    | 136/250 [02:57<02:04,  1.09s/it]
 55%|█████▌    | 138/250 [02:57<01:25,  1.30it/s]
 56%|█████▌    | 139/250 [02:57<01:11,  1.56it/s]
 56%|█████▌    | 140/250 [02:58<01:06,  1.67it/s]
 56%|█████▋    | 141/250 [02:58<00:55,  1.98it/s]
 57%|█████▋    | 142/250 [02:59<00:55,  1.94it/s]
 57%|█████▋    | 143/250 [02:59<01:03,  1.68it/s]
 58%|█████▊    | 144/250 [03:01<01:29,  1.18it/s]
 58%|█████▊    | 145/250 [03:01<01:08,  1.53it/s]
 59%|█████▉    | 147/250 [03:02<00:56,  1.81it/s]
 59%|█████▉    | 148/250 [03:02<00:45,  2.26it/s]
 60%|█████▉    | 149/250 [03:03<01:06,  1.53it/s]
 60%|██████    | 150/250 [03:04<00:59,  1.67it/s]
 60%|██████    | 151/250 [03:07<01:59,  1.21s/it]
 61%|██████    | 152/250 [03:07<01:31,  1.08it/s]
 61%|██████    | 153/250 [03:08<01:26,  1.13it/s]
 62%|██████▏   | 154/250 [03:08<01:06,  1.44it/s]
 62%|██████▏   | 155/250 [03:08<00:49,  1.90it/s]
 62%|██████▏   | 156/250 [03:09<01:06,  1.42it/s]
 63%|██████▎   | 157/250 [03:10<01:08,  1.36it/s]
 63%|██████▎   | 158/250 [03:10<01:02,  1.47it/s]
 64%|██████▎   | 159/250 [03:12<01:13,  1.23it/s]
 64%|██████▍   | 160/250 [03:12<00:55,  1.62it/s]
 65%|██████▌   | 163/250 [03:12<00:29,  2.95it/s]
 66%|██████▌   | 164/250 [03:15<01:08,  1.26it/s]
 66%|██████▌   | 165/250 [03:15<01:08,  1.24it/s]
 66%|██████▋   | 166/250 [03:16<01:04,  1.31it/s]
 67%|██████▋   | 167/250 [03:17<01:11,  1.16it/s]
 68%|██████▊   | 169/250 [03:18<00:45,  1.78it/s]
 68%|██████▊   | 170/250 [03:19<01:09,  1.15it/s]
 68%|██████▊   | 171/250 [03:20<00:58,  1.34it/s]
 69%|██████▉   | 172/250 [03:20<00:53,  1.45it/s]
 69%|██████▉   | 173/250 [03:24<01:59,  1.56s/it]
 70%|██████▉   | 174/250 [03:25<01:45,  1.38s/it]
 70%|███████   | 175/250 [03:28<02:19,  1.86s/it]
 70%|███████   | 176/250 [03:28<01:41,  1.37s/it]
 71%|███████   | 177/250 [03:29<01:19,  1.09s/it]
 71%|███████   | 178/250 [03:29<01:02,  1.16it/s]
 72%|███████▏  | 179/250 [03:33<01:57,  1.66s/it]
 72%|███████▏  | 180/250 [03:37<02:50,  2.44s/it]
 72%|███████▏  | 181/250 [03:38<02:20,  2.04s/it]
 73%|███████▎  | 182/250 [03:38<01:40,  1.48s/it]
 73%|███████▎  | 183/250 [03:46<03:52,  3.47s/it]
 74%|███████▎  | 184/250 [03:47<02:46,  2.52s/it]
 74%|███████▍  | 185/250 [03:47<02:02,  1.89s/it]
 74%|███████▍  | 186/250 [03:48<01:51,  1.74s/it]
 75%|███████▍  | 187/250 [03:51<02:06,  2.00s/it]
 75%|███████▌  | 188/250 [04:00<04:16,  4.13s/it]
 76%|███████▌  | 189/250 [04:00<03:00,  2.95s/it]
 76%|███████▌  | 190/250 [04:02<02:38,  2.64s/it]
 76%|███████▋  | 191/250 [04:04<02:17,  2.33s/it]
 77%|███████▋  | 192/250 [04:05<01:53,  1.96s/it]
 77%|███████▋  | 193/250 [04:09<02:22,  2.51s/it]
 78%|███████▊  | 194/250 [04:10<01:52,  2.02s/it]
 78%|███████▊  | 195/250 [04:10<01:22,  1.51s/it]
 78%|███████▊  | 196/250 [04:11<01:17,  1.44s/it]
 79%|███████▉  | 197/250 [04:13<01:24,  1.59s/it]
 79%|███████▉  | 198/250 [04:17<01:52,  2.17s/it]
 80%|███████▉  | 199/250 [04:20<02:11,  2.58s/it]
 80%|████████  | 200/250 [04:20<01:34,  1.89s/it]
 80%|████████  | 201/250 [04:26<02:19,  2.84s/it]
 81%|████████  | 202/250 [04:26<01:36,  2.02s/it]
 81%|████████  | 203/250 [04:26<01:14,  1.59s/it]
 82%|████████▏ | 204/250 [04:27<01:02,  1.35s/it]
 82%|████████▏ | 205/250 [04:29<01:04,  1.42s/it]
 83%|████████▎ | 207/250 [04:29<00:34,  1.26it/s]
 83%|████████▎ | 208/250 [04:29<00:29,  1.40it/s]
 84%|████████▎ | 209/250 [04:31<00:36,  1.14it/s]
 84%|████████▍ | 210/250 [04:33<00:49,  1.23s/it]
 84%|████████▍ | 211/250 [04:34<00:48,  1.25s/it]
 85%|████████▍ | 212/250 [04:35<00:39,  1.04s/it]
 85%|████████▌ | 213/250 [04:37<00:51,  1.40s/it]
 86%|████████▌ | 214/250 [04:37<00:41,  1.15s/it]
 86%|████████▌ | 215/250 [04:40<00:50,  1.45s/it]
 86%|████████▋ | 216/250 [04:41<00:47,  1.40s/it]
 87%|████████▋ | 217/250 [04:42<00:39,  1.20s/it]
 87%|████████▋ | 218/250 [04:42<00:28,  1.12it/s]
 88%|████████▊ | 219/250 [04:44<00:41,  1.35s/it]
 88%|████████▊ | 220/250 [04:45<00:38,  1.27s/it]
 89%|████████▉ | 222/250 [04:46<00:27,  1.03it/s]
 89%|████████▉ | 223/250 [04:47<00:21,  1.25it/s]
 90%|████████▉ | 224/250 [04:47<00:16,  1.61it/s]
 90%|█████████ | 225/250 [04:48<00:21,  1.17it/s]
 90%|█████████ | 226/250 [04:49<00:16,  1.47it/s]
 91%|█████████ | 227/250 [04:49<00:13,  1.69it/s]
 91%|█████████ | 228/250 [04:50<00:15,  1.43it/s]
 92%|█████████▏| 229/250 [04:50<00:13,  1.52it/s]
 92%|█████████▏| 230/250 [04:52<00:20,  1.05s/it]
 92%|█████████▏| 231/250 [04:53<00:15,  1.24it/s]
 93%|█████████▎| 232/250 [04:53<00:11,  1.52it/s]
 93%|█████████▎| 233/250 [04:53<00:10,  1.63it/s]
 94%|█████████▎| 234/250 [04:56<00:16,  1.03s/it]
 94%|█████████▍| 235/250 [04:56<00:13,  1.13it/s]
 94%|█████████▍| 236/250 [04:57<00:13,  1.02it/s]
 95%|█████████▍| 237/250 [04:57<00:09,  1.33it/s]
 95%|█████████▌| 238/250 [04:58<00:08,  1.43it/s]
 96%|█████████▌| 239/250 [04:59<00:08,  1.33it/s]
 96%|█████████▌| 240/250 [05:00<00:08,  1.17it/s]
 96%|█████████▋| 241/250 [05:00<00:06,  1.46it/s]
 97%|█████████▋| 242/250 [05:02<00:07,  1.12it/s]
 97%|█████████▋| 243/250 [05:02<00:05,  1.38it/s]
 98%|█████████▊| 245/250 [05:05<00:05,  1.16s/it]
 98%|█████████▊| 246/250 [05:06<00:03,  1.02it/s]
 99%|█████████▉| 247/250 [05:06<00:02,  1.29it/s]
 99%|█████████▉| 248/250 [05:07<00:01,  1.12it/s]
100%|█████████▉| 249/250 [05:09<00:01,  1.15s/it]
100%|██████████| 250/250 [05:11<00:00,  1.50s/it]
100%|██████████| 250/250 [05:11<00:00,  1.25s/it]
2025-05-15 12:29:11,749 - modnet - INFO - Loss per individual: ind 0: 0.526 	ind 1: 1.219 	ind 2: 0.539 	ind 3: 0.518 	ind 4: 0.485 	ind 5: 0.500 	ind 6: 1.495 	ind 7: 1.233 	ind 8: 1.316 	ind 9: 0.475 	ind 10: 1.188 	ind 11: 0.484 	ind 12: 0.900 	ind 13: 0.639 	ind 14: 0.491 	ind 15: 1.189 	ind 16: 0.507 	ind 17: 0.487 	ind 18: 0.505 	ind 19: 0.496 	ind 20: 0.738 	ind 21: 0.503 	ind 22: 1.345 	ind 23: 1.453 	ind 24: 2.218 	ind 25: 0.533 	ind 26: 1.131 	ind 27: 0.482 	ind 28: 0.491 	ind 29: 0.438 	ind 30: 0.567 	ind 31: 0.492 	ind 32: 1.276 	ind 33: 0.471 	ind 34: 0.487 	ind 35: 0.816 	ind 36: 0.974 	ind 37: 0.476 	ind 38: 0.521 	ind 39: 0.880 	ind 40: 3.690 	ind 41: 4.038 	ind 42: 0.453 	ind 43: 0.499 	ind 44: 0.478 	ind 45: 0.516 	ind 46: 0.502 	ind 47: 0.544 	ind 48: 0.481 	ind 49: 1.187 	
2025-05-15 12:29:11,755 - modnet - INFO - Generation number 1

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:18<1:14:53, 18.05s/it]
  1%|          | 2/250 [00:21<39:23,  9.53s/it]  
  1%|          | 3/250 [00:23<25:36,  6.22s/it]
  2%|▏         | 4/250 [00:24<17:05,  4.17s/it]
  2%|▏         | 5/250 [00:25<12:07,  2.97s/it]
  2%|▏         | 6/250 [00:27<09:50,  2.42s/it]
  3%|▎         | 7/250 [00:28<07:52,  1.94s/it]
  4%|▎         | 9/250 [00:28<04:30,  1.12s/it]
  4%|▍         | 11/250 [00:28<02:58,  1.34it/s]
  5%|▍         | 12/250 [00:29<02:33,  1.56it/s]
  5%|▌         | 13/250 [00:31<03:45,  1.05it/s]
  6%|▌         | 14/250 [00:31<03:05,  1.28it/s]
  6%|▌         | 15/250 [00:33<04:06,  1.05s/it]
  6%|▋         | 16/250 [00:33<03:08,  1.24it/s]
  8%|▊         | 19/250 [00:37<04:11,  1.09s/it]
  8%|▊         | 20/250 [00:39<04:43,  1.23s/it]
  8%|▊         | 21/250 [00:43<07:05,  1.86s/it]
  9%|▉         | 22/250 [00:45<07:26,  1.96s/it]
  9%|▉         | 23/250 [00:48<08:50,  2.34s/it]
 10%|█         | 25/250 [00:49<05:50,  1.56s/it]
 10%|█         | 26/250 [00:50<05:21,  1.44s/it]
 11%|█         | 27/250 [00:52<05:31,  1.49s/it]
 11%|█         | 28/250 [00:52<04:24,  1.19s/it]
 12%|█▏        | 29/250 [00:53<04:14,  1.15s/it]
 12%|█▏        | 30/250 [00:56<05:20,  1.46s/it]
 12%|█▏        | 31/250 [00:56<04:00,  1.10s/it]
 13%|█▎        | 32/250 [00:57<03:55,  1.08s/it]
 13%|█▎        | 33/250 [00:59<04:29,  1.24s/it]
 14%|█▎        | 34/250 [00:59<03:28,  1.04it/s]
 14%|█▍        | 35/250 [00:59<02:48,  1.28it/s]
 14%|█▍        | 36/250 [00:59<02:12,  1.62it/s]
 15%|█▍        | 37/250 [01:04<05:55,  1.67s/it]
 15%|█▌        | 38/250 [01:05<05:31,  1.56s/it]
 16%|█▌        | 39/250 [01:05<04:07,  1.18s/it]
 16%|█▌        | 40/250 [01:07<04:45,  1.36s/it]
 16%|█▋        | 41/250 [01:08<04:40,  1.34s/it]
 17%|█▋        | 42/250 [01:09<04:30,  1.30s/it]
 17%|█▋        | 43/250 [01:10<03:19,  1.04it/s]
 18%|█▊        | 44/250 [01:10<02:33,  1.34it/s]
 18%|█▊        | 45/250 [01:17<08:36,  2.52s/it]
 18%|█▊        | 46/250 [01:20<09:24,  2.77s/it]
 19%|█▉        | 47/250 [01:23<09:42,  2.87s/it]
 19%|█▉        | 48/250 [01:24<07:56,  2.36s/it]
 20%|█▉        | 49/250 [01:26<07:30,  2.24s/it]
 20%|██        | 50/250 [01:28<07:00,  2.10s/it]
 20%|██        | 51/250 [01:30<06:33,  1.98s/it]
 21%|██        | 52/250 [01:33<08:20,  2.53s/it]
 21%|██        | 53/250 [01:34<05:58,  1.82s/it]
 22%|██▏       | 55/250 [01:34<03:19,  1.02s/it]
 22%|██▏       | 56/250 [01:35<03:34,  1.11s/it]
 23%|██▎       | 57/250 [01:37<04:21,  1.36s/it]
 23%|██▎       | 58/250 [01:38<03:36,  1.13s/it]
 24%|██▎       | 59/250 [01:39<03:48,  1.20s/it]
 24%|██▍       | 60/250 [01:43<06:21,  2.01s/it]
 24%|██▍       | 61/250 [01:44<05:32,  1.76s/it]
 25%|██▍       | 62/250 [01:45<04:38,  1.48s/it]
 25%|██▌       | 63/250 [01:46<03:46,  1.21s/it]
 26%|██▌       | 65/250 [01:47<03:03,  1.01it/s]
 26%|██▋       | 66/250 [01:48<02:53,  1.06it/s]
 27%|██▋       | 67/250 [01:51<04:33,  1.50s/it]
 27%|██▋       | 68/250 [01:52<03:49,  1.26s/it]
 28%|██▊       | 69/250 [01:53<03:57,  1.31s/it]
 28%|██▊       | 70/250 [01:54<03:50,  1.28s/it]
 28%|██▊       | 71/250 [01:54<02:56,  1.01it/s]
 29%|██▉       | 72/250 [01:55<02:10,  1.36it/s]
 29%|██▉       | 73/250 [01:56<02:49,  1.04it/s]
 30%|██▉       | 74/250 [01:57<02:39,  1.11it/s]
 30%|███       | 75/250 [01:57<02:01,  1.44it/s]
 30%|███       | 76/250 [01:58<01:57,  1.48it/s]
 31%|███       | 77/250 [01:59<02:26,  1.18it/s]
 31%|███       | 78/250 [02:01<03:43,  1.30s/it]
 32%|███▏      | 79/250 [02:02<03:34,  1.25s/it]
 32%|███▏      | 80/250 [02:03<03:12,  1.13s/it]
 32%|███▏      | 81/250 [02:04<03:12,  1.14s/it]
 33%|███▎      | 82/250 [02:05<02:39,  1.05it/s]
 33%|███▎      | 83/250 [02:11<07:02,  2.53s/it]
 34%|███▎      | 84/250 [02:12<05:37,  2.03s/it]
 34%|███▍      | 85/250 [02:13<04:51,  1.77s/it]
 34%|███▍      | 86/250 [02:14<04:20,  1.59s/it]
 35%|███▍      | 87/250 [02:17<04:54,  1.81s/it]
 35%|███▌      | 88/250 [02:18<04:25,  1.64s/it]
 36%|███▌      | 89/250 [02:19<03:44,  1.39s/it]
 36%|███▌      | 90/250 [02:20<03:56,  1.48s/it]
 36%|███▋      | 91/250 [02:21<03:06,  1.17s/it]
 37%|███▋      | 92/250 [02:28<07:48,  2.96s/it]
 37%|███▋      | 93/250 [02:29<05:50,  2.23s/it]
 38%|███▊      | 94/250 [02:29<04:12,  1.62s/it]
 38%|███▊      | 95/250 [02:30<03:50,  1.48s/it]
 38%|███▊      | 96/250 [02:31<03:47,  1.48s/it]
 39%|███▉      | 97/250 [02:32<02:53,  1.13s/it]
 39%|███▉      | 98/250 [02:33<02:46,  1.10s/it]
 40%|███▉      | 99/250 [02:36<04:36,  1.83s/it]
 40%|████      | 100/250 [02:38<04:34,  1.83s/it]
 40%|████      | 101/250 [02:38<03:25,  1.38s/it]
 41%|████      | 102/250 [02:39<03:02,  1.23s/it]
 41%|████      | 103/250 [02:42<04:25,  1.81s/it]
 42%|████▏     | 104/250 [02:43<03:33,  1.46s/it]
 42%|████▏     | 105/250 [02:43<02:36,  1.08s/it]
 42%|████▏     | 106/250 [02:44<01:57,  1.23it/s]
 43%|████▎     | 107/250 [02:45<02:28,  1.04s/it]
 43%|████▎     | 108/250 [02:46<02:20,  1.01it/s]
 44%|████▎     | 109/250 [02:46<01:49,  1.28it/s]
 44%|████▍     | 110/250 [02:47<01:33,  1.49it/s]
 44%|████▍     | 111/250 [02:47<01:19,  1.75it/s]
 45%|████▍     | 112/250 [02:48<01:20,  1.70it/s]
 45%|████▌     | 113/250 [02:48<01:11,  1.92it/s]
 46%|████▌     | 114/250 [02:50<02:03,  1.10it/s]
 46%|████▌     | 115/250 [02:51<02:25,  1.08s/it]
 46%|████▋     | 116/250 [02:52<01:51,  1.20it/s]
 47%|████▋     | 118/250 [02:54<02:01,  1.09it/s]
 48%|████▊     | 119/250 [02:56<02:44,  1.25s/it]
 48%|████▊     | 120/250 [02:57<02:44,  1.26s/it]
 48%|████▊     | 121/250 [03:00<03:37,  1.69s/it]
 49%|████▉     | 122/250 [03:03<04:27,  2.09s/it]
 49%|████▉     | 123/250 [03:07<05:47,  2.73s/it]
 50%|████▉     | 124/250 [03:08<04:25,  2.11s/it]
 50%|█████     | 125/250 [03:08<03:19,  1.60s/it]
 50%|█████     | 126/250 [03:08<02:24,  1.17s/it]
 51%|█████     | 127/250 [03:09<02:01,  1.01it/s]
 51%|█████     | 128/250 [03:11<02:31,  1.24s/it]
 52%|█████▏    | 129/250 [03:14<03:27,  1.71s/it]
 52%|█████▏    | 130/250 [03:16<03:35,  1.79s/it]
 52%|█████▏    | 131/250 [03:16<02:53,  1.46s/it]
 53%|█████▎    | 132/250 [03:22<05:32,  2.82s/it]
 53%|█████▎    | 133/250 [03:23<04:29,  2.31s/it]
 54%|█████▎    | 134/250 [03:24<03:41,  1.91s/it]
 54%|█████▍    | 135/250 [03:26<03:29,  1.83s/it]
 54%|█████▍    | 136/250 [03:26<02:37,  1.39s/it]
 55%|█████▍    | 137/250 [03:27<02:22,  1.26s/it]
 55%|█████▌    | 138/250 [03:29<02:34,  1.38s/it]
 56%|█████▌    | 139/250 [03:29<01:51,  1.01s/it]
 56%|█████▌    | 140/250 [03:30<01:48,  1.01it/s]
 56%|█████▋    | 141/250 [03:31<01:52,  1.04s/it]
 57%|█████▋    | 142/250 [03:32<01:56,  1.08s/it]
 57%|█████▋    | 143/250 [03:34<02:02,  1.15s/it]
 58%|█████▊    | 144/250 [03:36<02:26,  1.38s/it]
 58%|█████▊    | 145/250 [03:36<01:47,  1.03s/it]
 58%|█████▊    | 146/250 [03:36<01:20,  1.29it/s]
 59%|█████▉    | 147/250 [03:37<01:26,  1.19it/s]
 59%|█████▉    | 148/250 [03:39<02:03,  1.21s/it]
 60%|█████▉    | 149/250 [03:43<03:35,  2.14s/it]
 60%|██████    | 150/250 [03:46<04:00,  2.40s/it]
 60%|██████    | 151/250 [03:48<03:40,  2.23s/it]
 61%|██████    | 152/250 [03:50<03:17,  2.02s/it]
 61%|██████    | 153/250 [03:54<04:08,  2.56s/it]
 62%|██████▏   | 154/250 [03:55<03:17,  2.06s/it]
 62%|██████▏   | 155/250 [03:55<02:24,  1.52s/it]
 62%|██████▏   | 156/250 [03:55<01:51,  1.19s/it]
 63%|██████▎   | 157/250 [03:56<01:26,  1.07it/s]
 63%|██████▎   | 158/250 [03:57<01:31,  1.01it/s]
 64%|██████▎   | 159/250 [03:58<01:32,  1.02s/it]
 64%|██████▍   | 160/250 [04:01<02:44,  1.83s/it]
 64%|██████▍   | 161/250 [04:04<03:08,  2.11s/it]
 65%|██████▍   | 162/250 [04:05<02:33,  1.74s/it]
 65%|██████▌   | 163/250 [04:10<03:52,  2.68s/it]
 66%|██████▌   | 164/250 [04:14<04:12,  2.94s/it]
 66%|██████▌   | 165/250 [04:16<03:51,  2.72s/it]
 66%|██████▋   | 166/250 [04:17<03:19,  2.37s/it]
 67%|██████▋   | 167/250 [04:18<02:43,  1.97s/it]
 67%|██████▋   | 168/250 [04:20<02:22,  1.74s/it]
 68%|██████▊   | 169/250 [04:20<02:01,  1.50s/it]
 68%|██████▊   | 170/250 [04:21<01:47,  1.35s/it]
 68%|██████▊   | 171/250 [04:23<02:00,  1.52s/it]
 69%|██████▉   | 172/250 [04:29<03:27,  2.66s/it]
 69%|██████▉   | 173/250 [04:30<03:03,  2.39s/it]
 70%|██████▉   | 174/250 [04:33<03:07,  2.46s/it]
 70%|███████   | 175/250 [04:33<02:17,  1.83s/it]
 70%|███████   | 176/250 [04:34<01:45,  1.42s/it]
 71%|███████   | 177/250 [04:35<01:31,  1.26s/it]
 71%|███████   | 178/250 [04:36<01:26,  1.20s/it]
 72%|███████▏  | 179/250 [04:39<02:14,  1.89s/it]
 72%|███████▏  | 180/250 [04:41<02:01,  1.74s/it]
 72%|███████▏  | 181/250 [04:41<01:33,  1.36s/it]
 73%|███████▎  | 182/250 [04:42<01:27,  1.28s/it]
 73%|███████▎  | 183/250 [04:44<01:27,  1.30s/it]
 74%|███████▎  | 184/250 [04:48<02:23,  2.17s/it]
 74%|███████▍  | 185/250 [04:50<02:11,  2.02s/it]
 74%|███████▍  | 186/250 [04:52<02:16,  2.13s/it]
 75%|███████▍  | 187/250 [04:53<01:47,  1.71s/it]
 75%|███████▌  | 188/250 [04:53<01:18,  1.27s/it]
 76%|███████▌  | 189/250 [04:53<00:57,  1.06it/s]
 76%|███████▌  | 190/250 [04:54<00:53,  1.12it/s]
 76%|███████▋  | 191/250 [04:54<00:39,  1.49it/s]
 77%|███████▋  | 192/250 [04:56<01:08,  1.18s/it]
 77%|███████▋  | 193/250 [04:58<01:09,  1.22s/it]
 78%|███████▊  | 194/250 [04:58<00:59,  1.06s/it]
 78%|███████▊  | 195/250 [05:00<01:10,  1.28s/it]
 78%|███████▊  | 196/250 [05:01<00:53,  1.00it/s]
 79%|███████▉  | 197/250 [05:01<00:43,  1.22it/s]
 79%|███████▉  | 198/250 [05:01<00:38,  1.36it/s]
 80%|███████▉  | 199/250 [05:02<00:38,  1.32it/s]
 80%|████████  | 200/250 [05:03<00:35,  1.41it/s]
 80%|████████  | 201/250 [05:04<00:42,  1.14it/s]
 81%|████████  | 202/250 [05:08<01:20,  1.67s/it]
 81%|████████  | 203/250 [05:08<00:59,  1.26s/it]
 82%|████████▏ | 204/250 [05:12<01:41,  2.21s/it]
 82%|████████▏ | 205/250 [05:13<01:21,  1.81s/it]
 82%|████████▏ | 206/250 [05:18<02:02,  2.79s/it]
 83%|████████▎ | 207/250 [05:21<02:03,  2.88s/it]
 83%|████████▎ | 208/250 [05:23<01:46,  2.53s/it]
 84%|████████▎ | 209/250 [05:25<01:38,  2.40s/it]
 84%|████████▍ | 210/250 [05:26<01:12,  1.82s/it]
 84%|████████▍ | 211/250 [05:29<01:27,  2.24s/it]
 85%|████████▍ | 212/250 [05:31<01:19,  2.10s/it]
 85%|████████▌ | 213/250 [05:31<00:56,  1.52s/it]
 86%|████████▌ | 214/250 [05:31<00:41,  1.17s/it]
 86%|████████▌ | 215/250 [05:33<00:45,  1.29s/it]
 86%|████████▋ | 216/250 [05:35<00:52,  1.55s/it]
 87%|████████▋ | 217/250 [05:37<00:54,  1.64s/it]
 88%|████████▊ | 219/250 [05:37<00:30,  1.02it/s]
 88%|████████▊ | 220/250 [05:39<00:32,  1.08s/it]
 88%|████████▊ | 221/250 [05:39<00:24,  1.20it/s]
 89%|████████▉ | 222/250 [05:40<00:25,  1.11it/s]
 89%|████████▉ | 223/250 [05:40<00:21,  1.26it/s]
 90%|████████▉ | 224/250 [05:41<00:16,  1.56it/s]
 90%|█████████ | 226/250 [05:42<00:13,  1.79it/s]
 91%|█████████ | 227/250 [05:42<00:10,  2.12it/s]
 91%|█████████ | 228/250 [05:43<00:12,  1.81it/s]
 92%|█████████▏| 229/250 [05:43<00:10,  2.06it/s]
 92%|█████████▏| 230/250 [05:43<00:08,  2.26it/s]
 92%|█████████▏| 231/250 [05:43<00:07,  2.54it/s]
 93%|█████████▎| 232/250 [05:44<00:06,  2.95it/s]
 93%|█████████▎| 233/250 [05:45<00:09,  1.72it/s]
 94%|█████████▎| 234/250 [05:45<00:07,  2.24it/s]
 94%|█████████▍| 235/250 [05:46<00:11,  1.30it/s]
 95%|█████████▍| 237/250 [05:48<00:09,  1.32it/s]
 95%|█████████▌| 238/250 [05:49<00:09,  1.26it/s]
 96%|█████████▌| 239/250 [05:51<00:12,  1.16s/it]
 96%|█████████▌| 240/250 [05:54<00:15,  1.52s/it]
 96%|█████████▋| 241/250 [05:54<00:10,  1.14s/it]
 97%|█████████▋| 242/250 [05:57<00:14,  1.87s/it]
 97%|█████████▋| 243/250 [06:03<00:21,  3.03s/it]
 98%|█████████▊| 244/250 [06:05<00:15,  2.53s/it]
 98%|█████████▊| 245/250 [06:07<00:12,  2.49s/it]
 98%|█████████▊| 246/250 [06:13<00:14,  3.54s/it]
 99%|█████████▉| 247/250 [06:19<00:13,  4.35s/it]
 99%|█████████▉| 248/250 [06:23<00:08,  4.22s/it]
100%|█████████▉| 249/250 [06:26<00:03,  3.71s/it]
100%|██████████| 250/250 [06:28<00:00,  3.40s/it]
100%|██████████| 250/250 [06:28<00:00,  1.56s/it]
2025-05-15 12:35:40,740 - modnet - INFO - Loss per individual: ind 0: 0.449 	ind 1: 0.485 	ind 2: 0.466 	ind 3: 0.544 	ind 4: 0.487 	ind 5: 0.503 	ind 6: 0.508 	ind 7: 0.523 	ind 8: 0.509 	ind 9: 0.478 	ind 10: 0.490 	ind 11: 0.522 	ind 12: 0.526 	ind 13: 0.458 	ind 14: 0.496 	ind 15: 0.469 	ind 16: 0.477 	ind 17: 0.501 	ind 18: 0.523 	ind 19: 0.523 	ind 20: 0.498 	ind 21: 0.491 	ind 22: 0.508 	ind 23: 0.480 	ind 24: 0.506 	ind 25: 0.520 	ind 26: 0.518 	ind 27: 0.495 	ind 28: 0.476 	ind 29: 0.491 	ind 30: 0.521 	ind 31: 0.555 	ind 32: 0.512 	ind 33: 0.475 	ind 34: 0.496 	ind 35: 0.511 	ind 36: 0.545 	ind 37: 0.512 	ind 38: 0.513 	ind 39: 0.494 	ind 40: 0.478 	ind 41: 0.455 	ind 42: 0.491 	ind 43: 0.514 	ind 44: 0.630 	ind 45: 0.527 	ind 46: 0.506 	ind 47: 0.515 	ind 48: 0.468 	ind 49: 0.494 	
2025-05-15 12:35:40,743 - modnet - INFO - Generation number 2

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:19<1:20:04, 19.30s/it]
  1%|          | 2/250 [00:20<35:00,  8.47s/it]  
  1%|          | 3/250 [00:25<28:59,  7.04s/it]
  2%|▏         | 4/250 [00:26<18:44,  4.57s/it]
  2%|▏         | 5/250 [00:26<12:43,  3.12s/it]
  2%|▏         | 6/250 [00:38<24:47,  6.10s/it]
  3%|▎         | 7/250 [00:40<18:45,  4.63s/it]
  3%|▎         | 8/250 [00:42<15:07,  3.75s/it]
  4%|▎         | 9/250 [00:43<12:05,  3.01s/it]
  4%|▍         | 10/250 [00:44<09:32,  2.38s/it]
  4%|▍         | 11/250 [00:45<08:07,  2.04s/it]
  5%|▍         | 12/250 [00:46<06:29,  1.63s/it]
  5%|▌         | 13/250 [00:47<05:29,  1.39s/it]
  6%|▌         | 14/250 [00:50<07:48,  1.98s/it]
  6%|▌         | 15/250 [00:51<06:01,  1.54s/it]
  6%|▋         | 16/250 [00:52<05:16,  1.35s/it]
  7%|▋         | 17/250 [00:52<04:22,  1.13s/it]
  7%|▋         | 18/250 [00:53<03:26,  1.12it/s]
  8%|▊         | 19/250 [00:53<03:03,  1.26it/s]
  8%|▊         | 20/250 [00:55<03:48,  1.01it/s]
  8%|▊         | 21/250 [00:55<02:50,  1.35it/s]
  9%|▉         | 22/250 [00:55<02:28,  1.54it/s]
  9%|▉         | 23/250 [00:55<02:01,  1.86it/s]
 10%|▉         | 24/250 [00:56<02:06,  1.79it/s]
 10%|█         | 25/250 [00:57<02:22,  1.58it/s]
 10%|█         | 26/250 [00:58<03:18,  1.13it/s]
 11%|█         | 27/250 [00:59<03:10,  1.17it/s]
 11%|█         | 28/250 [01:01<04:48,  1.30s/it]
 12%|█▏        | 29/250 [01:04<05:43,  1.56s/it]
 12%|█▏        | 30/250 [01:04<04:25,  1.20s/it]
 13%|█▎        | 32/250 [01:06<03:39,  1.01s/it]
 13%|█▎        | 33/250 [01:08<04:32,  1.26s/it]
 14%|█▎        | 34/250 [01:09<04:19,  1.20s/it]
 14%|█▍        | 35/250 [01:13<07:41,  2.15s/it]
 14%|█▍        | 36/250 [01:14<06:28,  1.81s/it]
 15%|█▍        | 37/250 [01:16<05:52,  1.66s/it]
 15%|█▌        | 38/250 [01:18<07:05,  2.01s/it]
 16%|█▌        | 39/250 [01:21<07:12,  2.05s/it]
 16%|█▌        | 40/250 [01:23<07:15,  2.07s/it]
 16%|█▋        | 41/250 [01:26<08:45,  2.51s/it]
 17%|█▋        | 42/250 [01:26<06:19,  1.82s/it]
 17%|█▋        | 43/250 [01:27<04:59,  1.45s/it]
 18%|█▊        | 44/250 [01:31<07:35,  2.21s/it]
 18%|█▊        | 45/250 [01:31<05:32,  1.62s/it]
 18%|█▊        | 46/250 [01:31<04:03,  1.19s/it]
 19%|█▉        | 47/250 [01:32<03:22,  1.00it/s]
 19%|█▉        | 48/250 [01:32<02:32,  1.32it/s]
 20%|█▉        | 49/250 [01:33<02:39,  1.26it/s]
 20%|██        | 50/250 [01:35<03:36,  1.08s/it]
 20%|██        | 51/250 [01:38<05:23,  1.63s/it]
 21%|██        | 52/250 [01:39<04:59,  1.51s/it]
 21%|██        | 53/250 [01:41<05:41,  1.73s/it]
 22%|██▏       | 54/250 [01:42<04:33,  1.40s/it]
 22%|██▏       | 55/250 [01:42<03:38,  1.12s/it]
 22%|██▏       | 56/250 [01:45<04:44,  1.47s/it]
 23%|██▎       | 57/250 [01:46<04:39,  1.45s/it]
 23%|██▎       | 58/250 [01:46<03:43,  1.16s/it]
 24%|██▎       | 59/250 [01:47<02:58,  1.07it/s]
 24%|██▍       | 60/250 [01:50<05:10,  1.63s/it]
 24%|██▍       | 61/250 [01:52<05:10,  1.64s/it]
 25%|██▍       | 62/250 [01:52<03:59,  1.27s/it]
 25%|██▌       | 63/250 [01:53<03:42,  1.19s/it]
 26%|██▌       | 64/250 [01:54<03:04,  1.01it/s]
 26%|██▌       | 65/250 [01:55<02:56,  1.05it/s]
 26%|██▋       | 66/250 [01:56<03:13,  1.05s/it]
 27%|██▋       | 67/250 [01:56<02:22,  1.28it/s]
 27%|██▋       | 68/250 [01:57<02:36,  1.16it/s]
 28%|██▊       | 69/250 [01:57<02:06,  1.43it/s]
 28%|██▊       | 70/250 [01:58<01:57,  1.54it/s]
 28%|██▊       | 71/250 [01:58<01:50,  1.62it/s]
 29%|██▉       | 72/250 [01:59<02:09,  1.38it/s]
 29%|██▉       | 73/250 [02:00<01:41,  1.75it/s]
 30%|██▉       | 74/250 [02:00<01:35,  1.85it/s]
 30%|███       | 75/250 [02:01<01:41,  1.72it/s]
 30%|███       | 76/250 [02:03<03:15,  1.12s/it]
 31%|███       | 77/250 [02:04<03:09,  1.09s/it]
 31%|███       | 78/250 [02:05<03:05,  1.08s/it]
 32%|███▏      | 79/250 [02:16<11:20,  3.98s/it]
 32%|███▏      | 80/250 [02:17<08:25,  2.97s/it]
 32%|███▏      | 81/250 [02:20<08:25,  2.99s/it]
 33%|███▎      | 82/250 [02:22<07:26,  2.66s/it]
 33%|███▎      | 83/250 [02:24<07:17,  2.62s/it]
 34%|███▎      | 84/250 [02:25<05:37,  2.03s/it]
 34%|███▍      | 85/250 [02:29<07:07,  2.59s/it]
 34%|███▍      | 86/250 [02:30<05:46,  2.11s/it]
 35%|███▍      | 87/250 [02:31<05:30,  2.03s/it]
 35%|███▌      | 88/250 [02:33<05:11,  1.93s/it]
 36%|███▌      | 89/250 [02:33<03:53,  1.45s/it]
 36%|███▌      | 90/250 [02:36<04:21,  1.63s/it]
 36%|███▋      | 91/250 [02:37<03:51,  1.45s/it]
 37%|███▋      | 92/250 [02:37<03:17,  1.25s/it]
 37%|███▋      | 93/250 [02:39<03:33,  1.36s/it]
 38%|███▊      | 95/250 [02:42<03:39,  1.42s/it]
 38%|███▊      | 96/250 [02:43<03:24,  1.33s/it]
 39%|███▉      | 97/250 [02:45<03:59,  1.56s/it]
 39%|███▉      | 98/250 [02:48<05:07,  2.02s/it]
 40%|███▉      | 99/250 [02:52<05:51,  2.33s/it]
 40%|████      | 100/250 [02:52<04:42,  1.89s/it]
 40%|████      | 101/250 [02:54<04:50,  1.95s/it]
 41%|████      | 102/250 [02:56<04:36,  1.87s/it]
 41%|████      | 103/250 [02:57<04:02,  1.65s/it]
 42%|████▏     | 104/250 [03:04<07:36,  3.13s/it]
 42%|████▏     | 105/250 [03:05<06:22,  2.64s/it]
 42%|████▏     | 106/250 [03:06<04:53,  2.04s/it]
 43%|████▎     | 107/250 [03:07<03:59,  1.68s/it]
 43%|████▎     | 108/250 [03:08<03:19,  1.41s/it]
 44%|████▎     | 109/250 [03:08<02:26,  1.04s/it]
 44%|████▍     | 110/250 [03:11<03:47,  1.63s/it]
 44%|████▍     | 111/250 [03:13<03:54,  1.69s/it]
 45%|████▍     | 112/250 [03:13<03:08,  1.37s/it]
 45%|████▌     | 113/250 [03:15<03:25,  1.50s/it]
 46%|████▌     | 114/250 [03:15<02:32,  1.12s/it]
 46%|████▌     | 115/250 [03:17<02:34,  1.14s/it]
 46%|████▋     | 116/250 [03:18<02:47,  1.25s/it]
 47%|████▋     | 117/250 [03:22<04:23,  1.98s/it]
 47%|████▋     | 118/250 [03:25<05:03,  2.30s/it]
 48%|████▊     | 119/250 [03:26<04:35,  2.11s/it]
 48%|████▊     | 120/250 [03:27<03:18,  1.52s/it]
 48%|████▊     | 121/250 [03:28<03:16,  1.52s/it]
 49%|████▉     | 122/250 [03:29<02:44,  1.28s/it]
 49%|████▉     | 123/250 [03:30<02:29,  1.18s/it]
 50%|████▉     | 124/250 [03:30<01:51,  1.13it/s]
 50%|█████     | 125/250 [03:32<02:53,  1.39s/it]
 50%|█████     | 126/250 [03:33<02:08,  1.03s/it]
 51%|█████     | 127/250 [03:33<01:45,  1.17it/s]
 51%|█████     | 128/250 [03:33<01:21,  1.50it/s]
 52%|█████▏    | 129/250 [03:34<01:02,  1.93it/s]
 52%|█████▏    | 130/250 [03:34<00:58,  2.04it/s]
 52%|█████▏    | 131/250 [03:39<03:42,  1.87s/it]
 53%|█████▎    | 132/250 [03:42<04:19,  2.20s/it]
 53%|█████▎    | 133/250 [03:43<03:26,  1.77s/it]
 54%|█████▎    | 134/250 [03:44<02:50,  1.47s/it]
 54%|█████▍    | 135/250 [03:44<02:29,  1.30s/it]
 54%|█████▍    | 136/250 [03:46<02:34,  1.35s/it]
 55%|█████▍    | 137/250 [03:50<04:11,  2.23s/it]
 55%|█████▌    | 138/250 [03:52<03:52,  2.07s/it]
 56%|█████▌    | 139/250 [03:53<03:06,  1.68s/it]
 56%|█████▌    | 140/250 [03:54<02:53,  1.58s/it]
 56%|█████▋    | 141/250 [03:55<02:20,  1.29s/it]
 57%|█████▋    | 142/250 [03:56<02:06,  1.17s/it]
 57%|█████▋    | 143/250 [03:57<02:30,  1.41s/it]
 58%|█████▊    | 144/250 [03:58<02:02,  1.16s/it]
 58%|█████▊    | 145/250 [04:01<02:42,  1.55s/it]
 58%|█████▊    | 146/250 [04:01<02:02,  1.18s/it]
 59%|█████▉    | 147/250 [04:01<01:43,  1.00s/it]
 59%|█████▉    | 148/250 [04:02<01:27,  1.16it/s]
 60%|█████▉    | 149/250 [04:03<01:27,  1.15it/s]
 60%|██████    | 150/250 [04:03<01:09,  1.44it/s]
 60%|██████    | 151/250 [04:05<01:35,  1.03it/s]
 61%|██████    | 152/250 [04:05<01:17,  1.27it/s]
 61%|██████    | 153/250 [04:05<01:00,  1.61it/s]
 62%|██████▏   | 154/250 [04:07<01:32,  1.04it/s]
 62%|██████▏   | 155/250 [04:08<01:26,  1.10it/s]
 62%|██████▏   | 156/250 [04:09<01:40,  1.07s/it]
 63%|██████▎   | 157/250 [04:11<02:08,  1.38s/it]
 63%|██████▎   | 158/250 [04:12<01:57,  1.28s/it]
 64%|██████▎   | 159/250 [04:14<02:05,  1.38s/it]
 64%|██████▍   | 160/250 [04:17<02:51,  1.91s/it]
 64%|██████▍   | 161/250 [04:18<02:18,  1.56s/it]
 65%|██████▍   | 162/250 [04:20<02:21,  1.60s/it]
 65%|██████▌   | 163/250 [04:21<02:16,  1.57s/it]
 66%|██████▌   | 164/250 [04:23<02:29,  1.74s/it]
 66%|██████▌   | 165/250 [04:24<01:54,  1.35s/it]
 66%|██████▋   | 166/250 [04:34<05:37,  4.02s/it]
 67%|██████▋   | 167/250 [04:34<03:58,  2.87s/it]
 67%|██████▋   | 168/250 [04:36<03:18,  2.42s/it]
 68%|██████▊   | 169/250 [04:36<02:31,  1.87s/it]
 68%|██████▊   | 170/250 [04:36<01:50,  1.39s/it]
 68%|██████▊   | 171/250 [04:38<01:51,  1.41s/it]
 69%|██████▉   | 172/250 [04:39<01:39,  1.27s/it]
 69%|██████▉   | 173/250 [04:40<01:40,  1.30s/it]
 70%|██████▉   | 174/250 [04:40<01:16,  1.00s/it]
 70%|███████   | 175/250 [04:41<01:04,  1.16it/s]
 70%|███████   | 176/250 [04:43<01:30,  1.22s/it]
 71%|███████   | 177/250 [04:43<01:05,  1.11it/s]
 71%|███████   | 178/250 [04:47<01:56,  1.62s/it]
 72%|███████▏  | 179/250 [04:47<01:26,  1.22s/it]
 72%|███████▏  | 180/250 [04:48<01:18,  1.12s/it]
 72%|███████▏  | 181/250 [04:48<00:57,  1.20it/s]
 73%|███████▎  | 182/250 [04:50<01:19,  1.17s/it]
 73%|███████▎  | 183/250 [04:51<01:25,  1.27s/it]
 74%|███████▎  | 184/250 [04:53<01:25,  1.29s/it]
 74%|███████▍  | 185/250 [04:53<01:02,  1.05it/s]
 74%|███████▍  | 186/250 [04:53<00:48,  1.31it/s]
 75%|███████▍  | 187/250 [04:54<00:57,  1.10it/s]
 75%|███████▌  | 188/250 [04:57<01:19,  1.29s/it]
 76%|███████▌  | 189/250 [04:57<01:01,  1.01s/it]
 76%|███████▌  | 190/250 [04:57<00:49,  1.20it/s]
 76%|███████▋  | 191/250 [05:00<01:20,  1.36s/it]
 77%|███████▋  | 192/250 [05:01<01:19,  1.38s/it]
 77%|███████▋  | 193/250 [05:08<02:41,  2.83s/it]
 78%|███████▊  | 194/250 [05:09<02:06,  2.26s/it]
 78%|███████▊  | 195/250 [05:12<02:22,  2.58s/it]
 78%|███████▊  | 196/250 [05:17<03:07,  3.48s/it]
 79%|███████▉  | 197/250 [05:20<02:49,  3.20s/it]
 79%|███████▉  | 198/250 [05:20<02:00,  2.31s/it]
 80%|███████▉  | 199/250 [05:20<01:25,  1.68s/it]
 80%|████████  | 200/250 [05:22<01:28,  1.77s/it]
 80%|████████  | 201/250 [05:25<01:37,  1.99s/it]
 81%|████████  | 202/250 [05:25<01:12,  1.51s/it]
 81%|████████  | 203/250 [05:26<00:59,  1.27s/it]
 82%|████████▏ | 204/250 [05:26<00:43,  1.06it/s]
 82%|████████▏ | 205/250 [05:26<00:33,  1.36it/s]
 82%|████████▏ | 206/250 [05:28<00:37,  1.18it/s]
 83%|████████▎ | 207/250 [05:34<01:52,  2.63s/it]
 83%|████████▎ | 208/250 [05:36<01:35,  2.27s/it]
 84%|████████▎ | 209/250 [05:37<01:22,  2.02s/it]
 84%|████████▍ | 210/250 [05:41<01:44,  2.61s/it]
 84%|████████▍ | 211/250 [05:43<01:37,  2.51s/it]
 85%|████████▍ | 212/250 [05:46<01:38,  2.58s/it]
 85%|████████▌ | 213/250 [05:46<01:10,  1.89s/it]
 86%|████████▌ | 214/250 [05:47<00:56,  1.56s/it]
 86%|████████▌ | 215/250 [05:48<00:46,  1.34s/it]
 86%|████████▋ | 216/250 [05:49<00:42,  1.25s/it]
 87%|████████▋ | 217/250 [05:50<00:39,  1.18s/it]
 87%|████████▋ | 218/250 [05:51<00:32,  1.01s/it]
 88%|████████▊ | 219/250 [05:51<00:22,  1.35it/s]
 88%|████████▊ | 220/250 [05:53<00:31,  1.04s/it]
 88%|████████▊ | 221/250 [05:58<01:10,  2.43s/it]
 89%|████████▉ | 223/250 [06:01<00:54,  2.04s/it]
 90%|████████▉ | 224/250 [06:03<00:51,  1.98s/it]
 90%|█████████ | 225/250 [06:04<00:40,  1.62s/it]
 91%|█████████ | 227/250 [06:05<00:25,  1.10s/it]
 91%|█████████ | 228/250 [06:05<00:22,  1.00s/it]
 92%|█████████▏| 229/250 [06:06<00:19,  1.10it/s]
 92%|█████████▏| 230/250 [06:07<00:18,  1.09it/s]
 92%|█████████▏| 231/250 [06:09<00:22,  1.18s/it]
 93%|█████████▎| 233/250 [06:10<00:17,  1.02s/it]
 94%|█████████▎| 234/250 [06:11<00:13,  1.21it/s]
 94%|█████████▍| 235/250 [06:11<00:10,  1.38it/s]
 95%|█████████▍| 237/250 [06:13<00:11,  1.11it/s]
 96%|█████████▌| 239/250 [06:13<00:06,  1.69it/s]
 96%|█████████▌| 240/250 [06:16<00:09,  1.07it/s]
 96%|█████████▋| 241/250 [06:16<00:07,  1.19it/s]
 97%|█████████▋| 242/250 [06:17<00:05,  1.43it/s]
 97%|█████████▋| 243/250 [06:17<00:04,  1.62it/s]
 98%|█████████▊| 244/250 [06:19<00:05,  1.12it/s]
 98%|█████████▊| 245/250 [06:21<00:06,  1.24s/it]
 99%|█████████▉| 247/250 [06:24<00:04,  1.36s/it]
 99%|█████████▉| 248/250 [06:24<00:02,  1.07s/it]
100%|█████████▉| 249/250 [06:24<00:00,  1.13it/s]
100%|██████████| 250/250 [06:37<00:00,  3.99s/it]
100%|██████████| 250/250 [06:37<00:00,  1.59s/it]
2025-05-15 12:42:17,967 - modnet - INFO - Loss per individual: ind 0: 0.532 	ind 1: 0.510 	ind 2: 0.507 	ind 3: 0.477 	ind 4: 0.462 	ind 5: 0.468 	ind 6: 0.478 	ind 7: 0.485 	ind 8: 0.507 	ind 9: 0.535 	ind 10: 0.504 	ind 11: 0.496 	ind 12: 0.474 	ind 13: 0.496 	ind 14: 0.532 	ind 15: 0.510 	ind 16: 0.502 	ind 17: 0.503 	ind 18: 0.517 	ind 19: 0.539 	ind 20: 0.509 	ind 21: 0.497 	ind 22: 0.484 	ind 23: 0.495 	ind 24: 0.520 	ind 25: 0.505 	ind 26: 0.486 	ind 27: 0.489 	ind 28: 0.508 	ind 29: 0.442 	ind 30: 0.492 	ind 31: 0.489 	ind 32: 0.487 	ind 33: 0.490 	ind 34: 0.488 	ind 35: 0.519 	ind 36: 0.493 	ind 37: 0.483 	ind 38: 0.557 	ind 39: 0.486 	ind 40: 0.480 	ind 41: 0.518 	ind 42: 0.546 	ind 43: 0.534 	ind 44: 0.477 	ind 45: 0.506 	ind 46: 0.502 	ind 47: 0.533 	ind 48: 0.523 	ind 49: 0.503 	
2025-05-15 12:42:17,968 - modnet - INFO - Generation number 3

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:15<1:05:57, 15.89s/it]
  1%|          | 2/250 [00:17<30:26,  7.37s/it]  
  1%|          | 3/250 [00:20<22:39,  5.50s/it]
  2%|▏         | 4/250 [00:26<22:28,  5.48s/it]
  2%|▏         | 5/250 [00:29<19:46,  4.84s/it]
  2%|▏         | 6/250 [00:30<13:53,  3.42s/it]
  3%|▎         | 7/250 [00:30<09:48,  2.42s/it]
  3%|▎         | 8/250 [00:30<06:54,  1.71s/it]
  4%|▎         | 9/250 [00:37<13:01,  3.24s/it]
  4%|▍         | 10/250 [00:38<10:43,  2.68s/it]
  4%|▍         | 11/250 [00:39<08:24,  2.11s/it]
  5%|▌         | 13/250 [00:40<05:04,  1.29s/it]
  6%|▌         | 14/250 [00:41<04:43,  1.20s/it]
  6%|▌         | 15/250 [00:41<03:53,  1.01it/s]
  6%|▋         | 16/250 [00:42<03:17,  1.18it/s]
  7%|▋         | 17/250 [00:43<04:04,  1.05s/it]
  7%|▋         | 18/250 [00:44<03:21,  1.15it/s]
  8%|▊         | 19/250 [00:48<06:59,  1.81s/it]
  8%|▊         | 20/250 [00:48<05:17,  1.38s/it]
  8%|▊         | 21/250 [00:49<04:22,  1.15s/it]
  9%|▉         | 22/250 [00:54<08:27,  2.23s/it]
  9%|▉         | 23/250 [00:58<10:43,  2.83s/it]
 10%|▉         | 24/250 [00:58<07:38,  2.03s/it]
 10%|█         | 25/250 [01:01<08:20,  2.22s/it]
 10%|█         | 26/250 [01:04<09:52,  2.64s/it]
 11%|█         | 27/250 [01:08<11:15,  3.03s/it]
 11%|█         | 28/250 [01:09<08:50,  2.39s/it]
 12%|█▏        | 29/250 [01:12<09:25,  2.56s/it]
 12%|█▏        | 30/250 [01:13<07:59,  2.18s/it]
 12%|█▏        | 31/250 [01:15<06:58,  1.91s/it]
 13%|█▎        | 32/250 [01:17<07:06,  1.96s/it]
 13%|█▎        | 33/250 [01:17<05:13,  1.44s/it]
 14%|█▎        | 34/250 [01:19<05:51,  1.63s/it]
 14%|█▍        | 35/250 [01:19<04:15,  1.19s/it]
 14%|█▍        | 36/250 [01:21<04:51,  1.36s/it]
 15%|█▍        | 37/250 [01:23<05:20,  1.51s/it]
 15%|█▌        | 38/250 [01:26<06:58,  1.97s/it]
 16%|█▌        | 39/250 [01:27<05:46,  1.64s/it]
 16%|█▌        | 40/250 [01:28<04:57,  1.42s/it]
 16%|█▋        | 41/250 [01:28<03:50,  1.10s/it]
 17%|█▋        | 42/250 [01:28<02:51,  1.21it/s]
 17%|█▋        | 43/250 [01:29<02:18,  1.49it/s]
 18%|█▊        | 44/250 [01:36<09:24,  2.74s/it]
 18%|█▊        | 45/250 [01:37<07:39,  2.24s/it]
 18%|█▊        | 46/250 [01:37<05:29,  1.62s/it]
 19%|█▉        | 47/250 [01:38<04:38,  1.37s/it]
 19%|█▉        | 48/250 [01:41<05:59,  1.78s/it]
 20%|█▉        | 49/250 [01:41<04:24,  1.32s/it]
 20%|██        | 50/250 [01:42<04:04,  1.22s/it]
 20%|██        | 51/250 [01:43<03:26,  1.04s/it]
 21%|██        | 52/250 [01:43<02:36,  1.26it/s]
 21%|██        | 53/250 [01:45<03:44,  1.14s/it]
 22%|██▏       | 54/250 [01:48<05:56,  1.82s/it]
 22%|██▏       | 55/250 [01:49<04:49,  1.48s/it]
 22%|██▏       | 56/250 [01:49<03:30,  1.09s/it]
 23%|██▎       | 57/250 [01:50<03:23,  1.05s/it]
 23%|██▎       | 58/250 [01:51<03:17,  1.03s/it]
 24%|██▎       | 59/250 [01:51<02:31,  1.26it/s]
 24%|██▍       | 60/250 [01:52<01:56,  1.64it/s]
 24%|██▍       | 61/250 [01:55<04:32,  1.44s/it]
 25%|██▍       | 62/250 [01:57<04:52,  1.56s/it]
 25%|██▌       | 63/250 [02:02<08:33,  2.75s/it]
 26%|██▌       | 64/250 [02:07<10:35,  3.42s/it]
 26%|██▌       | 65/250 [02:10<09:47,  3.17s/it]
 26%|██▋       | 66/250 [02:13<09:41,  3.16s/it]
 27%|██▋       | 67/250 [02:14<07:53,  2.59s/it]
 27%|██▋       | 68/250 [02:16<07:31,  2.48s/it]
 28%|██▊       | 69/250 [02:18<06:12,  2.06s/it]
 28%|██▊       | 70/250 [02:18<04:29,  1.50s/it]
 28%|██▊       | 71/250 [02:19<04:36,  1.55s/it]
 29%|██▉       | 72/250 [02:20<03:39,  1.23s/it]
 29%|██▉       | 73/250 [02:21<03:11,  1.08s/it]
 30%|██▉       | 74/250 [02:21<02:40,  1.10it/s]
 30%|███       | 75/250 [02:23<03:24,  1.17s/it]
 30%|███       | 76/250 [02:25<04:03,  1.40s/it]
 31%|███       | 77/250 [02:26<03:35,  1.25s/it]
 31%|███       | 78/250 [02:27<03:55,  1.37s/it]
 32%|███▏      | 79/250 [02:28<02:53,  1.01s/it]
 32%|███▏      | 80/250 [02:29<03:25,  1.21s/it]
 32%|███▏      | 81/250 [02:30<02:40,  1.05it/s]
 33%|███▎      | 82/250 [02:31<03:13,  1.15s/it]
 33%|███▎      | 83/250 [02:31<02:24,  1.15it/s]
 34%|███▎      | 84/250 [02:32<02:11,  1.26it/s]
 34%|███▍      | 85/250 [02:33<02:10,  1.26it/s]
 34%|███▍      | 86/250 [02:33<01:57,  1.39it/s]
 35%|███▌      | 88/250 [02:45<08:34,  3.18s/it]
 36%|███▌      | 89/250 [02:47<07:20,  2.73s/it]
 36%|███▌      | 90/250 [02:47<05:31,  2.07s/it]
 36%|███▋      | 91/250 [02:48<04:33,  1.72s/it]
 37%|███▋      | 92/250 [02:48<03:29,  1.33s/it]
 37%|███▋      | 93/250 [02:48<02:37,  1.01s/it]
 38%|███▊      | 94/250 [02:48<01:56,  1.34it/s]
 38%|███▊      | 95/250 [02:49<01:33,  1.65it/s]
 38%|███▊      | 96/250 [02:50<01:54,  1.34it/s]
 39%|███▉      | 97/250 [02:50<01:28,  1.74it/s]
 39%|███▉      | 98/250 [02:51<01:49,  1.38it/s]
 40%|████      | 100/250 [02:51<01:03,  2.35it/s]
 40%|████      | 101/250 [02:51<00:56,  2.64it/s]
 41%|████      | 102/250 [02:52<01:02,  2.37it/s]
 41%|████      | 103/250 [02:53<01:32,  1.58it/s]
 42%|████▏     | 104/250 [02:54<01:22,  1.77it/s]
 42%|████▏     | 106/250 [02:57<02:21,  1.02it/s]
 43%|████▎     | 107/250 [02:57<02:09,  1.10it/s]
 43%|████▎     | 108/250 [02:58<01:46,  1.33it/s]
 44%|████▎     | 109/250 [02:58<01:27,  1.61it/s]
 44%|████▍     | 110/250 [03:01<03:12,  1.38s/it]
 44%|████▍     | 111/250 [03:04<04:16,  1.85s/it]
 45%|████▍     | 112/250 [03:05<03:47,  1.65s/it]
 45%|████▌     | 113/250 [03:07<03:31,  1.55s/it]
 46%|████▌     | 114/250 [03:07<02:37,  1.16s/it]
 46%|████▌     | 115/250 [03:12<05:07,  2.28s/it]
 46%|████▋     | 116/250 [03:15<05:48,  2.60s/it]
 47%|████▋     | 117/250 [03:16<04:21,  1.97s/it]
 47%|████▋     | 118/250 [03:17<04:15,  1.94s/it]
 48%|████▊     | 119/250 [03:18<03:17,  1.51s/it]
 48%|████▊     | 120/250 [03:19<03:11,  1.48s/it]
 48%|████▊     | 121/250 [03:21<02:57,  1.37s/it]
 49%|████▉     | 122/250 [03:22<02:45,  1.29s/it]
 49%|████▉     | 123/250 [03:23<02:41,  1.27s/it]
 50%|█████     | 125/250 [03:25<02:29,  1.20s/it]
 50%|█████     | 126/250 [03:25<01:57,  1.06it/s]
 51%|█████     | 127/250 [03:26<01:46,  1.15it/s]
 51%|█████     | 128/250 [03:29<03:01,  1.48s/it]
 52%|█████▏    | 129/250 [03:31<03:29,  1.73s/it]
 52%|█████▏    | 130/250 [03:37<05:51,  2.93s/it]
 52%|█████▏    | 131/250 [03:37<04:13,  2.13s/it]
 53%|█████▎    | 132/250 [03:38<03:21,  1.70s/it]
 53%|█████▎    | 133/250 [03:39<02:47,  1.43s/it]
 54%|█████▎    | 134/250 [03:41<03:13,  1.67s/it]
 54%|█████▍    | 135/250 [03:46<04:57,  2.59s/it]
 54%|█████▍    | 136/250 [03:47<03:55,  2.07s/it]
 55%|█████▍    | 137/250 [03:47<03:03,  1.63s/it]
 55%|█████▌    | 138/250 [03:49<02:52,  1.54s/it]
 56%|█████▌    | 140/250 [03:49<01:35,  1.15it/s]
 57%|█████▋    | 142/250 [03:49<01:04,  1.67it/s]
 57%|█████▋    | 143/250 [03:50<01:13,  1.46it/s]
 58%|█████▊    | 144/250 [03:51<01:12,  1.47it/s]
 58%|█████▊    | 145/250 [03:52<01:14,  1.41it/s]
 58%|█████▊    | 146/250 [03:56<02:39,  1.53s/it]
 59%|█████▉    | 147/250 [03:59<03:24,  1.99s/it]
 59%|█████▉    | 148/250 [04:00<02:57,  1.74s/it]
 60%|█████▉    | 149/250 [04:00<02:22,  1.42s/it]
 60%|██████    | 150/250 [04:01<01:59,  1.19s/it]
 61%|██████    | 152/250 [04:04<02:04,  1.27s/it]
 61%|██████    | 153/250 [04:06<02:23,  1.48s/it]
 62%|██████▏   | 154/250 [04:09<02:54,  1.82s/it]
 62%|██████▏   | 155/250 [04:11<03:03,  1.93s/it]
 62%|██████▏   | 156/250 [04:17<04:47,  3.05s/it]
 63%|██████▎   | 157/250 [04:17<03:29,  2.25s/it]
 63%|██████▎   | 158/250 [04:19<03:04,  2.01s/it]
 64%|██████▎   | 159/250 [04:19<02:30,  1.66s/it]
 64%|██████▍   | 160/250 [04:21<02:25,  1.61s/it]
 64%|██████▍   | 161/250 [04:21<01:56,  1.31s/it]
 65%|██████▍   | 162/250 [04:24<02:34,  1.75s/it]
 65%|██████▌   | 163/250 [04:25<02:09,  1.49s/it]
 66%|██████▌   | 164/250 [04:30<03:43,  2.60s/it]
 66%|██████▌   | 165/250 [04:31<02:49,  2.00s/it]
 66%|██████▋   | 166/250 [04:32<02:13,  1.58s/it]
 67%|██████▋   | 167/250 [04:32<01:43,  1.25s/it]
 67%|██████▋   | 168/250 [04:35<02:15,  1.66s/it]
 68%|██████▊   | 169/250 [04:36<01:57,  1.45s/it]
 68%|██████▊   | 170/250 [04:36<01:33,  1.17s/it]
 68%|██████▊   | 171/250 [04:37<01:32,  1.17s/it]
 69%|██████▉   | 172/250 [04:41<02:39,  2.04s/it]
 69%|██████▉   | 173/250 [04:42<02:04,  1.62s/it]
 70%|██████▉   | 174/250 [04:45<02:44,  2.16s/it]
 70%|███████   | 175/250 [04:52<04:21,  3.48s/it]
 70%|███████   | 176/250 [04:55<04:17,  3.47s/it]
 71%|███████   | 177/250 [04:57<03:23,  2.79s/it]
 71%|███████   | 178/250 [04:57<02:35,  2.16s/it]
 72%|███████▏  | 179/250 [04:58<01:57,  1.65s/it]
 72%|███████▏  | 180/250 [04:58<01:34,  1.35s/it]
 72%|███████▏  | 181/250 [05:02<02:16,  1.97s/it]
 73%|███████▎  | 182/250 [05:03<01:51,  1.64s/it]
 73%|███████▎  | 183/250 [05:05<01:59,  1.79s/it]
 74%|███████▎  | 184/250 [05:05<01:28,  1.35s/it]
 74%|███████▍  | 185/250 [05:08<01:56,  1.80s/it]
 75%|███████▍  | 187/250 [05:10<01:29,  1.42s/it]
 75%|███████▌  | 188/250 [05:10<01:13,  1.18s/it]
 76%|███████▌  | 189/250 [05:12<01:22,  1.36s/it]
 76%|███████▌  | 190/250 [05:12<01:02,  1.04s/it]
 76%|███████▋  | 191/250 [05:13<00:47,  1.25it/s]
 77%|███████▋  | 192/250 [05:13<00:37,  1.54it/s]
 77%|███████▋  | 193/250 [05:13<00:31,  1.80it/s]
 78%|███████▊  | 194/250 [05:13<00:26,  2.13it/s]
 78%|███████▊  | 195/250 [05:14<00:28,  1.91it/s]
 78%|███████▊  | 196/250 [05:14<00:23,  2.32it/s]
 79%|███████▉  | 197/250 [05:15<00:33,  1.58it/s]
 79%|███████▉  | 198/250 [05:19<01:12,  1.39s/it]
 80%|███████▉  | 199/250 [05:20<01:10,  1.38s/it]
 80%|████████  | 200/250 [05:21<00:57,  1.14s/it]
 80%|████████  | 201/250 [05:22<01:05,  1.33s/it]
 81%|████████  | 202/250 [05:23<00:50,  1.06s/it]
 81%|████████  | 203/250 [05:24<00:56,  1.21s/it]
 82%|████████▏ | 204/250 [05:26<01:06,  1.45s/it]
 82%|████████▏ | 205/250 [05:27<00:54,  1.20s/it]
 82%|████████▏ | 206/250 [05:28<00:46,  1.06s/it]
 83%|████████▎ | 207/250 [05:28<00:39,  1.10it/s]
 83%|████████▎ | 208/250 [05:30<00:44,  1.07s/it]
 84%|████████▎ | 209/250 [05:30<00:37,  1.10it/s]
 84%|████████▍ | 210/250 [05:32<00:44,  1.11s/it]
 84%|████████▍ | 211/250 [05:32<00:34,  1.12it/s]
 85%|████████▍ | 212/250 [05:33<00:35,  1.06it/s]
 85%|████████▌ | 213/250 [05:38<01:14,  2.01s/it]
 86%|████████▌ | 214/250 [05:38<00:56,  1.56s/it]
 86%|████████▌ | 215/250 [05:42<01:18,  2.23s/it]
 86%|████████▋ | 216/250 [05:44<01:12,  2.14s/it]
 87%|████████▋ | 217/250 [05:46<01:06,  2.01s/it]
 87%|████████▋ | 218/250 [05:46<00:51,  1.61s/it]
 88%|████████▊ | 219/250 [05:47<00:43,  1.41s/it]
 88%|████████▊ | 220/250 [05:50<00:56,  1.87s/it]
 88%|████████▊ | 221/250 [05:51<00:42,  1.45s/it]
 89%|████████▉ | 222/250 [05:55<01:02,  2.23s/it]
 89%|████████▉ | 223/250 [05:55<00:48,  1.78s/it]
 90%|████████▉ | 224/250 [05:57<00:40,  1.56s/it]
 90%|█████████ | 225/250 [06:00<00:50,  2.01s/it]
 90%|█████████ | 226/250 [06:03<00:54,  2.28s/it]
 91%|█████████ | 227/250 [06:03<00:37,  1.65s/it]
 91%|█████████ | 228/250 [06:03<00:27,  1.23s/it]
 92%|█████████▏| 229/250 [06:04<00:23,  1.11s/it]
 92%|█████████▏| 230/250 [06:05<00:25,  1.29s/it]
 92%|█████████▏| 231/250 [06:08<00:30,  1.62s/it]
 93%|█████████▎| 232/250 [06:09<00:24,  1.34s/it]
 93%|█████████▎| 233/250 [06:10<00:24,  1.45s/it]
 94%|█████████▎| 234/250 [06:13<00:29,  1.84s/it]
 94%|█████████▍| 235/250 [06:14<00:22,  1.53s/it]
 94%|█████████▍| 236/250 [06:14<00:17,  1.22s/it]
 95%|█████████▍| 237/250 [06:14<00:11,  1.13it/s]
 96%|█████████▌| 239/250 [06:15<00:07,  1.42it/s]
 96%|█████████▌| 240/250 [06:16<00:06,  1.52it/s]
 96%|█████████▋| 241/250 [06:17<00:06,  1.34it/s]
 97%|█████████▋| 242/250 [06:18<00:07,  1.05it/s]
 98%|█████████▊| 244/250 [06:19<00:03,  1.61it/s]
 98%|█████████▊| 245/250 [06:22<00:06,  1.34s/it]
 98%|█████████▊| 246/250 [06:26<00:07,  1.97s/it]
 99%|█████████▉| 247/250 [06:33<00:09,  3.25s/it]
100%|█████████▉| 249/250 [06:37<00:02,  2.71s/it]
100%|██████████| 250/250 [06:41<00:00,  3.02s/it]
100%|██████████| 250/250 [06:41<00:00,  1.61s/it]
2025-05-15 12:48:59,607 - modnet - INFO - Loss per individual: ind 0: 0.489 	ind 1: 0.485 	ind 2: 0.484 	ind 3: 0.490 	ind 4: 0.516 	ind 5: 0.480 	ind 6: 0.496 	ind 7: 0.500 	ind 8: 0.496 	ind 9: 0.548 	ind 10: 0.487 	ind 11: 0.517 	ind 12: 0.510 	ind 13: 0.506 	ind 14: 0.478 	ind 15: 0.498 	ind 16: 0.507 	ind 17: 0.501 	ind 18: 0.496 	ind 19: 0.504 	ind 20: 0.778 	ind 21: 0.502 	ind 22: 0.508 	ind 23: 0.507 	ind 24: 0.469 	ind 25: 0.524 	ind 26: 0.502 	ind 27: 0.481 	ind 28: 0.494 	ind 29: 0.499 	ind 30: 0.506 	ind 31: 0.503 	ind 32: 0.508 	ind 33: 0.505 	ind 34: 0.505 	ind 35: 0.478 	ind 36: 0.501 	ind 37: 0.498 	ind 38: 0.435 	ind 39: 0.535 	ind 40: 0.445 	ind 41: 0.964 	ind 42: 0.518 	ind 43: 0.499 	ind 44: 0.479 	ind 45: 0.495 	ind 46: 0.524 	ind 47: 0.491 	ind 48: 0.487 	ind 49: 0.488 	
2025-05-15 12:48:59,610 - modnet - INFO - Generation number 4

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:25<1:44:59, 25.30s/it]
  1%|          | 2/250 [00:27<49:37, 12.01s/it]  
  1%|          | 3/250 [00:33<36:51,  8.95s/it]
  2%|▏         | 4/250 [00:33<23:02,  5.62s/it]
  2%|▏         | 5/250 [00:34<15:42,  3.85s/it]
  2%|▏         | 6/250 [00:35<10:59,  2.70s/it]
  3%|▎         | 7/250 [00:38<11:34,  2.86s/it]
  3%|▎         | 8/250 [00:38<08:31,  2.11s/it]
  4%|▎         | 9/250 [00:40<08:10,  2.03s/it]
  4%|▍         | 10/250 [00:40<06:06,  1.53s/it]
  4%|▍         | 11/250 [00:42<06:28,  1.63s/it]
  5%|▌         | 13/250 [00:49<09:14,  2.34s/it]
  6%|▌         | 14/250 [00:52<10:36,  2.70s/it]
  6%|▌         | 15/250 [00:54<09:31,  2.43s/it]
  6%|▋         | 16/250 [00:58<10:46,  2.76s/it]
  7%|▋         | 17/250 [01:02<12:34,  3.24s/it]
  7%|▋         | 18/250 [01:03<10:20,  2.68s/it]
  8%|▊         | 19/250 [01:04<07:37,  1.98s/it]
  8%|▊         | 20/250 [01:04<05:53,  1.54s/it]
  8%|▊         | 21/250 [01:05<04:55,  1.29s/it]
  9%|▉         | 22/250 [01:07<05:32,  1.46s/it]
  9%|▉         | 23/250 [01:10<07:28,  1.98s/it]
 10%|▉         | 24/250 [01:11<06:35,  1.75s/it]
 10%|█         | 25/250 [01:12<05:26,  1.45s/it]
 10%|█         | 26/250 [01:12<04:04,  1.09s/it]
 11%|█         | 27/250 [01:13<03:11,  1.16it/s]
 11%|█         | 28/250 [01:13<02:43,  1.36it/s]
 12%|█▏        | 29/250 [01:13<02:19,  1.59it/s]
 12%|█▏        | 30/250 [01:17<05:07,  1.40s/it]
 12%|█▏        | 31/250 [01:18<04:43,  1.30s/it]
 13%|█▎        | 32/250 [01:18<03:42,  1.02s/it]
 14%|█▎        | 34/250 [01:19<02:53,  1.25it/s]
 14%|█▍        | 35/250 [01:20<03:02,  1.18it/s]
 14%|█▍        | 36/250 [01:21<02:45,  1.29it/s]
 15%|█▍        | 37/250 [01:23<04:08,  1.16s/it]
 15%|█▌        | 38/250 [01:26<06:11,  1.75s/it]
 16%|█▌        | 39/250 [01:26<04:36,  1.31s/it]
 16%|█▌        | 40/250 [01:27<04:16,  1.22s/it]
 16%|█▋        | 41/250 [01:29<04:35,  1.32s/it]
 17%|█▋        | 42/250 [01:29<03:23,  1.02it/s]
 18%|█▊        | 44/250 [01:30<02:43,  1.26it/s]
 18%|█▊        | 45/250 [01:31<02:55,  1.17it/s]
 18%|█▊        | 46/250 [01:31<02:20,  1.45it/s]
 19%|█▉        | 47/250 [01:33<02:49,  1.20it/s]
 19%|█▉        | 48/250 [01:33<02:25,  1.39it/s]
 20%|█▉        | 49/250 [01:34<02:50,  1.18it/s]
 20%|██        | 50/250 [01:35<02:49,  1.18it/s]
 20%|██        | 51/250 [01:36<02:43,  1.22it/s]
 21%|██        | 52/250 [01:36<02:20,  1.41it/s]
 21%|██        | 53/250 [01:36<01:48,  1.82it/s]
 22%|██▏       | 54/250 [01:37<02:01,  1.61it/s]
 22%|██▏       | 55/250 [01:40<04:15,  1.31s/it]
 22%|██▏       | 56/250 [01:46<08:17,  2.57s/it]
 23%|██▎       | 57/250 [01:53<12:44,  3.96s/it]
 23%|██▎       | 58/250 [01:56<11:28,  3.59s/it]
 24%|██▎       | 59/250 [01:56<08:10,  2.57s/it]
 24%|██▍       | 60/250 [01:57<06:50,  2.16s/it]
 24%|██▍       | 61/250 [01:57<05:06,  1.62s/it]
 25%|██▍       | 62/250 [01:58<03:48,  1.22s/it]
 25%|██▌       | 63/250 [01:58<03:17,  1.05s/it]
 26%|██▌       | 64/250 [02:01<04:34,  1.48s/it]
 26%|██▌       | 65/250 [02:01<03:26,  1.12s/it]
 26%|██▋       | 66/250 [02:03<03:43,  1.22s/it]
 27%|██▋       | 67/250 [02:08<07:20,  2.40s/it]
 28%|██▊       | 69/250 [02:09<04:43,  1.56s/it]
 28%|██▊       | 70/250 [02:11<04:48,  1.61s/it]
 29%|██▉       | 72/250 [02:12<03:53,  1.31s/it]
 29%|██▉       | 73/250 [02:14<03:59,  1.35s/it]
 30%|██▉       | 74/250 [02:14<03:15,  1.11s/it]
 30%|███       | 75/250 [02:14<02:32,  1.15it/s]
 30%|███       | 76/250 [02:16<02:53,  1.00it/s]
 31%|███       | 77/250 [02:17<02:55,  1.02s/it]
 31%|███       | 78/250 [02:17<02:13,  1.29it/s]
 32%|███▏      | 79/250 [02:25<08:14,  2.89s/it]
 32%|███▏      | 80/250 [02:27<07:20,  2.59s/it]
 32%|███▏      | 81/250 [02:27<05:18,  1.88s/it]
 33%|███▎      | 82/250 [02:28<04:29,  1.61s/it]
 33%|███▎      | 83/250 [02:32<06:05,  2.19s/it]
 34%|███▎      | 84/250 [02:32<04:26,  1.61s/it]
 34%|███▍      | 85/250 [02:32<03:13,  1.17s/it]
 34%|███▍      | 86/250 [02:32<02:31,  1.08it/s]
 35%|███▍      | 87/250 [02:34<02:42,  1.00it/s]
 35%|███▌      | 88/250 [02:34<02:04,  1.30it/s]
 36%|███▌      | 89/250 [02:35<02:13,  1.21it/s]
 36%|███▌      | 90/250 [02:35<01:59,  1.34it/s]
 36%|███▋      | 91/250 [02:36<01:36,  1.64it/s]
 37%|███▋      | 92/250 [02:37<01:59,  1.32it/s]
 37%|███▋      | 93/250 [02:38<02:33,  1.02it/s]
 38%|███▊      | 95/250 [02:41<02:52,  1.12s/it]
 38%|███▊      | 96/250 [02:41<02:36,  1.02s/it]
 39%|███▉      | 97/250 [02:42<02:16,  1.12it/s]
 39%|███▉      | 98/250 [02:42<01:47,  1.41it/s]
 40%|███▉      | 99/250 [02:48<05:21,  2.13s/it]
 40%|████      | 100/250 [02:48<03:56,  1.58s/it]
 40%|████      | 101/250 [02:49<03:13,  1.30s/it]
 41%|████      | 102/250 [02:50<03:18,  1.34s/it]
 41%|████      | 103/250 [02:51<02:52,  1.17s/it]
 42%|████▏     | 104/250 [02:53<03:10,  1.30s/it]
 42%|████▏     | 105/250 [02:55<03:35,  1.49s/it]
 42%|████▏     | 106/250 [02:55<02:42,  1.13s/it]
 43%|████▎     | 107/250 [02:57<03:10,  1.33s/it]
 43%|████▎     | 108/250 [02:57<02:36,  1.10s/it]
 44%|████▎     | 109/250 [02:57<01:57,  1.20it/s]
 44%|████▍     | 110/250 [02:58<01:59,  1.17it/s]
 44%|████▍     | 111/250 [02:59<01:30,  1.54it/s]
 45%|████▍     | 112/250 [02:59<01:35,  1.44it/s]
 45%|████▌     | 113/250 [03:00<01:31,  1.50it/s]
 46%|████▌     | 114/250 [03:00<01:25,  1.59it/s]
 46%|████▌     | 115/250 [03:01<01:07,  1.99it/s]
 46%|████▋     | 116/250 [03:02<01:43,  1.29it/s]
 47%|████▋     | 117/250 [03:03<01:28,  1.50it/s]
 47%|████▋     | 118/250 [03:04<02:07,  1.04it/s]
 48%|████▊     | 119/250 [03:04<01:38,  1.33it/s]
 48%|████▊     | 120/250 [03:05<01:38,  1.32it/s]
 48%|████▊     | 121/250 [03:06<01:37,  1.33it/s]
 49%|████▉     | 122/250 [03:07<01:46,  1.21it/s]
 49%|████▉     | 123/250 [03:12<04:08,  1.96s/it]
 50%|████▉     | 124/250 [03:13<03:56,  1.88s/it]
 50%|█████     | 125/250 [03:17<05:05,  2.44s/it]
 50%|█████     | 126/250 [03:20<05:19,  2.58s/it]
 51%|█████     | 127/250 [03:20<03:45,  1.84s/it]
 51%|█████     | 128/250 [03:25<05:25,  2.67s/it]
 52%|█████▏    | 129/250 [03:25<04:04,  2.02s/it]
 52%|█████▏    | 130/250 [03:28<04:23,  2.19s/it]
 52%|█████▏    | 131/250 [03:29<03:51,  1.95s/it]
 53%|█████▎    | 132/250 [03:30<03:08,  1.60s/it]
 53%|█████▎    | 133/250 [03:31<02:59,  1.54s/it]
 54%|█████▎    | 134/250 [03:33<03:19,  1.72s/it]
 54%|█████▍    | 135/250 [03:35<03:23,  1.77s/it]
 54%|█████▍    | 136/250 [03:38<03:45,  1.97s/it]
 55%|█████▍    | 137/250 [03:40<03:43,  1.98s/it]
 55%|█████▌    | 138/250 [03:41<03:01,  1.62s/it]
 56%|█████▌    | 139/250 [03:43<03:19,  1.80s/it]
 56%|█████▌    | 140/250 [03:49<05:53,  3.21s/it]
 56%|█████▋    | 141/250 [03:51<05:09,  2.84s/it]
 57%|█████▋    | 142/250 [03:53<04:40,  2.60s/it]
 57%|█████▋    | 143/250 [03:58<06:00,  3.37s/it]
 58%|█████▊    | 144/250 [03:59<04:25,  2.51s/it]
 58%|█████▊    | 145/250 [04:02<04:48,  2.74s/it]
 58%|█████▊    | 146/250 [04:03<03:43,  2.15s/it]
 59%|█████▉    | 148/250 [04:05<02:54,  1.71s/it]
 60%|█████▉    | 149/250 [04:06<02:20,  1.39s/it]
 60%|██████    | 150/250 [04:07<02:04,  1.24s/it]
 60%|██████    | 151/250 [04:07<01:51,  1.13s/it]
 61%|██████    | 152/250 [04:08<01:28,  1.11it/s]
 61%|██████    | 153/250 [04:11<02:22,  1.47s/it]
 62%|██████▏   | 154/250 [04:12<02:09,  1.35s/it]
 62%|██████▏   | 155/250 [04:13<01:54,  1.21s/it]
 62%|██████▏   | 156/250 [04:16<02:48,  1.79s/it]
 63%|██████▎   | 157/250 [04:18<02:52,  1.85s/it]
 63%|██████▎   | 158/250 [04:18<02:04,  1.36s/it]
 64%|██████▎   | 159/250 [04:18<01:32,  1.02s/it]
 64%|██████▍   | 160/250 [04:19<01:17,  1.15it/s]
 64%|██████▍   | 161/250 [04:20<01:31,  1.03s/it]
 65%|██████▍   | 162/250 [04:21<01:33,  1.06s/it]
 65%|██████▌   | 163/250 [04:23<01:40,  1.16s/it]
 66%|██████▌   | 164/250 [04:24<01:38,  1.15s/it]
 66%|██████▌   | 165/250 [04:24<01:19,  1.07it/s]
 66%|██████▋   | 166/250 [04:31<03:39,  2.61s/it]
 67%|██████▋   | 167/250 [04:32<03:05,  2.24s/it]
 67%|██████▋   | 168/250 [04:32<02:15,  1.65s/it]
 68%|██████▊   | 169/250 [04:33<01:38,  1.21s/it]
 68%|██████▊   | 170/250 [04:33<01:13,  1.08it/s]
 68%|██████▊   | 171/250 [04:33<01:00,  1.30it/s]
 69%|██████▉   | 172/250 [04:34<01:04,  1.22it/s]
 69%|██████▉   | 173/250 [04:34<00:49,  1.56it/s]
 70%|███████   | 175/250 [04:35<00:29,  2.53it/s]
 70%|███████   | 176/250 [04:35<00:27,  2.74it/s]
 71%|███████   | 177/250 [04:36<00:50,  1.44it/s]
 71%|███████   | 178/250 [04:37<00:53,  1.34it/s]
 72%|███████▏  | 179/250 [04:38<00:58,  1.21it/s]
 72%|███████▏  | 180/250 [04:41<01:27,  1.25s/it]
 72%|███████▏  | 181/250 [04:43<01:48,  1.57s/it]
 73%|███████▎  | 182/250 [04:45<01:45,  1.55s/it]
 73%|███████▎  | 183/250 [04:47<02:03,  1.84s/it]
 74%|███████▎  | 184/250 [04:48<01:49,  1.66s/it]
 74%|███████▍  | 185/250 [04:49<01:22,  1.27s/it]
 74%|███████▍  | 186/250 [04:50<01:19,  1.24s/it]
 75%|███████▍  | 187/250 [04:51<01:20,  1.27s/it]
 75%|███████▌  | 188/250 [04:51<00:58,  1.07it/s]
 76%|███████▌  | 189/250 [04:52<00:47,  1.29it/s]
 76%|███████▌  | 190/250 [04:52<00:40,  1.48it/s]
 76%|███████▋  | 191/250 [04:54<00:54,  1.08it/s]
 77%|███████▋  | 192/250 [04:55<01:09,  1.19s/it]
 77%|███████▋  | 193/250 [04:56<00:58,  1.03s/it]
 78%|███████▊  | 194/250 [04:56<00:45,  1.23it/s]
 78%|███████▊  | 195/250 [04:57<00:34,  1.61it/s]
 78%|███████▊  | 196/250 [04:57<00:27,  1.97it/s]
 79%|███████▉  | 197/250 [04:59<00:53,  1.01s/it]
 79%|███████▉  | 198/250 [04:59<00:42,  1.24it/s]
 80%|███████▉  | 199/250 [05:00<00:34,  1.48it/s]
 80%|████████  | 200/250 [05:00<00:32,  1.52it/s]
 80%|████████  | 201/250 [05:05<01:28,  1.81s/it]
 81%|████████  | 202/250 [05:07<01:26,  1.79s/it]
 81%|████████  | 203/250 [05:09<01:28,  1.87s/it]
 82%|████████▏ | 204/250 [05:10<01:13,  1.59s/it]
 82%|████████▏ | 205/250 [05:10<00:52,  1.17s/it]
 82%|████████▏ | 206/250 [05:10<00:39,  1.13it/s]
 83%|████████▎ | 207/250 [05:11<00:34,  1.23it/s]
 83%|████████▎ | 208/250 [05:13<00:55,  1.32s/it]
 84%|████████▎ | 209/250 [05:13<00:40,  1.00it/s]
 84%|████████▍ | 210/250 [05:15<00:41,  1.03s/it]
 84%|████████▍ | 211/250 [05:18<01:04,  1.65s/it]
 85%|████████▍ | 212/250 [05:18<00:49,  1.29s/it]
 85%|████████▌ | 213/250 [05:18<00:37,  1.01s/it]
 86%|████████▌ | 214/250 [05:19<00:34,  1.05it/s]
 86%|████████▌ | 215/250 [05:20<00:26,  1.32it/s]
 86%|████████▋ | 216/250 [05:22<00:38,  1.12s/it]
 87%|████████▋ | 217/250 [05:22<00:28,  1.17it/s]
 87%|████████▋ | 218/250 [05:23<00:26,  1.20it/s]
 88%|████████▊ | 219/250 [05:23<00:26,  1.17it/s]
 88%|████████▊ | 220/250 [05:24<00:19,  1.57it/s]
 88%|████████▊ | 221/250 [05:25<00:28,  1.03it/s]
 89%|████████▉ | 222/250 [05:27<00:29,  1.05s/it]
 89%|████████▉ | 223/250 [05:27<00:21,  1.25it/s]
 90%|████████▉ | 224/250 [05:27<00:16,  1.59it/s]
 90%|█████████ | 225/250 [05:28<00:14,  1.69it/s]
 90%|█████████ | 226/250 [05:28<00:11,  2.09it/s]
 91%|█████████ | 227/250 [05:28<00:09,  2.33it/s]
 91%|█████████ | 228/250 [05:28<00:09,  2.39it/s]
 92%|█████████▏| 229/250 [05:29<00:07,  2.79it/s]
 92%|█████████▏| 230/250 [05:29<00:06,  3.14it/s]
 92%|█████████▏| 231/250 [05:30<00:10,  1.81it/s]
 93%|█████████▎| 232/250 [05:32<00:18,  1.03s/it]
 93%|█████████▎| 233/250 [05:33<00:17,  1.05s/it]
 94%|█████████▎| 234/250 [05:34<00:13,  1.15it/s]
 94%|█████████▍| 235/250 [05:34<00:10,  1.39it/s]
 94%|█████████▍| 236/250 [05:35<00:09,  1.51it/s]
 95%|█████████▍| 237/250 [05:36<00:11,  1.09it/s]
 95%|█████████▌| 238/250 [05:37<00:10,  1.11it/s]
 96%|█████████▌| 239/250 [05:37<00:07,  1.48it/s]
 96%|█████████▌| 240/250 [05:37<00:05,  1.73it/s]
 96%|█████████▋| 241/250 [05:38<00:06,  1.48it/s]
 97%|█████████▋| 242/250 [05:39<00:05,  1.57it/s]
 97%|█████████▋| 243/250 [05:39<00:04,  1.63it/s]
 98%|█████████▊| 244/250 [05:40<00:04,  1.46it/s]
 98%|█████████▊| 246/250 [05:41<00:02,  1.83it/s]
 99%|█████████▉| 248/250 [05:41<00:00,  2.49it/s]
100%|█████████▉| 249/250 [05:48<00:01,  1.84s/it]
100%|██████████| 250/250 [05:57<00:00,  3.43s/it]
100%|██████████| 250/250 [05:57<00:00,  1.43s/it]
2025-05-15 12:54:57,009 - modnet - INFO - Loss per individual: ind 0: 0.550 	ind 1: 0.498 	ind 2: 0.501 	ind 3: 0.499 	ind 4: 0.473 	ind 5: 0.746 	ind 6: 0.503 	ind 7: 0.494 	ind 8: 0.475 	ind 9: 0.486 	ind 10: 0.517 	ind 11: 0.470 	ind 12: 0.482 	ind 13: 0.908 	ind 14: 0.521 	ind 15: 0.479 	ind 16: 0.441 	ind 17: 0.626 	ind 18: 0.478 	ind 19: 0.510 	ind 20: 0.537 	ind 21: 0.515 	ind 22: 0.445 	ind 23: 0.480 	ind 24: 0.515 	ind 25: 0.512 	ind 26: 0.493 	ind 27: 0.481 	ind 28: 0.509 	ind 29: 0.501 	ind 30: 0.491 	ind 31: 0.450 	ind 32: 0.516 	ind 33: 0.524 	ind 34: 0.471 	ind 35: 0.494 	ind 36: 0.481 	ind 37: 0.512 	ind 38: 0.487 	ind 39: 0.496 	ind 40: 0.489 	ind 41: 0.484 	ind 42: 0.527 	ind 43: 0.497 	ind 44: 0.489 	ind 45: 0.481 	ind 46: 0.491 	ind 47: 0.539 	ind 48: 0.549 	ind 49: 0.487 	
2025-05-15 12:54:57,015 - modnet - INFO - Generation number 5

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:25<1:44:02, 25.07s/it]
  1%|          | 2/250 [00:25<44:31, 10.77s/it]  
  1%|          | 3/250 [00:26<24:25,  5.93s/it]
  2%|▏         | 4/250 [00:27<16:34,  4.04s/it]
  2%|▏         | 5/250 [00:27<11:07,  2.72s/it]
  2%|▏         | 6/250 [00:28<08:56,  2.20s/it]
  3%|▎         | 7/250 [00:30<08:44,  2.16s/it]
  4%|▎         | 9/250 [00:30<04:38,  1.16s/it]
  4%|▍         | 10/250 [00:31<04:21,  1.09s/it]
  4%|▍         | 11/250 [00:32<03:34,  1.11it/s]
  5%|▍         | 12/250 [00:33<04:15,  1.08s/it]
  5%|▌         | 13/250 [00:34<03:49,  1.03it/s]
  6%|▌         | 14/250 [00:35<03:40,  1.07it/s]
  6%|▌         | 15/250 [00:35<02:50,  1.38it/s]
  7%|▋         | 17/250 [00:37<03:19,  1.17it/s]
  7%|▋         | 18/250 [00:38<03:36,  1.07it/s]
  8%|▊         | 19/250 [00:41<05:25,  1.41s/it]
  8%|▊         | 20/250 [00:44<07:13,  1.89s/it]
  8%|▊         | 21/250 [00:49<10:49,  2.84s/it]
  9%|▉         | 22/250 [00:50<08:17,  2.18s/it]
  9%|▉         | 23/250 [00:51<06:42,  1.77s/it]
 10%|▉         | 24/250 [00:53<07:19,  1.94s/it]
 10%|█         | 25/250 [00:53<05:22,  1.43s/it]
 10%|█         | 26/250 [00:54<04:39,  1.25s/it]
 11%|█         | 27/250 [00:55<03:40,  1.01it/s]
 11%|█         | 28/250 [00:56<04:10,  1.13s/it]
 12%|█▏        | 29/250 [00:56<03:05,  1.19it/s]
 12%|█▏        | 30/250 [00:58<03:50,  1.05s/it]
 12%|█▏        | 31/250 [00:59<04:18,  1.18s/it]
 13%|█▎        | 32/250 [01:02<05:33,  1.53s/it]
 13%|█▎        | 33/250 [01:02<04:43,  1.31s/it]
 14%|█▎        | 34/250 [01:06<07:02,  1.96s/it]
 14%|█▍        | 35/250 [01:09<08:45,  2.44s/it]
 14%|█▍        | 36/250 [01:10<06:28,  1.82s/it]
 15%|█▍        | 37/250 [01:13<08:11,  2.31s/it]
 15%|█▌        | 38/250 [01:14<06:38,  1.88s/it]
 16%|█▌        | 39/250 [01:17<07:41,  2.19s/it]
 16%|█▋        | 41/250 [01:17<04:29,  1.29s/it]
 17%|█▋        | 42/250 [01:18<03:55,  1.13s/it]
 17%|█▋        | 43/250 [01:23<07:04,  2.05s/it]
 18%|█▊        | 44/250 [01:24<06:29,  1.89s/it]
 18%|█▊        | 45/250 [01:24<04:50,  1.42s/it]
 18%|█▊        | 46/250 [01:26<05:09,  1.52s/it]
 19%|█▉        | 47/250 [01:27<04:07,  1.22s/it]
 20%|█▉        | 49/250 [01:28<03:32,  1.06s/it]
 20%|██        | 50/250 [01:29<03:11,  1.04it/s]
 21%|██        | 52/250 [01:35<05:36,  1.70s/it]
 21%|██        | 53/250 [01:37<06:29,  1.98s/it]
 22%|██▏       | 54/250 [01:38<05:38,  1.73s/it]
 22%|██▏       | 55/250 [01:39<04:43,  1.46s/it]
 22%|██▏       | 56/250 [01:41<04:51,  1.50s/it]
 23%|██▎       | 57/250 [01:43<05:11,  1.61s/it]
 23%|██▎       | 58/250 [01:45<05:33,  1.74s/it]
 24%|██▎       | 59/250 [01:49<07:49,  2.46s/it]
 24%|██▍       | 60/250 [01:58<14:01,  4.43s/it]
 24%|██▍       | 61/250 [02:00<11:44,  3.73s/it]
 25%|██▍       | 62/250 [02:00<08:29,  2.71s/it]
 25%|██▌       | 63/250 [02:01<06:46,  2.17s/it]
 26%|██▌       | 64/250 [02:02<04:58,  1.60s/it]
 26%|██▌       | 65/250 [02:03<05:06,  1.66s/it]
 26%|██▋       | 66/250 [02:05<05:18,  1.73s/it]
 27%|██▋       | 67/250 [02:07<04:55,  1.62s/it]
 27%|██▋       | 68/250 [02:07<03:35,  1.19s/it]
 28%|██▊       | 69/250 [02:07<03:04,  1.02s/it]
 28%|██▊       | 70/250 [02:08<02:43,  1.10it/s]
 28%|██▊       | 71/250 [02:09<03:06,  1.04s/it]
 29%|██▉       | 72/250 [02:11<03:26,  1.16s/it]
 29%|██▉       | 73/250 [02:11<02:52,  1.02it/s]
 30%|██▉       | 74/250 [02:12<02:09,  1.36it/s]
 30%|███       | 75/250 [02:12<01:48,  1.61it/s]
 30%|███       | 76/250 [02:19<07:00,  2.42s/it]
 31%|███       | 77/250 [02:19<05:31,  1.91s/it]
 31%|███       | 78/250 [02:21<04:51,  1.70s/it]
 32%|███▏      | 79/250 [02:21<04:08,  1.46s/it]
 32%|███▏      | 80/250 [02:23<04:03,  1.43s/it]
 32%|███▏      | 81/250 [02:26<05:52,  2.08s/it]
 33%|███▎      | 82/250 [02:29<06:24,  2.29s/it]
 33%|███▎      | 83/250 [02:32<06:44,  2.42s/it]
 34%|███▎      | 84/250 [02:33<05:34,  2.01s/it]
 34%|███▍      | 85/250 [02:34<04:22,  1.59s/it]
 34%|███▍      | 86/250 [02:39<07:19,  2.68s/it]
 35%|███▍      | 87/250 [02:39<05:20,  1.97s/it]
 35%|███▌      | 88/250 [02:39<03:48,  1.41s/it]
 36%|███▌      | 89/250 [02:41<04:12,  1.57s/it]
 36%|███▌      | 90/250 [02:41<03:12,  1.21s/it]
 36%|███▋      | 91/250 [02:42<02:33,  1.04it/s]
 37%|███▋      | 92/250 [02:43<02:30,  1.05it/s]
 37%|███▋      | 93/250 [02:47<04:44,  1.81s/it]
 38%|███▊      | 94/250 [02:48<04:02,  1.55s/it]
 38%|███▊      | 95/250 [02:48<03:04,  1.19s/it]
 38%|███▊      | 96/250 [02:49<02:42,  1.06s/it]
 39%|███▉      | 97/250 [02:52<04:45,  1.87s/it]
 39%|███▉      | 98/250 [02:54<04:51,  1.92s/it]
 40%|███▉      | 99/250 [02:55<03:37,  1.44s/it]
 40%|████      | 100/250 [02:56<03:29,  1.39s/it]
 40%|████      | 101/250 [02:57<02:55,  1.18s/it]
 41%|████      | 102/250 [02:59<03:32,  1.43s/it]
 41%|████      | 103/250 [03:00<03:26,  1.40s/it]
 42%|████▏     | 104/250 [03:01<02:57,  1.22s/it]
 42%|████▏     | 105/250 [03:03<03:38,  1.50s/it]
 42%|████▏     | 106/250 [03:04<03:24,  1.42s/it]
 43%|████▎     | 107/250 [03:04<02:28,  1.04s/it]
 43%|████▎     | 108/250 [03:07<03:13,  1.36s/it]
 44%|████▎     | 109/250 [03:07<02:33,  1.09s/it]
 44%|████▍     | 110/250 [03:07<01:53,  1.23it/s]
 44%|████▍     | 111/250 [03:08<01:56,  1.20it/s]
 45%|████▍     | 112/250 [03:08<01:31,  1.51it/s]
 45%|████▌     | 113/250 [03:09<01:23,  1.63it/s]
 46%|████▌     | 114/250 [03:10<01:57,  1.16it/s]
 46%|████▌     | 115/250 [03:11<01:48,  1.25it/s]
 46%|████▋     | 116/250 [03:12<01:53,  1.18it/s]
 47%|████▋     | 117/250 [03:14<02:44,  1.23s/it]
 47%|████▋     | 118/250 [03:14<02:00,  1.09it/s]
 48%|████▊     | 120/250 [03:15<01:26,  1.51it/s]
 48%|████▊     | 121/250 [03:19<03:26,  1.60s/it]
 49%|████▉     | 122/250 [03:25<05:24,  2.53s/it]
 49%|████▉     | 123/250 [03:25<04:06,  1.94s/it]
 50%|████▉     | 124/250 [03:26<03:20,  1.59s/it]
 50%|█████     | 125/250 [03:26<02:50,  1.36s/it]
 50%|█████     | 126/250 [03:27<02:31,  1.22s/it]
 51%|█████     | 127/250 [03:28<02:28,  1.20s/it]
 51%|█████     | 128/250 [03:33<04:18,  2.12s/it]
 52%|█████▏    | 129/250 [03:33<03:06,  1.54s/it]
 52%|█████▏    | 130/250 [03:35<03:09,  1.58s/it]
 52%|█████▏    | 131/250 [03:37<03:48,  1.92s/it]
 53%|█████▎    | 132/250 [03:39<03:32,  1.80s/it]
 53%|█████▎    | 133/250 [03:39<02:50,  1.46s/it]
 54%|█████▎    | 134/250 [03:40<02:22,  1.23s/it]
 54%|█████▍    | 135/250 [03:40<01:47,  1.07it/s]
 54%|█████▍    | 136/250 [03:41<01:20,  1.42it/s]
 55%|█████▍    | 137/250 [03:41<01:11,  1.58it/s]
 55%|█████▌    | 138/250 [03:43<01:52,  1.01s/it]
 56%|█████▌    | 139/250 [03:43<01:24,  1.32it/s]
 56%|█████▋    | 141/250 [03:43<00:48,  2.23it/s]
 57%|█████▋    | 142/250 [03:43<00:42,  2.56it/s]
 57%|█████▋    | 143/250 [03:45<01:02,  1.70it/s]
 58%|█████▊    | 144/250 [03:47<01:50,  1.04s/it]
 58%|█████▊    | 145/250 [03:47<01:30,  1.16it/s]
 58%|█████▊    | 146/250 [03:50<02:28,  1.43s/it]
 59%|█████▉    | 147/250 [03:50<01:51,  1.08s/it]
 59%|█████▉    | 148/250 [03:53<02:24,  1.42s/it]
 60%|█████▉    | 149/250 [03:57<04:06,  2.44s/it]
 60%|██████    | 150/250 [03:58<03:03,  1.84s/it]
 60%|██████    | 151/250 [03:58<02:13,  1.35s/it]
 61%|██████    | 152/250 [03:59<02:02,  1.25s/it]
 61%|██████    | 153/250 [04:01<02:14,  1.38s/it]
 62%|██████▏   | 154/250 [04:01<01:38,  1.02s/it]
 62%|██████▏   | 155/250 [04:02<01:39,  1.05s/it]
 62%|██████▏   | 156/250 [04:04<01:50,  1.17s/it]
 63%|██████▎   | 157/250 [04:05<01:53,  1.22s/it]
 63%|██████▎   | 158/250 [04:06<01:51,  1.21s/it]
 64%|██████▎   | 159/250 [04:08<02:10,  1.44s/it]
 64%|██████▍   | 160/250 [04:08<01:37,  1.08s/it]
 64%|██████▍   | 161/250 [04:11<02:30,  1.70s/it]
 65%|██████▍   | 162/250 [04:12<01:50,  1.26s/it]
 65%|██████▌   | 163/250 [04:12<01:23,  1.05it/s]
 66%|██████▌   | 164/250 [04:12<01:05,  1.31it/s]
 66%|██████▌   | 165/250 [04:14<01:40,  1.18s/it]
 66%|██████▋   | 166/250 [04:15<01:13,  1.14it/s]
 67%|██████▋   | 167/250 [04:15<00:55,  1.50it/s]
 67%|██████▋   | 168/250 [04:16<01:10,  1.17it/s]
 68%|██████▊   | 169/250 [04:17<01:24,  1.04s/it]
 68%|██████▊   | 170/250 [04:18<01:21,  1.01s/it]
 68%|██████▊   | 171/250 [04:20<01:28,  1.12s/it]
 69%|██████▉   | 172/250 [04:22<02:01,  1.56s/it]
 69%|██████▉   | 173/250 [04:24<01:59,  1.55s/it]
 70%|██████▉   | 174/250 [04:28<03:02,  2.40s/it]
 70%|███████   | 175/250 [04:29<02:20,  1.87s/it]
 70%|███████   | 176/250 [04:30<02:11,  1.78s/it]
 71%|███████   | 177/250 [04:32<02:13,  1.83s/it]
 71%|███████   | 178/250 [04:33<01:42,  1.42s/it]
 72%|███████▏  | 179/250 [04:36<02:21,  1.99s/it]
 72%|███████▏  | 180/250 [04:41<03:27,  2.97s/it]
 72%|███████▏  | 181/250 [04:42<02:26,  2.13s/it]
 73%|███████▎  | 182/250 [04:44<02:19,  2.06s/it]
 73%|███████▎  | 183/250 [04:45<01:59,  1.78s/it]
 74%|███████▎  | 184/250 [04:46<01:51,  1.69s/it]
 74%|███████▍  | 185/250 [04:50<02:25,  2.24s/it]
 74%|███████▍  | 186/250 [04:51<02:07,  1.99s/it]
 75%|███████▍  | 187/250 [04:52<01:49,  1.73s/it]
 75%|███████▌  | 188/250 [04:52<01:19,  1.28s/it]
 76%|███████▌  | 189/250 [04:53<01:00,  1.01it/s]
 76%|███████▌  | 190/250 [04:54<01:04,  1.08s/it]
 76%|███████▋  | 191/250 [04:55<00:54,  1.09it/s]
 77%|███████▋  | 192/250 [04:57<01:13,  1.27s/it]
 77%|███████▋  | 193/250 [04:59<01:22,  1.45s/it]
 78%|███████▊  | 194/250 [05:00<01:14,  1.33s/it]
 78%|███████▊  | 195/250 [05:00<00:59,  1.09s/it]
 78%|███████▊  | 196/250 [05:00<00:45,  1.20it/s]
 79%|███████▉  | 197/250 [05:01<00:41,  1.27it/s]
 79%|███████▉  | 198/250 [05:02<00:42,  1.23it/s]
 80%|███████▉  | 199/250 [05:03<00:43,  1.17it/s]
 80%|████████  | 200/250 [05:05<00:56,  1.13s/it]
 80%|████████  | 201/250 [05:05<00:43,  1.12it/s]
 81%|████████  | 202/250 [05:06<00:43,  1.11it/s]
 81%|████████  | 203/250 [05:07<00:50,  1.08s/it]
 82%|████████▏ | 204/250 [05:08<00:39,  1.16it/s]
 82%|████████▏ | 205/250 [05:09<00:42,  1.07it/s]
 82%|████████▏ | 206/250 [05:13<01:19,  1.80s/it]
 83%|████████▎ | 207/250 [05:14<01:13,  1.71s/it]
 83%|████████▎ | 208/250 [05:14<00:52,  1.26s/it]
 84%|████████▍ | 210/250 [05:16<00:44,  1.12s/it]
 84%|████████▍ | 211/250 [05:23<01:33,  2.41s/it]
 85%|████████▍ | 212/250 [05:23<01:11,  1.87s/it]
 85%|████████▌ | 213/250 [05:25<01:15,  2.04s/it]
 86%|████████▌ | 214/250 [05:27<01:07,  1.88s/it]
 86%|████████▌ | 215/250 [05:28<00:54,  1.55s/it]
 86%|████████▋ | 216/250 [05:28<00:40,  1.19s/it]
 87%|████████▋ | 217/250 [05:29<00:34,  1.04s/it]
 87%|████████▋ | 218/250 [05:30<00:39,  1.23s/it]
 88%|████████▊ | 219/250 [05:31<00:34,  1.11s/it]
 88%|████████▊ | 220/250 [05:36<01:05,  2.19s/it]
 88%|████████▊ | 221/250 [05:36<00:46,  1.60s/it]
 89%|████████▉ | 222/250 [05:38<00:47,  1.68s/it]
 89%|████████▉ | 223/250 [05:44<01:19,  2.95s/it]
 90%|████████▉ | 224/250 [05:45<01:02,  2.41s/it]
 90%|█████████ | 225/250 [05:46<00:51,  2.07s/it]
 90%|█████████ | 226/250 [05:47<00:38,  1.59s/it]
 91%|█████████ | 227/250 [05:47<00:26,  1.17s/it]
 92%|█████████▏| 229/250 [05:47<00:15,  1.37it/s]
 92%|█████████▏| 230/250 [05:48<00:14,  1.39it/s]
 93%|█████████▎| 232/250 [05:50<00:15,  1.16it/s]
 93%|█████████▎| 233/250 [05:51<00:13,  1.24it/s]
 94%|█████████▎| 234/250 [05:52<00:13,  1.23it/s]
 94%|█████████▍| 235/250 [05:53<00:15,  1.02s/it]
 94%|█████████▍| 236/250 [05:55<00:16,  1.16s/it]
 95%|█████████▍| 237/250 [05:55<00:12,  1.05it/s]
 95%|█████████▌| 238/250 [05:56<00:11,  1.08it/s]
 96%|█████████▌| 239/250 [05:58<00:12,  1.16s/it]
 96%|█████████▌| 240/250 [05:59<00:10,  1.08s/it]
 96%|█████████▋| 241/250 [05:59<00:08,  1.09it/s]
 97%|█████████▋| 242/250 [06:00<00:07,  1.14it/s]
 97%|█████████▋| 243/250 [06:01<00:05,  1.22it/s]
 98%|█████████▊| 244/250 [06:03<00:07,  1.17s/it]
 98%|█████████▊| 245/250 [06:04<00:05,  1.12s/it]
 98%|█████████▊| 246/250 [06:05<00:04,  1.09s/it]
 99%|█████████▉| 247/250 [06:06<00:03,  1.14s/it]
 99%|█████████▉| 248/250 [06:11<00:04,  2.27s/it]
100%|█████████▉| 249/250 [06:11<00:01,  1.73s/it]
100%|██████████| 250/250 [06:16<00:00,  2.65s/it]
100%|██████████| 250/250 [06:16<00:00,  1.51s/it]
2025-05-15 13:01:13,749 - modnet - INFO - Loss per individual: ind 0: 0.488 	ind 1: 0.517 	ind 2: 0.501 	ind 3: 0.494 	ind 4: 0.505 	ind 5: 0.509 	ind 6: 0.501 	ind 7: 0.486 	ind 8: 0.611 	ind 9: 0.466 	ind 10: 0.495 	ind 11: 0.496 	ind 12: 0.488 	ind 13: 0.487 	ind 14: 0.494 	ind 15: 0.508 	ind 16: 0.477 	ind 17: 0.573 	ind 18: 0.522 	ind 19: 0.487 	ind 20: 0.498 	ind 21: 0.496 	ind 22: 0.483 	ind 23: 0.490 	ind 24: 0.503 	ind 25: 0.522 	ind 26: 0.533 	ind 27: 0.441 	ind 28: 0.499 	ind 29: 0.463 	ind 30: 0.500 	ind 31: 0.525 	ind 32: 0.517 	ind 33: 0.487 	ind 34: 0.462 	ind 35: 0.598 	ind 36: 0.638 	ind 37: 0.555 	ind 38: 0.485 	ind 39: 0.478 	ind 40: 0.449 	ind 41: 0.515 	ind 42: 0.487 	ind 43: 0.465 	ind 44: 0.519 	ind 45: 0.459 	ind 46: 0.507 	ind 47: 0.492 	ind 48: 0.441 	ind 49: 0.489 	
2025-05-15 13:01:13,754 - modnet - INFO - Generation number 6

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:17<1:12:48, 17.54s/it]
  1%|          | 2/250 [00:19<35:48,  8.66s/it]  
  1%|          | 3/250 [00:20<20:29,  4.98s/it]
  2%|▏         | 4/250 [00:22<14:41,  3.58s/it]
  2%|▏         | 5/250 [00:22<10:26,  2.56s/it]
  2%|▏         | 6/250 [00:23<07:58,  1.96s/it]
  3%|▎         | 7/250 [00:24<06:16,  1.55s/it]
  3%|▎         | 8/250 [00:28<09:46,  2.42s/it]
  4%|▍         | 10/250 [00:28<05:17,  1.32s/it]
  4%|▍         | 11/250 [00:29<04:09,  1.05s/it]
  5%|▍         | 12/250 [00:30<04:42,  1.19s/it]
  5%|▌         | 13/250 [00:32<05:43,  1.45s/it]
  6%|▌         | 14/250 [00:34<05:58,  1.52s/it]
  6%|▌         | 15/250 [00:35<05:45,  1.47s/it]
  6%|▋         | 16/250 [00:37<05:59,  1.54s/it]
  7%|▋         | 17/250 [00:41<09:02,  2.33s/it]
  7%|▋         | 18/250 [00:46<11:32,  2.98s/it]
  8%|▊         | 19/250 [00:46<08:20,  2.17s/it]
  8%|▊         | 20/250 [00:48<08:02,  2.10s/it]
  8%|▊         | 21/250 [00:52<09:46,  2.56s/it]
  9%|▉         | 22/250 [00:53<08:28,  2.23s/it]
  9%|▉         | 23/250 [00:55<07:58,  2.11s/it]
 10%|▉         | 24/250 [00:55<06:12,  1.65s/it]
 10%|█         | 25/250 [00:58<07:08,  1.91s/it]
 10%|█         | 26/250 [00:58<05:13,  1.40s/it]
 11%|█         | 27/250 [00:59<04:10,  1.12s/it]
 11%|█         | 28/250 [01:01<05:26,  1.47s/it]
 12%|█▏        | 29/250 [01:02<04:50,  1.31s/it]
 12%|█▏        | 30/250 [01:02<04:01,  1.10s/it]
 12%|█▏        | 31/250 [01:03<03:56,  1.08s/it]
 13%|█▎        | 32/250 [01:04<02:59,  1.21it/s]
 14%|█▎        | 34/250 [01:06<03:12,  1.12it/s]
 14%|█▍        | 35/250 [01:08<04:36,  1.29s/it]
 14%|█▍        | 36/250 [01:10<05:05,  1.43s/it]
 15%|█▍        | 37/250 [01:14<07:47,  2.19s/it]
 15%|█▌        | 38/250 [01:16<07:40,  2.17s/it]
 16%|█▌        | 39/250 [01:19<07:38,  2.17s/it]
 16%|█▌        | 40/250 [01:20<07:01,  2.01s/it]
 17%|█▋        | 42/250 [01:23<05:50,  1.69s/it]
 17%|█▋        | 43/250 [01:24<05:06,  1.48s/it]
 18%|█▊        | 44/250 [01:26<05:35,  1.63s/it]
 18%|█▊        | 45/250 [01:30<07:53,  2.31s/it]
 18%|█▊        | 46/250 [01:30<06:05,  1.79s/it]
 19%|█▉        | 47/250 [01:34<07:34,  2.24s/it]
 19%|█▉        | 48/250 [01:35<06:41,  1.99s/it]
 20%|█▉        | 49/250 [01:36<05:17,  1.58s/it]
 20%|██        | 50/250 [01:39<07:23,  2.22s/it]
 20%|██        | 51/250 [01:41<07:00,  2.12s/it]
 21%|██        | 52/250 [01:42<05:50,  1.77s/it]
 21%|██        | 53/250 [01:43<04:41,  1.43s/it]
 22%|██▏       | 54/250 [01:43<03:26,  1.05s/it]
 22%|██▏       | 55/250 [01:44<03:14,  1.00it/s]
 22%|██▏       | 56/250 [01:45<03:17,  1.02s/it]
 23%|██▎       | 57/250 [01:46<03:24,  1.06s/it]
 23%|██▎       | 58/250 [01:47<03:29,  1.09s/it]
 24%|██▎       | 59/250 [01:48<03:40,  1.15s/it]
 24%|██▍       | 60/250 [01:49<03:28,  1.10s/it]
 24%|██▍       | 61/250 [01:50<02:42,  1.16it/s]
 25%|██▍       | 62/250 [01:50<02:02,  1.53it/s]
 25%|██▌       | 63/250 [01:50<01:40,  1.86it/s]
 26%|██▌       | 64/250 [01:51<01:36,  1.94it/s]
 26%|██▌       | 65/250 [01:52<02:26,  1.27it/s]
 26%|██▋       | 66/250 [01:52<01:50,  1.66it/s]
 27%|██▋       | 68/250 [01:53<01:34,  1.94it/s]
 28%|██▊       | 69/250 [02:02<07:41,  2.55s/it]
 28%|██▊       | 70/250 [02:02<06:02,  2.01s/it]
 28%|██▊       | 71/250 [02:03<05:00,  1.68s/it]
 29%|██▉       | 72/250 [02:04<04:18,  1.45s/it]
 29%|██▉       | 73/250 [02:05<03:55,  1.33s/it]
 30%|███       | 75/250 [02:08<04:05,  1.41s/it]
 30%|███       | 76/250 [02:08<03:21,  1.16s/it]
 31%|███       | 77/250 [02:09<02:47,  1.03it/s]
 31%|███       | 78/250 [02:09<02:11,  1.30it/s]
 32%|███▏      | 79/250 [02:11<03:25,  1.20s/it]
 32%|███▏      | 80/250 [02:12<02:37,  1.08it/s]
 32%|███▏      | 81/250 [02:13<03:17,  1.17s/it]
 33%|███▎      | 83/250 [02:13<01:53,  1.48it/s]
 34%|███▎      | 84/250 [02:16<03:11,  1.15s/it]
 34%|███▍      | 85/250 [02:17<02:47,  1.02s/it]
 34%|███▍      | 86/250 [02:19<03:39,  1.34s/it]
 35%|███▍      | 87/250 [02:22<04:39,  1.72s/it]
 35%|███▌      | 88/250 [02:24<05:28,  2.03s/it]
 36%|███▌      | 89/250 [02:25<04:35,  1.71s/it]
 36%|███▌      | 90/250 [02:31<07:38,  2.87s/it]
 36%|███▋      | 91/250 [02:34<07:28,  2.82s/it]
 37%|███▋      | 92/250 [02:34<05:45,  2.19s/it]
 37%|███▋      | 93/250 [02:35<04:36,  1.76s/it]
 38%|███▊      | 94/250 [02:38<05:03,  1.95s/it]
 38%|███▊      | 95/250 [02:39<04:23,  1.70s/it]
 38%|███▊      | 96/250 [02:41<04:32,  1.77s/it]
 39%|███▉      | 97/250 [02:42<04:12,  1.65s/it]
 39%|███▉      | 98/250 [02:43<03:31,  1.39s/it]
 40%|███▉      | 99/250 [02:44<03:13,  1.28s/it]
 40%|████      | 101/250 [02:49<04:25,  1.78s/it]
 41%|████      | 102/250 [02:49<03:26,  1.39s/it]
 41%|████      | 103/250 [02:50<03:00,  1.23s/it]
 42%|████▏     | 104/250 [02:51<02:58,  1.22s/it]
 42%|████▏     | 106/250 [02:53<02:54,  1.21s/it]
 43%|████▎     | 107/250 [02:55<03:16,  1.38s/it]
 43%|████▎     | 108/250 [02:57<03:32,  1.50s/it]
 44%|████▎     | 109/250 [02:58<03:19,  1.41s/it]
 44%|████▍     | 110/250 [03:03<05:33,  2.38s/it]
 44%|████▍     | 111/250 [03:04<04:36,  1.99s/it]
 45%|████▍     | 112/250 [03:04<03:21,  1.46s/it]
 45%|████▌     | 113/250 [03:05<02:56,  1.29s/it]
 46%|████▌     | 114/250 [03:07<03:39,  1.62s/it]
 46%|████▌     | 115/250 [03:09<03:19,  1.48s/it]
 46%|████▋     | 116/250 [03:12<04:16,  1.92s/it]
 47%|████▋     | 117/250 [03:12<03:04,  1.39s/it]
 47%|████▋     | 118/250 [03:12<02:19,  1.05s/it]
 48%|████▊     | 119/250 [03:16<04:29,  2.05s/it]
 48%|████▊     | 120/250 [03:18<04:13,  1.95s/it]
 48%|████▊     | 121/250 [03:19<03:45,  1.75s/it]
 49%|████▉     | 122/250 [03:19<02:41,  1.27s/it]
 49%|████▉     | 123/250 [03:23<04:05,  1.94s/it]
 50%|████▉     | 124/250 [03:24<03:43,  1.77s/it]
 50%|█████     | 125/250 [03:25<02:52,  1.38s/it]
 50%|█████     | 126/250 [03:33<06:52,  3.33s/it]
 51%|█████     | 127/250 [03:34<05:29,  2.68s/it]
 51%|█████     | 128/250 [03:34<04:10,  2.05s/it]
 52%|█████▏    | 129/250 [03:37<04:11,  2.08s/it]
 52%|█████▏    | 130/250 [03:37<03:00,  1.50s/it]
 52%|█████▏    | 131/250 [03:40<04:15,  2.15s/it]
 53%|█████▎    | 132/250 [03:42<03:46,  1.92s/it]
 53%|█████▎    | 133/250 [03:45<04:43,  2.43s/it]
 54%|█████▎    | 134/250 [03:46<03:53,  2.01s/it]
 54%|█████▍    | 135/250 [03:47<03:13,  1.68s/it]
 54%|█████▍    | 136/250 [03:48<02:31,  1.33s/it]
 55%|█████▍    | 137/250 [03:48<02:07,  1.13s/it]
 55%|█████▌    | 138/250 [03:51<02:51,  1.53s/it]
 56%|█████▌    | 139/250 [03:54<03:24,  1.84s/it]
 56%|█████▌    | 140/250 [03:54<02:32,  1.38s/it]
 56%|█████▋    | 141/250 [03:55<02:16,  1.25s/it]
 57%|█████▋    | 142/250 [03:55<01:45,  1.02it/s]
 57%|█████▋    | 143/250 [03:55<01:21,  1.31it/s]
 58%|█████▊    | 144/250 [03:58<02:14,  1.27s/it]
 58%|█████▊    | 145/250 [03:59<01:56,  1.11s/it]
 59%|█████▉    | 147/250 [04:01<01:48,  1.05s/it]
 59%|█████▉    | 148/250 [04:04<02:45,  1.63s/it]
 60%|█████▉    | 149/250 [04:05<02:28,  1.47s/it]
 60%|██████    | 150/250 [04:06<02:07,  1.28s/it]
 61%|██████    | 152/250 [04:07<01:35,  1.03it/s]
 61%|██████    | 153/250 [04:15<04:12,  2.60s/it]
 62%|██████▏   | 154/250 [04:16<03:39,  2.29s/it]
 62%|██████▏   | 155/250 [04:17<03:14,  2.04s/it]
 62%|██████▏   | 156/250 [04:18<02:47,  1.78s/it]
 63%|██████▎   | 157/250 [04:21<03:03,  1.98s/it]
 63%|██████▎   | 158/250 [04:23<02:59,  1.95s/it]
 64%|██████▎   | 159/250 [04:24<02:33,  1.68s/it]
 64%|██████▍   | 160/250 [04:26<02:51,  1.91s/it]
 64%|██████▍   | 161/250 [04:27<02:16,  1.53s/it]
 65%|██████▍   | 162/250 [04:28<02:07,  1.45s/it]
 65%|██████▌   | 163/250 [04:29<01:56,  1.33s/it]
 66%|██████▌   | 164/250 [04:36<04:14,  2.96s/it]
 66%|██████▌   | 165/250 [04:37<03:16,  2.31s/it]
 66%|██████▋   | 166/250 [04:39<03:10,  2.27s/it]
 67%|██████▋   | 167/250 [04:40<02:47,  2.01s/it]
 67%|██████▋   | 168/250 [04:41<02:07,  1.55s/it]
 68%|██████▊   | 169/250 [04:44<02:48,  2.08s/it]
 68%|██████▊   | 170/250 [04:45<02:19,  1.74s/it]
 68%|██████▊   | 171/250 [04:46<02:04,  1.57s/it]
 69%|██████▉   | 172/250 [04:48<02:04,  1.60s/it]
 69%|██████▉   | 173/250 [04:51<02:35,  2.03s/it]
 70%|██████▉   | 174/250 [04:52<02:10,  1.72s/it]
 70%|███████   | 175/250 [04:53<01:50,  1.47s/it]
 70%|███████   | 176/250 [04:54<01:37,  1.32s/it]
 71%|███████   | 177/250 [05:01<03:51,  3.18s/it]
 71%|███████   | 178/250 [05:02<02:55,  2.43s/it]
 72%|███████▏  | 179/250 [05:03<02:16,  1.93s/it]
 72%|███████▏  | 180/250 [05:04<01:52,  1.61s/it]
 72%|███████▏  | 181/250 [05:05<01:37,  1.41s/it]
 73%|███████▎  | 182/250 [05:05<01:20,  1.18s/it]
 73%|███████▎  | 183/250 [05:08<01:42,  1.54s/it]
 74%|███████▎  | 184/250 [05:09<01:37,  1.48s/it]
 74%|███████▍  | 185/250 [05:12<01:58,  1.82s/it]
 74%|███████▍  | 186/250 [05:12<01:24,  1.32s/it]
 75%|███████▍  | 187/250 [05:12<01:03,  1.01s/it]
 75%|███████▌  | 188/250 [05:14<01:13,  1.18s/it]
 76%|███████▌  | 189/250 [05:14<00:52,  1.15it/s]
 76%|███████▌  | 190/250 [05:15<00:59,  1.00it/s]
 76%|███████▋  | 191/250 [05:15<00:44,  1.32it/s]
 77%|███████▋  | 192/250 [05:16<00:37,  1.55it/s]
 77%|███████▋  | 193/250 [05:19<01:17,  1.36s/it]
 78%|███████▊  | 194/250 [05:19<01:06,  1.18s/it]
 78%|███████▊  | 195/250 [05:20<00:56,  1.02s/it]
 78%|███████▊  | 196/250 [05:21<00:59,  1.11s/it]
 79%|███████▉  | 197/250 [05:27<02:13,  2.52s/it]
 79%|███████▉  | 198/250 [05:31<02:37,  3.03s/it]
 80%|███████▉  | 199/250 [05:32<01:54,  2.24s/it]
 80%|████████  | 200/250 [05:32<01:24,  1.69s/it]
 80%|████████  | 201/250 [05:35<01:39,  2.02s/it]
 81%|████████  | 202/250 [05:37<01:41,  2.11s/it]
 81%|████████  | 203/250 [05:39<01:32,  1.96s/it]
 82%|████████▏ | 204/250 [05:41<01:31,  2.00s/it]
 82%|████████▏ | 205/250 [05:44<01:47,  2.38s/it]
 82%|████████▏ | 206/250 [05:45<01:27,  1.98s/it]
 83%|████████▎ | 207/250 [05:47<01:18,  1.84s/it]
 83%|████████▎ | 208/250 [05:49<01:14,  1.79s/it]
 84%|████████▍ | 210/250 [05:50<00:53,  1.33s/it]
 84%|████████▍ | 211/250 [05:51<00:49,  1.27s/it]
 85%|████████▍ | 212/250 [05:53<00:54,  1.43s/it]
 85%|████████▌ | 213/250 [05:53<00:41,  1.11s/it]
 86%|████████▌ | 214/250 [05:54<00:36,  1.00s/it]
 86%|████████▌ | 215/250 [05:55<00:35,  1.02s/it]
 86%|████████▋ | 216/250 [05:56<00:33,  1.01it/s]
 87%|████████▋ | 217/250 [05:58<00:41,  1.25s/it]
 87%|████████▋ | 218/250 [05:59<00:37,  1.16s/it]
 88%|████████▊ | 219/250 [06:00<00:38,  1.25s/it]
 88%|████████▊ | 220/250 [06:01<00:35,  1.17s/it]
 88%|████████▊ | 221/250 [06:04<00:49,  1.71s/it]
 89%|████████▉ | 222/250 [06:04<00:34,  1.24s/it]
 89%|████████▉ | 223/250 [06:05<00:29,  1.08s/it]
 90%|████████▉ | 224/250 [06:06<00:29,  1.15s/it]
 90%|█████████ | 225/250 [06:07<00:21,  1.14it/s]
 90%|█████████ | 226/250 [06:08<00:20,  1.16it/s]
 91%|█████████ | 227/250 [06:09<00:25,  1.13s/it]
 91%|█████████ | 228/250 [06:11<00:26,  1.20s/it]
 92%|█████████▏| 229/250 [06:12<00:22,  1.09s/it]
 92%|█████████▏| 230/250 [06:13<00:22,  1.12s/it]
 92%|█████████▏| 231/250 [06:13<00:17,  1.11it/s]
 93%|█████████▎| 232/250 [06:13<00:12,  1.42it/s]
 94%|█████████▎| 234/250 [06:15<00:13,  1.19it/s]
 94%|█████████▍| 235/250 [06:18<00:17,  1.18s/it]
 94%|█████████▍| 236/250 [06:18<00:14,  1.03s/it]
 95%|█████████▍| 237/250 [06:19<00:11,  1.11it/s]
 96%|█████████▌| 239/250 [06:19<00:07,  1.47it/s]
 96%|█████████▌| 240/250 [06:20<00:06,  1.50it/s]
 96%|█████████▋| 241/250 [06:28<00:22,  2.50s/it]
 97%|█████████▋| 242/250 [06:29<00:15,  2.00s/it]
 97%|█████████▋| 243/250 [06:29<00:10,  1.49s/it]
 98%|█████████▊| 244/250 [06:30<00:08,  1.41s/it]
 98%|█████████▊| 245/250 [06:32<00:07,  1.45s/it]
 98%|█████████▊| 246/250 [06:37<00:10,  2.52s/it]
 99%|█████████▉| 247/250 [06:41<00:09,  3.07s/it]
 99%|█████████▉| 248/250 [06:44<00:06,  3.14s/it]
100%|██████████| 250/250 [06:46<00:00,  1.99s/it]
100%|██████████| 250/250 [06:46<00:00,  1.62s/it]
2025-05-15 13:07:59,955 - modnet - INFO - Loss per individual: ind 0: 0.506 	ind 1: 0.475 	ind 2: 0.492 	ind 3: 0.456 	ind 4: 0.507 	ind 5: 0.573 	ind 6: 0.495 	ind 7: 0.492 	ind 8: 0.612 	ind 9: 0.542 	ind 10: 0.645 	ind 11: 0.484 	ind 12: 0.509 	ind 13: 0.474 	ind 14: 0.516 	ind 15: 0.500 	ind 16: 0.497 	ind 17: 0.465 	ind 18: 0.501 	ind 19: 0.497 	ind 20: 0.512 	ind 21: 0.496 	ind 22: 0.433 	ind 23: 0.509 	ind 24: 0.495 	ind 25: 0.495 	ind 26: 0.510 	ind 27: 0.511 	ind 28: 0.487 	ind 29: 0.490 	ind 30: 0.532 	ind 31: 0.490 	ind 32: 0.493 	ind 33: 0.502 	ind 34: 0.480 	ind 35: 0.484 	ind 36: 0.484 	ind 37: 0.539 	ind 38: 0.510 	ind 39: 0.472 	ind 40: 0.493 	ind 41: 0.479 	ind 42: 0.508 	ind 43: 0.445 	ind 44: 0.483 	ind 45: 0.494 	ind 46: 0.494 	ind 47: 0.523 	ind 48: 0.500 	ind 49: 0.493 	
2025-05-15 13:07:59,958 - modnet - INFO - Generation number 7

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:17<1:13:26, 17.70s/it]
  1%|          | 2/250 [00:24<45:37, 11.04s/it]  
  1%|          | 3/250 [00:24<25:24,  6.17s/it]
  2%|▏         | 4/250 [00:26<19:07,  4.67s/it]
  2%|▏         | 5/250 [00:28<14:22,  3.52s/it]
  2%|▏         | 6/250 [00:28<09:53,  2.43s/it]
  3%|▎         | 7/250 [00:30<09:00,  2.22s/it]
  3%|▎         | 8/250 [00:30<06:18,  1.57s/it]
  4%|▍         | 10/250 [00:33<05:39,  1.41s/it]
  4%|▍         | 11/250 [00:33<05:08,  1.29s/it]
  5%|▍         | 12/250 [00:39<09:57,  2.51s/it]
  5%|▌         | 13/250 [00:48<16:47,  4.25s/it]
  6%|▌         | 14/250 [00:50<14:31,  3.69s/it]
  6%|▌         | 15/250 [00:51<11:25,  2.92s/it]
  6%|▋         | 16/250 [00:52<08:16,  2.12s/it]
  7%|▋         | 17/250 [00:54<08:36,  2.22s/it]
  7%|▋         | 18/250 [00:54<06:15,  1.62s/it]
  8%|▊         | 19/250 [00:55<04:42,  1.22s/it]
  8%|▊         | 20/250 [00:55<03:44,  1.02it/s]
  8%|▊         | 21/250 [00:55<02:47,  1.37it/s]
  9%|▉         | 22/250 [00:57<04:36,  1.21s/it]
  9%|▉         | 23/250 [00:59<04:23,  1.16s/it]
 10%|▉         | 24/250 [01:00<04:45,  1.26s/it]
 10%|█         | 25/250 [01:00<03:32,  1.06it/s]
 10%|█         | 26/250 [01:03<05:11,  1.39s/it]
 11%|█         | 27/250 [01:05<06:10,  1.66s/it]
 11%|█         | 28/250 [01:06<05:01,  1.36s/it]
 12%|█▏        | 29/250 [01:08<05:38,  1.53s/it]
 12%|█▏        | 30/250 [01:08<04:29,  1.23s/it]
 12%|█▏        | 31/250 [01:09<04:00,  1.10s/it]
 13%|█▎        | 32/250 [01:09<03:04,  1.18it/s]
 13%|█▎        | 33/250 [01:13<06:52,  1.90s/it]
 14%|█▎        | 34/250 [01:16<07:08,  1.98s/it]
 14%|█▍        | 35/250 [01:20<09:30,  2.65s/it]
 14%|█▍        | 36/250 [01:21<07:43,  2.17s/it]
 15%|█▍        | 37/250 [01:29<14:02,  3.96s/it]
 15%|█▌        | 38/250 [01:31<11:34,  3.28s/it]
 16%|█▌        | 39/250 [01:31<08:32,  2.43s/it]
 16%|█▌        | 40/250 [01:32<06:31,  1.86s/it]
 16%|█▋        | 41/250 [01:33<05:33,  1.59s/it]
 17%|█▋        | 42/250 [01:35<06:09,  1.78s/it]
 17%|█▋        | 43/250 [01:37<06:40,  1.93s/it]
 18%|█▊        | 44/250 [01:37<04:46,  1.39s/it]
 18%|█▊        | 45/250 [01:43<08:56,  2.62s/it]
 18%|█▊        | 46/250 [01:45<08:19,  2.45s/it]
 19%|█▉        | 47/250 [01:47<07:31,  2.22s/it]
 19%|█▉        | 48/250 [01:51<09:35,  2.85s/it]
 20%|█▉        | 49/250 [01:51<06:56,  2.07s/it]
 20%|██        | 50/250 [01:51<04:59,  1.50s/it]
 20%|██        | 51/250 [01:52<03:52,  1.17s/it]
 21%|██        | 52/250 [01:53<04:10,  1.26s/it]
 21%|██        | 53/250 [01:56<05:56,  1.81s/it]
 22%|██▏       | 54/250 [01:57<05:02,  1.54s/it]
 22%|██▏       | 55/250 [01:59<05:08,  1.58s/it]
 22%|██▏       | 56/250 [02:01<06:02,  1.87s/it]
 23%|██▎       | 57/250 [02:02<05:12,  1.62s/it]
 23%|██▎       | 58/250 [02:03<04:41,  1.47s/it]
 24%|██▎       | 59/250 [02:04<04:08,  1.30s/it]
 24%|██▍       | 60/250 [02:05<03:19,  1.05s/it]
 24%|██▍       | 61/250 [02:06<03:03,  1.03it/s]
 25%|██▍       | 62/250 [02:07<03:01,  1.04it/s]
 25%|██▌       | 63/250 [02:09<04:10,  1.34s/it]
 26%|██▌       | 64/250 [02:09<03:18,  1.07s/it]
 26%|██▌       | 65/250 [02:11<03:53,  1.26s/it]
 26%|██▋       | 66/250 [02:11<02:55,  1.05it/s]
 27%|██▋       | 68/250 [02:12<02:07,  1.43it/s]
 28%|██▊       | 69/250 [02:14<02:42,  1.11it/s]
 28%|██▊       | 70/250 [02:14<02:20,  1.28it/s]
 28%|██▊       | 71/250 [02:17<04:06,  1.38s/it]
 29%|██▉       | 72/250 [02:18<03:34,  1.20s/it]
 29%|██▉       | 73/250 [02:22<05:59,  2.03s/it]
 30%|██▉       | 74/250 [02:24<05:53,  2.01s/it]
 30%|███       | 75/250 [02:25<05:21,  1.84s/it]
 30%|███       | 76/250 [02:26<04:48,  1.66s/it]
 31%|███       | 77/250 [02:33<09:18,  3.23s/it]
 31%|███       | 78/250 [02:35<08:18,  2.90s/it]
 32%|███▏      | 79/250 [02:37<06:40,  2.34s/it]
 32%|███▏      | 80/250 [02:40<07:29,  2.64s/it]
 32%|███▏      | 81/250 [02:40<05:23,  1.91s/it]
 33%|███▎      | 82/250 [02:41<04:35,  1.64s/it]
 33%|███▎      | 83/250 [02:45<06:32,  2.35s/it]
 34%|███▎      | 84/250 [02:46<04:58,  1.80s/it]
 34%|███▍      | 85/250 [02:46<03:50,  1.40s/it]
 34%|███▍      | 86/250 [02:46<02:51,  1.05s/it]
 35%|███▍      | 87/250 [02:49<04:11,  1.54s/it]
 35%|███▌      | 88/250 [02:50<03:28,  1.29s/it]
 36%|███▌      | 89/250 [02:52<03:55,  1.46s/it]
 36%|███▌      | 90/250 [02:53<04:08,  1.55s/it]
 36%|███▋      | 91/250 [02:53<02:59,  1.13s/it]
 37%|███▋      | 92/250 [02:56<03:43,  1.42s/it]
 37%|███▋      | 93/250 [02:57<03:22,  1.29s/it]
 38%|███▊      | 94/250 [02:58<03:39,  1.41s/it]
 38%|███▊      | 95/250 [03:01<04:37,  1.79s/it]
 38%|███▊      | 96/250 [03:02<03:46,  1.47s/it]
 39%|███▉      | 97/250 [03:02<02:48,  1.10s/it]
 39%|███▉      | 98/250 [03:04<03:15,  1.29s/it]
 40%|███▉      | 99/250 [03:05<03:42,  1.47s/it]
 40%|████      | 100/250 [03:09<05:13,  2.09s/it]
 40%|████      | 101/250 [03:11<04:47,  1.93s/it]
 41%|████      | 102/250 [03:11<03:38,  1.47s/it]
 41%|████      | 103/250 [03:12<03:08,  1.28s/it]
 42%|████▏     | 104/250 [03:12<02:39,  1.09s/it]
 42%|████▏     | 105/250 [03:13<01:57,  1.23it/s]
 42%|████▏     | 106/250 [03:14<02:01,  1.19it/s]
 43%|████▎     | 107/250 [03:15<02:35,  1.09s/it]
 43%|████▎     | 108/250 [03:17<03:14,  1.37s/it]
 44%|████▎     | 109/250 [03:19<03:13,  1.37s/it]
 44%|████▍     | 110/250 [03:21<03:42,  1.59s/it]
 44%|████▍     | 111/250 [03:22<03:32,  1.53s/it]
 45%|████▍     | 112/250 [03:24<03:32,  1.54s/it]
 45%|████▌     | 113/250 [03:24<02:49,  1.24s/it]
 46%|████▌     | 114/250 [03:24<02:04,  1.09it/s]
 46%|████▌     | 115/250 [03:25<01:36,  1.40it/s]
 46%|████▋     | 116/250 [03:25<01:11,  1.87it/s]
 47%|████▋     | 117/250 [03:27<02:17,  1.03s/it]
 47%|████▋     | 118/250 [03:28<02:03,  1.07it/s]
 48%|████▊     | 119/250 [03:29<02:10,  1.00it/s]
 48%|████▊     | 120/250 [03:30<02:24,  1.11s/it]
 49%|████▉     | 122/250 [03:32<02:02,  1.04it/s]
 49%|████▉     | 123/250 [03:34<02:44,  1.29s/it]
 50%|████▉     | 124/250 [03:34<02:10,  1.04s/it]
 50%|█████     | 125/250 [03:37<03:14,  1.55s/it]
 50%|█████     | 126/250 [03:39<03:02,  1.47s/it]
 51%|█████     | 127/250 [03:40<02:59,  1.46s/it]
 51%|█████     | 128/250 [03:42<03:02,  1.50s/it]
 52%|█████▏    | 129/250 [03:42<02:13,  1.11s/it]
 52%|█████▏    | 130/250 [03:43<02:07,  1.06s/it]
 52%|█████▏    | 131/250 [03:44<02:03,  1.04s/it]
 53%|█████▎    | 132/250 [03:45<02:20,  1.19s/it]
 53%|█████▎    | 133/250 [03:46<01:54,  1.02it/s]
 54%|█████▎    | 134/250 [03:50<03:44,  1.93s/it]
 54%|█████▍    | 135/250 [03:50<02:57,  1.54s/it]
 54%|█████▍    | 136/250 [03:53<03:33,  1.87s/it]
 55%|█████▍    | 137/250 [03:54<02:43,  1.44s/it]
 55%|█████▌    | 138/250 [03:55<02:29,  1.34s/it]
 56%|█████▌    | 139/250 [03:55<01:51,  1.01s/it]
 56%|█████▋    | 141/250 [03:55<01:13,  1.49it/s]
 57%|█████▋    | 142/250 [03:56<01:04,  1.68it/s]
 57%|█████▋    | 143/250 [03:56<00:52,  2.02it/s]
 58%|█████▊    | 144/250 [04:05<04:55,  2.79s/it]
 58%|█████▊    | 145/250 [04:05<03:44,  2.14s/it]
 58%|█████▊    | 146/250 [04:06<02:49,  1.63s/it]
 59%|█████▉    | 147/250 [04:08<03:16,  1.91s/it]
 59%|█████▉    | 148/250 [04:09<02:49,  1.66s/it]
 60%|█████▉    | 149/250 [04:10<02:21,  1.40s/it]
 60%|██████    | 150/250 [04:12<02:42,  1.63s/it]
 60%|██████    | 151/250 [04:15<03:15,  1.97s/it]
 61%|██████    | 152/250 [04:16<02:49,  1.73s/it]
 61%|██████    | 153/250 [04:17<02:10,  1.34s/it]
 62%|██████▏   | 154/250 [04:19<02:40,  1.67s/it]
 62%|██████▏   | 155/250 [04:20<02:22,  1.50s/it]
 62%|██████▏   | 156/250 [04:23<03:02,  1.95s/it]
 63%|██████▎   | 157/250 [04:25<02:56,  1.90s/it]
 63%|██████▎   | 158/250 [04:26<02:18,  1.50s/it]
 64%|██████▎   | 159/250 [04:27<02:25,  1.60s/it]
 64%|██████▍   | 160/250 [04:28<01:55,  1.29s/it]
 64%|██████▍   | 161/250 [04:30<01:59,  1.35s/it]
 65%|██████▍   | 162/250 [04:30<01:27,  1.00it/s]
 65%|██████▌   | 163/250 [04:31<01:25,  1.02it/s]
 66%|██████▌   | 164/250 [04:32<01:23,  1.03it/s]
 66%|██████▌   | 165/250 [04:32<01:12,  1.18it/s]
 66%|██████▋   | 166/250 [04:36<02:21,  1.68s/it]
 67%|██████▋   | 167/250 [04:40<03:16,  2.37s/it]
 67%|██████▋   | 168/250 [04:44<04:12,  3.08s/it]
 68%|██████▊   | 169/250 [04:50<05:07,  3.80s/it]
 68%|██████▊   | 170/250 [04:50<03:38,  2.73s/it]
 68%|██████▊   | 171/250 [04:51<02:44,  2.09s/it]
 69%|██████▉   | 172/250 [04:53<02:53,  2.23s/it]
 69%|██████▉   | 173/250 [04:54<02:05,  1.62s/it]
 70%|██████▉   | 174/250 [04:54<01:44,  1.37s/it]
 70%|███████   | 175/250 [04:57<02:01,  1.62s/it]
 70%|███████   | 176/250 [04:57<01:27,  1.18s/it]
 71%|███████   | 177/250 [04:57<01:03,  1.15it/s]
 71%|███████   | 178/250 [04:57<00:47,  1.51it/s]
 72%|███████▏  | 179/250 [04:59<01:25,  1.21s/it]
 72%|███████▏  | 180/250 [05:00<01:02,  1.12it/s]
 72%|███████▏  | 181/250 [05:00<00:47,  1.45it/s]
 73%|███████▎  | 182/250 [05:00<00:39,  1.72it/s]
 73%|███████▎  | 183/250 [05:00<00:31,  2.16it/s]
 74%|███████▎  | 184/250 [05:03<01:12,  1.10s/it]
 74%|███████▍  | 185/250 [05:04<01:01,  1.06it/s]
 74%|███████▍  | 186/250 [05:04<00:58,  1.10it/s]
 75%|███████▍  | 187/250 [05:07<01:25,  1.35s/it]
 75%|███████▌  | 188/250 [05:08<01:28,  1.43s/it]
 76%|███████▌  | 189/250 [05:19<04:21,  4.29s/it]
 76%|███████▌  | 190/250 [05:22<03:52,  3.87s/it]
 76%|███████▋  | 191/250 [05:23<02:59,  3.04s/it]
 77%|███████▋  | 192/250 [05:24<02:07,  2.19s/it]
 77%|███████▋  | 193/250 [05:25<02:00,  2.12s/it]
 78%|███████▊  | 194/250 [05:28<01:56,  2.09s/it]
 78%|███████▊  | 195/250 [05:28<01:23,  1.52s/it]
 78%|███████▊  | 196/250 [05:28<01:00,  1.12s/it]
 79%|███████▉  | 197/250 [05:29<00:59,  1.12s/it]
 79%|███████▉  | 198/250 [05:30<01:02,  1.20s/it]
 80%|███████▉  | 199/250 [05:31<00:59,  1.17s/it]
 80%|████████  | 200/250 [05:32<00:49,  1.02it/s]
 80%|████████  | 201/250 [05:33<00:45,  1.09it/s]
 81%|████████  | 202/250 [05:38<01:47,  2.24s/it]
 81%|████████  | 203/250 [05:44<02:41,  3.43s/it]
 82%|████████▏ | 204/250 [05:46<02:07,  2.78s/it]
 82%|████████▏ | 205/250 [05:46<01:29,  1.98s/it]
 82%|████████▏ | 206/250 [05:46<01:04,  1.46s/it]
 83%|████████▎ | 207/250 [05:46<00:49,  1.15s/it]
 83%|████████▎ | 208/250 [05:47<00:42,  1.02s/it]
 84%|████████▎ | 209/250 [05:48<00:36,  1.12it/s]
 84%|████████▍ | 210/250 [05:49<00:36,  1.09it/s]
 84%|████████▍ | 211/250 [05:49<00:30,  1.29it/s]
 85%|████████▍ | 212/250 [05:50<00:31,  1.20it/s]
 85%|████████▌ | 213/250 [05:51<00:29,  1.25it/s]
 86%|████████▌ | 215/250 [05:53<00:35,  1.00s/it]
 86%|████████▋ | 216/250 [05:54<00:35,  1.04s/it]
 87%|████████▋ | 217/250 [05:55<00:34,  1.03s/it]
 87%|████████▋ | 218/250 [05:57<00:38,  1.21s/it]
 88%|████████▊ | 219/250 [05:58<00:34,  1.11s/it]
 88%|████████▊ | 220/250 [05:59<00:34,  1.17s/it]
 88%|████████▊ | 221/250 [06:01<00:38,  1.33s/it]
 89%|████████▉ | 222/250 [06:02<00:32,  1.15s/it]
 89%|████████▉ | 223/250 [06:04<00:36,  1.35s/it]
 90%|████████▉ | 224/250 [06:05<00:34,  1.32s/it]
 90%|█████████ | 225/250 [06:07<00:35,  1.43s/it]
 90%|█████████ | 226/250 [06:08<00:33,  1.41s/it]
 91%|█████████ | 227/250 [06:08<00:24,  1.05s/it]
 91%|█████████ | 228/250 [06:09<00:23,  1.05s/it]
 92%|█████████▏| 229/250 [06:10<00:23,  1.11s/it]
 92%|█████████▏| 230/250 [06:11<00:18,  1.06it/s]
 92%|█████████▏| 231/250 [06:11<00:14,  1.36it/s]
 93%|█████████▎| 232/250 [06:13<00:18,  1.03s/it]
 93%|█████████▎| 233/250 [06:15<00:23,  1.38s/it]
 94%|█████████▎| 234/250 [06:16<00:17,  1.12s/it]
 94%|█████████▍| 235/250 [06:16<00:14,  1.05it/s]
 94%|█████████▍| 236/250 [06:18<00:15,  1.09s/it]
 95%|█████████▍| 237/250 [06:20<00:17,  1.38s/it]
 95%|█████████▌| 238/250 [06:20<00:12,  1.01s/it]
 96%|█████████▌| 239/250 [06:20<00:08,  1.23it/s]
 96%|█████████▌| 240/250 [06:21<00:07,  1.35it/s]
 96%|█████████▋| 241/250 [06:21<00:05,  1.63it/s]
 97%|█████████▋| 242/250 [06:23<00:08,  1.06s/it]
 97%|█████████▋| 243/250 [06:24<00:06,  1.11it/s]
 98%|█████████▊| 244/250 [06:24<00:04,  1.25it/s]
 98%|█████████▊| 245/250 [06:26<00:05,  1.04s/it]
 98%|█████████▊| 246/250 [06:29<00:06,  1.71s/it]
 99%|█████████▉| 247/250 [06:30<00:04,  1.38s/it]
 99%|█████████▉| 248/250 [06:38<00:06,  3.44s/it]
100%|█████████▉| 249/250 [06:47<00:05,  5.11s/it]
100%|██████████| 250/250 [06:53<00:00,  5.41s/it]
100%|██████████| 250/250 [06:53<00:00,  1.65s/it]
2025-05-15 13:14:53,598 - modnet - INFO - Loss per individual: ind 0: 0.448 	ind 1: 0.492 	ind 2: 0.483 	ind 3: 0.474 	ind 4: 0.481 	ind 5: 0.490 	ind 6: 0.472 	ind 7: 0.488 	ind 8: 0.554 	ind 9: 0.481 	ind 10: 0.519 	ind 11: 0.512 	ind 12: 0.482 	ind 13: 0.506 	ind 14: 0.506 	ind 15: 0.496 	ind 16: 0.490 	ind 17: 0.487 	ind 18: 0.488 	ind 19: 0.477 	ind 20: 0.476 	ind 21: 0.504 	ind 22: 0.525 	ind 23: 0.521 	ind 24: 0.505 	ind 25: 0.488 	ind 26: 0.500 	ind 27: 0.484 	ind 28: 0.445 	ind 29: 0.473 	ind 30: 0.488 	ind 31: 0.505 	ind 32: 0.528 	ind 33: 0.492 	ind 34: 0.493 	ind 35: 0.560 	ind 36: 0.529 	ind 37: 0.480 	ind 38: 0.479 	ind 39: 0.485 	ind 40: 0.473 	ind 41: 0.535 	ind 42: 0.497 	ind 43: 0.485 	ind 44: 0.473 	ind 45: 0.489 	ind 46: 0.523 	ind 47: 0.488 	ind 48: 0.476 	ind 49: 0.508 	
2025-05-15 13:14:53,600 - modnet - INFO - Generation number 8

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:21<1:29:10, 21.49s/it]
  1%|          | 2/250 [00:21<37:13,  9.01s/it]  
  1%|          | 3/250 [00:27<31:11,  7.58s/it]
  2%|▏         | 4/250 [00:30<23:07,  5.64s/it]
  2%|▏         | 5/250 [00:30<15:02,  3.68s/it]
  2%|▏         | 6/250 [00:33<14:16,  3.51s/it]
  3%|▎         | 7/250 [00:33<09:58,  2.46s/it]
  3%|▎         | 8/250 [00:34<07:10,  1.78s/it]
  4%|▎         | 9/250 [00:34<05:32,  1.38s/it]
  4%|▍         | 10/250 [00:35<04:31,  1.13s/it]
  4%|▍         | 11/250 [00:36<04:04,  1.03s/it]
  5%|▍         | 12/250 [00:37<04:10,  1.05s/it]
  5%|▌         | 13/250 [00:38<03:56,  1.00it/s]
  6%|▌         | 14/250 [00:38<03:05,  1.27it/s]
  6%|▌         | 15/250 [00:39<02:47,  1.40it/s]
  6%|▋         | 16/250 [00:41<05:12,  1.33s/it]
  7%|▋         | 17/250 [00:42<03:53,  1.00s/it]
  7%|▋         | 18/250 [00:44<05:06,  1.32s/it]
  8%|▊         | 19/250 [00:44<04:04,  1.06s/it]
  8%|▊         | 20/250 [00:45<03:51,  1.01s/it]
  8%|▊         | 21/250 [00:48<06:47,  1.78s/it]
  9%|▉         | 22/250 [00:50<05:54,  1.56s/it]
  9%|▉         | 23/250 [00:50<05:04,  1.34s/it]
 10%|▉         | 24/250 [00:54<07:23,  1.96s/it]
 10%|█         | 25/250 [01:03<15:22,  4.10s/it]
 10%|█         | 26/250 [01:03<11:18,  3.03s/it]
 11%|█         | 27/250 [01:06<10:34,  2.84s/it]
 11%|█         | 28/250 [01:10<12:12,  3.30s/it]
 12%|█▏        | 29/250 [01:12<10:37,  2.89s/it]
 12%|█▏        | 30/250 [01:15<10:07,  2.76s/it]
 12%|█▏        | 31/250 [01:15<07:19,  2.01s/it]
 13%|█▎        | 32/250 [01:15<05:17,  1.46s/it]
 13%|█▎        | 33/250 [01:15<03:59,  1.10s/it]
 14%|█▎        | 34/250 [01:22<10:35,  2.94s/it]
 14%|█▍        | 35/250 [01:23<07:37,  2.13s/it]
 14%|█▍        | 36/250 [01:24<06:23,  1.79s/it]
 15%|█▍        | 37/250 [01:24<04:38,  1.31s/it]
 15%|█▌        | 38/250 [01:25<04:20,  1.23s/it]
 16%|█▌        | 39/250 [01:28<05:47,  1.65s/it]
 16%|█▌        | 40/250 [01:28<04:11,  1.20s/it]
 16%|█▋        | 41/250 [01:31<06:27,  1.86s/it]
 17%|█▋        | 42/250 [01:34<07:17,  2.10s/it]
 17%|█▋        | 43/250 [01:34<05:26,  1.58s/it]
 18%|█▊        | 44/250 [01:34<04:05,  1.19s/it]
 18%|█▊        | 45/250 [01:38<06:34,  1.92s/it]
 18%|█▊        | 46/250 [01:38<04:59,  1.47s/it]
 19%|█▉        | 47/250 [01:39<03:54,  1.16s/it]
 19%|█▉        | 48/250 [01:40<03:29,  1.04s/it]
 20%|█▉        | 49/250 [01:43<05:33,  1.66s/it]
 20%|██        | 50/250 [01:45<05:54,  1.77s/it]
 20%|██        | 51/250 [01:48<07:38,  2.31s/it]
 21%|██        | 52/250 [01:50<06:44,  2.04s/it]
 21%|██        | 53/250 [01:51<05:26,  1.66s/it]
 22%|██▏       | 54/250 [01:53<06:23,  1.96s/it]
 22%|██▏       | 55/250 [01:56<07:27,  2.30s/it]
 22%|██▏       | 56/250 [01:59<08:09,  2.52s/it]
 23%|██▎       | 57/250 [02:01<06:56,  2.16s/it]
 23%|██▎       | 58/250 [02:02<05:48,  1.81s/it]
 24%|██▎       | 59/250 [02:02<04:19,  1.36s/it]
 24%|██▍       | 60/250 [02:02<03:18,  1.04s/it]
 24%|██▍       | 61/250 [02:03<02:40,  1.18it/s]
 25%|██▍       | 62/250 [02:04<02:43,  1.15it/s]
 25%|██▌       | 63/250 [02:05<03:28,  1.11s/it]
 26%|██▌       | 64/250 [02:06<03:34,  1.15s/it]
 26%|██▌       | 65/250 [02:09<04:21,  1.41s/it]
 26%|██▋       | 66/250 [02:11<05:16,  1.72s/it]
 27%|██▋       | 67/250 [02:13<05:39,  1.85s/it]
 27%|██▋       | 68/250 [02:14<04:34,  1.51s/it]
 28%|██▊       | 69/250 [02:17<05:46,  1.92s/it]
 28%|██▊       | 70/250 [02:17<04:10,  1.39s/it]
 28%|██▊       | 71/250 [02:18<04:00,  1.34s/it]
 29%|██▉       | 72/250 [02:21<05:04,  1.71s/it]
 29%|██▉       | 73/250 [02:29<10:33,  3.58s/it]
 30%|██▉       | 74/250 [02:30<08:44,  2.98s/it]
 30%|███       | 75/250 [02:31<06:24,  2.20s/it]
 30%|███       | 76/250 [02:33<06:29,  2.24s/it]
 31%|███       | 77/250 [02:35<06:42,  2.33s/it]
 31%|███       | 78/250 [02:39<07:43,  2.70s/it]
 32%|███▏      | 79/250 [02:39<05:34,  1.96s/it]
 32%|███▏      | 80/250 [02:41<05:01,  1.77s/it]
 33%|███▎      | 82/250 [02:41<02:47,  1.00it/s]
 33%|███▎      | 83/250 [02:41<02:14,  1.25it/s]
 34%|███▎      | 84/250 [02:43<02:58,  1.08s/it]
 34%|███▍      | 85/250 [02:43<02:19,  1.19it/s]
 34%|███▍      | 86/250 [02:43<01:52,  1.46it/s]
 35%|███▍      | 87/250 [02:47<04:20,  1.60s/it]
 35%|███▌      | 88/250 [02:48<03:43,  1.38s/it]
 36%|███▌      | 89/250 [02:51<05:00,  1.87s/it]
 36%|███▌      | 90/250 [02:52<03:57,  1.48s/it]
 36%|███▋      | 91/250 [02:54<04:51,  1.83s/it]
 37%|███▋      | 92/250 [02:59<07:11,  2.73s/it]
 38%|███▊      | 94/250 [03:00<04:03,  1.56s/it]
 38%|███▊      | 95/250 [03:02<04:40,  1.81s/it]
 38%|███▊      | 96/250 [03:03<03:46,  1.47s/it]
 39%|███▉      | 97/250 [03:03<02:53,  1.13s/it]
 39%|███▉      | 98/250 [03:04<02:45,  1.09s/it]
 40%|███▉      | 99/250 [03:05<03:05,  1.23s/it]
 40%|████      | 100/250 [03:06<02:58,  1.19s/it]
 40%|████      | 101/250 [03:08<03:27,  1.39s/it]
 41%|████      | 102/250 [03:09<02:56,  1.19s/it]
 41%|████      | 103/250 [03:10<02:25,  1.01it/s]
 42%|████▏     | 104/250 [03:10<01:51,  1.31it/s]
 42%|████▏     | 105/250 [03:12<03:07,  1.29s/it]
 42%|████▏     | 106/250 [03:13<02:49,  1.18s/it]
 43%|████▎     | 107/250 [03:14<02:12,  1.08it/s]
 43%|████▎     | 108/250 [03:16<03:07,  1.32s/it]
 44%|████▎     | 109/250 [03:16<02:18,  1.02it/s]
 44%|████▍     | 110/250 [03:20<04:27,  1.91s/it]
 44%|████▍     | 111/250 [03:21<03:27,  1.50s/it]
 45%|████▍     | 112/250 [03:22<03:37,  1.57s/it]
 45%|████▌     | 113/250 [03:27<05:27,  2.39s/it]
 46%|████▌     | 114/250 [03:27<03:55,  1.73s/it]
 46%|████▋     | 116/250 [03:29<03:02,  1.36s/it]
 47%|████▋     | 118/250 [03:30<02:27,  1.12s/it]
 48%|████▊     | 119/250 [03:32<02:56,  1.34s/it]
 48%|████▊     | 120/250 [03:33<02:29,  1.15s/it]
 48%|████▊     | 121/250 [03:34<02:19,  1.08s/it]
 49%|████▉     | 122/250 [03:34<01:52,  1.14it/s]
 49%|████▉     | 123/250 [03:35<01:50,  1.15it/s]
 50%|████▉     | 124/250 [03:36<01:51,  1.13it/s]
 50%|█████     | 125/250 [03:39<03:15,  1.56s/it]
 50%|█████     | 126/250 [03:40<02:47,  1.35s/it]
 51%|█████     | 127/250 [03:40<02:16,  1.11s/it]
 51%|█████     | 128/250 [03:41<01:49,  1.11it/s]
 52%|█████▏    | 129/250 [03:44<03:03,  1.51s/it]
 52%|█████▏    | 130/250 [03:45<02:44,  1.37s/it]
 52%|█████▏    | 131/250 [03:47<03:18,  1.67s/it]
 53%|█████▎    | 132/250 [03:48<02:52,  1.46s/it]
 53%|█████▎    | 133/250 [03:52<04:10,  2.14s/it]
 54%|█████▎    | 134/250 [03:53<03:35,  1.86s/it]
 54%|█████▍    | 135/250 [03:53<02:35,  1.35s/it]
 54%|█████▍    | 136/250 [03:54<02:27,  1.29s/it]
 55%|█████▍    | 137/250 [03:55<02:07,  1.13s/it]
 55%|█████▌    | 138/250 [03:57<02:41,  1.44s/it]
 56%|█████▌    | 139/250 [03:58<01:59,  1.07s/it]
 56%|█████▌    | 140/250 [03:58<01:26,  1.28it/s]
 56%|█████▋    | 141/250 [03:58<01:14,  1.46it/s]
 57%|█████▋    | 142/250 [03:59<01:08,  1.57it/s]
 57%|█████▋    | 143/250 [03:59<00:54,  1.96it/s]
 58%|█████▊    | 144/250 [03:59<00:44,  2.38it/s]
 58%|█████▊    | 145/250 [04:00<00:50,  2.09it/s]
 58%|█████▊    | 146/250 [04:01<01:08,  1.53it/s]
 59%|█████▉    | 147/250 [04:04<02:24,  1.41s/it]
 59%|█████▉    | 148/250 [04:05<02:14,  1.32s/it]
 60%|█████▉    | 149/250 [04:06<02:10,  1.29s/it]
 60%|██████    | 150/250 [04:07<01:37,  1.02it/s]
 60%|██████    | 151/250 [04:07<01:30,  1.09it/s]
 61%|██████    | 152/250 [04:09<01:43,  1.06s/it]
 61%|██████    | 153/250 [04:10<01:54,  1.18s/it]
 62%|██████▏   | 154/250 [04:12<02:01,  1.27s/it]
 62%|██████▏   | 155/250 [04:14<02:39,  1.68s/it]
 62%|██████▏   | 156/250 [04:15<02:03,  1.31s/it]
 63%|██████▎   | 157/250 [04:17<02:15,  1.46s/it]
 63%|██████▎   | 158/250 [04:17<01:48,  1.17s/it]
 64%|██████▎   | 159/250 [04:18<01:40,  1.10s/it]
 64%|██████▍   | 160/250 [04:19<01:24,  1.07it/s]
 64%|██████▍   | 161/250 [04:20<01:26,  1.03it/s]
 65%|██████▍   | 162/250 [04:21<01:40,  1.15s/it]
 65%|██████▌   | 163/250 [04:21<01:15,  1.15it/s]
 66%|██████▌   | 164/250 [04:22<01:10,  1.23it/s]
 66%|██████▌   | 165/250 [04:23<01:07,  1.26it/s]
 66%|██████▋   | 166/250 [04:25<01:32,  1.10s/it]
 67%|██████▋   | 167/250 [04:30<03:06,  2.25s/it]
 67%|██████▋   | 168/250 [04:32<03:07,  2.28s/it]
 68%|██████▊   | 169/250 [04:33<02:29,  1.84s/it]
 68%|██████▊   | 170/250 [04:33<01:54,  1.44s/it]
 68%|██████▊   | 171/250 [04:33<01:23,  1.06s/it]
 69%|██████▉   | 172/250 [04:34<01:07,  1.16it/s]
 69%|██████▉   | 173/250 [04:34<00:50,  1.54it/s]
 70%|██████▉   | 174/250 [04:38<02:04,  1.64s/it]
 70%|███████   | 175/250 [04:38<01:30,  1.21s/it]
 70%|███████   | 176/250 [04:39<01:29,  1.20s/it]
 71%|███████   | 177/250 [04:43<02:22,  1.96s/it]
 71%|███████   | 178/250 [04:45<02:16,  1.90s/it]
 72%|███████▏  | 181/250 [04:45<00:59,  1.15it/s]
 73%|███████▎  | 182/250 [04:46<01:04,  1.06it/s]
 73%|███████▎  | 183/250 [04:47<01:07,  1.00s/it]
 74%|███████▎  | 184/250 [04:48<00:57,  1.15it/s]
 74%|███████▍  | 185/250 [04:51<01:41,  1.57s/it]
 74%|███████▍  | 186/250 [04:53<01:33,  1.46s/it]
 75%|███████▍  | 187/250 [04:53<01:15,  1.20s/it]
 75%|███████▌  | 188/250 [04:53<00:55,  1.11it/s]
 76%|███████▌  | 189/250 [04:55<01:03,  1.04s/it]
 76%|███████▌  | 190/250 [04:56<01:06,  1.10s/it]
 76%|███████▋  | 191/250 [04:57<00:59,  1.00s/it]
 77%|███████▋  | 192/250 [04:57<00:44,  1.30it/s]
 77%|███████▋  | 193/250 [04:57<00:36,  1.57it/s]
 78%|███████▊  | 194/250 [05:00<01:08,  1.22s/it]
 78%|███████▊  | 195/250 [05:01<01:12,  1.32s/it]
 78%|███████▊  | 196/250 [05:02<01:06,  1.23s/it]
 79%|███████▉  | 197/250 [05:02<00:47,  1.12it/s]
 79%|███████▉  | 198/250 [05:03<00:44,  1.17it/s]
 80%|███████▉  | 199/250 [05:06<01:18,  1.54s/it]
 80%|████████  | 200/250 [05:07<01:09,  1.39s/it]
 80%|████████  | 201/250 [05:10<01:27,  1.79s/it]
 81%|████████  | 202/250 [05:14<01:53,  2.37s/it]
 81%|████████  | 203/250 [05:15<01:30,  1.93s/it]
 82%|████████▏ | 204/250 [05:16<01:22,  1.80s/it]
 82%|████████▏ | 205/250 [05:18<01:24,  1.87s/it]
 82%|████████▏ | 206/250 [05:20<01:18,  1.79s/it]
 83%|████████▎ | 207/250 [05:21<01:04,  1.51s/it]
 83%|████████▎ | 208/250 [05:21<00:46,  1.10s/it]
 84%|████████▎ | 209/250 [05:21<00:33,  1.22it/s]
 84%|████████▍ | 210/250 [05:22<00:39,  1.01it/s]
 84%|████████▍ | 211/250 [05:25<00:56,  1.45s/it]
 85%|████████▌ | 213/250 [05:26<00:38,  1.04s/it]
 86%|████████▌ | 214/250 [05:26<00:31,  1.14it/s]
 86%|████████▌ | 215/250 [05:28<00:40,  1.15s/it]
 86%|████████▋ | 216/250 [05:29<00:30,  1.13it/s]
 87%|████████▋ | 217/250 [05:29<00:28,  1.17it/s]
 87%|████████▋ | 218/250 [05:30<00:25,  1.26it/s]
 88%|████████▊ | 219/250 [05:31<00:27,  1.11it/s]
 88%|████████▊ | 220/250 [05:32<00:27,  1.09it/s]
 88%|████████▊ | 221/250 [05:34<00:32,  1.11s/it]
 89%|████████▉ | 222/250 [05:36<00:38,  1.39s/it]
 89%|████████▉ | 223/250 [05:36<00:27,  1.02s/it]
 90%|████████▉ | 224/250 [05:37<00:31,  1.20s/it]
 90%|█████████ | 225/250 [05:38<00:23,  1.06it/s]
 90%|█████████ | 226/250 [05:41<00:38,  1.62s/it]
 91%|█████████ | 227/250 [05:43<00:38,  1.68s/it]
 92%|█████████▏| 229/250 [05:44<00:26,  1.27s/it]
 92%|█████████▏| 230/250 [05:45<00:20,  1.01s/it]
 92%|█████████▏| 231/250 [05:48<00:28,  1.52s/it]
 93%|█████████▎| 232/250 [05:50<00:29,  1.62s/it]
 93%|█████████▎| 233/250 [05:50<00:21,  1.26s/it]
 94%|█████████▎| 234/250 [05:51<00:17,  1.09s/it]
 94%|█████████▍| 235/250 [05:51<00:15,  1.04s/it]
 95%|█████████▍| 237/250 [05:53<00:12,  1.03it/s]
 95%|█████████▌| 238/250 [05:54<00:10,  1.18it/s]
 96%|█████████▌| 239/250 [05:54<00:08,  1.28it/s]
 96%|█████████▌| 240/250 [05:55<00:06,  1.51it/s]
 96%|█████████▋| 241/250 [05:55<00:05,  1.68it/s]
 97%|█████████▋| 242/250 [05:56<00:05,  1.60it/s]
 97%|█████████▋| 243/250 [05:57<00:05,  1.34it/s]
 98%|█████████▊| 245/250 [05:58<00:02,  1.70it/s]
 98%|█████████▊| 246/250 [05:59<00:03,  1.17it/s]
 99%|█████████▉| 247/250 [06:02<00:03,  1.32s/it]
 99%|█████████▉| 248/250 [06:02<00:02,  1.04s/it]
100%|█████████▉| 249/250 [06:05<00:01,  1.59s/it]
100%|██████████| 250/250 [06:06<00:00,  1.27s/it]
100%|██████████| 250/250 [06:06<00:00,  1.46s/it]
2025-05-15 13:20:59,827 - modnet - INFO - Loss per individual: ind 0: 0.466 	ind 1: 0.545 	ind 2: 0.513 	ind 3: 0.479 	ind 4: 0.514 	ind 5: 0.528 	ind 6: 0.511 	ind 7: 0.446 	ind 8: 0.442 	ind 9: 0.487 	ind 10: 0.502 	ind 11: 0.493 	ind 12: 0.516 	ind 13: 0.489 	ind 14: 0.502 	ind 15: 0.501 	ind 16: 0.485 	ind 17: 0.514 	ind 18: 0.510 	ind 19: 0.448 	ind 20: 0.490 	ind 21: 0.474 	ind 22: 0.492 	ind 23: 0.488 	ind 24: 0.454 	ind 25: 0.480 	ind 26: 0.455 	ind 27: 0.499 	ind 28: 0.477 	ind 29: 0.505 	ind 30: 0.492 	ind 31: 0.455 	ind 32: 0.506 	ind 33: 0.497 	ind 34: 0.471 	ind 35: 0.510 	ind 36: 0.516 	ind 37: 0.489 	ind 38: 0.503 	ind 39: 0.508 	ind 40: 0.510 	ind 41: 0.491 	ind 42: 0.494 	ind 43: 0.483 	ind 44: 0.508 	ind 45: 0.499 	ind 46: 0.505 	ind 47: 0.520 	ind 48: 0.506 	ind 49: 0.507 	
2025-05-15 13:20:59,833 - modnet - INFO - Generation number 9

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:23<1:37:39, 23.53s/it]
  1%|          | 2/250 [00:25<44:53, 10.86s/it]  
  1%|          | 3/250 [00:29<31:53,  7.75s/it]
  2%|▏         | 4/250 [00:32<24:05,  5.88s/it]
  2%|▏         | 5/250 [00:33<16:22,  4.01s/it]
  2%|▏         | 6/250 [00:33<11:28,  2.82s/it]
  3%|▎         | 7/250 [00:36<11:10,  2.76s/it]
  3%|▎         | 8/250 [00:38<09:48,  2.43s/it]
  4%|▎         | 9/250 [00:41<10:36,  2.64s/it]
  4%|▍         | 10/250 [00:41<07:40,  1.92s/it]
  4%|▍         | 11/250 [00:44<08:25,  2.12s/it]
  5%|▍         | 12/250 [00:44<06:41,  1.69s/it]
  5%|▌         | 13/250 [00:46<06:35,  1.67s/it]
  6%|▌         | 14/250 [00:46<04:49,  1.23s/it]
  6%|▌         | 15/250 [00:46<03:30,  1.12it/s]
  6%|▋         | 16/250 [00:47<03:01,  1.29it/s]
  7%|▋         | 17/250 [00:50<05:35,  1.44s/it]
  7%|▋         | 18/250 [00:51<05:48,  1.50s/it]
  8%|▊         | 19/250 [00:53<05:28,  1.42s/it]
  8%|▊         | 21/250 [00:53<03:35,  1.06it/s]
  9%|▉         | 22/250 [00:55<04:02,  1.07s/it]
  9%|▉         | 23/250 [00:58<06:11,  1.64s/it]
 10%|▉         | 24/250 [00:59<05:53,  1.57s/it]
 10%|█         | 25/250 [01:01<05:52,  1.57s/it]
 10%|█         | 26/250 [01:03<05:47,  1.55s/it]
 11%|█         | 27/250 [01:03<04:50,  1.30s/it]
 11%|█         | 28/250 [01:04<03:53,  1.05s/it]
 12%|█▏        | 29/250 [01:04<03:10,  1.16it/s]
 12%|█▏        | 30/250 [01:06<04:38,  1.27s/it]
 12%|█▏        | 31/250 [01:07<03:34,  1.02it/s]
 13%|█▎        | 32/250 [01:08<03:35,  1.01it/s]
 13%|█▎        | 33/250 [01:08<02:59,  1.21it/s]
 14%|█▎        | 34/250 [01:08<02:18,  1.56it/s]
 14%|█▍        | 35/250 [01:10<02:57,  1.21it/s]
 14%|█▍        | 36/250 [01:10<02:40,  1.34it/s]
 15%|█▍        | 37/250 [01:11<02:22,  1.49it/s]
 15%|█▌        | 38/250 [01:12<03:16,  1.08it/s]
 16%|█▌        | 39/250 [01:17<07:52,  2.24s/it]
 16%|█▌        | 40/250 [01:18<05:44,  1.64s/it]
 16%|█▋        | 41/250 [01:20<06:14,  1.79s/it]
 17%|█▋        | 42/250 [01:21<05:14,  1.51s/it]
 17%|█▋        | 43/250 [01:21<04:00,  1.16s/it]
 18%|█▊        | 44/250 [01:25<06:40,  1.94s/it]
 18%|█▊        | 45/250 [01:26<06:10,  1.81s/it]
 18%|█▊        | 46/250 [01:34<11:48,  3.47s/it]
 19%|█▉        | 47/250 [01:34<08:51,  2.62s/it]
 19%|█▉        | 48/250 [01:35<07:12,  2.14s/it]
 20%|█▉        | 49/250 [01:36<05:28,  1.63s/it]
 20%|██        | 50/250 [01:36<04:00,  1.20s/it]
 20%|██        | 51/250 [01:37<03:32,  1.07s/it]
 21%|██        | 52/250 [01:38<03:23,  1.03s/it]
 21%|██        | 53/250 [01:39<03:24,  1.04s/it]
 22%|██▏       | 54/250 [01:40<03:33,  1.09s/it]
 22%|██▏       | 55/250 [01:41<03:29,  1.07s/it]
 22%|██▏       | 56/250 [01:42<03:44,  1.16s/it]
 23%|██▎       | 57/250 [01:47<07:00,  2.18s/it]
 23%|██▎       | 58/250 [01:52<09:49,  3.07s/it]
 24%|██▎       | 59/250 [01:52<07:15,  2.28s/it]
 24%|██▍       | 60/250 [01:57<09:09,  2.89s/it]
 24%|██▍       | 61/250 [01:58<07:13,  2.29s/it]
 25%|██▍       | 62/250 [01:59<05:53,  1.88s/it]
 25%|██▌       | 63/250 [01:59<04:51,  1.56s/it]
 26%|██▌       | 64/250 [02:00<03:38,  1.17s/it]
 26%|██▌       | 65/250 [02:01<03:46,  1.22s/it]
 26%|██▋       | 66/250 [02:04<04:58,  1.62s/it]
 27%|██▋       | 67/250 [02:04<04:00,  1.31s/it]
 27%|██▋       | 68/250 [02:05<03:12,  1.06s/it]
 28%|██▊       | 69/250 [02:06<03:23,  1.12s/it]
 28%|██▊       | 70/250 [02:07<03:47,  1.27s/it]
 28%|██▊       | 71/250 [02:08<02:50,  1.05it/s]
 29%|██▉       | 72/250 [02:10<03:57,  1.34s/it]
 29%|██▉       | 73/250 [02:10<02:56,  1.00it/s]
 30%|██▉       | 74/250 [02:11<02:50,  1.04it/s]
 30%|███       | 75/250 [02:11<02:08,  1.37it/s]
 30%|███       | 76/250 [02:11<01:39,  1.75it/s]
 31%|███       | 77/250 [02:12<01:41,  1.71it/s]
 31%|███       | 78/250 [02:15<03:40,  1.28s/it]
 32%|███▏      | 79/250 [02:16<03:04,  1.08s/it]
 32%|███▏      | 80/250 [02:18<03:58,  1.41s/it]
 32%|███▏      | 81/250 [02:22<06:07,  2.17s/it]
 33%|███▎      | 82/250 [02:22<04:22,  1.56s/it]
 33%|███▎      | 83/250 [02:22<03:11,  1.14s/it]
 34%|███▎      | 84/250 [02:25<04:38,  1.68s/it]
 34%|███▍      | 85/250 [02:26<04:16,  1.55s/it]
 34%|███▍      | 86/250 [02:29<05:35,  2.04s/it]
 35%|███▍      | 87/250 [02:32<05:51,  2.16s/it]
 35%|███▌      | 88/250 [02:33<04:41,  1.74s/it]
 36%|███▌      | 89/250 [02:33<03:27,  1.29s/it]
 36%|███▌      | 90/250 [02:33<02:36,  1.02it/s]
 36%|███▋      | 91/250 [02:34<02:40,  1.01s/it]
 37%|███▋      | 92/250 [02:37<04:32,  1.72s/it]
 37%|███▋      | 93/250 [02:39<04:06,  1.57s/it]
 38%|███▊      | 94/250 [02:39<03:00,  1.16s/it]
 38%|███▊      | 95/250 [02:41<04:00,  1.55s/it]
 38%|███▊      | 96/250 [02:43<04:22,  1.71s/it]
 39%|███▉      | 97/250 [02:46<05:03,  1.99s/it]
 39%|███▉      | 98/250 [02:47<03:55,  1.55s/it]
 40%|███▉      | 99/250 [02:48<03:36,  1.44s/it]
 40%|████      | 100/250 [02:48<03:00,  1.21s/it]
 40%|████      | 101/250 [02:49<02:22,  1.05it/s]
 41%|████      | 102/250 [02:50<02:37,  1.07s/it]
 41%|████      | 103/250 [02:50<02:03,  1.19it/s]
 42%|████▏     | 104/250 [02:51<01:48,  1.35it/s]
 42%|████▏     | 105/250 [02:51<01:23,  1.73it/s]
 42%|████▏     | 106/250 [02:52<01:15,  1.90it/s]
 43%|████▎     | 107/250 [02:52<01:01,  2.33it/s]
 43%|████▎     | 108/250 [02:55<03:04,  1.30s/it]
 44%|████▎     | 109/250 [02:56<02:34,  1.10s/it]
 44%|████▍     | 110/250 [02:56<02:06,  1.10it/s]
 44%|████▍     | 111/250 [02:57<01:44,  1.33it/s]
 45%|████▍     | 112/250 [02:59<02:57,  1.29s/it]
 45%|████▌     | 113/250 [03:01<03:19,  1.46s/it]
 46%|████▌     | 114/250 [03:01<02:23,  1.05s/it]
 46%|████▌     | 115/250 [03:04<03:18,  1.47s/it]
 46%|████▋     | 116/250 [03:06<03:44,  1.68s/it]
 47%|████▋     | 117/250 [03:10<05:21,  2.42s/it]
 47%|████▋     | 118/250 [03:12<04:59,  2.27s/it]
 48%|████▊     | 119/250 [03:12<03:35,  1.65s/it]
 48%|████▊     | 120/250 [03:15<04:24,  2.03s/it]
 48%|████▊     | 121/250 [03:16<04:04,  1.90s/it]
 49%|████▉     | 122/250 [03:17<03:22,  1.58s/it]
 49%|████▉     | 123/250 [03:20<04:19,  2.04s/it]
 50%|████▉     | 124/250 [03:23<04:30,  2.15s/it]
 50%|█████     | 125/250 [03:24<03:36,  1.73s/it]
 50%|█████     | 126/250 [03:24<02:43,  1.32s/it]
 51%|█████     | 127/250 [03:24<02:03,  1.00s/it]
 51%|█████     | 128/250 [03:25<01:37,  1.25it/s]
 52%|█████▏    | 129/250 [03:25<01:15,  1.60it/s]
 52%|█████▏    | 130/250 [03:26<01:48,  1.11it/s]
 52%|█████▏    | 131/250 [03:27<01:26,  1.38it/s]
 53%|█████▎    | 133/250 [03:27<00:51,  2.29it/s]
 54%|█████▎    | 134/250 [03:28<00:59,  1.95it/s]
 54%|█████▍    | 136/250 [03:29<01:07,  1.70it/s]
 55%|█████▍    | 137/250 [03:30<01:11,  1.58it/s]
 55%|█████▌    | 138/250 [03:31<01:41,  1.10it/s]
 56%|█████▌    | 139/250 [03:34<02:31,  1.37s/it]
 56%|█████▌    | 140/250 [03:34<01:59,  1.09s/it]
 56%|█████▋    | 141/250 [03:35<01:38,  1.11it/s]
 57%|█████▋    | 142/250 [03:40<03:34,  1.99s/it]
 57%|█████▋    | 143/250 [03:43<04:20,  2.44s/it]
 58%|█████▊    | 144/250 [03:43<03:10,  1.80s/it]
 58%|█████▊    | 145/250 [03:45<02:53,  1.65s/it]
 58%|█████▊    | 146/250 [03:46<02:27,  1.42s/it]
 59%|█████▉    | 147/250 [03:51<04:43,  2.75s/it]
 59%|█████▉    | 148/250 [03:52<03:24,  2.00s/it]
 60%|█████▉    | 149/250 [03:52<02:44,  1.62s/it]
 60%|██████    | 150/250 [03:55<03:10,  1.91s/it]
 60%|██████    | 151/250 [03:55<02:23,  1.45s/it]
 61%|██████    | 152/250 [03:56<01:43,  1.05s/it]
 61%|██████    | 153/250 [03:57<02:06,  1.30s/it]
 62%|██████▏   | 154/250 [03:58<01:40,  1.05s/it]
 62%|██████▏   | 155/250 [03:59<01:44,  1.10s/it]
 62%|██████▏   | 156/250 [03:59<01:17,  1.22it/s]
 63%|██████▎   | 157/250 [04:00<01:07,  1.37it/s]
 63%|██████▎   | 158/250 [04:01<01:11,  1.29it/s]
 64%|██████▎   | 159/250 [04:05<02:55,  1.92s/it]
 64%|██████▍   | 160/250 [04:05<02:06,  1.41s/it]
 64%|██████▍   | 161/250 [04:06<01:33,  1.05s/it]
 65%|██████▍   | 162/250 [04:09<02:27,  1.67s/it]
 65%|██████▌   | 163/250 [04:09<01:56,  1.34s/it]
 66%|██████▌   | 164/250 [04:14<03:31,  2.46s/it]
 66%|██████▌   | 165/250 [04:15<02:30,  1.78s/it]
 66%|██████▋   | 166/250 [04:15<01:53,  1.35s/it]
 67%|██████▋   | 167/250 [04:19<02:56,  2.12s/it]
 67%|██████▋   | 168/250 [04:20<02:36,  1.91s/it]
 68%|██████▊   | 169/250 [04:22<02:26,  1.81s/it]
 68%|██████▊   | 170/250 [04:25<02:57,  2.22s/it]
 68%|██████▊   | 171/250 [04:26<02:19,  1.77s/it]
 69%|██████▉   | 172/250 [04:26<01:48,  1.39s/it]
 69%|██████▉   | 173/250 [04:27<01:35,  1.24s/it]
 70%|██████▉   | 174/250 [04:27<01:13,  1.04it/s]
 70%|███████   | 175/250 [04:29<01:13,  1.02it/s]
 70%|███████   | 176/250 [04:30<01:30,  1.22s/it]
 71%|███████   | 177/250 [04:31<01:07,  1.08it/s]
 71%|███████   | 178/250 [04:32<01:22,  1.15s/it]
 72%|███████▏  | 179/250 [04:34<01:44,  1.47s/it]
 72%|███████▏  | 180/250 [04:36<01:47,  1.54s/it]
 72%|███████▏  | 181/250 [04:37<01:23,  1.21s/it]
 73%|███████▎  | 182/250 [04:38<01:30,  1.34s/it]
 73%|███████▎  | 183/250 [04:39<01:16,  1.14s/it]
 74%|███████▎  | 184/250 [04:44<02:24,  2.19s/it]
 74%|███████▍  | 185/250 [04:45<01:58,  1.83s/it]
 75%|███████▍  | 187/250 [04:45<01:12,  1.15s/it]
 75%|███████▌  | 188/250 [04:47<01:18,  1.27s/it]
 76%|███████▌  | 189/250 [04:48<01:10,  1.15s/it]
 76%|███████▋  | 191/250 [04:49<00:57,  1.02it/s]
 77%|███████▋  | 192/250 [04:49<00:46,  1.26it/s]
 77%|███████▋  | 193/250 [04:54<01:33,  1.64s/it]
 78%|███████▊  | 194/250 [04:55<01:24,  1.52s/it]
 78%|███████▊  | 195/250 [04:56<01:20,  1.46s/it]
 78%|███████▊  | 196/250 [04:56<00:59,  1.10s/it]
 79%|███████▉  | 197/250 [04:59<01:23,  1.57s/it]
 79%|███████▉  | 198/250 [05:01<01:35,  1.83s/it]
 80%|███████▉  | 199/250 [05:03<01:33,  1.83s/it]
 80%|████████  | 200/250 [05:03<01:07,  1.34s/it]
 80%|████████  | 201/250 [05:04<00:59,  1.20s/it]
 81%|████████  | 202/250 [05:05<00:54,  1.14s/it]
 81%|████████  | 203/250 [05:05<00:39,  1.18it/s]
 82%|████████▏ | 204/250 [05:06<00:33,  1.39it/s]
 82%|████████▏ | 205/250 [05:07<00:36,  1.23it/s]
 82%|████████▏ | 206/250 [05:10<01:10,  1.60s/it]
 83%|████████▎ | 207/250 [05:11<00:51,  1.21s/it]
 83%|████████▎ | 208/250 [05:11<00:38,  1.10it/s]
 84%|████████▎ | 209/250 [05:13<00:50,  1.23s/it]
 84%|████████▍ | 210/250 [05:15<00:59,  1.49s/it]
 84%|████████▍ | 211/250 [05:17<01:08,  1.76s/it]
 85%|████████▍ | 212/250 [05:18<00:59,  1.57s/it]
 85%|████████▌ | 213/250 [05:19<00:45,  1.24s/it]
 86%|████████▌ | 215/250 [05:19<00:26,  1.34it/s]
 86%|████████▋ | 216/250 [05:21<00:34,  1.03s/it]
 87%|████████▋ | 217/250 [05:21<00:27,  1.19it/s]
 87%|████████▋ | 218/250 [05:22<00:23,  1.39it/s]
 88%|████████▊ | 219/250 [05:23<00:24,  1.27it/s]
 88%|████████▊ | 220/250 [05:23<00:18,  1.66it/s]
 88%|████████▊ | 221/250 [05:23<00:13,  2.15it/s]
 89%|████████▉ | 222/250 [05:23<00:10,  2.71it/s]
 89%|████████▉ | 223/250 [05:24<00:14,  1.93it/s]
 90%|████████▉ | 224/250 [05:25<00:12,  2.02it/s]
 90%|█████████ | 225/250 [05:25<00:11,  2.09it/s]
 90%|█████████ | 226/250 [05:29<00:37,  1.56s/it]
 91%|█████████ | 227/250 [05:30<00:31,  1.35s/it]
 91%|█████████ | 228/250 [05:30<00:23,  1.09s/it]
 92%|█████████▏| 229/250 [05:34<00:39,  1.90s/it]
 92%|█████████▏| 230/250 [05:35<00:33,  1.67s/it]
 92%|█████████▏| 231/250 [05:36<00:28,  1.48s/it]
 93%|█████████▎| 232/250 [05:40<00:40,  2.25s/it]
 93%|█████████▎| 233/250 [05:42<00:33,  1.99s/it]
 94%|█████████▎| 234/250 [05:42<00:23,  1.46s/it]
 94%|█████████▍| 235/250 [05:47<00:38,  2.57s/it]
 94%|█████████▍| 236/250 [05:48<00:27,  1.94s/it]
 95%|█████████▍| 237/250 [05:48<00:18,  1.43s/it]
 95%|█████████▌| 238/250 [05:48<00:12,  1.03s/it]
 96%|█████████▌| 240/250 [05:50<00:10,  1.06s/it]
 96%|█████████▋| 241/250 [05:53<00:12,  1.43s/it]
 97%|█████████▋| 242/250 [05:53<00:09,  1.24s/it]
 97%|█████████▋| 243/250 [05:54<00:07,  1.02s/it]
 98%|█████████▊| 244/250 [05:56<00:07,  1.24s/it]
 98%|█████████▊| 246/250 [05:57<00:04,  1.09s/it]
 99%|█████████▉| 247/250 [06:03<00:06,  2.06s/it]
 99%|█████████▉| 248/250 [06:03<00:03,  1.64s/it]
100%|█████████▉| 249/250 [06:05<00:01,  1.79s/it]
100%|██████████| 250/250 [06:09<00:00,  2.26s/it]
100%|██████████| 250/250 [06:09<00:00,  1.48s/it]
2025-05-15 13:27:09,132 - modnet - INFO - Loss per individual: ind 0: 0.496 	ind 1: 0.488 	ind 2: 0.489 	ind 3: 0.511 	ind 4: 0.490 	ind 5: 0.503 	ind 6: 0.493 	ind 7: 0.493 	ind 8: 0.479 	ind 9: 0.485 	ind 10: 0.495 	ind 11: 0.503 	ind 12: 0.468 	ind 13: 0.481 	ind 14: 0.495 	ind 15: 0.632 	ind 16: 0.466 	ind 17: 0.491 	ind 18: 0.493 	ind 19: 0.544 	ind 20: 0.473 	ind 21: 0.475 	ind 22: 0.501 	ind 23: 0.489 	ind 24: 0.489 	ind 25: 0.491 	ind 26: 0.770 	ind 27: 0.478 	ind 28: 0.468 	ind 29: 0.501 	ind 30: 0.539 	ind 31: 0.484 	ind 32: 0.483 	ind 33: 0.486 	ind 34: 0.481 	ind 35: 0.454 	ind 36: 0.440 	ind 37: 0.500 	ind 38: 0.501 	ind 39: 0.439 	ind 40: 0.495 	ind 41: 0.446 	ind 42: 0.475 	ind 43: 0.488 	ind 44: 0.455 	ind 45: 0.499 	ind 46: 0.576 	ind 47: 0.497 	ind 48: 0.485 	ind 49: 0.509 	
2025-05-15 13:27:09,134 - modnet - INFO - Generation number 10

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:14<1:00:54, 14.68s/it]
  1%|          | 2/250 [00:18<34:11,  8.27s/it]  
  1%|          | 3/250 [00:25<31:42,  7.70s/it]
  2%|▏         | 4/250 [00:28<23:12,  5.66s/it]
  2%|▏         | 5/250 [00:35<25:18,  6.20s/it]
  2%|▏         | 6/250 [00:39<22:12,  5.46s/it]
  3%|▎         | 7/250 [00:40<17:00,  4.20s/it]
  3%|▎         | 8/250 [00:41<12:48,  3.18s/it]
  4%|▎         | 9/250 [00:41<09:01,  2.25s/it]
  4%|▍         | 10/250 [00:43<07:50,  1.96s/it]
  4%|▍         | 11/250 [00:45<07:32,  1.89s/it]
  5%|▍         | 12/250 [00:46<06:46,  1.71s/it]
  6%|▌         | 14/250 [00:48<05:46,  1.47s/it]
  6%|▌         | 15/250 [00:49<05:04,  1.29s/it]
  6%|▋         | 16/250 [00:51<05:36,  1.44s/it]
  7%|▋         | 17/250 [00:54<07:38,  1.97s/it]
  7%|▋         | 18/250 [00:56<07:19,  1.90s/it]
  8%|▊         | 19/250 [00:56<05:38,  1.47s/it]
  8%|▊         | 20/250 [00:57<05:15,  1.37s/it]
  8%|▊         | 21/250 [00:58<03:57,  1.04s/it]
  9%|▉         | 22/250 [00:58<03:09,  1.20it/s]
  9%|▉         | 23/250 [00:59<02:48,  1.34it/s]
 10%|▉         | 24/250 [01:02<06:15,  1.66s/it]
 10%|█         | 25/250 [01:04<05:54,  1.57s/it]
 10%|█         | 26/250 [01:04<04:18,  1.15s/it]
 11%|█         | 27/250 [01:05<04:11,  1.13s/it]
 11%|█         | 28/250 [01:05<03:18,  1.12it/s]
 12%|█▏        | 29/250 [01:12<09:40,  2.63s/it]
 12%|█▏        | 30/250 [01:14<09:02,  2.47s/it]
 13%|█▎        | 32/250 [01:18<08:12,  2.26s/it]
 14%|█▎        | 34/250 [01:18<05:07,  1.42s/it]
 14%|█▍        | 35/250 [01:19<04:28,  1.25s/it]
 14%|█▍        | 36/250 [01:22<05:29,  1.54s/it]
 15%|█▍        | 37/250 [01:22<04:41,  1.32s/it]
 15%|█▌        | 38/250 [01:22<03:35,  1.02s/it]
 16%|█▌        | 39/250 [01:26<06:15,  1.78s/it]
 16%|█▌        | 40/250 [01:27<05:29,  1.57s/it]
 16%|█▋        | 41/250 [01:28<04:17,  1.23s/it]
 17%|█▋        | 42/250 [01:28<03:14,  1.07it/s]
 17%|█▋        | 43/250 [01:29<03:24,  1.01it/s]
 18%|█▊        | 44/250 [01:30<03:21,  1.02it/s]
 18%|█▊        | 45/250 [01:30<02:41,  1.27it/s]
 18%|█▊        | 46/250 [01:31<02:31,  1.35it/s]
 19%|█▉        | 47/250 [01:31<02:15,  1.50it/s]
 19%|█▉        | 48/250 [01:33<03:04,  1.09it/s]
 20%|█▉        | 49/250 [01:35<04:15,  1.27s/it]
 20%|██        | 50/250 [01:35<03:14,  1.03it/s]
 20%|██        | 51/250 [01:35<02:25,  1.37it/s]
 21%|██        | 52/250 [01:37<03:02,  1.09it/s]
 21%|██        | 53/250 [01:38<03:03,  1.07it/s]
 22%|██▏       | 54/250 [01:43<06:51,  2.10s/it]
 22%|██▏       | 55/250 [01:43<04:56,  1.52s/it]
 22%|██▏       | 56/250 [01:44<04:45,  1.47s/it]
 23%|██▎       | 57/250 [01:45<04:06,  1.28s/it]
 23%|██▎       | 58/250 [01:45<03:13,  1.01s/it]
 24%|██▎       | 59/250 [01:46<03:17,  1.04s/it]
 24%|██▍       | 60/250 [01:47<02:42,  1.17it/s]
 24%|██▍       | 61/250 [01:47<02:24,  1.31it/s]
 25%|██▍       | 62/250 [01:48<01:53,  1.66it/s]
 25%|██▌       | 63/250 [01:49<03:02,  1.02it/s]
 26%|██▌       | 64/250 [01:50<02:27,  1.26it/s]
 26%|██▌       | 65/250 [01:52<03:38,  1.18s/it]
 26%|██▋       | 66/250 [01:53<03:48,  1.24s/it]
 27%|██▋       | 67/250 [01:56<04:53,  1.60s/it]
 27%|██▋       | 68/250 [02:01<07:55,  2.61s/it]
 28%|██▊       | 69/250 [02:02<06:36,  2.19s/it]
 28%|██▊       | 70/250 [02:02<05:03,  1.69s/it]
 28%|██▊       | 71/250 [02:09<09:46,  3.27s/it]
 29%|██▉       | 72/250 [02:12<09:17,  3.13s/it]
 29%|██▉       | 73/250 [02:12<06:42,  2.27s/it]
 30%|██▉       | 74/250 [02:13<05:11,  1.77s/it]
 30%|███       | 75/250 [02:17<07:16,  2.49s/it]
 30%|███       | 76/250 [02:18<05:36,  1.93s/it]
 31%|███       | 77/250 [02:19<04:30,  1.56s/it]
 31%|███       | 78/250 [02:20<04:15,  1.49s/it]
 32%|███▏      | 79/250 [02:20<03:14,  1.14s/it]
 32%|███▏      | 80/250 [02:21<02:56,  1.04s/it]
 32%|███▏      | 81/250 [02:22<02:43,  1.03it/s]
 33%|███▎      | 82/250 [02:23<03:14,  1.16s/it]
 34%|███▎      | 84/250 [02:29<05:14,  1.89s/it]
 34%|███▍      | 85/250 [02:31<05:18,  1.93s/it]
 34%|███▍      | 86/250 [02:32<04:33,  1.67s/it]
 35%|███▍      | 87/250 [02:32<03:29,  1.28s/it]
 35%|███▌      | 88/250 [02:34<03:43,  1.38s/it]
 36%|███▌      | 89/250 [02:34<02:49,  1.05s/it]
 36%|███▌      | 90/250 [02:34<02:14,  1.19it/s]
 36%|███▋      | 91/250 [02:38<04:29,  1.69s/it]
 37%|███▋      | 92/250 [02:38<03:18,  1.26s/it]
 37%|███▋      | 93/250 [02:40<03:56,  1.51s/it]
 38%|███▊      | 94/250 [02:42<03:45,  1.44s/it]
 38%|███▊      | 95/250 [02:44<04:19,  1.67s/it]
 38%|███▊      | 96/250 [02:46<04:16,  1.67s/it]
 39%|███▉      | 97/250 [02:50<06:01,  2.37s/it]
 39%|███▉      | 98/250 [02:52<06:24,  2.53s/it]
 40%|███▉      | 99/250 [02:53<04:35,  1.82s/it]
 40%|████      | 100/250 [02:54<04:12,  1.68s/it]
 40%|████      | 101/250 [02:54<03:03,  1.23s/it]
 41%|████      | 102/250 [02:56<03:13,  1.31s/it]
 41%|████      | 103/250 [02:58<03:50,  1.57s/it]
 42%|████▏     | 104/250 [03:01<04:59,  2.05s/it]
 42%|████▏     | 105/250 [03:03<04:48,  1.99s/it]
 42%|████▏     | 106/250 [03:04<03:53,  1.62s/it]
 43%|████▎     | 107/250 [03:05<03:43,  1.56s/it]
 43%|████▎     | 108/250 [03:05<02:43,  1.15s/it]
 44%|████▎     | 109/250 [03:11<05:43,  2.43s/it]
 44%|████▍     | 111/250 [03:12<03:48,  1.64s/it]
 45%|████▍     | 112/250 [03:15<04:26,  1.93s/it]
 45%|████▌     | 113/250 [03:16<03:39,  1.60s/it]
 46%|████▌     | 114/250 [03:17<03:47,  1.67s/it]
 46%|████▌     | 115/250 [03:19<03:25,  1.52s/it]
 46%|████▋     | 116/250 [03:21<03:43,  1.67s/it]
 47%|████▋     | 117/250 [03:22<03:19,  1.50s/it]
 47%|████▋     | 118/250 [03:23<02:57,  1.34s/it]
 48%|████▊     | 119/250 [03:23<02:36,  1.19s/it]
 48%|████▊     | 120/250 [03:24<02:01,  1.07it/s]
 48%|████▊     | 121/250 [03:25<02:22,  1.11s/it]
 49%|████▉     | 122/250 [03:26<01:51,  1.15it/s]
 49%|████▉     | 123/250 [03:26<01:25,  1.48it/s]
 50%|████▉     | 124/250 [03:26<01:13,  1.72it/s]
 50%|█████     | 125/250 [03:27<01:03,  1.96it/s]
 50%|█████     | 126/250 [03:27<01:13,  1.70it/s]
 51%|█████     | 127/250 [03:28<01:17,  1.58it/s]
 51%|█████     | 128/250 [03:29<01:37,  1.26it/s]
 52%|█████▏    | 129/250 [03:32<02:45,  1.37s/it]
 52%|█████▏    | 130/250 [03:34<03:20,  1.67s/it]
 52%|█████▏    | 131/250 [03:40<05:25,  2.74s/it]
 53%|█████▎    | 132/250 [03:40<04:15,  2.17s/it]
 53%|█████▎    | 133/250 [03:43<04:32,  2.33s/it]
 54%|█████▎    | 134/250 [03:44<03:30,  1.82s/it]
 54%|█████▍    | 135/250 [03:46<03:36,  1.89s/it]
 54%|█████▍    | 136/250 [03:47<02:57,  1.56s/it]
 55%|█████▍    | 137/250 [03:48<03:03,  1.62s/it]
 55%|█████▌    | 138/250 [03:49<02:21,  1.27s/it]
 56%|█████▌    | 139/250 [03:52<03:21,  1.82s/it]
 56%|█████▌    | 140/250 [03:54<03:13,  1.76s/it]
 56%|█████▋    | 141/250 [03:58<04:52,  2.68s/it]
 57%|█████▋    | 142/250 [04:02<05:31,  3.07s/it]
 57%|█████▋    | 143/250 [04:03<04:21,  2.45s/it]
 58%|█████▊    | 144/250 [04:05<03:53,  2.20s/it]
 58%|█████▊    | 145/250 [04:06<03:13,  1.84s/it]
 58%|█████▊    | 146/250 [04:08<03:24,  1.97s/it]
 59%|█████▉    | 147/250 [04:10<03:21,  1.95s/it]
 59%|█████▉    | 148/250 [04:13<03:33,  2.09s/it]
 60%|██████    | 150/250 [04:13<02:00,  1.20s/it]
 60%|██████    | 151/250 [04:13<01:35,  1.04it/s]
 61%|██████    | 152/250 [04:14<01:35,  1.02it/s]
 61%|██████    | 153/250 [04:17<02:16,  1.40s/it]
 62%|██████▏   | 154/250 [04:17<01:59,  1.24s/it]
 62%|██████▏   | 155/250 [04:18<01:46,  1.12s/it]
 62%|██████▏   | 156/250 [04:19<01:36,  1.02s/it]
 63%|██████▎   | 157/250 [04:20<01:40,  1.08s/it]
 63%|██████▎   | 158/250 [04:21<01:37,  1.06s/it]
 64%|██████▎   | 159/250 [04:24<02:06,  1.39s/it]
 64%|██████▍   | 160/250 [04:29<03:46,  2.51s/it]
 64%|██████▍   | 161/250 [04:29<02:55,  1.97s/it]
 65%|██████▍   | 162/250 [04:30<02:16,  1.56s/it]
 65%|██████▌   | 163/250 [04:32<02:23,  1.65s/it]
 66%|██████▌   | 164/250 [04:35<03:07,  2.18s/it]
 66%|██████▌   | 165/250 [04:35<02:16,  1.60s/it]
 66%|██████▋   | 166/250 [04:37<02:23,  1.71s/it]
 67%|██████▋   | 167/250 [04:38<01:44,  1.26s/it]
 67%|██████▋   | 168/250 [04:39<01:40,  1.22s/it]
 68%|██████▊   | 169/250 [04:41<01:55,  1.42s/it]
 68%|██████▊   | 170/250 [04:41<01:23,  1.05s/it]
 68%|██████▊   | 171/250 [04:42<01:26,  1.09s/it]
 69%|██████▉   | 172/250 [04:42<01:04,  1.22it/s]
 69%|██████▉   | 173/250 [04:43<01:13,  1.05it/s]
 70%|██████▉   | 174/250 [04:44<00:56,  1.34it/s]
 70%|███████   | 175/250 [04:44<00:51,  1.45it/s]
 70%|███████   | 176/250 [04:45<00:51,  1.43it/s]
 71%|███████   | 177/250 [04:46<00:52,  1.40it/s]
 71%|███████   | 178/250 [04:46<00:45,  1.58it/s]
 72%|███████▏  | 179/250 [04:48<01:07,  1.05it/s]
 72%|███████▏  | 180/250 [04:49<01:02,  1.12it/s]
 72%|███████▏  | 181/250 [04:49<00:51,  1.35it/s]
 73%|███████▎  | 182/250 [04:51<01:18,  1.15s/it]
 73%|███████▎  | 183/250 [04:51<00:59,  1.13it/s]
 74%|███████▎  | 184/250 [04:52<00:52,  1.25it/s]
 74%|███████▍  | 185/250 [04:53<00:50,  1.27it/s]
 74%|███████▍  | 186/250 [04:53<00:44,  1.45it/s]
 75%|███████▍  | 187/250 [04:56<01:25,  1.36s/it]
 75%|███████▌  | 188/250 [04:58<01:30,  1.46s/it]
 76%|███████▌  | 189/250 [04:58<01:12,  1.19s/it]
 76%|███████▌  | 190/250 [05:00<01:17,  1.29s/it]
 76%|███████▋  | 191/250 [05:02<01:35,  1.62s/it]
 77%|███████▋  | 192/250 [05:03<01:10,  1.22s/it]
 77%|███████▋  | 193/250 [05:05<01:27,  1.53s/it]
 78%|███████▊  | 194/250 [05:07<01:40,  1.80s/it]
 78%|███████▊  | 195/250 [05:08<01:19,  1.45s/it]
 78%|███████▊  | 196/250 [05:10<01:20,  1.50s/it]
 79%|███████▉  | 197/250 [05:10<01:01,  1.16s/it]
 79%|███████▉  | 198/250 [05:10<00:46,  1.12it/s]
 80%|███████▉  | 199/250 [05:10<00:35,  1.44it/s]
 80%|████████  | 200/250 [05:12<00:43,  1.15it/s]
 80%|████████  | 201/250 [05:15<01:17,  1.59s/it]
 81%|████████  | 202/250 [05:16<01:03,  1.32s/it]
 81%|████████  | 203/250 [05:16<00:54,  1.16s/it]
 82%|████████▏ | 204/250 [05:18<00:52,  1.15s/it]
 82%|████████▏ | 205/250 [05:19<00:52,  1.17s/it]
 82%|████████▏ | 206/250 [05:20<00:58,  1.33s/it]
 83%|████████▎ | 207/250 [05:21<00:51,  1.20s/it]
 83%|████████▎ | 208/250 [05:23<01:00,  1.45s/it]
 84%|████████▎ | 209/250 [05:25<00:56,  1.38s/it]
 84%|████████▍ | 210/250 [05:27<01:03,  1.58s/it]
 84%|████████▍ | 211/250 [05:28<00:57,  1.49s/it]
 85%|████████▍ | 212/250 [05:30<00:58,  1.54s/it]
 85%|████████▌ | 213/250 [05:32<01:09,  1.87s/it]
 86%|████████▌ | 214/250 [05:33<00:53,  1.49s/it]
 86%|████████▌ | 215/250 [05:36<01:11,  2.03s/it]
 86%|████████▋ | 216/250 [05:38<01:02,  1.83s/it]
 87%|████████▋ | 217/250 [05:39<00:54,  1.64s/it]
 87%|████████▋ | 218/250 [05:39<00:39,  1.23s/it]
 88%|████████▊ | 219/250 [05:40<00:40,  1.31s/it]
 88%|████████▊ | 220/250 [05:42<00:37,  1.25s/it]
 89%|████████▉ | 222/250 [05:44<00:33,  1.20s/it]
 89%|████████▉ | 223/250 [05:44<00:25,  1.06it/s]
 90%|████████▉ | 224/250 [05:45<00:25,  1.02it/s]
 90%|█████████ | 225/250 [05:45<00:18,  1.33it/s]
 90%|█████████ | 226/250 [05:46<00:16,  1.47it/s]
 91%|█████████ | 227/250 [05:46<00:15,  1.44it/s]
 91%|█████████ | 228/250 [05:49<00:25,  1.14s/it]
 92%|█████████▏| 229/250 [05:49<00:17,  1.17it/s]
 92%|█████████▏| 230/250 [05:51<00:24,  1.23s/it]
 92%|█████████▏| 231/250 [05:54<00:32,  1.69s/it]
 93%|█████████▎| 232/250 [05:54<00:24,  1.35s/it]
 93%|█████████▎| 233/250 [05:55<00:21,  1.24s/it]
 94%|█████████▎| 234/250 [05:57<00:21,  1.32s/it]
 94%|█████████▍| 235/250 [05:59<00:23,  1.53s/it]
 94%|█████████▍| 236/250 [06:00<00:20,  1.44s/it]
 95%|█████████▌| 238/250 [06:06<00:25,  2.13s/it]
 96%|█████████▌| 239/250 [06:08<00:24,  2.19s/it]
 96%|█████████▌| 240/250 [06:09<00:17,  1.71s/it]
 96%|█████████▋| 241/250 [06:10<00:13,  1.53s/it]
 97%|█████████▋| 242/250 [06:15<00:20,  2.55s/it]
 97%|█████████▋| 243/250 [06:16<00:15,  2.15s/it]
 98%|█████████▊| 244/250 [06:18<00:12,  2.10s/it]
 98%|█████████▊| 245/250 [06:20<00:09,  1.99s/it]
 98%|█████████▊| 246/250 [06:20<00:06,  1.57s/it]
 99%|█████████▉| 247/250 [06:22<00:04,  1.62s/it]
100%|█████████▉| 249/250 [06:23<00:01,  1.19s/it]
100%|██████████| 250/250 [06:27<00:00,  1.72s/it]
100%|██████████| 250/250 [06:27<00:00,  1.55s/it]
2025-05-15 13:33:36,458 - modnet - INFO - Loss per individual: ind 0: 0.485 	ind 1: 0.490 	ind 2: 0.505 	ind 3: 0.468 	ind 4: 0.491 	ind 5: 0.507 	ind 6: 0.483 	ind 7: 0.503 	ind 8: 0.440 	ind 9: 0.538 	ind 10: 0.458 	ind 11: 0.479 	ind 12: 0.495 	ind 13: 0.448 	ind 14: 0.473 	ind 15: 0.469 	ind 16: 0.489 	ind 17: 0.498 	ind 18: 0.471 	ind 19: 0.513 	ind 20: 0.506 	ind 21: 0.478 	ind 22: 0.490 	ind 23: 0.506 	ind 24: 0.474 	ind 25: 0.498 	ind 26: 0.477 	ind 27: 0.490 	ind 28: 0.494 	ind 29: 0.501 	ind 30: 0.533 	ind 31: 0.503 	ind 32: 0.518 	ind 33: 0.447 	ind 34: 0.464 	ind 35: 0.507 	ind 36: 0.487 	ind 37: 0.501 	ind 38: 0.477 	ind 39: 0.480 	ind 40: 0.513 	ind 41: 0.482 	ind 42: 0.511 	ind 43: 0.482 	ind 44: 0.486 	ind 45: 0.534 	ind 46: 0.487 	ind 47: 0.487 	ind 48: 0.465 	ind 49: 0.483 	
2025-05-15 13:33:36,462 - modnet - INFO - Generation number 11

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:27<1:53:57, 27.46s/it]
  1%|          | 2/250 [00:27<47:53, 11.59s/it]  
  1%|          | 3/250 [00:30<31:12,  7.58s/it]
  2%|▏         | 4/250 [00:31<19:50,  4.84s/it]
  2%|▏         | 5/250 [00:33<15:44,  3.86s/it]
  2%|▏         | 6/250 [00:34<11:20,  2.79s/it]
  3%|▎         | 7/250 [00:39<14:36,  3.60s/it]
  3%|▎         | 8/250 [00:40<11:24,  2.83s/it]
  4%|▎         | 9/250 [00:41<08:33,  2.13s/it]
  4%|▍         | 10/250 [00:41<06:21,  1.59s/it]
  4%|▍         | 11/250 [00:41<04:36,  1.16s/it]
  5%|▍         | 12/250 [00:42<03:46,  1.05it/s]
  5%|▌         | 13/250 [00:42<03:02,  1.30it/s]
  6%|▌         | 14/250 [00:42<02:18,  1.71it/s]
  6%|▋         | 16/250 [00:43<02:09,  1.81it/s]
  7%|▋         | 17/250 [00:44<01:50,  2.11it/s]
  7%|▋         | 18/250 [00:45<02:34,  1.50it/s]
  8%|▊         | 19/250 [00:47<03:45,  1.02it/s]
  8%|▊         | 20/250 [00:47<03:00,  1.27it/s]
  8%|▊         | 21/250 [00:49<04:08,  1.08s/it]
  9%|▉         | 22/250 [00:49<03:43,  1.02it/s]
  9%|▉         | 23/250 [00:51<04:41,  1.24s/it]
 10%|▉         | 24/250 [00:54<06:19,  1.68s/it]
 10%|█         | 25/250 [00:55<05:59,  1.60s/it]
 11%|█         | 27/250 [00:56<03:29,  1.07it/s]
 11%|█         | 28/250 [01:00<06:10,  1.67s/it]
 12%|█▏        | 29/250 [01:02<06:47,  1.85s/it]
 12%|█▏        | 30/250 [01:08<11:16,  3.08s/it]
 12%|█▏        | 31/250 [01:12<11:20,  3.11s/it]
 13%|█▎        | 32/250 [01:15<11:39,  3.21s/it]
 13%|█▎        | 33/250 [01:19<12:38,  3.49s/it]
 14%|█▎        | 34/250 [01:20<10:00,  2.78s/it]
 14%|█▍        | 35/250 [01:22<08:31,  2.38s/it]
 14%|█▍        | 36/250 [01:22<06:25,  1.80s/it]
 15%|█▍        | 37/250 [01:24<06:21,  1.79s/it]
 15%|█▌        | 38/250 [01:25<05:20,  1.51s/it]
 16%|█▌        | 39/250 [01:26<04:51,  1.38s/it]
 16%|█▌        | 40/250 [01:26<03:42,  1.06s/it]
 16%|█▋        | 41/250 [01:26<02:51,  1.22it/s]
 17%|█▋        | 42/250 [01:27<02:10,  1.60it/s]
 17%|█▋        | 43/250 [01:27<02:01,  1.71it/s]
 18%|█▊        | 44/250 [01:31<05:24,  1.57s/it]
 18%|█▊        | 45/250 [01:33<06:17,  1.84s/it]
 18%|█▊        | 46/250 [01:34<05:07,  1.51s/it]
 19%|█▉        | 47/250 [01:35<04:38,  1.37s/it]
 19%|█▉        | 48/250 [01:37<05:12,  1.55s/it]
 20%|█▉        | 49/250 [01:37<03:55,  1.17s/it]
 20%|██        | 50/250 [01:38<03:33,  1.07s/it]
 20%|██        | 51/250 [01:43<07:31,  2.27s/it]
 21%|██        | 52/250 [01:44<05:44,  1.74s/it]
 21%|██        | 53/250 [01:47<07:13,  2.20s/it]
 22%|██▏       | 54/250 [01:47<05:20,  1.64s/it]
 22%|██▏       | 55/250 [01:50<06:35,  2.03s/it]
 22%|██▏       | 56/250 [01:53<07:18,  2.26s/it]
 23%|██▎       | 57/250 [01:57<09:13,  2.87s/it]
 23%|██▎       | 58/250 [01:59<07:25,  2.32s/it]
 24%|██▎       | 59/250 [01:59<05:55,  1.86s/it]
 24%|██▍       | 60/250 [02:00<04:25,  1.40s/it]
 24%|██▍       | 61/250 [02:00<03:52,  1.23s/it]
 25%|██▍       | 62/250 [02:01<03:26,  1.10s/it]
 25%|██▌       | 63/250 [02:01<02:34,  1.21it/s]
 26%|██▌       | 64/250 [02:02<01:59,  1.56it/s]
 26%|██▌       | 65/250 [02:02<01:58,  1.56it/s]
 27%|██▋       | 67/250 [02:04<02:23,  1.28it/s]
 27%|██▋       | 68/250 [02:05<02:18,  1.31it/s]
 28%|██▊       | 70/250 [02:06<02:08,  1.41it/s]
 28%|██▊       | 71/250 [02:07<02:05,  1.42it/s]
 29%|██▉       | 72/250 [02:08<02:15,  1.32it/s]
 29%|██▉       | 73/250 [02:08<02:09,  1.37it/s]
 30%|██▉       | 74/250 [02:12<04:03,  1.38s/it]
 30%|███       | 75/250 [02:12<03:21,  1.15s/it]
 30%|███       | 76/250 [02:14<04:17,  1.48s/it]
 31%|███       | 77/250 [02:15<03:30,  1.22s/it]
 31%|███       | 78/250 [02:17<03:56,  1.38s/it]
 32%|███▏      | 79/250 [02:17<02:56,  1.03s/it]
 32%|███▏      | 80/250 [02:22<06:29,  2.29s/it]
 32%|███▏      | 81/250 [02:25<06:26,  2.28s/it]
 33%|███▎      | 82/250 [02:27<06:21,  2.27s/it]
 33%|███▎      | 83/250 [02:27<04:46,  1.72s/it]
 34%|███▎      | 84/250 [02:28<04:12,  1.52s/it]
 34%|███▍      | 85/250 [02:36<09:15,  3.37s/it]
 34%|███▍      | 86/250 [02:38<08:20,  3.05s/it]
 35%|███▍      | 87/250 [02:39<06:43,  2.48s/it]
 35%|███▌      | 88/250 [02:40<05:13,  1.93s/it]
 36%|███▌      | 89/250 [02:40<03:47,  1.41s/it]
 36%|███▌      | 90/250 [02:41<02:54,  1.09s/it]
 36%|███▋      | 91/250 [02:41<02:42,  1.02s/it]
 37%|███▋      | 92/250 [02:42<02:03,  1.28it/s]
 37%|███▋      | 93/250 [02:44<03:10,  1.21s/it]
 38%|███▊      | 94/250 [02:45<03:28,  1.34s/it]
 38%|███▊      | 95/250 [02:47<03:21,  1.30s/it]
 38%|███▊      | 96/250 [02:48<02:58,  1.16s/it]
 39%|███▉      | 97/250 [02:48<02:45,  1.08s/it]
 39%|███▉      | 98/250 [02:50<03:19,  1.31s/it]
 40%|███▉      | 99/250 [02:51<03:07,  1.24s/it]
 40%|████      | 100/250 [02:54<03:49,  1.53s/it]
 40%|████      | 101/250 [02:55<03:23,  1.37s/it]
 41%|████      | 102/250 [02:55<02:46,  1.12s/it]
 41%|████      | 103/250 [02:55<02:05,  1.17it/s]
 42%|████▏     | 104/250 [02:57<02:56,  1.21s/it]
 42%|████▏     | 105/250 [03:04<06:51,  2.84s/it]
 42%|████▏     | 106/250 [03:06<05:50,  2.44s/it]
 43%|████▎     | 107/250 [03:06<04:38,  1.95s/it]
 43%|████▎     | 108/250 [03:07<03:30,  1.48s/it]
 44%|████▎     | 109/250 [03:11<05:09,  2.19s/it]
 44%|████▍     | 110/250 [03:13<05:34,  2.39s/it]
 44%|████▍     | 111/250 [03:14<04:00,  1.73s/it]
 45%|████▍     | 112/250 [03:25<10:19,  4.49s/it]
 45%|████▌     | 113/250 [03:25<07:30,  3.29s/it]
 46%|████▌     | 114/250 [03:26<05:45,  2.54s/it]
 46%|████▌     | 115/250 [03:26<04:25,  1.97s/it]
 46%|████▋     | 116/250 [03:27<03:39,  1.63s/it]
 47%|████▋     | 117/250 [03:30<04:37,  2.08s/it]
 47%|████▋     | 118/250 [03:31<03:19,  1.51s/it]
 48%|████▊     | 119/250 [03:31<02:28,  1.14s/it]
 48%|████▊     | 120/250 [03:31<01:55,  1.13it/s]
 48%|████▊     | 121/250 [03:36<04:25,  2.06s/it]
 49%|████▉     | 122/250 [03:36<03:13,  1.51s/it]
 50%|████▉     | 124/250 [03:36<01:49,  1.15it/s]
 50%|█████     | 125/250 [03:37<01:36,  1.30it/s]
 50%|█████     | 126/250 [03:37<01:15,  1.65it/s]
 51%|█████     | 127/250 [03:38<01:17,  1.59it/s]
 51%|█████     | 128/250 [03:40<01:55,  1.05it/s]
 52%|█████▏    | 129/250 [03:40<01:32,  1.31it/s]
 52%|█████▏    | 130/250 [03:43<02:41,  1.34s/it]
 52%|█████▏    | 131/250 [03:43<02:18,  1.16s/it]
 53%|█████▎    | 132/250 [03:43<01:42,  1.15it/s]
 53%|█████▎    | 133/250 [03:44<01:39,  1.18it/s]
 54%|█████▎    | 134/250 [03:45<01:37,  1.19it/s]
 54%|█████▍    | 135/250 [03:46<01:35,  1.21it/s]
 54%|█████▍    | 136/250 [03:47<02:00,  1.05s/it]
 55%|█████▍    | 137/250 [03:48<01:30,  1.25it/s]
 55%|█████▌    | 138/250 [03:50<02:18,  1.24s/it]
 56%|█████▌    | 139/250 [03:52<02:46,  1.50s/it]
 56%|█████▌    | 140/250 [03:55<03:21,  1.83s/it]
 56%|█████▋    | 141/250 [03:55<02:43,  1.50s/it]
 57%|█████▋    | 142/250 [03:56<02:14,  1.24s/it]
 57%|█████▋    | 143/250 [03:56<01:44,  1.02it/s]
 58%|█████▊    | 144/250 [04:01<03:26,  1.95s/it]
 58%|█████▊    | 145/250 [04:01<02:36,  1.49s/it]
 58%|█████▊    | 146/250 [04:02<02:22,  1.37s/it]
 59%|█████▉    | 147/250 [04:02<01:44,  1.02s/it]
 59%|█████▉    | 148/250 [04:03<01:22,  1.23it/s]
 60%|█████▉    | 149/250 [04:04<01:40,  1.00it/s]
 60%|██████    | 150/250 [04:05<01:34,  1.06it/s]
 60%|██████    | 151/250 [04:06<01:41,  1.02s/it]
 61%|██████    | 152/250 [04:07<01:38,  1.00s/it]
 61%|██████    | 153/250 [04:07<01:13,  1.31it/s]
 62%|██████▏   | 154/250 [04:08<01:12,  1.33it/s]
 62%|██████▏   | 155/250 [04:08<00:53,  1.76it/s]
 62%|██████▏   | 156/250 [04:12<02:14,  1.43s/it]
 63%|██████▎   | 157/250 [04:13<02:11,  1.41s/it]
 63%|██████▎   | 158/250 [04:14<02:01,  1.32s/it]
 64%|██████▎   | 159/250 [04:20<04:03,  2.68s/it]
 64%|██████▍   | 160/250 [04:20<02:56,  1.96s/it]
 64%|██████▍   | 161/250 [04:21<02:25,  1.63s/it]
 65%|██████▍   | 162/250 [04:22<02:04,  1.42s/it]
 65%|██████▌   | 163/250 [04:23<02:05,  1.44s/it]
 66%|██████▌   | 164/250 [04:25<02:13,  1.55s/it]
 66%|██████▌   | 165/250 [04:27<02:11,  1.54s/it]
 66%|██████▋   | 166/250 [04:28<02:00,  1.44s/it]
 67%|██████▋   | 167/250 [04:29<01:45,  1.27s/it]
 67%|██████▋   | 168/250 [04:30<01:36,  1.18s/it]
 68%|██████▊   | 169/250 [04:31<01:23,  1.04s/it]
 68%|██████▊   | 170/250 [04:31<01:09,  1.15it/s]
 68%|██████▊   | 171/250 [04:35<02:22,  1.80s/it]
 69%|██████▉   | 172/250 [04:36<01:55,  1.48s/it]
 70%|██████▉   | 174/250 [04:37<01:30,  1.20s/it]
 70%|███████   | 175/250 [04:40<02:02,  1.63s/it]
 70%|███████   | 176/250 [04:43<02:20,  1.90s/it]
 71%|███████   | 177/250 [04:47<03:02,  2.49s/it]
 71%|███████   | 178/250 [04:49<02:55,  2.44s/it]
 72%|███████▏  | 179/250 [04:53<03:24,  2.88s/it]
 72%|███████▏  | 180/250 [04:56<03:06,  2.67s/it]
 72%|███████▏  | 181/250 [04:57<02:41,  2.33s/it]
 73%|███████▎  | 182/250 [04:59<02:33,  2.25s/it]
 73%|███████▎  | 183/250 [05:00<01:58,  1.76s/it]
 74%|███████▎  | 184/250 [05:01<01:52,  1.70s/it]
 74%|███████▍  | 185/250 [05:02<01:26,  1.33s/it]
 74%|███████▍  | 186/250 [05:05<02:00,  1.88s/it]
 75%|███████▍  | 187/250 [05:05<01:24,  1.35s/it]
 75%|███████▌  | 188/250 [05:06<01:24,  1.36s/it]
 76%|███████▌  | 189/250 [05:07<01:17,  1.27s/it]
 76%|███████▌  | 190/250 [05:08<01:03,  1.05s/it]
 76%|███████▋  | 191/250 [05:08<00:46,  1.27it/s]
 77%|███████▋  | 193/250 [05:09<00:29,  1.94it/s]
 78%|███████▊  | 194/250 [05:12<01:11,  1.28s/it]
 78%|███████▊  | 195/250 [05:16<01:46,  1.94s/it]
 78%|███████▊  | 196/250 [05:17<01:27,  1.62s/it]
 79%|███████▉  | 197/250 [05:19<01:32,  1.74s/it]
 79%|███████▉  | 198/250 [05:20<01:27,  1.69s/it]
 80%|███████▉  | 199/250 [05:26<02:17,  2.69s/it]
 80%|████████  | 200/250 [05:27<01:50,  2.22s/it]
 80%|████████  | 201/250 [05:27<01:18,  1.61s/it]
 81%|████████  | 203/250 [05:28<00:49,  1.05s/it]
 82%|████████▏ | 204/250 [05:28<00:38,  1.20it/s]
 82%|████████▏ | 205/250 [05:31<01:05,  1.45s/it]
 82%|████████▏ | 206/250 [05:32<00:58,  1.33s/it]
 83%|████████▎ | 207/250 [05:33<00:53,  1.24s/it]
 83%|████████▎ | 208/250 [05:33<00:41,  1.01it/s]
 84%|████████▎ | 209/250 [05:38<01:29,  2.18s/it]
 84%|████████▍ | 210/250 [05:39<01:12,  1.82s/it]
 84%|████████▍ | 211/250 [05:40<01:02,  1.62s/it]
 85%|████████▍ | 212/250 [05:43<01:11,  1.88s/it]
 85%|████████▌ | 213/250 [05:44<00:58,  1.58s/it]
 86%|████████▌ | 214/250 [05:45<00:51,  1.42s/it]
 86%|████████▌ | 215/250 [05:45<00:37,  1.07s/it]
 87%|████████▋ | 217/250 [05:45<00:21,  1.53it/s]
 87%|████████▋ | 218/250 [05:46<00:17,  1.84it/s]
 88%|████████▊ | 219/250 [05:46<00:16,  1.94it/s]
 88%|████████▊ | 220/250 [05:47<00:14,  2.03it/s]
 88%|████████▊ | 221/250 [05:47<00:13,  2.10it/s]
 89%|████████▉ | 222/250 [05:48<00:20,  1.34it/s]
 89%|████████▉ | 223/250 [05:51<00:31,  1.17s/it]
 90%|████████▉ | 224/250 [05:51<00:22,  1.17it/s]
 90%|█████████ | 225/250 [05:52<00:25,  1.02s/it]
 90%|█████████ | 226/250 [05:55<00:34,  1.43s/it]
 91%|█████████ | 227/250 [05:55<00:29,  1.27s/it]
 91%|█████████ | 228/250 [05:58<00:34,  1.58s/it]
 92%|█████████▏| 229/250 [05:59<00:31,  1.49s/it]
 92%|█████████▏| 230/250 [06:00<00:24,  1.22s/it]
 92%|█████████▏| 231/250 [06:01<00:23,  1.25s/it]
 93%|█████████▎| 232/250 [06:02<00:23,  1.32s/it]
 93%|█████████▎| 233/250 [06:03<00:20,  1.21s/it]
 94%|█████████▎| 234/250 [06:05<00:19,  1.24s/it]
 94%|█████████▍| 235/250 [06:06<00:19,  1.27s/it]
 94%|█████████▍| 236/250 [06:08<00:19,  1.38s/it]
 95%|█████████▍| 237/250 [06:09<00:17,  1.34s/it]
 95%|█████████▌| 238/250 [06:10<00:14,  1.23s/it]
 96%|█████████▌| 239/250 [06:12<00:15,  1.42s/it]
 96%|█████████▌| 240/250 [06:16<00:22,  2.29s/it]
 97%|█████████▋| 242/250 [06:16<00:10,  1.29s/it]
 97%|█████████▋| 243/250 [06:18<00:10,  1.50s/it]
 98%|█████████▊| 244/250 [06:19<00:07,  1.32s/it]
 98%|█████████▊| 245/250 [06:22<00:08,  1.62s/it]
 98%|█████████▊| 246/250 [06:26<00:09,  2.41s/it]
 99%|█████████▉| 247/250 [06:29<00:07,  2.57s/it]
 99%|█████████▉| 248/250 [06:32<00:05,  2.59s/it]
100%|█████████▉| 249/250 [06:35<00:02,  2.73s/it]
100%|██████████| 250/250 [06:36<00:00,  2.27s/it]
100%|██████████| 250/250 [06:36<00:00,  1.59s/it]
2025-05-15 13:40:12,970 - modnet - INFO - Loss per individual: ind 0: 0.509 	ind 1: 0.475 	ind 2: 0.483 	ind 3: 0.480 	ind 4: 0.514 	ind 5: 0.502 	ind 6: 0.469 	ind 7: 0.491 	ind 8: 0.495 	ind 9: 0.633 	ind 10: 0.500 	ind 11: 0.514 	ind 12: 0.480 	ind 13: 0.502 	ind 14: 0.521 	ind 15: 0.515 	ind 16: 0.472 	ind 17: 0.455 	ind 18: 0.518 	ind 19: 0.487 	ind 20: 0.485 	ind 21: 0.516 	ind 22: 0.543 	ind 23: 0.473 	ind 24: 0.486 	ind 25: 0.453 	ind 26: 0.484 	ind 27: 0.508 	ind 28: 0.462 	ind 29: 0.482 	ind 30: 0.438 	ind 31: 0.504 	ind 32: 0.494 	ind 33: 0.650 	ind 34: 0.487 	ind 35: 0.512 	ind 36: 0.477 	ind 37: 0.430 	ind 38: 0.538 	ind 39: 0.503 	ind 40: 0.443 	ind 41: 0.502 	ind 42: 0.604 	ind 43: 0.466 	ind 44: 0.482 	ind 45: 0.518 	ind 46: 0.488 	ind 47: 0.481 	ind 48: 0.465 	ind 49: 0.518 	
2025-05-15 13:40:12,974 - modnet - INFO - Generation number 12

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:20<1:24:03, 20.26s/it]
  1%|          | 2/250 [00:24<45:24, 10.99s/it]  
  1%|          | 3/250 [00:25<25:23,  6.17s/it]
  2%|▏         | 4/250 [00:25<15:40,  3.82s/it]
  2%|▏         | 5/250 [00:28<14:10,  3.47s/it]
  2%|▏         | 6/250 [00:28<09:57,  2.45s/it]
  3%|▎         | 7/250 [00:29<08:08,  2.01s/it]
  3%|▎         | 8/250 [00:29<05:41,  1.41s/it]
  4%|▎         | 9/250 [00:31<05:57,  1.48s/it]
  4%|▍         | 10/250 [00:31<04:27,  1.12s/it]
  4%|▍         | 11/250 [00:32<03:13,  1.23it/s]
  5%|▍         | 12/250 [00:32<02:22,  1.67it/s]
  5%|▌         | 13/250 [00:33<02:41,  1.47it/s]
  6%|▌         | 14/250 [00:33<02:16,  1.73it/s]
  6%|▌         | 15/250 [00:35<04:37,  1.18s/it]
  6%|▋         | 16/250 [00:39<07:31,  1.93s/it]
  7%|▋         | 18/250 [00:40<05:16,  1.36s/it]
  8%|▊         | 19/250 [00:42<05:11,  1.35s/it]
  8%|▊         | 20/250 [00:43<04:33,  1.19s/it]
  8%|▊         | 21/250 [00:45<06:03,  1.59s/it]
  9%|▉         | 22/250 [00:46<04:53,  1.29s/it]
  9%|▉         | 23/250 [00:46<03:57,  1.05s/it]
 10%|▉         | 24/250 [00:48<04:50,  1.28s/it]
 10%|█         | 25/250 [00:48<03:54,  1.04s/it]
 10%|█         | 26/250 [00:51<05:10,  1.39s/it]
 11%|█         | 27/250 [00:52<04:32,  1.22s/it]
 11%|█         | 28/250 [00:54<05:29,  1.49s/it]
 12%|█▏        | 29/250 [00:56<06:48,  1.85s/it]
 12%|█▏        | 30/250 [00:56<04:53,  1.33s/it]
 12%|█▏        | 31/250 [00:57<03:43,  1.02s/it]
 13%|█▎        | 32/250 [00:57<03:15,  1.12it/s]
 13%|█▎        | 33/250 [00:58<03:15,  1.11it/s]
 14%|█▎        | 34/250 [01:00<04:07,  1.14s/it]
 14%|█▍        | 35/250 [01:03<06:06,  1.70s/it]
 14%|█▍        | 36/250 [01:03<04:27,  1.25s/it]
 15%|█▍        | 37/250 [01:03<03:19,  1.07it/s]
 15%|█▌        | 38/250 [01:05<04:21,  1.23s/it]
 16%|█▌        | 39/250 [01:06<03:14,  1.08it/s]
 16%|█▌        | 40/250 [01:06<02:33,  1.36it/s]
 16%|█▋        | 41/250 [01:09<05:34,  1.60s/it]
 17%|█▋        | 42/250 [01:10<04:40,  1.35s/it]
 17%|█▋        | 43/250 [01:11<04:13,  1.23s/it]
 18%|█▊        | 44/250 [01:11<03:10,  1.08it/s]
 18%|█▊        | 45/250 [01:12<02:33,  1.34it/s]
 18%|█▊        | 46/250 [01:12<01:58,  1.73it/s]
 19%|█▉        | 47/250 [01:12<01:54,  1.77it/s]
 20%|█▉        | 49/250 [01:13<01:10,  2.85it/s]
 20%|██        | 50/250 [01:13<01:11,  2.80it/s]
 20%|██        | 51/250 [01:14<01:54,  1.75it/s]
 21%|██        | 52/250 [01:14<01:32,  2.14it/s]
 21%|██        | 53/250 [01:15<01:58,  1.66it/s]
 22%|██▏       | 54/250 [01:16<01:46,  1.84it/s]
 22%|██▏       | 55/250 [01:16<01:38,  1.97it/s]
 22%|██▏       | 56/250 [01:18<03:03,  1.05it/s]
 23%|██▎       | 57/250 [01:21<04:24,  1.37s/it]
 23%|██▎       | 58/250 [01:21<03:53,  1.22s/it]
 24%|██▎       | 59/250 [01:22<03:32,  1.11s/it]
 24%|██▍       | 60/250 [01:23<02:52,  1.10it/s]
 24%|██▍       | 61/250 [01:25<04:20,  1.38s/it]
 25%|██▍       | 62/250 [01:26<03:28,  1.11s/it]
 25%|██▌       | 63/250 [01:26<02:53,  1.08it/s]
 26%|██▌       | 64/250 [01:28<03:22,  1.09s/it]
 26%|██▌       | 65/250 [01:31<05:16,  1.71s/it]
 26%|██▋       | 66/250 [01:31<03:54,  1.27s/it]
 27%|██▋       | 67/250 [01:32<03:42,  1.22s/it]
 27%|██▋       | 68/250 [01:33<03:00,  1.01it/s]
 28%|██▊       | 69/250 [01:36<05:12,  1.73s/it]
 28%|██▊       | 70/250 [01:36<04:02,  1.35s/it]
 28%|██▊       | 71/250 [01:37<02:57,  1.01it/s]
 29%|██▉       | 72/250 [01:37<02:31,  1.18it/s]
 29%|██▉       | 73/250 [01:38<02:47,  1.06it/s]
 30%|███       | 75/250 [01:39<01:44,  1.67it/s]
 30%|███       | 76/250 [01:40<02:32,  1.14it/s]
 31%|███       | 77/250 [01:43<04:01,  1.39s/it]
 31%|███       | 78/250 [01:46<04:45,  1.66s/it]
 32%|███▏      | 79/250 [01:46<03:58,  1.40s/it]
 32%|███▏      | 80/250 [01:47<03:38,  1.28s/it]
 32%|███▏      | 81/250 [01:49<04:08,  1.47s/it]
 33%|███▎      | 82/250 [01:51<04:35,  1.64s/it]
 33%|███▎      | 83/250 [01:53<04:22,  1.57s/it]
 34%|███▎      | 84/250 [01:54<04:28,  1.62s/it]
 34%|███▍      | 85/250 [01:55<03:42,  1.35s/it]
 34%|███▍      | 86/250 [01:56<02:59,  1.09s/it]
 35%|███▍      | 87/250 [01:56<02:18,  1.18it/s]
 35%|███▌      | 88/250 [01:57<02:16,  1.19it/s]
 36%|███▌      | 89/250 [01:57<01:43,  1.56it/s]
 36%|███▌      | 90/250 [01:57<01:31,  1.75it/s]
 36%|███▋      | 91/250 [01:58<01:22,  1.92it/s]
 37%|███▋      | 92/250 [01:59<01:42,  1.54it/s]
 37%|███▋      | 93/250 [01:59<01:34,  1.65it/s]
 38%|███▊      | 94/250 [02:00<01:46,  1.46it/s]
 38%|███▊      | 95/250 [02:00<01:23,  1.85it/s]
 38%|███▊      | 96/250 [02:00<01:06,  2.32it/s]
 39%|███▉      | 97/250 [02:03<02:31,  1.01it/s]
 40%|███▉      | 99/250 [02:03<01:27,  1.72it/s]
 40%|████      | 100/250 [02:04<01:30,  1.65it/s]
 40%|████      | 101/250 [02:05<02:06,  1.18it/s]
 41%|████      | 102/250 [02:05<01:38,  1.51it/s]
 41%|████      | 103/250 [02:07<02:09,  1.13it/s]
 42%|████▏     | 104/250 [02:10<03:59,  1.64s/it]
 42%|████▏     | 105/250 [02:11<03:32,  1.46s/it]
 42%|████▏     | 106/250 [02:14<04:21,  1.82s/it]
 43%|████▎     | 107/250 [02:20<07:31,  3.16s/it]
 43%|████▎     | 108/250 [02:21<05:33,  2.35s/it]
 44%|████▎     | 109/250 [02:21<04:04,  1.74s/it]
 44%|████▍     | 110/250 [02:22<03:08,  1.34s/it]
 44%|████▍     | 111/250 [02:23<03:04,  1.33s/it]
 45%|████▍     | 112/250 [02:30<07:07,  3.10s/it]
 45%|████▌     | 113/250 [02:31<05:16,  2.31s/it]
 46%|████▌     | 114/250 [02:31<03:51,  1.70s/it]
 46%|████▌     | 115/250 [02:33<03:58,  1.77s/it]
 46%|████▋     | 116/250 [02:35<04:00,  1.79s/it]
 47%|████▋     | 117/250 [02:40<06:10,  2.79s/it]
 47%|████▋     | 118/250 [02:41<05:20,  2.43s/it]
 48%|████▊     | 119/250 [02:42<04:13,  1.93s/it]
 48%|████▊     | 120/250 [02:45<05:07,  2.37s/it]
 48%|████▊     | 121/250 [02:46<04:02,  1.88s/it]
 49%|████▉     | 122/250 [02:47<03:39,  1.71s/it]
 49%|████▉     | 123/250 [02:49<03:14,  1.53s/it]
 50%|████▉     | 124/250 [02:50<03:14,  1.55s/it]
 50%|█████     | 125/250 [02:55<05:11,  2.49s/it]
 50%|█████     | 126/250 [02:55<03:53,  1.88s/it]
 51%|█████     | 127/250 [02:56<03:15,  1.59s/it]
 51%|█████     | 128/250 [02:59<03:50,  1.89s/it]
 52%|█████▏    | 129/250 [03:02<04:35,  2.28s/it]
 52%|█████▏    | 130/250 [03:03<03:39,  1.83s/it]
 52%|█████▏    | 131/250 [03:05<03:46,  1.90s/it]
 53%|█████▎    | 132/250 [03:05<02:49,  1.43s/it]
 53%|█████▎    | 133/250 [03:07<02:45,  1.41s/it]
 54%|█████▎    | 134/250 [03:07<02:08,  1.11s/it]
 54%|█████▍    | 135/250 [03:08<01:53,  1.01it/s]
 54%|█████▍    | 136/250 [03:08<01:27,  1.30it/s]
 55%|█████▍    | 137/250 [03:09<01:49,  1.03it/s]
 55%|█████▌    | 138/250 [03:12<02:48,  1.50s/it]
 56%|█████▌    | 139/250 [03:14<03:04,  1.67s/it]
 56%|█████▌    | 140/250 [03:15<02:40,  1.46s/it]
 56%|█████▋    | 141/250 [03:23<06:11,  3.41s/it]
 57%|█████▋    | 142/250 [03:24<04:34,  2.54s/it]
 57%|█████▋    | 143/250 [03:25<03:54,  2.19s/it]
 58%|█████▊    | 144/250 [03:27<03:47,  2.15s/it]
 58%|█████▊    | 145/250 [03:30<04:14,  2.43s/it]
 58%|█████▊    | 146/250 [03:33<04:40,  2.69s/it]
 59%|█████▉    | 147/250 [03:35<04:17,  2.50s/it]
 59%|█████▉    | 148/250 [03:36<03:10,  1.87s/it]
 60%|█████▉    | 149/250 [03:36<02:27,  1.46s/it]
 60%|██████    | 150/250 [03:37<01:54,  1.15s/it]
 60%|██████    | 151/250 [03:37<01:39,  1.01s/it]
 61%|██████    | 152/250 [03:43<04:04,  2.49s/it]
 61%|██████    | 153/250 [03:46<04:00,  2.48s/it]
 62%|██████▏   | 154/250 [03:51<05:24,  3.38s/it]
 62%|██████▏   | 155/250 [03:52<04:00,  2.53s/it]
 62%|██████▏   | 156/250 [03:52<02:58,  1.89s/it]
 63%|██████▎   | 157/250 [03:54<02:42,  1.75s/it]
 63%|██████▎   | 158/250 [03:54<02:13,  1.45s/it]
 64%|██████▎   | 159/250 [03:57<02:42,  1.79s/it]
 64%|██████▍   | 160/250 [03:57<01:57,  1.31s/it]
 64%|██████▍   | 161/250 [03:58<01:39,  1.12s/it]
 65%|██████▍   | 162/250 [04:01<02:31,  1.72s/it]
 65%|██████▌   | 163/250 [04:02<02:15,  1.56s/it]
 66%|██████▌   | 164/250 [04:04<02:09,  1.51s/it]
 66%|██████▋   | 166/250 [04:04<01:12,  1.16it/s]
 67%|██████▋   | 167/250 [04:04<01:03,  1.31it/s]
 67%|██████▋   | 168/250 [04:09<02:32,  1.86s/it]
 68%|██████▊   | 169/250 [04:13<03:00,  2.23s/it]
 68%|██████▊   | 170/250 [04:13<02:19,  1.75s/it]
 68%|██████▊   | 171/250 [04:13<01:43,  1.30s/it]
 69%|██████▉   | 172/250 [04:16<02:14,  1.72s/it]
 69%|██████▉   | 173/250 [04:16<01:43,  1.34s/it]
 70%|███████   | 175/250 [04:17<00:59,  1.26it/s]
 70%|███████   | 176/250 [04:18<01:00,  1.22it/s]
 71%|███████   | 177/250 [04:19<01:06,  1.10it/s]
 71%|███████   | 178/250 [04:19<01:03,  1.14it/s]
 72%|███████▏  | 179/250 [04:25<02:24,  2.03s/it]
 72%|███████▏  | 180/250 [04:25<01:48,  1.55s/it]
 72%|███████▏  | 181/250 [04:31<03:16,  2.85s/it]
 73%|███████▎  | 182/250 [04:32<02:42,  2.38s/it]
 73%|███████▎  | 183/250 [04:33<02:13,  1.99s/it]
 74%|███████▎  | 184/250 [04:37<02:52,  2.61s/it]
 74%|███████▍  | 185/250 [04:38<02:15,  2.08s/it]
 74%|███████▍  | 186/250 [04:40<02:00,  1.88s/it]
 75%|███████▍  | 187/250 [04:44<02:54,  2.77s/it]
 75%|███████▌  | 188/250 [04:46<02:31,  2.44s/it]
 76%|███████▌  | 189/250 [04:48<02:19,  2.29s/it]
 76%|███████▌  | 190/250 [04:56<04:06,  4.10s/it]
 76%|███████▋  | 191/250 [04:58<03:17,  3.35s/it]
 77%|███████▋  | 192/250 [05:02<03:19,  3.43s/it]
 77%|███████▋  | 193/250 [05:02<02:20,  2.47s/it]
 78%|███████▊  | 194/250 [05:02<01:38,  1.77s/it]
 78%|███████▊  | 195/250 [05:04<01:41,  1.85s/it]
 78%|███████▊  | 196/250 [05:04<01:12,  1.35s/it]
 79%|███████▉  | 197/250 [05:05<01:05,  1.23s/it]
 79%|███████▉  | 198/250 [05:05<00:47,  1.09it/s]
 80%|███████▉  | 199/250 [05:06<00:49,  1.02it/s]
 80%|████████  | 200/250 [05:08<00:56,  1.14s/it]
 80%|████████  | 201/250 [05:08<00:42,  1.16it/s]
 81%|████████  | 202/250 [05:10<00:51,  1.08s/it]
 81%|████████  | 203/250 [05:12<01:10,  1.49s/it]
 82%|████████▏ | 204/250 [05:14<01:14,  1.61s/it]
 82%|████████▏ | 205/250 [05:17<01:37,  2.16s/it]
 82%|████████▏ | 206/250 [05:18<01:12,  1.66s/it]
 83%|████████▎ | 207/250 [05:20<01:14,  1.74s/it]
 83%|████████▎ | 208/250 [05:21<00:59,  1.41s/it]
 84%|████████▎ | 209/250 [05:23<01:12,  1.78s/it]
 84%|████████▍ | 210/250 [05:25<01:08,  1.70s/it]
 84%|████████▍ | 211/250 [05:26<00:59,  1.54s/it]
 85%|████████▍ | 212/250 [05:26<00:48,  1.28s/it]
 85%|████████▌ | 213/250 [05:29<00:59,  1.61s/it]
 86%|████████▌ | 214/250 [05:33<01:27,  2.43s/it]
 86%|████████▌ | 215/250 [05:34<01:06,  1.91s/it]
 86%|████████▋ | 216/250 [05:34<00:50,  1.48s/it]
 87%|████████▋ | 217/250 [05:36<00:52,  1.59s/it]
 87%|████████▋ | 218/250 [05:38<00:49,  1.55s/it]
 88%|████████▊ | 219/250 [05:40<00:51,  1.66s/it]
 88%|████████▊ | 220/250 [05:40<00:40,  1.35s/it]
 88%|████████▊ | 221/250 [05:41<00:31,  1.09s/it]
 89%|████████▉ | 222/250 [05:41<00:25,  1.10it/s]
 89%|████████▉ | 223/250 [05:43<00:30,  1.13s/it]
 90%|████████▉ | 224/250 [05:44<00:31,  1.20s/it]
 90%|█████████ | 225/250 [05:44<00:22,  1.11it/s]
 90%|█████████ | 226/250 [05:46<00:27,  1.14s/it]
 91%|█████████ | 227/250 [05:49<00:35,  1.53s/it]
 91%|█████████ | 228/250 [05:50<00:31,  1.44s/it]
 92%|█████████▏| 229/250 [05:50<00:25,  1.20s/it]
 92%|█████████▏| 230/250 [05:51<00:21,  1.08s/it]
 92%|█████████▏| 231/250 [05:51<00:15,  1.22it/s]
 93%|█████████▎| 232/250 [05:52<00:11,  1.57it/s]
 93%|█████████▎| 233/250 [05:52<00:09,  1.85it/s]
 94%|█████████▎| 234/250 [05:55<00:18,  1.17s/it]
 94%|█████████▍| 235/250 [05:55<00:14,  1.05it/s]
 94%|█████████▍| 236/250 [05:55<00:10,  1.30it/s]
 95%|█████████▍| 237/250 [05:56<00:07,  1.66it/s]
 95%|█████████▌| 238/250 [05:56<00:06,  1.84it/s]
 96%|█████████▌| 240/250 [05:58<00:06,  1.47it/s]
 96%|█████████▋| 241/250 [05:59<00:07,  1.28it/s]
 97%|█████████▋| 242/250 [06:00<00:07,  1.04it/s]
 97%|█████████▋| 243/250 [06:01<00:06,  1.01it/s]
 98%|█████████▊| 244/250 [06:03<00:06,  1.08s/it]
 98%|█████████▊| 245/250 [06:03<00:04,  1.10it/s]
 98%|█████████▊| 246/250 [06:04<00:03,  1.17it/s]
 99%|█████████▉| 247/250 [06:07<00:04,  1.51s/it]
 99%|█████████▉| 248/250 [06:09<00:03,  1.61s/it]
100%|█████████▉| 249/250 [06:11<00:01,  1.87s/it]
100%|██████████| 250/250 [06:18<00:00,  3.32s/it]
100%|██████████| 250/250 [06:18<00:00,  1.51s/it]
2025-05-15 13:46:31,595 - modnet - INFO - Loss per individual: ind 0: 0.490 	ind 1: 0.487 	ind 2: 0.536 	ind 3: 0.446 	ind 4: 0.523 	ind 5: 0.444 	ind 6: 0.537 	ind 7: 0.447 	ind 8: 0.475 	ind 9: 2.798 	ind 10: 0.515 	ind 11: 0.525 	ind 12: 0.459 	ind 13: 0.491 	ind 14: 0.647 	ind 15: 0.444 	ind 16: 0.521 	ind 17: 0.503 	ind 18: 0.532 	ind 19: 0.512 	ind 20: 0.495 	ind 21: 0.486 	ind 22: 0.476 	ind 23: 0.495 	ind 24: 0.562 	ind 25: 0.481 	ind 26: 0.505 	ind 27: 0.791 	ind 28: 0.495 	ind 29: 0.456 	ind 30: 0.505 	ind 31: 0.504 	ind 32: 0.480 	ind 33: 0.500 	ind 34: 0.491 	ind 35: 0.493 	ind 36: 0.503 	ind 37: 0.484 	ind 38: 0.440 	ind 39: 0.482 	ind 40: 0.492 	ind 41: 0.478 	ind 42: 0.491 	ind 43: 0.437 	ind 44: 0.510 	ind 45: 0.445 	ind 46: 0.495 	ind 47: 0.485 	ind 48: 0.497 	ind 49: 0.460 	
2025-05-15 13:46:31,601 - modnet - INFO - Generation number 13

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:20<1:24:55, 20.46s/it]
  1%|          | 2/250 [00:23<41:49, 10.12s/it]  
  1%|          | 3/250 [00:25<26:29,  6.44s/it]
  2%|▏         | 4/250 [00:25<16:19,  3.98s/it]
  2%|▏         | 5/250 [00:26<12:00,  2.94s/it]
  2%|▏         | 6/250 [00:27<08:50,  2.17s/it]
  3%|▎         | 7/250 [00:27<06:40,  1.65s/it]
  3%|▎         | 8/250 [00:28<05:26,  1.35s/it]
  4%|▎         | 9/250 [00:28<03:56,  1.02it/s]
  4%|▍         | 10/250 [00:29<02:58,  1.35it/s]
  4%|▍         | 11/250 [00:29<02:19,  1.72it/s]
  5%|▍         | 12/250 [00:29<02:05,  1.89it/s]
  5%|▌         | 13/250 [00:31<03:15,  1.21it/s]
  6%|▌         | 14/250 [00:31<03:08,  1.25it/s]
  6%|▋         | 16/250 [00:32<02:08,  1.82it/s]
  7%|▋         | 17/250 [00:34<03:53,  1.00s/it]
  7%|▋         | 18/250 [00:35<03:38,  1.06it/s]
  8%|▊         | 19/250 [00:37<04:27,  1.16s/it]
  8%|▊         | 20/250 [00:37<03:49,  1.00it/s]
  8%|▊         | 21/250 [00:39<04:18,  1.13s/it]
  9%|▉         | 22/250 [00:43<07:12,  1.90s/it]
  9%|▉         | 23/250 [00:43<05:55,  1.57s/it]
 10%|▉         | 24/250 [00:45<05:24,  1.44s/it]
 10%|█         | 25/250 [00:46<05:50,  1.56s/it]
 10%|█         | 26/250 [00:49<06:40,  1.79s/it]
 11%|█         | 27/250 [00:50<05:32,  1.49s/it]
 11%|█         | 28/250 [00:52<06:38,  1.80s/it]
 12%|█▏        | 29/250 [00:52<05:02,  1.37s/it]
 12%|█▏        | 30/250 [00:53<04:31,  1.23s/it]
 12%|█▏        | 31/250 [00:55<04:34,  1.25s/it]
 13%|█▎        | 32/250 [00:55<03:43,  1.03s/it]
 13%|█▎        | 33/250 [00:56<03:32,  1.02it/s]
 14%|█▎        | 34/250 [00:59<05:33,  1.54s/it]
 14%|█▍        | 35/250 [00:59<04:15,  1.19s/it]
 14%|█▍        | 36/250 [01:01<05:00,  1.41s/it]
 15%|█▍        | 37/250 [01:02<03:57,  1.11s/it]
 15%|█▌        | 38/250 [01:05<06:34,  1.86s/it]
 16%|█▌        | 39/250 [01:06<04:57,  1.41s/it]
 16%|█▌        | 40/250 [01:06<03:57,  1.13s/it]
 16%|█▋        | 41/250 [01:07<03:20,  1.04it/s]
 17%|█▋        | 42/250 [01:08<03:29,  1.01s/it]
 17%|█▋        | 43/250 [01:09<04:08,  1.20s/it]
 18%|█▊        | 44/250 [01:12<06:06,  1.78s/it]
 18%|█▊        | 45/250 [01:16<08:12,  2.40s/it]
 18%|█▊        | 46/250 [01:17<05:58,  1.76s/it]
 19%|█▉        | 47/250 [01:17<04:20,  1.28s/it]
 19%|█▉        | 48/250 [01:18<04:21,  1.29s/it]
 20%|█▉        | 49/250 [01:20<05:13,  1.56s/it]
 20%|██        | 50/250 [01:22<05:04,  1.52s/it]
 20%|██        | 51/250 [01:23<04:37,  1.39s/it]
 21%|██        | 52/250 [01:25<05:22,  1.63s/it]
 21%|██        | 53/250 [01:27<05:24,  1.65s/it]
 22%|██▏       | 54/250 [01:28<04:41,  1.44s/it]
 22%|██▏       | 56/250 [01:32<05:43,  1.77s/it]
 23%|██▎       | 57/250 [01:32<04:32,  1.41s/it]
 23%|██▎       | 58/250 [01:34<04:30,  1.41s/it]
 24%|██▎       | 59/250 [01:36<05:09,  1.62s/it]
 24%|██▍       | 60/250 [01:44<10:58,  3.46s/it]
 24%|██▍       | 61/250 [01:46<10:01,  3.18s/it]
 25%|██▍       | 62/250 [01:49<09:32,  3.05s/it]
 25%|██▌       | 63/250 [01:53<10:09,  3.26s/it]
 26%|██▌       | 64/250 [01:53<07:17,  2.35s/it]
 26%|██▌       | 65/250 [01:54<06:13,  2.02s/it]
 26%|██▋       | 66/250 [01:57<06:25,  2.09s/it]
 27%|██▋       | 67/250 [02:01<08:48,  2.89s/it]
 27%|██▋       | 68/250 [02:04<08:39,  2.85s/it]
 28%|██▊       | 69/250 [02:05<06:46,  2.25s/it]
 28%|██▊       | 70/250 [02:05<04:54,  1.64s/it]
 28%|██▊       | 71/250 [02:07<05:16,  1.77s/it]
 29%|██▉       | 72/250 [02:07<03:49,  1.29s/it]
 30%|██▉       | 74/250 [02:08<02:42,  1.08it/s]
 30%|███       | 75/250 [02:11<04:11,  1.44s/it]
 30%|███       | 76/250 [02:12<03:31,  1.22s/it]
 31%|███       | 77/250 [02:13<02:59,  1.04s/it]
 31%|███       | 78/250 [02:13<02:17,  1.25it/s]
 32%|███▏      | 79/250 [02:13<02:01,  1.41it/s]
 32%|███▏      | 80/250 [02:14<01:43,  1.64it/s]
 32%|███▏      | 81/250 [02:17<03:50,  1.36s/it]
 33%|███▎      | 82/250 [02:17<03:15,  1.16s/it]
 33%|███▎      | 83/250 [02:18<02:34,  1.08it/s]
 34%|███▎      | 84/250 [02:18<01:56,  1.42it/s]
 34%|███▍      | 85/250 [02:19<01:50,  1.50it/s]
 34%|███▍      | 86/250 [02:19<01:26,  1.90it/s]
 35%|███▍      | 87/250 [02:21<02:59,  1.10s/it]
 35%|███▌      | 88/250 [02:22<02:23,  1.13it/s]
 36%|███▌      | 89/250 [02:22<02:00,  1.33it/s]
 36%|███▌      | 90/250 [02:23<02:16,  1.17it/s]
 36%|███▋      | 91/250 [02:28<05:25,  2.04s/it]
 37%|███▋      | 92/250 [02:32<06:45,  2.57s/it]
 37%|███▋      | 93/250 [02:33<05:56,  2.27s/it]
 38%|███▊      | 94/250 [02:34<04:24,  1.70s/it]
 38%|███▊      | 95/250 [02:35<04:25,  1.72s/it]
 38%|███▊      | 96/250 [02:39<05:32,  2.16s/it]
 39%|███▉      | 97/250 [02:41<05:20,  2.09s/it]
 39%|███▉      | 98/250 [02:41<03:52,  1.53s/it]
 40%|███▉      | 99/250 [02:42<03:53,  1.55s/it]
 40%|████      | 100/250 [02:43<03:18,  1.32s/it]
 40%|████      | 101/250 [02:45<03:31,  1.42s/it]
 41%|████      | 102/250 [02:46<03:17,  1.34s/it]
 41%|████      | 103/250 [02:47<02:47,  1.14s/it]
 42%|████▏     | 104/250 [02:49<03:30,  1.44s/it]
 42%|████▏     | 105/250 [02:49<02:34,  1.07s/it]
 42%|████▏     | 106/250 [02:51<02:52,  1.20s/it]
 43%|████▎     | 107/250 [02:53<03:54,  1.64s/it]
 44%|████▎     | 109/250 [02:56<03:49,  1.63s/it]
 44%|████▍     | 110/250 [02:58<03:52,  1.66s/it]
 44%|████▍     | 111/250 [02:59<03:29,  1.51s/it]
 45%|████▍     | 112/250 [03:02<04:12,  1.83s/it]
 45%|████▌     | 113/250 [03:03<03:21,  1.47s/it]
 46%|████▌     | 114/250 [03:06<04:39,  2.05s/it]
 46%|████▌     | 115/250 [03:09<05:23,  2.40s/it]
 47%|████▋     | 117/250 [03:09<02:57,  1.34s/it]
 47%|████▋     | 118/250 [03:10<02:18,  1.05s/it]
 48%|████▊     | 120/250 [03:10<01:25,  1.52it/s]
 48%|████▊     | 121/250 [03:11<01:55,  1.12it/s]
 49%|████▉     | 122/250 [03:16<03:36,  1.69s/it]
 49%|████▉     | 123/250 [03:16<02:50,  1.34s/it]
 50%|████▉     | 124/250 [03:18<03:01,  1.44s/it]
 50%|█████     | 125/250 [03:20<03:15,  1.56s/it]
 50%|█████     | 126/250 [03:20<02:25,  1.17s/it]
 51%|█████     | 127/250 [03:21<02:34,  1.25s/it]
 51%|█████     | 128/250 [03:22<02:01,  1.01it/s]
 52%|█████▏    | 129/250 [03:25<03:22,  1.68s/it]
 52%|█████▏    | 130/250 [03:27<03:33,  1.78s/it]
 52%|█████▏    | 131/250 [03:30<04:31,  2.28s/it]
 53%|█████▎    | 132/250 [03:31<03:49,  1.94s/it]
 53%|█████▎    | 133/250 [03:34<03:57,  2.03s/it]
 54%|█████▎    | 134/250 [03:35<03:34,  1.85s/it]
 54%|█████▍    | 135/250 [03:36<02:58,  1.56s/it]
 54%|█████▍    | 136/250 [03:42<05:23,  2.84s/it]
 55%|█████▍    | 137/250 [03:42<04:05,  2.17s/it]
 55%|█████▌    | 138/250 [03:43<02:57,  1.59s/it]
 56%|█████▌    | 139/250 [03:44<02:48,  1.51s/it]
 56%|█████▌    | 140/250 [03:44<02:04,  1.14s/it]
 56%|█████▋    | 141/250 [03:47<03:08,  1.73s/it]
 57%|█████▋    | 142/250 [03:48<02:36,  1.45s/it]
 58%|█████▊    | 144/250 [03:49<01:35,  1.11it/s]
 58%|█████▊    | 145/250 [03:51<02:01,  1.16s/it]
 59%|█████▉    | 147/250 [03:53<02:03,  1.20s/it]
 59%|█████▉    | 148/250 [03:54<01:43,  1.02s/it]
 60%|█████▉    | 149/250 [03:56<02:12,  1.31s/it]
 60%|██████    | 150/250 [03:56<01:41,  1.01s/it]
 60%|██████    | 151/250 [03:56<01:17,  1.28it/s]
 61%|██████    | 152/250 [03:58<01:38,  1.00s/it]
 61%|██████    | 153/250 [03:58<01:19,  1.23it/s]
 62%|██████▏   | 154/250 [03:58<00:58,  1.64it/s]
 62%|██████▏   | 155/250 [04:00<01:31,  1.04it/s]
 62%|██████▏   | 156/250 [04:10<05:53,  3.76s/it]
 63%|██████▎   | 157/250 [04:15<06:25,  4.14s/it]
 63%|██████▎   | 158/250 [04:18<05:48,  3.78s/it]
 64%|██████▎   | 159/250 [04:20<04:38,  3.06s/it]
 64%|██████▍   | 160/250 [04:20<03:18,  2.20s/it]
 65%|██████▌   | 163/250 [04:21<01:37,  1.13s/it]
 66%|██████▌   | 164/250 [04:21<01:23,  1.03it/s]
 66%|██████▌   | 165/250 [04:22<01:11,  1.19it/s]
 66%|██████▋   | 166/250 [04:27<02:41,  1.92s/it]
 67%|██████▋   | 167/250 [04:30<03:16,  2.36s/it]
 67%|██████▋   | 168/250 [04:32<02:53,  2.12s/it]
 68%|██████▊   | 169/250 [04:32<02:17,  1.69s/it]
 68%|██████▊   | 170/250 [04:33<01:59,  1.49s/it]
 68%|██████▊   | 171/250 [04:35<01:55,  1.47s/it]
 69%|██████▉   | 172/250 [04:37<02:17,  1.77s/it]
 69%|██████▉   | 173/250 [04:39<02:24,  1.88s/it]
 70%|██████▉   | 174/250 [04:41<02:11,  1.72s/it]
 70%|███████   | 175/250 [04:41<01:42,  1.37s/it]
 70%|███████   | 176/250 [04:42<01:20,  1.09s/it]
 71%|███████   | 177/250 [04:43<01:29,  1.22s/it]
 71%|███████   | 178/250 [04:45<01:36,  1.34s/it]
 72%|███████▏  | 179/250 [04:45<01:16,  1.08s/it]
 72%|███████▏  | 180/250 [04:46<00:58,  1.19it/s]
 72%|███████▏  | 181/250 [04:46<00:46,  1.49it/s]
 73%|███████▎  | 182/250 [04:48<01:19,  1.16s/it]
 73%|███████▎  | 183/250 [04:48<00:57,  1.16it/s]
 74%|███████▎  | 184/250 [04:49<00:43,  1.52it/s]
 74%|███████▍  | 185/250 [04:53<02:02,  1.89s/it]
 74%|███████▍  | 186/250 [04:54<01:32,  1.45s/it]
 75%|███████▍  | 187/250 [04:55<01:30,  1.44s/it]
 75%|███████▌  | 188/250 [04:58<01:49,  1.76s/it]
 76%|███████▌  | 189/250 [05:00<02:02,  2.01s/it]
 76%|███████▌  | 190/250 [05:04<02:30,  2.51s/it]
 76%|███████▋  | 191/250 [05:09<03:14,  3.30s/it]
 77%|███████▋  | 192/250 [05:12<03:10,  3.29s/it]
 77%|███████▋  | 193/250 [05:13<02:14,  2.37s/it]
 78%|███████▊  | 194/250 [05:13<01:39,  1.78s/it]
 78%|███████▊  | 195/250 [05:13<01:14,  1.36s/it]
 78%|███████▊  | 196/250 [05:14<00:54,  1.00s/it]
 79%|███████▉  | 197/250 [05:17<01:35,  1.79s/it]
 79%|███████▉  | 198/250 [05:18<01:12,  1.39s/it]
 80%|███████▉  | 199/250 [05:20<01:20,  1.57s/it]
 80%|████████  | 200/250 [05:20<01:03,  1.28s/it]
 80%|████████  | 201/250 [05:22<01:05,  1.33s/it]
 81%|████████  | 202/250 [05:24<01:14,  1.55s/it]
 81%|████████  | 203/250 [05:24<00:54,  1.17s/it]
 82%|████████▏ | 204/250 [05:28<01:32,  2.00s/it]
 82%|████████▏ | 205/250 [05:30<01:33,  2.08s/it]
 82%|████████▏ | 206/250 [05:32<01:20,  1.84s/it]
 83%|████████▎ | 207/250 [05:33<01:16,  1.78s/it]
 83%|████████▎ | 208/250 [05:35<01:11,  1.71s/it]
 84%|████████▎ | 209/250 [05:36<01:00,  1.47s/it]
 84%|████████▍ | 210/250 [05:36<00:47,  1.18s/it]
 84%|████████▍ | 211/250 [05:38<00:48,  1.24s/it]
 85%|████████▍ | 212/250 [05:38<00:43,  1.13s/it]
 85%|████████▌ | 213/250 [05:43<01:17,  2.10s/it]
 86%|████████▌ | 214/250 [05:44<01:07,  1.89s/it]
 86%|████████▌ | 215/250 [05:45<00:51,  1.48s/it]
 86%|████████▋ | 216/250 [05:48<01:07,  1.99s/it]
 87%|████████▋ | 217/250 [05:48<00:50,  1.53s/it]
 87%|████████▋ | 218/250 [05:49<00:40,  1.26s/it]
 88%|████████▊ | 219/250 [05:50<00:36,  1.17s/it]
 88%|████████▊ | 220/250 [05:50<00:26,  1.13it/s]
 88%|████████▊ | 221/250 [05:52<00:31,  1.09s/it]
 89%|████████▉ | 222/250 [05:55<00:47,  1.69s/it]
 89%|████████▉ | 223/250 [05:55<00:37,  1.40s/it]
 90%|████████▉ | 224/250 [05:59<00:55,  2.14s/it]
 90%|█████████ | 225/250 [06:01<00:53,  2.14s/it]
 90%|█████████ | 226/250 [06:02<00:36,  1.54s/it]
 91%|█████████ | 227/250 [06:03<00:36,  1.58s/it]
 91%|█████████ | 228/250 [06:05<00:36,  1.65s/it]
 92%|█████████▏| 229/250 [06:06<00:32,  1.53s/it]
 92%|█████████▏| 230/250 [06:07<00:25,  1.25s/it]
 92%|█████████▏| 231/250 [06:08<00:22,  1.17s/it]
 93%|█████████▎| 232/250 [06:09<00:19,  1.06s/it]
 94%|█████████▍| 235/250 [06:09<00:07,  2.06it/s]
 94%|█████████▍| 236/250 [06:09<00:06,  2.23it/s]
 95%|█████████▌| 238/250 [06:10<00:04,  2.86it/s]
 96%|█████████▌| 239/250 [06:10<00:03,  3.29it/s]
 96%|█████████▌| 240/250 [06:10<00:02,  3.71it/s]
 96%|█████████▋| 241/250 [06:11<00:03,  2.69it/s]
 97%|█████████▋| 242/250 [06:11<00:04,  1.89it/s]
 97%|█████████▋| 243/250 [06:12<00:04,  1.66it/s]
 98%|█████████▊| 244/250 [06:13<00:03,  1.58it/s]
 98%|█████████▊| 245/250 [06:15<00:04,  1.13it/s]
 98%|█████████▊| 246/250 [06:18<00:06,  1.71s/it]
 99%|█████████▉| 247/250 [06:20<00:05,  1.75s/it]
100%|█████████▉| 249/250 [06:23<00:01,  1.54s/it]
100%|██████████| 250/250 [06:27<00:00,  2.31s/it]
100%|██████████| 250/250 [06:27<00:00,  1.55s/it]
2025-05-15 13:52:59,530 - modnet - INFO - Loss per individual: ind 0: 0.546 	ind 1: 0.485 	ind 2: 0.505 	ind 3: 0.489 	ind 4: 0.522 	ind 5: 0.529 	ind 6: 0.481 	ind 7: 0.456 	ind 8: 0.511 	ind 9: 0.767 	ind 10: 0.444 	ind 11: 0.484 	ind 12: 0.470 	ind 13: 1.040 	ind 14: 0.476 	ind 15: 0.495 	ind 16: 0.486 	ind 17: 0.476 	ind 18: 0.485 	ind 19: 0.600 	ind 20: 0.463 	ind 21: 0.492 	ind 22: 1.030 	ind 23: 0.486 	ind 24: 0.486 	ind 25: 0.468 	ind 26: 0.447 	ind 27: 0.486 	ind 28: 0.489 	ind 29: 0.486 	ind 30: 0.454 	ind 31: 0.502 	ind 32: 0.507 	ind 33: 0.473 	ind 34: 0.517 	ind 35: 0.498 	ind 36: 0.536 	ind 37: 0.531 	ind 38: 0.471 	ind 39: 0.444 	ind 40: 0.513 	ind 41: 0.470 	ind 42: 0.489 	ind 43: 0.525 	ind 44: 0.505 	ind 45: 0.513 	ind 46: 0.548 	ind 47: 0.517 	ind 48: 0.451 	ind 49: 0.495 	
2025-05-15 13:52:59,536 - modnet - INFO - Generation number 14

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:19<1:22:55, 19.98s/it]
  1%|          | 2/250 [00:23<41:23, 10.01s/it]  
  1%|          | 3/250 [00:23<23:17,  5.66s/it]
  2%|▏         | 4/250 [00:23<14:22,  3.50s/it]
  2%|▏         | 5/250 [00:24<09:47,  2.40s/it]
  2%|▏         | 6/250 [00:24<06:58,  1.72s/it]
  3%|▎         | 7/250 [00:26<06:59,  1.73s/it]
  4%|▎         | 9/250 [00:26<03:46,  1.06it/s]
  4%|▍         | 10/250 [00:28<04:23,  1.10s/it]
  4%|▍         | 11/250 [00:29<04:54,  1.23s/it]
  5%|▍         | 12/250 [00:29<03:47,  1.05it/s]
  6%|▌         | 14/250 [00:36<08:02,  2.04s/it]
  6%|▌         | 15/250 [00:37<06:55,  1.77s/it]
  6%|▋         | 16/250 [00:37<05:27,  1.40s/it]
  7%|▋         | 17/250 [00:44<10:32,  2.72s/it]
  7%|▋         | 18/250 [00:44<08:03,  2.08s/it]
  8%|▊         | 19/250 [00:45<06:51,  1.78s/it]
  8%|▊         | 20/250 [00:48<08:09,  2.13s/it]
  8%|▊         | 21/250 [00:49<06:24,  1.68s/it]
  9%|▉         | 22/250 [00:52<07:45,  2.04s/it]
  9%|▉         | 23/250 [00:52<06:07,  1.62s/it]
 10%|▉         | 24/250 [00:53<05:17,  1.40s/it]
 10%|█         | 25/250 [00:56<07:07,  1.90s/it]
 11%|█         | 27/250 [00:57<04:25,  1.19s/it]
 11%|█         | 28/250 [00:58<04:16,  1.16s/it]
 12%|█▏        | 29/250 [01:00<05:07,  1.39s/it]
 12%|█▏        | 30/250 [01:00<04:00,  1.09s/it]
 12%|█▏        | 31/250 [01:07<09:25,  2.58s/it]
 13%|█▎        | 32/250 [01:07<06:53,  1.90s/it]
 14%|█▎        | 34/250 [01:10<06:20,  1.76s/it]
 14%|█▍        | 35/250 [01:11<05:51,  1.64s/it]
 14%|█▍        | 36/250 [01:12<05:18,  1.49s/it]
 15%|█▍        | 37/250 [01:13<04:48,  1.35s/it]
 16%|█▌        | 39/250 [01:14<03:17,  1.07it/s]
 16%|█▌        | 40/250 [01:15<02:53,  1.21it/s]
 16%|█▋        | 41/250 [01:17<04:18,  1.24s/it]
 17%|█▋        | 42/250 [01:18<03:46,  1.09s/it]
 17%|█▋        | 43/250 [01:21<05:18,  1.54s/it]
 18%|█▊        | 45/250 [01:21<03:16,  1.04it/s]
 18%|█▊        | 46/250 [01:24<04:42,  1.39s/it]
 19%|█▉        | 47/250 [01:24<03:40,  1.09s/it]
 19%|█▉        | 48/250 [01:25<03:19,  1.01it/s]
 20%|█▉        | 49/250 [01:25<02:42,  1.24it/s]
 20%|██        | 50/250 [01:25<02:04,  1.61it/s]
 20%|██        | 51/250 [01:26<02:22,  1.40it/s]
 21%|██        | 52/250 [01:29<04:04,  1.24s/it]
 22%|██▏       | 54/250 [01:29<02:38,  1.24it/s]
 22%|██▏       | 55/250 [01:30<02:37,  1.24it/s]
 22%|██▏       | 56/250 [01:32<03:11,  1.01it/s]
 23%|██▎       | 57/250 [01:34<04:38,  1.44s/it]
 23%|██▎       | 58/250 [01:34<03:34,  1.12s/it]
 24%|██▎       | 59/250 [01:38<05:45,  1.81s/it]
 24%|██▍       | 60/250 [01:39<04:32,  1.43s/it]
 24%|██▍       | 61/250 [01:40<04:36,  1.46s/it]
 25%|██▍       | 62/250 [01:43<05:32,  1.77s/it]
 25%|██▌       | 63/250 [01:44<04:55,  1.58s/it]
 26%|██▌       | 64/250 [01:53<12:15,  3.95s/it]
 26%|██▌       | 65/250 [01:54<09:06,  2.95s/it]
 26%|██▋       | 66/250 [01:54<06:34,  2.15s/it]
 27%|██▋       | 67/250 [01:58<07:55,  2.60s/it]
 27%|██▋       | 68/250 [01:58<05:43,  1.89s/it]
 28%|██▊       | 69/250 [01:59<04:39,  1.54s/it]
 28%|██▊       | 70/250 [01:59<03:41,  1.23s/it]
 28%|██▊       | 71/250 [02:01<04:05,  1.37s/it]
 29%|██▉       | 72/250 [02:02<03:55,  1.32s/it]
 29%|██▉       | 73/250 [02:03<03:56,  1.34s/it]
 30%|██▉       | 74/250 [02:04<03:16,  1.12s/it]
 30%|███       | 75/250 [02:04<02:28,  1.18it/s]
 30%|███       | 76/250 [02:05<02:23,  1.22it/s]
 31%|███       | 78/250 [02:09<04:07,  1.44s/it]
 32%|███▏      | 79/250 [02:10<03:13,  1.13s/it]
 32%|███▏      | 80/250 [02:11<03:24,  1.21s/it]
 32%|███▏      | 81/250 [02:15<05:35,  1.99s/it]
 33%|███▎      | 82/250 [02:16<04:40,  1.67s/it]
 33%|███▎      | 83/250 [02:18<05:20,  1.92s/it]
 34%|███▎      | 84/250 [02:20<04:56,  1.79s/it]
 34%|███▍      | 85/250 [02:22<04:55,  1.79s/it]
 34%|███▍      | 86/250 [02:23<04:35,  1.68s/it]
 35%|███▍      | 87/250 [02:24<03:31,  1.30s/it]
 35%|███▌      | 88/250 [02:25<03:42,  1.37s/it]
 36%|███▌      | 89/250 [02:27<03:51,  1.44s/it]
 36%|███▌      | 90/250 [02:27<03:09,  1.18s/it]
 36%|███▋      | 91/250 [02:30<04:22,  1.65s/it]
 37%|███▋      | 92/250 [02:30<03:12,  1.22s/it]
 37%|███▋      | 93/250 [02:30<02:22,  1.10it/s]
 38%|███▊      | 94/250 [02:31<02:12,  1.18it/s]
 38%|███▊      | 95/250 [02:35<04:36,  1.78s/it]
 38%|███▊      | 96/250 [02:35<03:21,  1.31s/it]
 39%|███▉      | 97/250 [02:36<02:36,  1.02s/it]
 39%|███▉      | 98/250 [02:38<03:29,  1.38s/it]
 40%|███▉      | 99/250 [02:42<05:46,  2.29s/it]
 40%|████      | 100/250 [02:43<04:18,  1.72s/it]
 40%|████      | 101/250 [02:43<03:31,  1.42s/it]
 41%|████      | 102/250 [02:44<02:36,  1.06s/it]
 41%|████      | 103/250 [02:44<01:53,  1.29it/s]
 42%|████▏     | 104/250 [02:46<03:00,  1.23s/it]
 42%|████▏     | 105/250 [02:47<03:04,  1.27s/it]
 42%|████▏     | 106/250 [02:50<04:04,  1.70s/it]
 43%|████▎     | 107/250 [02:52<03:57,  1.66s/it]
 43%|████▎     | 108/250 [02:54<04:42,  1.99s/it]
 44%|████▎     | 109/250 [02:55<03:25,  1.46s/it]
 44%|████▍     | 110/250 [02:56<03:11,  1.36s/it]
 44%|████▍     | 111/250 [02:57<02:57,  1.28s/it]
 45%|████▍     | 112/250 [02:57<02:11,  1.05it/s]
 45%|████▌     | 113/250 [02:57<01:36,  1.42it/s]
 46%|████▌     | 114/250 [02:58<01:32,  1.46it/s]
 46%|████▌     | 115/250 [03:00<02:48,  1.25s/it]
 46%|████▋     | 116/250 [03:03<03:28,  1.56s/it]
 47%|████▋     | 117/250 [03:04<03:07,  1.41s/it]
 47%|████▋     | 118/250 [03:06<03:43,  1.70s/it]
 48%|████▊     | 119/250 [03:07<03:24,  1.56s/it]
 48%|████▊     | 120/250 [03:08<02:29,  1.15s/it]
 48%|████▊     | 121/250 [03:09<02:29,  1.16s/it]
 49%|████▉     | 122/250 [03:09<02:10,  1.02s/it]
 49%|████▉     | 123/250 [03:10<01:38,  1.29it/s]
 50%|████▉     | 124/250 [03:10<01:41,  1.24it/s]
 50%|█████     | 125/250 [03:12<02:24,  1.16s/it]
 50%|█████     | 126/250 [03:15<03:12,  1.55s/it]
 51%|█████     | 127/250 [03:17<03:43,  1.82s/it]
 52%|█████▏    | 129/250 [03:18<02:20,  1.16s/it]
 52%|█████▏    | 130/250 [03:19<02:19,  1.16s/it]
 52%|█████▏    | 131/250 [03:21<02:21,  1.19s/it]
 53%|█████▎    | 132/250 [03:21<01:51,  1.06it/s]
 53%|█████▎    | 133/250 [03:21<01:24,  1.38it/s]
 54%|█████▍    | 135/250 [03:21<00:49,  2.32it/s]
 54%|█████▍    | 136/250 [03:24<01:49,  1.04it/s]
 55%|█████▍    | 137/250 [03:26<02:14,  1.19s/it]
 55%|█████▌    | 138/250 [03:26<01:53,  1.01s/it]
 56%|█████▌    | 139/250 [03:31<03:55,  2.12s/it]
 56%|█████▌    | 140/250 [03:32<03:10,  1.73s/it]
 56%|█████▋    | 141/250 [03:34<03:22,  1.86s/it]
 57%|█████▋    | 142/250 [03:35<03:01,  1.68s/it]
 57%|█████▋    | 143/250 [03:41<05:19,  2.99s/it]
 58%|█████▊    | 144/250 [03:43<04:23,  2.48s/it]
 58%|█████▊    | 145/250 [03:43<03:08,  1.80s/it]
 58%|█████▊    | 146/250 [03:44<02:32,  1.47s/it]
 59%|█████▉    | 147/250 [03:50<05:04,  2.96s/it]
 59%|█████▉    | 148/250 [03:51<04:08,  2.44s/it]
 60%|█████▉    | 149/250 [03:51<02:57,  1.76s/it]
 60%|██████    | 150/250 [03:53<02:45,  1.66s/it]
 60%|██████    | 151/250 [03:55<03:10,  1.93s/it]
 61%|██████    | 152/250 [03:56<02:31,  1.55s/it]
 61%|██████    | 153/250 [03:56<01:53,  1.17s/it]
 62%|██████▏   | 154/250 [03:57<01:35,  1.00it/s]
 62%|██████▏   | 156/250 [03:58<01:06,  1.40it/s]
 63%|██████▎   | 157/250 [03:58<00:57,  1.60it/s]
 63%|██████▎   | 158/250 [03:59<00:57,  1.61it/s]
 64%|██████▎   | 159/250 [04:01<01:43,  1.14s/it]
 64%|██████▍   | 160/250 [04:02<01:21,  1.10it/s]
 64%|██████▍   | 161/250 [04:02<01:18,  1.13it/s]
 65%|██████▍   | 162/250 [04:03<01:14,  1.19it/s]
 65%|██████▌   | 163/250 [04:04<01:05,  1.32it/s]
 66%|██████▌   | 165/250 [04:06<01:14,  1.14it/s]
 66%|██████▋   | 166/250 [04:06<01:04,  1.31it/s]
 67%|██████▋   | 168/250 [04:06<00:39,  2.07it/s]
 68%|██████▊   | 169/250 [04:07<00:39,  2.07it/s]
 68%|██████▊   | 170/250 [04:08<00:47,  1.70it/s]
 68%|██████▊   | 171/250 [04:11<01:48,  1.38s/it]
 69%|██████▉   | 172/250 [04:19<03:54,  3.01s/it]
 69%|██████▉   | 173/250 [04:20<03:07,  2.43s/it]
 70%|██████▉   | 174/250 [04:21<02:40,  2.11s/it]
 70%|███████   | 175/250 [04:22<02:06,  1.69s/it]
 70%|███████   | 176/250 [04:23<01:53,  1.54s/it]
 71%|███████   | 177/250 [04:24<01:38,  1.35s/it]
 71%|███████   | 178/250 [04:25<01:25,  1.19s/it]
 72%|███████▏  | 179/250 [04:27<01:47,  1.52s/it]
 72%|███████▏  | 180/250 [04:27<01:26,  1.24s/it]
 72%|███████▏  | 181/250 [04:29<01:32,  1.34s/it]
 73%|███████▎  | 182/250 [04:30<01:26,  1.27s/it]
 73%|███████▎  | 183/250 [04:30<01:04,  1.04it/s]
 74%|███████▎  | 184/250 [04:31<00:56,  1.18it/s]
 74%|███████▍  | 185/250 [04:31<00:45,  1.42it/s]
 74%|███████▍  | 186/250 [04:32<00:36,  1.75it/s]
 75%|███████▍  | 187/250 [04:32<00:34,  1.81it/s]
 75%|███████▌  | 188/250 [04:32<00:31,  1.99it/s]
 76%|███████▌  | 189/250 [04:33<00:31,  1.95it/s]
 76%|███████▌  | 190/250 [04:33<00:29,  2.00it/s]
 76%|███████▋  | 191/250 [04:34<00:30,  1.91it/s]
 77%|███████▋  | 193/250 [04:34<00:18,  3.03it/s]
 78%|███████▊  | 194/250 [04:35<00:23,  2.43it/s]
 78%|███████▊  | 195/250 [04:38<00:59,  1.07s/it]
 78%|███████▊  | 196/250 [04:38<00:45,  1.20it/s]
 79%|███████▉  | 197/250 [04:41<01:18,  1.48s/it]
 79%|███████▉  | 198/250 [04:41<01:00,  1.15s/it]
 80%|███████▉  | 199/250 [04:42<00:52,  1.03s/it]
 80%|████████  | 200/250 [04:43<00:43,  1.14it/s]
 80%|████████  | 201/250 [04:43<00:34,  1.43it/s]
 81%|████████  | 202/250 [04:47<01:17,  1.61s/it]
 81%|████████  | 203/250 [04:50<01:35,  2.03s/it]
 82%|████████▏ | 204/250 [04:51<01:21,  1.78s/it]
 82%|████████▏ | 205/250 [04:55<01:47,  2.38s/it]
 82%|████████▏ | 206/250 [04:56<01:26,  1.97s/it]
 83%|████████▎ | 207/250 [04:57<01:09,  1.62s/it]
 83%|████████▎ | 208/250 [04:58<01:00,  1.44s/it]
 84%|████████▎ | 209/250 [04:59<00:59,  1.45s/it]
 84%|████████▍ | 210/250 [05:00<00:52,  1.30s/it]
 84%|████████▍ | 211/250 [05:01<00:51,  1.33s/it]
 85%|████████▍ | 212/250 [05:06<01:28,  2.32s/it]
 86%|████████▌ | 214/250 [05:06<00:47,  1.32s/it]
 86%|████████▋ | 216/250 [05:09<00:46,  1.37s/it]
 87%|████████▋ | 217/250 [05:10<00:39,  1.19s/it]
 87%|████████▋ | 218/250 [05:12<00:48,  1.53s/it]
 88%|████████▊ | 219/250 [05:13<00:36,  1.17s/it]
 88%|████████▊ | 220/250 [05:24<01:54,  3.81s/it]
 88%|████████▊ | 221/250 [05:31<02:16,  4.70s/it]
 89%|████████▉ | 222/250 [05:33<01:56,  4.15s/it]
 89%|████████▉ | 223/250 [05:37<01:49,  4.04s/it]
 90%|████████▉ | 224/250 [05:40<01:38,  3.79s/it]
 90%|█████████ | 225/250 [05:42<01:22,  3.31s/it]
 90%|█████████ | 226/250 [05:45<01:13,  3.06s/it]
 91%|█████████ | 227/250 [05:45<00:53,  2.31s/it]
 92%|█████████▏| 229/250 [05:47<00:32,  1.57s/it]
 92%|█████████▏| 230/250 [05:48<00:27,  1.35s/it]
 92%|█████████▏| 231/250 [05:49<00:26,  1.42s/it]
 93%|█████████▎| 232/250 [05:49<00:20,  1.13s/it]
 93%|█████████▎| 233/250 [05:50<00:15,  1.09it/s]
 94%|█████████▎| 234/250 [05:52<00:20,  1.29s/it]
 94%|█████████▍| 235/250 [05:55<00:26,  1.80s/it]
 94%|█████████▍| 236/250 [05:57<00:25,  1.79s/it]
 95%|█████████▍| 237/250 [05:58<00:21,  1.62s/it]
 95%|█████████▌| 238/250 [05:59<00:18,  1.55s/it]
 96%|█████████▌| 239/250 [06:01<00:15,  1.41s/it]
 96%|█████████▋| 241/250 [06:01<00:08,  1.12it/s]
 97%|█████████▋| 242/250 [06:02<00:07,  1.01it/s]
 97%|█████████▋| 243/250 [06:04<00:08,  1.22s/it]
 98%|█████████▊| 244/250 [06:05<00:06,  1.11s/it]
 98%|█████████▊| 245/250 [06:07<00:06,  1.28s/it]
 98%|█████████▊| 246/250 [06:08<00:05,  1.33s/it]
 99%|█████████▉| 247/250 [06:10<00:04,  1.55s/it]
 99%|█████████▉| 248/250 [06:12<00:03,  1.66s/it]
100%|█████████▉| 249/250 [06:15<00:01,  1.95s/it]
100%|██████████| 250/250 [06:21<00:00,  3.08s/it]
100%|██████████| 250/250 [06:21<00:00,  1.52s/it]
2025-05-15 13:59:20,851 - modnet - INFO - Loss per individual: ind 0: 0.501 	ind 1: 0.460 	ind 2: 0.485 	ind 3: 0.505 	ind 4: 0.508 	ind 5: 0.507 	ind 6: 0.470 	ind 7: 0.506 	ind 8: 0.511 	ind 9: 0.511 	ind 10: 0.502 	ind 11: 0.499 	ind 12: 0.512 	ind 13: 0.482 	ind 14: 0.480 	ind 15: 0.492 	ind 16: 0.445 	ind 17: 0.506 	ind 18: 0.556 	ind 19: 0.489 	ind 20: 0.535 	ind 21: 0.492 	ind 22: 0.496 	ind 23: 0.494 	ind 24: 0.484 	ind 25: 0.489 	ind 26: 0.478 	ind 27: 0.493 	ind 28: 0.506 	ind 29: 0.788 	ind 30: 0.489 	ind 31: 0.489 	ind 32: 0.500 	ind 33: 0.465 	ind 34: 0.495 	ind 35: 0.484 	ind 36: 0.497 	ind 37: 0.488 	ind 38: 0.519 	ind 39: 0.508 	ind 40: 0.531 	ind 41: 0.505 	ind 42: 0.514 	ind 43: 0.513 	ind 44: 0.462 	ind 45: 0.534 	ind 46: 0.494 	ind 47: 0.468 	ind 48: 0.533 	ind 49: 0.475 	
2025-05-15 13:59:20,857 - modnet - INFO - Generation number 15

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:25<1:44:22, 25.15s/it]
  1%|          | 2/250 [00:29<52:50, 12.78s/it]  
  1%|          | 3/250 [00:29<29:07,  7.08s/it]
  2%|▏         | 4/250 [00:31<21:16,  5.19s/it]
  2%|▏         | 5/250 [00:33<15:55,  3.90s/it]
  2%|▏         | 6/250 [00:35<13:05,  3.22s/it]
  3%|▎         | 7/250 [00:36<10:48,  2.67s/it]
  3%|▎         | 8/250 [00:37<07:54,  1.96s/it]
  4%|▎         | 9/250 [00:38<06:47,  1.69s/it]
  4%|▍         | 10/250 [00:39<06:26,  1.61s/it]
  4%|▍         | 11/250 [00:41<06:21,  1.59s/it]
  5%|▍         | 12/250 [00:44<07:40,  1.93s/it]
  5%|▌         | 13/250 [00:45<06:36,  1.67s/it]
  6%|▌         | 14/250 [00:45<05:03,  1.29s/it]
  6%|▌         | 15/250 [00:47<05:10,  1.32s/it]
  6%|▋         | 16/250 [00:47<03:56,  1.01s/it]
  7%|▋         | 17/250 [00:49<05:47,  1.49s/it]
  7%|▋         | 18/250 [00:50<04:59,  1.29s/it]
  8%|▊         | 19/250 [00:51<04:46,  1.24s/it]
  8%|▊         | 20/250 [00:52<03:30,  1.09it/s]
  8%|▊         | 21/250 [00:53<03:48,  1.00it/s]
  9%|▉         | 22/250 [00:53<03:25,  1.11it/s]
  9%|▉         | 23/250 [00:56<05:28,  1.45s/it]
 10%|█         | 25/250 [00:59<05:08,  1.37s/it]
 10%|█         | 26/250 [00:59<03:57,  1.06s/it]
 11%|█         | 27/250 [00:59<03:25,  1.09it/s]
 11%|█         | 28/250 [01:01<03:49,  1.03s/it]
 12%|█▏        | 29/250 [01:01<02:58,  1.24it/s]
 12%|█▏        | 30/250 [01:01<02:19,  1.58it/s]
 12%|█▏        | 31/250 [01:03<03:38,  1.00it/s]
 13%|█▎        | 32/250 [01:04<03:30,  1.04it/s]
 13%|█▎        | 33/250 [01:05<04:13,  1.17s/it]
 14%|█▎        | 34/250 [01:06<03:39,  1.02s/it]
 14%|█▍        | 35/250 [01:08<04:41,  1.31s/it]
 14%|█▍        | 36/250 [01:10<05:33,  1.56s/it]
 15%|█▍        | 37/250 [01:13<07:13,  2.04s/it]
 15%|█▌        | 38/250 [01:15<06:34,  1.86s/it]
 16%|█▌        | 39/250 [01:15<04:52,  1.38s/it]
 16%|█▌        | 40/250 [01:16<03:51,  1.10s/it]
 16%|█▋        | 41/250 [01:21<08:03,  2.31s/it]
 17%|█▋        | 42/250 [01:22<07:23,  2.13s/it]
 17%|█▋        | 43/250 [01:25<08:10,  2.37s/it]
 18%|█▊        | 44/250 [01:27<06:56,  2.02s/it]
 18%|█▊        | 45/250 [01:27<05:20,  1.56s/it]
 18%|█▊        | 46/250 [01:28<04:13,  1.24s/it]
 19%|█▉        | 47/250 [01:28<03:18,  1.02it/s]
 19%|█▉        | 48/250 [01:32<06:01,  1.79s/it]
 20%|█▉        | 49/250 [01:32<05:03,  1.51s/it]
 20%|██        | 51/250 [01:40<08:49,  2.66s/it]
 21%|██        | 52/250 [01:41<07:03,  2.14s/it]
 21%|██        | 53/250 [01:42<06:09,  1.87s/it]
 22%|██▏       | 54/250 [01:43<05:33,  1.70s/it]
 22%|██▏       | 55/250 [01:47<07:21,  2.27s/it]
 22%|██▏       | 56/250 [01:47<05:23,  1.67s/it]
 23%|██▎       | 57/250 [01:53<09:26,  2.93s/it]
 23%|██▎       | 58/250 [01:54<06:48,  2.13s/it]
 24%|██▎       | 59/250 [01:55<05:53,  1.85s/it]
 24%|██▍       | 60/250 [01:56<05:31,  1.74s/it]
 24%|██▍       | 61/250 [01:56<04:00,  1.27s/it]
 25%|██▍       | 62/250 [01:59<04:52,  1.56s/it]
 25%|██▌       | 63/250 [01:59<03:35,  1.15s/it]
 26%|██▌       | 64/250 [02:01<04:57,  1.60s/it]
 26%|██▌       | 65/250 [02:03<05:02,  1.63s/it]
 26%|██▋       | 66/250 [02:04<04:02,  1.32s/it]
 27%|██▋       | 67/250 [02:05<04:17,  1.41s/it]
 27%|██▋       | 68/250 [02:08<05:46,  1.90s/it]
 28%|██▊       | 69/250 [02:09<04:27,  1.48s/it]
 28%|██▊       | 70/250 [02:10<04:14,  1.41s/it]
 28%|██▊       | 71/250 [02:13<05:21,  1.80s/it]
 29%|██▉       | 72/250 [02:14<04:29,  1.51s/it]
 29%|██▉       | 73/250 [02:14<03:16,  1.11s/it]
 30%|██▉       | 74/250 [02:14<02:29,  1.17it/s]
 30%|███       | 75/250 [02:14<01:51,  1.56it/s]
 30%|███       | 76/250 [02:14<01:25,  2.03it/s]
 31%|███       | 77/250 [02:15<01:15,  2.28it/s]
 31%|███       | 78/250 [02:15<01:00,  2.83it/s]
 32%|███▏      | 79/250 [02:23<07:24,  2.60s/it]
 32%|███▏      | 80/250 [02:26<07:35,  2.68s/it]
 32%|███▏      | 81/250 [02:29<07:52,  2.79s/it]
 33%|███▎      | 82/250 [02:29<05:40,  2.03s/it]
 33%|███▎      | 83/250 [02:29<04:15,  1.53s/it]
 34%|███▎      | 84/250 [02:31<04:18,  1.56s/it]
 34%|███▍      | 85/250 [02:33<04:33,  1.66s/it]
 35%|███▍      | 87/250 [02:34<03:05,  1.14s/it]
 35%|███▌      | 88/250 [02:40<06:34,  2.44s/it]
 36%|███▌      | 89/250 [02:45<08:01,  2.99s/it]
 36%|███▌      | 90/250 [02:45<06:11,  2.32s/it]
 36%|███▋      | 91/250 [02:47<05:28,  2.07s/it]
 37%|███▋      | 92/250 [02:50<06:22,  2.42s/it]
 37%|███▋      | 93/250 [02:51<05:03,  1.93s/it]
 38%|███▊      | 94/250 [02:53<05:12,  2.00s/it]
 38%|███▊      | 95/250 [02:56<05:40,  2.20s/it]
 38%|███▊      | 96/250 [02:56<04:21,  1.70s/it]
 39%|███▉      | 97/250 [02:58<04:32,  1.78s/it]
 39%|███▉      | 98/250 [02:58<03:16,  1.29s/it]
 40%|████      | 100/250 [02:59<02:05,  1.19it/s]
 40%|████      | 101/250 [03:00<02:19,  1.06it/s]
 41%|████      | 102/250 [03:01<02:17,  1.08it/s]
 41%|████      | 103/250 [03:04<03:37,  1.48s/it]
 42%|████▏     | 106/250 [03:04<01:44,  1.38it/s]
 43%|████▎     | 107/250 [03:12<05:20,  2.24s/it]
 43%|████▎     | 108/250 [03:15<05:31,  2.33s/it]
 44%|████▎     | 109/250 [03:17<05:31,  2.35s/it]
 44%|████▍     | 110/250 [03:18<04:27,  1.91s/it]
 44%|████▍     | 111/250 [03:21<04:55,  2.13s/it]
 45%|████▍     | 112/250 [03:21<03:36,  1.57s/it]
 45%|████▌     | 113/250 [03:22<02:59,  1.31s/it]
 46%|████▌     | 114/250 [03:26<04:44,  2.09s/it]
 46%|████▌     | 115/250 [03:30<06:31,  2.90s/it]
 46%|████▋     | 116/250 [03:32<05:27,  2.44s/it]
 47%|████▋     | 117/250 [03:32<04:06,  1.85s/it]
 47%|████▋     | 118/250 [03:35<04:46,  2.17s/it]
 48%|████▊     | 119/250 [03:36<03:35,  1.64s/it]
 48%|████▊     | 120/250 [03:37<03:42,  1.71s/it]
 48%|████▊     | 121/250 [03:38<02:42,  1.26s/it]
 49%|████▉     | 122/250 [03:38<02:16,  1.07s/it]
 49%|████▉     | 123/250 [03:39<02:17,  1.08s/it]
 50%|████▉     | 124/250 [03:40<01:53,  1.11it/s]
 50%|█████     | 125/250 [03:41<01:45,  1.19it/s]
 50%|█████     | 126/250 [03:41<01:21,  1.52it/s]
 51%|█████     | 127/250 [03:41<01:05,  1.88it/s]
 51%|█████     | 128/250 [03:42<01:23,  1.45it/s]
 52%|█████▏    | 129/250 [03:43<01:40,  1.20it/s]
 52%|█████▏    | 130/250 [03:43<01:14,  1.62it/s]
 52%|█████▏    | 131/250 [03:45<01:36,  1.23it/s]
 53%|█████▎    | 132/250 [03:45<01:14,  1.59it/s]
 53%|█████▎    | 133/250 [03:45<01:03,  1.85it/s]
 54%|█████▎    | 134/250 [03:47<01:53,  1.02it/s]
 54%|█████▍    | 135/250 [03:50<02:44,  1.43s/it]
 54%|█████▍    | 136/250 [03:51<02:28,  1.31s/it]
 55%|█████▍    | 137/250 [03:51<01:54,  1.01s/it]
 55%|█████▌    | 138/250 [03:59<05:39,  3.03s/it]
 56%|█████▌    | 139/250 [04:00<04:26,  2.40s/it]
 56%|█████▌    | 140/250 [04:03<04:49,  2.64s/it]
 56%|█████▋    | 141/250 [04:05<04:21,  2.40s/it]
 57%|█████▋    | 142/250 [04:06<03:31,  1.96s/it]
 57%|█████▋    | 143/250 [04:06<02:44,  1.54s/it]
 58%|█████▊    | 144/250 [04:06<02:04,  1.17s/it]
 58%|█████▊    | 145/250 [04:07<01:57,  1.12s/it]
 58%|█████▊    | 146/250 [04:08<01:43,  1.00it/s]
 59%|█████▉    | 147/250 [04:10<02:11,  1.27s/it]
 59%|█████▉    | 148/250 [04:11<01:53,  1.11s/it]
 60%|█████▉    | 149/250 [04:14<03:10,  1.88s/it]
 60%|██████    | 150/250 [04:23<06:15,  3.76s/it]
 60%|██████    | 151/250 [04:23<04:28,  2.71s/it]
 61%|██████    | 152/250 [04:24<03:43,  2.28s/it]
 61%|██████    | 153/250 [04:25<03:10,  1.97s/it]
 62%|██████▏   | 154/250 [04:26<02:19,  1.46s/it]
 62%|██████▏   | 155/250 [04:28<02:55,  1.84s/it]
 62%|██████▏   | 156/250 [04:33<04:17,  2.74s/it]
 63%|██████▎   | 157/250 [04:34<03:23,  2.19s/it]
 63%|██████▎   | 158/250 [04:36<03:12,  2.09s/it]
 64%|██████▎   | 159/250 [04:36<02:16,  1.50s/it]
 64%|██████▍   | 160/250 [04:36<01:42,  1.14s/it]
 64%|██████▍   | 161/250 [04:39<02:26,  1.65s/it]
 65%|██████▍   | 162/250 [04:42<02:51,  1.95s/it]
 65%|██████▌   | 163/250 [04:43<02:35,  1.78s/it]
 66%|██████▌   | 164/250 [04:44<02:11,  1.52s/it]
 66%|██████▋   | 166/250 [04:45<01:32,  1.10s/it]
 67%|██████▋   | 167/250 [04:47<01:48,  1.31s/it]
 67%|██████▋   | 168/250 [04:52<03:03,  2.24s/it]
 68%|██████▊   | 169/250 [04:56<03:43,  2.76s/it]
 68%|██████▊   | 170/250 [04:57<02:51,  2.14s/it]
 68%|██████▊   | 171/250 [04:59<02:42,  2.06s/it]
 69%|██████▉   | 172/250 [05:04<03:55,  3.02s/it]
 69%|██████▉   | 173/250 [05:08<04:09,  3.24s/it]
 70%|██████▉   | 174/250 [05:09<03:06,  2.46s/it]
 70%|███████   | 175/250 [05:10<02:33,  2.05s/it]
 70%|███████   | 176/250 [05:10<01:51,  1.51s/it]
 71%|███████   | 178/250 [05:11<01:23,  1.16s/it]
 72%|███████▏  | 179/250 [05:14<01:46,  1.50s/it]
 72%|███████▏  | 180/250 [05:14<01:24,  1.21s/it]
 72%|███████▏  | 181/250 [05:16<01:40,  1.45s/it]
 73%|███████▎  | 182/250 [05:18<01:43,  1.53s/it]
 73%|███████▎  | 183/250 [05:21<02:00,  1.80s/it]
 74%|███████▎  | 184/250 [05:21<01:36,  1.46s/it]
 74%|███████▍  | 185/250 [05:23<01:33,  1.44s/it]
 74%|███████▍  | 186/250 [05:24<01:29,  1.39s/it]
 75%|███████▍  | 187/250 [05:24<01:10,  1.12s/it]
 75%|███████▌  | 188/250 [05:25<00:52,  1.18it/s]
 76%|███████▌  | 189/250 [05:27<01:15,  1.23s/it]
 76%|███████▌  | 190/250 [05:28<01:18,  1.31s/it]
 76%|███████▋  | 191/250 [05:30<01:23,  1.41s/it]
 77%|███████▋  | 192/250 [05:32<01:29,  1.54s/it]
 77%|███████▋  | 193/250 [05:32<01:08,  1.20s/it]
 78%|███████▊  | 194/250 [05:36<01:57,  2.10s/it]
 78%|███████▊  | 195/250 [05:38<01:51,  2.03s/it]
 78%|███████▊  | 196/250 [05:39<01:26,  1.60s/it]
 79%|███████▉  | 197/250 [05:44<02:15,  2.56s/it]
 79%|███████▉  | 198/250 [05:44<01:39,  1.92s/it]
 80%|███████▉  | 199/250 [05:46<01:31,  1.80s/it]
 80%|████████  | 200/250 [05:46<01:05,  1.31s/it]
 80%|████████  | 201/250 [05:47<00:58,  1.20s/it]
 81%|████████  | 202/250 [05:47<00:44,  1.08it/s]
 81%|████████  | 203/250 [05:47<00:33,  1.41it/s]
 82%|████████▏ | 204/250 [05:48<00:37,  1.21it/s]
 82%|████████▏ | 205/250 [05:50<00:43,  1.03it/s]
 82%|████████▏ | 206/250 [05:50<00:32,  1.35it/s]
 83%|████████▎ | 207/250 [05:52<00:56,  1.31s/it]
 83%|████████▎ | 208/250 [05:53<00:50,  1.20s/it]
 84%|████████▎ | 209/250 [05:54<00:40,  1.02it/s]
 84%|████████▍ | 210/250 [05:55<00:41,  1.03s/it]
 84%|████████▍ | 211/250 [05:56<00:38,  1.01it/s]
 85%|████████▍ | 212/250 [05:58<00:49,  1.30s/it]
 86%|████████▌ | 214/250 [05:59<00:36,  1.02s/it]
 86%|████████▋ | 216/250 [06:00<00:22,  1.49it/s]
 87%|████████▋ | 217/250 [06:00<00:19,  1.66it/s]
 87%|████████▋ | 218/250 [06:00<00:17,  1.80it/s]
 88%|████████▊ | 219/250 [06:06<00:56,  1.82s/it]
 88%|████████▊ | 220/250 [06:06<00:41,  1.39s/it]
 88%|████████▊ | 221/250 [06:12<01:17,  2.68s/it]
 89%|████████▉ | 222/250 [06:13<01:04,  2.31s/it]
 89%|████████▉ | 223/250 [06:16<01:01,  2.26s/it]
 90%|████████▉ | 224/250 [06:16<00:45,  1.74s/it]
 90%|█████████ | 225/250 [06:17<00:37,  1.49s/it]
 90%|█████████ | 226/250 [06:18<00:30,  1.28s/it]
 92%|█████████▏| 229/250 [06:18<00:13,  1.51it/s]
 92%|█████████▏| 230/250 [06:19<00:12,  1.59it/s]
 93%|█████████▎| 232/250 [06:19<00:08,  2.12it/s]
 93%|█████████▎| 233/250 [06:19<00:07,  2.41it/s]
 94%|█████████▎| 234/250 [06:21<00:09,  1.66it/s]
 94%|█████████▍| 235/250 [06:21<00:08,  1.81it/s]
 94%|█████████▍| 236/250 [06:21<00:07,  1.91it/s]
 95%|█████████▍| 237/250 [06:22<00:06,  2.10it/s]
 96%|█████████▌| 239/250 [06:22<00:04,  2.72it/s]
 96%|█████████▌| 240/250 [06:23<00:05,  1.95it/s]
 97%|█████████▋| 242/250 [06:23<00:02,  2.81it/s]
 98%|█████████▊| 244/250 [06:25<00:02,  2.04it/s]
 98%|█████████▊| 245/250 [06:26<00:03,  1.55it/s]
 98%|█████████▊| 246/250 [06:38<00:13,  3.30s/it]
 99%|█████████▉| 247/250 [06:42<00:10,  3.49s/it]
 99%|█████████▉| 248/250 [06:53<00:10,  5.20s/it]
100%|█████████▉| 249/250 [06:54<00:04,  4.30s/it]
100%|██████████| 250/250 [07:11<00:00,  7.61s/it]
100%|██████████| 250/250 [07:11<00:00,  1.72s/it]
2025-05-15 14:06:32,036 - modnet - INFO - Loss per individual: ind 0: 0.667 	ind 1: 0.440 	ind 2: 0.511 	ind 3: 0.494 	ind 4: 0.525 	ind 5: 0.515 	ind 6: 0.519 	ind 7: 0.451 	ind 8: 0.472 	ind 9: 0.511 	ind 10: 0.534 	ind 11: 0.487 	ind 12: 0.505 	ind 13: 0.632 	ind 14: 0.491 	ind 15: 0.492 	ind 16: 0.486 	ind 17: 0.531 	ind 18: 0.491 	ind 19: 0.501 	ind 20: 0.480 	ind 21: 0.490 	ind 22: 0.486 	ind 23: 0.523 	ind 24: 0.454 	ind 25: 0.444 	ind 26: 0.506 	ind 27: 0.491 	ind 28: 0.463 	ind 29: 0.498 	ind 30: 0.455 	ind 31: 0.494 	ind 32: 0.505 	ind 33: 0.500 	ind 34: 0.533 	ind 35: 0.472 	ind 36: 0.491 	ind 37: 0.477 	ind 38: 0.490 	ind 39: 0.519 	ind 40: 0.480 	ind 41: 0.511 	ind 42: 0.502 	ind 43: 0.442 	ind 44: 0.500 	ind 45: 0.515 	ind 46: 0.462 	ind 47: 0.562 	ind 48: 0.509 	ind 49: 0.488 	
2025-05-15 14:06:32,041 - modnet - INFO - Generation number 16

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:24<1:42:52, 24.79s/it]
  1%|          | 2/250 [00:25<43:13, 10.46s/it]  
  1%|          | 3/250 [00:27<27:48,  6.76s/it]
  2%|▏         | 4/250 [00:28<17:51,  4.36s/it]
  2%|▏         | 5/250 [00:29<13:48,  3.38s/it]
  3%|▎         | 7/250 [00:31<08:31,  2.10s/it]
  3%|▎         | 8/250 [00:34<09:24,  2.33s/it]
  4%|▎         | 9/250 [00:35<08:08,  2.03s/it]
  4%|▍         | 10/250 [00:39<09:52,  2.47s/it]
  4%|▍         | 11/250 [00:40<07:44,  1.94s/it]
  5%|▍         | 12/250 [00:43<09:02,  2.28s/it]
  5%|▌         | 13/250 [00:43<07:15,  1.84s/it]
  6%|▌         | 14/250 [00:44<05:17,  1.35s/it]
  6%|▌         | 15/250 [00:49<09:57,  2.54s/it]
  6%|▋         | 16/250 [00:49<07:08,  1.83s/it]
  7%|▋         | 17/250 [00:51<07:23,  1.90s/it]
  8%|▊         | 19/250 [00:53<05:56,  1.54s/it]
  8%|▊         | 20/250 [00:55<05:33,  1.45s/it]
  8%|▊         | 21/250 [00:55<04:23,  1.15s/it]
  9%|▉         | 22/250 [00:55<03:30,  1.08it/s]
  9%|▉         | 23/250 [00:58<05:19,  1.41s/it]
 10%|▉         | 24/250 [00:59<05:09,  1.37s/it]
 10%|█         | 25/250 [01:03<08:19,  2.22s/it]
 10%|█         | 26/250 [01:06<08:32,  2.29s/it]
 11%|█         | 27/250 [01:07<06:59,  1.88s/it]
 11%|█         | 28/250 [01:10<08:10,  2.21s/it]
 12%|█▏        | 29/250 [01:11<06:33,  1.78s/it]
 12%|█▏        | 30/250 [01:22<16:51,  4.60s/it]
 12%|█▏        | 31/250 [01:25<15:18,  4.19s/it]
 13%|█▎        | 32/250 [01:25<11:09,  3.07s/it]
 13%|█▎        | 33/250 [01:27<08:52,  2.45s/it]
 14%|█▎        | 34/250 [01:27<06:25,  1.79s/it]
 14%|█▍        | 35/250 [01:32<10:33,  2.95s/it]
 14%|█▍        | 36/250 [01:34<08:40,  2.43s/it]
 15%|█▍        | 37/250 [01:35<07:40,  2.16s/it]
 15%|█▌        | 38/250 [01:35<05:27,  1.55s/it]
 16%|█▌        | 39/250 [01:36<04:42,  1.34s/it]
 16%|█▌        | 40/250 [01:41<08:25,  2.41s/it]
 16%|█▋        | 41/250 [01:41<06:03,  1.74s/it]
 17%|█▋        | 42/250 [01:43<06:03,  1.75s/it]
 17%|█▋        | 43/250 [01:44<04:58,  1.44s/it]
 18%|█▊        | 44/250 [01:45<04:36,  1.34s/it]
 18%|█▊        | 45/250 [01:52<10:27,  3.06s/it]
 18%|█▊        | 46/250 [01:55<10:02,  2.95s/it]
 19%|█▉        | 47/250 [01:57<09:33,  2.82s/it]
 19%|█▉        | 48/250 [02:00<09:07,  2.71s/it]
 20%|█▉        | 49/250 [02:03<10:08,  3.03s/it]
 20%|██        | 50/250 [02:04<07:19,  2.20s/it]
 20%|██        | 51/250 [02:08<09:35,  2.89s/it]
 21%|██        | 52/250 [02:09<07:14,  2.19s/it]
 21%|██        | 53/250 [02:10<06:08,  1.87s/it]
 22%|██▏       | 54/250 [02:11<05:18,  1.62s/it]
 22%|██▏       | 55/250 [02:11<03:58,  1.22s/it]
 22%|██▏       | 56/250 [02:11<02:56,  1.10it/s]
 23%|██▎       | 57/250 [02:11<02:13,  1.45it/s]
 23%|██▎       | 58/250 [02:12<01:51,  1.72it/s]
 24%|██▎       | 59/250 [02:13<02:13,  1.43it/s]
 24%|██▍       | 60/250 [02:13<01:43,  1.84it/s]
 24%|██▍       | 61/250 [02:14<02:06,  1.49it/s]
 25%|██▍       | 62/250 [02:17<03:58,  1.27s/it]
 25%|██▌       | 63/250 [02:17<03:11,  1.03s/it]
 26%|██▌       | 64/250 [02:18<03:15,  1.05s/it]
 26%|██▌       | 65/250 [02:20<03:40,  1.19s/it]
 26%|██▋       | 66/250 [02:21<03:53,  1.27s/it]
 27%|██▋       | 67/250 [02:22<03:55,  1.29s/it]
 27%|██▋       | 68/250 [02:23<03:38,  1.20s/it]
 28%|██▊       | 69/250 [02:24<02:51,  1.06it/s]
 28%|██▊       | 70/250 [02:25<03:08,  1.05s/it]
 28%|██▊       | 71/250 [02:26<02:58,  1.00it/s]
 29%|██▉       | 72/250 [02:26<02:17,  1.30it/s]
 29%|██▉       | 73/250 [02:28<02:57,  1.00s/it]
 30%|██▉       | 74/250 [02:34<07:59,  2.72s/it]
 30%|███       | 75/250 [02:50<18:48,  6.45s/it]
 30%|███       | 76/250 [02:53<15:54,  5.49s/it]
 31%|███       | 77/250 [02:53<11:26,  3.97s/it]
 31%|███       | 78/250 [02:57<11:10,  3.90s/it]
 32%|███▏      | 79/250 [02:58<08:26,  2.96s/it]
 32%|███▏      | 80/250 [02:58<06:13,  2.20s/it]
 32%|███▏      | 81/250 [02:59<04:40,  1.66s/it]
 33%|███▎      | 83/250 [03:00<03:05,  1.11s/it]
 34%|███▎      | 84/250 [03:01<03:07,  1.13s/it]
 34%|███▍      | 85/250 [03:01<02:48,  1.02s/it]
 34%|███▍      | 86/250 [03:02<02:37,  1.04it/s]
 35%|███▍      | 87/250 [03:07<05:16,  1.94s/it]
 35%|███▌      | 88/250 [03:07<03:51,  1.43s/it]
 36%|███▌      | 89/250 [03:07<03:05,  1.15s/it]
 36%|███▌      | 90/250 [03:10<03:57,  1.48s/it]
 36%|███▋      | 91/250 [03:10<03:23,  1.28s/it]
 37%|███▋      | 92/250 [03:11<02:40,  1.01s/it]
 37%|███▋      | 93/250 [03:14<04:05,  1.56s/it]
 38%|███▊      | 94/250 [03:18<06:07,  2.36s/it]
 38%|███▊      | 96/250 [03:21<04:54,  1.91s/it]
 39%|███▉      | 97/250 [03:21<03:48,  1.49s/it]
 39%|███▉      | 98/250 [03:22<03:13,  1.28s/it]
 40%|███▉      | 99/250 [03:22<02:30,  1.00it/s]
 40%|████      | 100/250 [03:22<02:02,  1.23it/s]
 40%|████      | 101/250 [03:24<02:39,  1.07s/it]
 41%|████      | 102/250 [03:28<04:43,  1.91s/it]
 41%|████      | 103/250 [03:30<04:43,  1.93s/it]
 42%|████▏     | 104/250 [03:30<03:36,  1.48s/it]
 42%|████▏     | 105/250 [03:31<03:19,  1.38s/it]
 43%|████▎     | 107/250 [03:32<02:09,  1.10it/s]
 43%|████▎     | 108/250 [03:32<01:42,  1.38it/s]
 44%|████▎     | 109/250 [03:38<04:58,  2.11s/it]
 44%|████▍     | 110/250 [03:44<07:25,  3.18s/it]
 45%|████▍     | 112/250 [03:45<04:13,  1.84s/it]
 45%|████▌     | 113/250 [03:45<03:31,  1.55s/it]
 46%|████▌     | 114/250 [03:47<03:30,  1.55s/it]
 46%|████▌     | 115/250 [03:49<03:55,  1.75s/it]
 46%|████▋     | 116/250 [03:52<04:53,  2.19s/it]
 47%|████▋     | 117/250 [03:54<04:21,  1.97s/it]
 47%|████▋     | 118/250 [03:55<03:34,  1.62s/it]
 48%|████▊     | 119/250 [03:55<02:39,  1.22s/it]
 48%|████▊     | 121/250 [03:55<01:36,  1.33it/s]
 49%|████▉     | 122/250 [04:00<03:31,  1.65s/it]
 49%|████▉     | 123/250 [04:02<03:53,  1.84s/it]
 50%|████▉     | 124/250 [04:02<03:08,  1.49s/it]
 50%|█████     | 125/250 [04:04<02:53,  1.38s/it]
 50%|█████     | 126/250 [04:05<02:53,  1.40s/it]
 51%|█████     | 128/250 [04:08<02:54,  1.43s/it]
 52%|█████▏    | 129/250 [04:12<04:04,  2.02s/it]
 52%|█████▏    | 130/250 [04:14<04:06,  2.06s/it]
 52%|█████▏    | 131/250 [04:14<03:12,  1.62s/it]
 53%|█████▎    | 132/250 [04:15<02:25,  1.24s/it]
 53%|█████▎    | 133/250 [04:18<03:32,  1.82s/it]
 54%|█████▎    | 134/250 [04:19<03:21,  1.73s/it]
 54%|█████▍    | 136/250 [04:21<02:28,  1.30s/it]
 55%|█████▍    | 137/250 [04:26<04:10,  2.22s/it]
 55%|█████▌    | 138/250 [04:27<03:17,  1.76s/it]
 56%|█████▌    | 139/250 [04:27<02:28,  1.34s/it]
 56%|█████▌    | 140/250 [04:27<01:51,  1.01s/it]
 56%|█████▋    | 141/250 [04:29<02:12,  1.21s/it]
 57%|█████▋    | 142/250 [04:31<02:42,  1.51s/it]
 57%|█████▋    | 143/250 [04:32<02:31,  1.42s/it]
 58%|█████▊    | 144/250 [04:33<02:15,  1.28s/it]
 58%|█████▊    | 145/250 [04:34<02:01,  1.16s/it]
 58%|█████▊    | 146/250 [04:35<01:55,  1.12s/it]
 59%|█████▉    | 147/250 [04:36<01:54,  1.11s/it]
 59%|█████▉    | 148/250 [04:36<01:28,  1.16it/s]
 60%|█████▉    | 149/250 [04:38<01:42,  1.01s/it]
 60%|██████    | 150/250 [04:40<02:19,  1.39s/it]
 60%|██████    | 151/250 [04:40<01:46,  1.08s/it]
 61%|██████    | 152/250 [04:41<01:47,  1.09s/it]
 62%|██████▏   | 154/250 [04:44<01:58,  1.23s/it]
 62%|██████▏   | 155/250 [04:46<02:16,  1.44s/it]
 62%|██████▏   | 156/250 [04:50<03:01,  1.93s/it]
 63%|██████▎   | 157/250 [04:50<02:25,  1.57s/it]
 63%|██████▎   | 158/250 [04:50<01:47,  1.17s/it]
 64%|██████▎   | 159/250 [04:51<01:22,  1.10it/s]
 64%|██████▍   | 160/250 [04:51<01:14,  1.21it/s]
 64%|██████▍   | 161/250 [04:52<01:04,  1.39it/s]
 65%|██████▍   | 162/250 [04:52<00:56,  1.56it/s]
 65%|██████▌   | 163/250 [04:54<01:33,  1.07s/it]
 66%|██████▌   | 164/250 [05:00<03:35,  2.51s/it]
 66%|██████▌   | 165/250 [05:01<02:50,  2.01s/it]
 66%|██████▋   | 166/250 [05:03<03:01,  2.16s/it]
 67%|██████▋   | 167/250 [05:04<02:17,  1.65s/it]
 67%|██████▋   | 168/250 [05:04<01:43,  1.26s/it]
 68%|██████▊   | 169/250 [05:09<03:01,  2.24s/it]
 68%|██████▊   | 170/250 [05:10<02:44,  2.06s/it]
 68%|██████▊   | 171/250 [05:12<02:26,  1.86s/it]
 69%|██████▉   | 172/250 [05:13<02:05,  1.61s/it]
 69%|██████▉   | 173/250 [05:14<01:56,  1.51s/it]
 70%|██████▉   | 174/250 [05:17<02:21,  1.86s/it]
 70%|███████   | 175/250 [05:17<01:45,  1.41s/it]
 70%|███████   | 176/250 [05:18<01:32,  1.26s/it]
 71%|███████   | 177/250 [05:20<01:41,  1.39s/it]
 71%|███████   | 178/250 [05:22<01:57,  1.64s/it]
 72%|███████▏  | 179/250 [05:22<01:29,  1.27s/it]
 72%|███████▏  | 180/250 [05:23<01:07,  1.03it/s]
 72%|███████▏  | 181/250 [05:25<01:36,  1.40s/it]
 73%|███████▎  | 182/250 [05:27<01:43,  1.53s/it]
 73%|███████▎  | 183/250 [05:27<01:18,  1.17s/it]
 74%|███████▎  | 184/250 [05:28<01:02,  1.06it/s]
 74%|███████▍  | 185/250 [05:29<01:16,  1.18s/it]
 74%|███████▍  | 186/250 [05:30<00:56,  1.13it/s]
 75%|███████▍  | 187/250 [05:33<01:46,  1.69s/it]
 75%|███████▌  | 188/250 [05:34<01:25,  1.38s/it]
 76%|███████▌  | 189/250 [05:34<01:05,  1.07s/it]
 76%|███████▌  | 190/250 [05:36<01:10,  1.18s/it]
 76%|███████▋  | 191/250 [05:36<01:02,  1.06s/it]
 77%|███████▋  | 192/250 [05:38<01:17,  1.34s/it]
 77%|███████▋  | 193/250 [05:40<01:25,  1.50s/it]
 78%|███████▊  | 194/250 [05:40<01:01,  1.10s/it]
 78%|███████▊  | 195/250 [05:41<00:53,  1.04it/s]
 78%|███████▊  | 196/250 [05:42<00:44,  1.21it/s]
 79%|███████▉  | 197/250 [05:42<00:45,  1.16it/s]
 79%|███████▉  | 198/250 [05:44<00:53,  1.02s/it]
 80%|███████▉  | 199/250 [05:47<01:27,  1.72s/it]
 80%|████████  | 200/250 [05:52<02:09,  2.59s/it]
 80%|████████  | 201/250 [05:57<02:43,  3.34s/it]
 81%|████████  | 202/250 [06:03<03:13,  4.02s/it]
 81%|████████  | 203/250 [06:05<02:43,  3.47s/it]
 82%|████████▏ | 204/250 [06:07<02:29,  3.25s/it]
 82%|████████▏ | 205/250 [06:08<01:44,  2.33s/it]
 82%|████████▏ | 206/250 [06:09<01:35,  2.16s/it]
 83%|████████▎ | 207/250 [06:10<01:07,  1.58s/it]
 83%|████████▎ | 208/250 [06:10<00:49,  1.19s/it]
 84%|████████▎ | 209/250 [06:13<01:09,  1.70s/it]
 84%|████████▍ | 210/250 [06:15<01:08,  1.71s/it]
 85%|████████▍ | 212/250 [06:15<00:42,  1.12s/it]
 85%|████████▌ | 213/250 [06:17<00:45,  1.24s/it]
 86%|████████▌ | 214/250 [06:17<00:35,  1.01it/s]
 86%|████████▌ | 215/250 [06:18<00:30,  1.16it/s]
 86%|████████▋ | 216/250 [06:20<00:43,  1.29s/it]
 87%|████████▋ | 217/250 [06:22<00:48,  1.48s/it]
 87%|████████▋ | 218/250 [06:26<01:09,  2.17s/it]
 88%|████████▊ | 219/250 [06:28<01:04,  2.07s/it]
 88%|████████▊ | 220/250 [06:28<00:48,  1.61s/it]
 88%|████████▊ | 221/250 [06:30<00:44,  1.52s/it]
 89%|████████▉ | 222/250 [06:31<00:41,  1.47s/it]
 89%|████████▉ | 223/250 [06:33<00:46,  1.71s/it]
 90%|████████▉ | 224/250 [06:34<00:36,  1.39s/it]
 90%|█████████ | 225/250 [06:35<00:30,  1.22s/it]
 90%|█████████ | 226/250 [06:35<00:23,  1.02it/s]
 91%|█████████ | 227/250 [06:36<00:18,  1.23it/s]
 91%|█████████ | 228/250 [06:36<00:14,  1.47it/s]
 92%|█████████▏| 229/250 [06:37<00:15,  1.34it/s]
 92%|█████████▏| 230/250 [06:38<00:16,  1.22it/s]
 92%|█████████▏| 231/250 [06:39<00:17,  1.08it/s]
 93%|█████████▎| 232/250 [06:42<00:25,  1.42s/it]
 94%|█████████▎| 234/250 [06:42<00:14,  1.11it/s]
 94%|█████████▍| 235/250 [06:45<00:20,  1.35s/it]
 94%|█████████▍| 236/250 [06:45<00:14,  1.07s/it]
 95%|█████████▍| 237/250 [06:46<00:12,  1.04it/s]
 95%|█████████▌| 238/250 [06:50<00:21,  1.78s/it]
 96%|█████████▌| 239/250 [06:53<00:24,  2.20s/it]
 96%|█████████▌| 240/250 [06:57<00:26,  2.63s/it]
 96%|█████████▋| 241/250 [06:59<00:22,  2.46s/it]
 97%|█████████▋| 242/250 [07:00<00:17,  2.19s/it]
 97%|█████████▋| 243/250 [07:01<00:11,  1.62s/it]
 98%|█████████▊| 244/250 [07:01<00:07,  1.17s/it]
 98%|█████████▊| 245/250 [07:04<00:09,  1.95s/it]
 98%|█████████▊| 246/250 [07:15<00:18,  4.59s/it]
 99%|█████████▉| 248/250 [07:18<00:06,  3.15s/it]
100%|█████████▉| 249/250 [07:21<00:03,  3.15s/it]
100%|██████████| 250/250 [07:24<00:00,  2.96s/it]
100%|██████████| 250/250 [07:24<00:00,  1.78s/it]
2025-05-15 14:13:56,393 - modnet - INFO - Loss per individual: ind 0: 0.506 	ind 1: 0.637 	ind 2: 0.478 	ind 3: 0.482 	ind 4: 0.487 	ind 5: 0.525 	ind 6: 0.511 	ind 7: 0.484 	ind 8: 0.496 	ind 9: 0.481 	ind 10: 0.484 	ind 11: 0.480 	ind 12: 0.490 	ind 13: 0.489 	ind 14: 0.466 	ind 15: 0.476 	ind 16: 0.529 	ind 17: 0.440 	ind 18: 0.519 	ind 19: 0.501 	ind 20: 0.497 	ind 21: 0.447 	ind 22: 0.494 	ind 23: 0.490 	ind 24: 0.637 	ind 25: 0.462 	ind 26: 0.500 	ind 27: 0.450 	ind 28: 0.510 	ind 29: 0.476 	ind 30: 0.569 	ind 31: 0.464 	ind 32: 0.498 	ind 33: 0.485 	ind 34: 0.548 	ind 35: 0.497 	ind 36: 0.486 	ind 37: 0.466 	ind 38: 0.502 	ind 39: 0.488 	ind 40: 0.521 	ind 41: 0.572 	ind 42: 0.489 	ind 43: 0.461 	ind 44: 0.492 	ind 45: 0.483 	ind 46: 0.498 	ind 47: 0.477 	ind 48: 0.515 	ind 49: 0.491 	
2025-05-15 14:13:56,399 - modnet - INFO - Generation number 17

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:25<1:46:07, 25.57s/it]
  1%|          | 2/250 [00:25<44:16, 10.71s/it]  
  1%|          | 3/250 [00:26<24:42,  6.00s/it]
  2%|▏         | 4/250 [00:27<17:33,  4.28s/it]
  2%|▏         | 5/250 [00:28<12:16,  3.01s/it]
  2%|▏         | 6/250 [00:29<08:46,  2.16s/it]
  3%|▎         | 7/250 [00:29<06:17,  1.55s/it]
  3%|▎         | 8/250 [00:30<04:55,  1.22s/it]
  4%|▎         | 9/250 [00:30<04:18,  1.07s/it]
  4%|▍         | 10/250 [00:31<03:25,  1.17it/s]
  5%|▍         | 12/250 [00:31<02:30,  1.58it/s]
  5%|▌         | 13/250 [00:32<02:50,  1.39it/s]
  6%|▌         | 14/250 [00:34<03:41,  1.06it/s]
  6%|▌         | 15/250 [00:34<03:17,  1.19it/s]
  6%|▋         | 16/250 [00:35<03:27,  1.13it/s]
  7%|▋         | 17/250 [00:37<04:13,  1.09s/it]
  7%|▋         | 18/250 [00:39<05:13,  1.35s/it]
  8%|▊         | 19/250 [00:40<04:40,  1.21s/it]
  8%|▊         | 20/250 [00:40<03:52,  1.01s/it]
  8%|▊         | 21/250 [00:41<03:05,  1.23it/s]
  9%|▉         | 22/250 [00:43<04:17,  1.13s/it]
  9%|▉         | 23/250 [00:43<03:17,  1.15it/s]
 10%|▉         | 24/250 [00:44<03:49,  1.02s/it]
 10%|█         | 25/250 [00:45<03:14,  1.16it/s]
 10%|█         | 26/250 [00:46<03:22,  1.11it/s]
 11%|█         | 27/250 [00:47<04:02,  1.09s/it]
 11%|█         | 28/250 [00:49<04:46,  1.29s/it]
 12%|█▏        | 29/250 [00:51<05:03,  1.37s/it]
 12%|█▏        | 30/250 [00:52<05:08,  1.40s/it]
 12%|█▏        | 31/250 [00:53<04:39,  1.28s/it]
 13%|█▎        | 32/250 [00:54<04:26,  1.22s/it]
 13%|█▎        | 33/250 [00:58<06:40,  1.84s/it]
 14%|█▎        | 34/250 [00:58<05:13,  1.45s/it]
 14%|█▍        | 35/250 [00:59<04:51,  1.36s/it]
 15%|█▍        | 37/250 [01:00<02:54,  1.22it/s]
 15%|█▌        | 38/250 [01:00<02:48,  1.26it/s]
 16%|█▌        | 39/250 [01:01<02:55,  1.20it/s]
 16%|█▌        | 40/250 [01:03<04:03,  1.16s/it]
 16%|█▋        | 41/250 [01:04<03:34,  1.03s/it]
 17%|█▋        | 42/250 [01:06<04:36,  1.33s/it]
 17%|█▋        | 43/250 [01:07<04:16,  1.24s/it]
 18%|█▊        | 44/250 [01:09<04:42,  1.37s/it]
 18%|█▊        | 45/250 [01:10<04:11,  1.23s/it]
 18%|█▊        | 46/250 [01:14<07:13,  2.12s/it]
 19%|█▉        | 47/250 [01:17<08:21,  2.47s/it]
 19%|█▉        | 48/250 [01:18<06:35,  1.96s/it]
 20%|█▉        | 49/250 [01:19<05:51,  1.75s/it]
 20%|██        | 50/250 [01:20<04:59,  1.50s/it]
 20%|██        | 51/250 [01:21<04:04,  1.23s/it]
 21%|██        | 52/250 [01:24<05:49,  1.76s/it]
 21%|██        | 53/250 [01:24<04:21,  1.33s/it]
 22%|██▏       | 55/250 [01:26<03:38,  1.12s/it]
 22%|██▏       | 56/250 [01:26<03:00,  1.07it/s]
 23%|██▎       | 57/250 [01:29<04:50,  1.51s/it]
 23%|██▎       | 58/250 [01:29<03:38,  1.14s/it]
 24%|██▎       | 59/250 [01:30<03:18,  1.04s/it]
 24%|██▍       | 60/250 [01:33<04:35,  1.45s/it]
 24%|██▍       | 61/250 [01:35<05:04,  1.61s/it]
 25%|██▍       | 62/250 [01:35<03:59,  1.28s/it]
 25%|██▌       | 63/250 [01:39<06:38,  2.13s/it]
 26%|██▌       | 64/250 [01:39<04:48,  1.55s/it]
 26%|██▌       | 65/250 [01:40<03:51,  1.25s/it]
 26%|██▋       | 66/250 [01:42<04:56,  1.61s/it]
 27%|██▋       | 67/250 [01:47<07:51,  2.58s/it]
 27%|██▋       | 68/250 [01:50<07:30,  2.48s/it]
 28%|██▊       | 69/250 [01:51<06:06,  2.02s/it]
 28%|██▊       | 70/250 [01:51<04:35,  1.53s/it]
 28%|██▊       | 71/250 [01:52<04:29,  1.51s/it]
 29%|██▉       | 72/250 [01:53<03:47,  1.28s/it]
 29%|██▉       | 73/250 [01:56<05:31,  1.87s/it]
 30%|██▉       | 74/250 [02:01<07:53,  2.69s/it]
 30%|███       | 75/250 [02:02<06:35,  2.26s/it]
 30%|███       | 76/250 [02:03<05:03,  1.75s/it]
 31%|███       | 77/250 [02:04<04:37,  1.60s/it]
 31%|███       | 78/250 [02:05<04:05,  1.43s/it]
 32%|███▏      | 79/250 [02:07<04:29,  1.57s/it]
 32%|███▏      | 80/250 [02:08<03:54,  1.38s/it]
 32%|███▏      | 81/250 [02:09<03:25,  1.22s/it]
 33%|███▎      | 82/250 [02:10<03:45,  1.34s/it]
 33%|███▎      | 83/250 [02:11<02:59,  1.07s/it]
 34%|███▎      | 84/250 [02:11<02:27,  1.13it/s]
 34%|███▍      | 86/250 [02:12<01:56,  1.41it/s]
 35%|███▍      | 87/250 [02:12<01:35,  1.70it/s]
 35%|███▌      | 88/250 [02:13<01:47,  1.51it/s]
 36%|███▌      | 89/250 [02:14<01:35,  1.68it/s]
 36%|███▌      | 90/250 [02:14<01:31,  1.74it/s]
 36%|███▋      | 91/250 [02:16<02:06,  1.26it/s]
 37%|███▋      | 93/250 [02:18<02:27,  1.06it/s]
 38%|███▊      | 94/250 [02:21<03:31,  1.35s/it]
 38%|███▊      | 95/250 [02:23<04:32,  1.76s/it]
 38%|███▊      | 96/250 [02:26<04:43,  1.84s/it]
 39%|███▉      | 97/250 [02:27<04:09,  1.63s/it]
 39%|███▉      | 98/250 [02:28<03:47,  1.50s/it]
 40%|███▉      | 99/250 [02:30<03:59,  1.59s/it]
 40%|████      | 100/250 [02:31<03:48,  1.52s/it]
 40%|████      | 101/250 [02:32<03:29,  1.41s/it]
 41%|████      | 102/250 [02:34<03:32,  1.43s/it]
 41%|████      | 103/250 [02:34<02:52,  1.17s/it]
 42%|████▏     | 104/250 [02:35<02:21,  1.03it/s]
 42%|████▏     | 105/250 [02:35<01:54,  1.27it/s]
 42%|████▏     | 106/250 [02:36<01:59,  1.21it/s]
 43%|████▎     | 107/250 [02:38<02:35,  1.09s/it]
 43%|████▎     | 108/250 [02:38<02:20,  1.01it/s]
 44%|████▎     | 109/250 [02:39<01:54,  1.24it/s]
 44%|████▍     | 110/250 [02:39<01:26,  1.62it/s]
 44%|████▍     | 111/250 [02:40<01:54,  1.22it/s]
 45%|████▍     | 112/250 [02:44<04:04,  1.77s/it]
 45%|████▌     | 113/250 [02:45<03:22,  1.48s/it]
 46%|████▌     | 114/250 [02:45<02:29,  1.10s/it]
 46%|████▌     | 115/250 [02:51<05:36,  2.49s/it]
 46%|████▋     | 116/250 [02:52<04:31,  2.03s/it]
 47%|████▋     | 117/250 [02:53<04:02,  1.82s/it]
 47%|████▋     | 118/250 [02:57<04:59,  2.27s/it]
 48%|████▊     | 119/250 [02:59<04:50,  2.21s/it]
 48%|████▊     | 120/250 [03:00<04:08,  1.91s/it]
 48%|████▊     | 121/250 [03:01<03:25,  1.59s/it]
 49%|████▉     | 122/250 [03:07<06:06,  2.86s/it]
 49%|████▉     | 123/250 [03:08<05:05,  2.41s/it]
 50%|████▉     | 124/250 [03:09<03:59,  1.90s/it]
 50%|█████     | 126/250 [03:09<02:13,  1.08s/it]
 51%|█████     | 127/250 [03:10<02:13,  1.08s/it]
 51%|█████     | 128/250 [03:10<01:53,  1.08it/s]
 52%|█████▏    | 129/250 [03:11<01:45,  1.14it/s]
 52%|█████▏    | 130/250 [03:11<01:22,  1.45it/s]
 52%|█████▏    | 131/250 [03:12<01:10,  1.69it/s]
 53%|█████▎    | 133/250 [03:16<02:24,  1.24s/it]
 54%|█████▎    | 134/250 [03:16<01:53,  1.02it/s]
 54%|█████▍    | 135/250 [03:19<03:02,  1.59s/it]
 54%|█████▍    | 136/250 [03:21<03:00,  1.59s/it]
 55%|█████▍    | 137/250 [03:25<04:18,  2.28s/it]
 55%|█████▌    | 138/250 [03:27<04:01,  2.15s/it]
 56%|█████▌    | 139/250 [03:27<02:59,  1.61s/it]
 56%|█████▌    | 140/250 [03:27<02:14,  1.22s/it]
 56%|█████▋    | 141/250 [03:28<01:51,  1.02s/it]
 57%|█████▋    | 142/250 [03:28<01:25,  1.26it/s]
 57%|█████▋    | 143/250 [03:29<01:12,  1.48it/s]
 58%|█████▊    | 144/250 [03:34<03:39,  2.07s/it]
 58%|█████▊    | 145/250 [03:38<04:54,  2.81s/it]
 59%|█████▉    | 147/250 [03:41<03:35,  2.09s/it]
 59%|█████▉    | 148/250 [03:42<03:10,  1.86s/it]
 60%|█████▉    | 149/250 [03:43<02:34,  1.53s/it]
 60%|██████    | 150/250 [03:43<01:55,  1.15s/it]
 60%|██████    | 151/250 [03:45<02:29,  1.51s/it]
 61%|██████    | 153/250 [03:49<02:33,  1.59s/it]
 62%|██████▏   | 154/250 [03:50<02:22,  1.48s/it]
 62%|██████▏   | 155/250 [03:53<03:02,  1.92s/it]
 62%|██████▏   | 156/250 [03:54<02:34,  1.65s/it]
 63%|██████▎   | 157/250 [03:57<03:04,  1.98s/it]
 63%|██████▎   | 158/250 [03:59<03:21,  2.19s/it]
 64%|██████▎   | 159/250 [04:03<03:57,  2.61s/it]
 64%|██████▍   | 160/250 [04:04<02:57,  1.97s/it]
 64%|██████▍   | 161/250 [04:04<02:16,  1.53s/it]
 65%|██████▍   | 162/250 [04:05<01:49,  1.25s/it]
 66%|██████▌   | 164/250 [04:05<01:03,  1.36it/s]
 66%|██████▌   | 165/250 [04:13<03:37,  2.56s/it]
 66%|██████▋   | 166/250 [04:16<03:43,  2.66s/it]
 67%|██████▋   | 167/250 [04:18<03:18,  2.39s/it]
 67%|██████▋   | 168/250 [04:19<02:50,  2.08s/it]
 68%|██████▊   | 169/250 [04:31<06:35,  4.89s/it]
 68%|██████▊   | 170/250 [04:31<04:53,  3.66s/it]
 68%|██████▊   | 171/250 [04:32<03:30,  2.67s/it]
 69%|██████▉   | 172/250 [04:34<03:12,  2.47s/it]
 69%|██████▉   | 173/250 [04:34<02:22,  1.85s/it]
 70%|██████▉   | 174/250 [04:36<02:20,  1.85s/it]
 70%|███████   | 175/250 [04:38<02:17,  1.84s/it]
 70%|███████   | 176/250 [04:40<02:27,  2.00s/it]
 71%|███████   | 177/250 [04:40<01:46,  1.46s/it]
 71%|███████   | 178/250 [04:42<01:44,  1.46s/it]
 72%|███████▏  | 179/250 [04:42<01:26,  1.22s/it]
 72%|███████▏  | 180/250 [04:43<01:05,  1.07it/s]
 72%|███████▏  | 181/250 [04:43<00:55,  1.25it/s]
 73%|███████▎  | 182/250 [04:44<00:48,  1.40it/s]
 73%|███████▎  | 183/250 [04:45<00:52,  1.27it/s]
 74%|███████▎  | 184/250 [04:47<01:31,  1.39s/it]
 74%|███████▍  | 185/250 [04:48<01:06,  1.03s/it]
 75%|███████▍  | 187/250 [04:50<01:15,  1.19s/it]
 76%|███████▌  | 189/250 [04:51<00:48,  1.27it/s]
 76%|███████▌  | 190/250 [04:52<00:55,  1.08it/s]
 76%|███████▋  | 191/250 [04:53<00:52,  1.12it/s]
 77%|███████▋  | 192/250 [04:54<00:52,  1.09it/s]
 77%|███████▋  | 193/250 [04:54<00:40,  1.41it/s]
 78%|███████▊  | 194/250 [05:03<02:44,  2.93s/it]
 78%|███████▊  | 195/250 [05:03<01:57,  2.13s/it]
 78%|███████▊  | 196/250 [05:10<03:11,  3.55s/it]
 79%|███████▉  | 197/250 [05:16<03:52,  4.39s/it]
 79%|███████▉  | 198/250 [05:21<03:50,  4.44s/it]
 80%|███████▉  | 199/250 [05:22<02:49,  3.32s/it]
 80%|████████  | 200/250 [05:22<02:03,  2.46s/it]
 80%|████████  | 201/250 [05:24<01:57,  2.39s/it]
 81%|████████  | 202/250 [05:28<02:12,  2.76s/it]
 81%|████████  | 203/250 [05:31<02:20,  2.98s/it]
 82%|████████▏ | 204/250 [05:44<04:27,  5.82s/it]
 82%|████████▏ | 205/250 [05:45<03:23,  4.53s/it]
 82%|████████▏ | 206/250 [05:48<02:48,  3.82s/it]
 83%|████████▎ | 207/250 [05:48<02:07,  2.95s/it]
 83%|████████▎ | 208/250 [05:49<01:29,  2.13s/it]
 84%|████████▎ | 209/250 [05:53<01:54,  2.78s/it]
 84%|████████▍ | 210/250 [05:54<01:26,  2.16s/it]
 84%|████████▍ | 211/250 [05:56<01:21,  2.09s/it]
 85%|████████▍ | 212/250 [05:57<01:09,  1.84s/it]
 85%|████████▌ | 213/250 [05:57<00:50,  1.36s/it]
 86%|████████▌ | 214/250 [05:59<00:51,  1.44s/it]
 86%|████████▌ | 215/250 [06:00<00:48,  1.37s/it]
 86%|████████▋ | 216/250 [06:09<02:05,  3.68s/it]
 87%|████████▋ | 217/250 [06:09<01:26,  2.63s/it]
 87%|████████▋ | 218/250 [06:10<01:10,  2.22s/it]
 88%|████████▊ | 219/250 [06:15<01:33,  3.01s/it]
 88%|████████▊ | 220/250 [06:17<01:18,  2.62s/it]
 88%|████████▊ | 221/250 [06:22<01:35,  3.29s/it]
 89%|████████▉ | 222/250 [06:26<01:35,  3.40s/it]
 89%|████████▉ | 223/250 [06:26<01:06,  2.46s/it]
 90%|████████▉ | 224/250 [06:28<00:59,  2.28s/it]
 90%|█████████ | 225/250 [06:28<00:41,  1.65s/it]
 90%|█████████ | 226/250 [06:29<00:35,  1.49s/it]
 91%|█████████ | 227/250 [06:31<00:35,  1.54s/it]
 91%|█████████ | 228/250 [06:32<00:31,  1.44s/it]
 92%|█████████▏| 229/250 [06:33<00:27,  1.30s/it]
 92%|█████████▏| 230/250 [06:35<00:33,  1.69s/it]
 92%|█████████▏| 231/250 [06:36<00:23,  1.23s/it]
 93%|█████████▎| 232/250 [06:36<00:18,  1.02s/it]
 93%|█████████▎| 233/250 [06:37<00:17,  1.02s/it]
 94%|█████████▎| 234/250 [06:38<00:15,  1.02it/s]
 94%|█████████▍| 235/250 [06:38<00:11,  1.26it/s]
 95%|█████████▍| 237/250 [06:39<00:08,  1.48it/s]
 95%|█████████▌| 238/250 [06:40<00:06,  1.76it/s]
 96%|█████████▌| 239/250 [06:40<00:05,  2.02it/s]
 96%|█████████▌| 240/250 [06:42<00:08,  1.12it/s]
 97%|█████████▋| 242/250 [06:45<00:09,  1.14s/it]
 98%|█████████▊| 244/250 [06:49<00:08,  1.47s/it]
 98%|█████████▊| 245/250 [06:51<00:08,  1.66s/it]
 98%|█████████▊| 246/250 [06:51<00:05,  1.34s/it]
 99%|█████████▉| 247/250 [06:56<00:06,  2.25s/it]
 99%|█████████▉| 248/250 [06:57<00:03,  1.82s/it]
100%|██████████| 250/250 [06:58<00:00,  1.27s/it]
100%|██████████| 250/250 [06:58<00:00,  1.67s/it]
2025-05-15 14:20:55,178 - modnet - INFO - Loss per individual: ind 0: 0.456 	ind 1: 0.442 	ind 2: 0.446 	ind 3: 0.436 	ind 4: 0.453 	ind 5: 1.052 	ind 6: 0.511 	ind 7: 0.438 	ind 8: 0.485 	ind 9: 0.517 	ind 10: 0.491 	ind 11: 0.474 	ind 12: 0.499 	ind 13: 0.447 	ind 14: 0.437 	ind 15: 0.527 	ind 16: 0.541 	ind 17: 0.521 	ind 18: 0.482 	ind 19: 0.475 	ind 20: 0.446 	ind 21: 0.478 	ind 22: 0.455 	ind 23: 0.487 	ind 24: 0.522 	ind 25: 0.491 	ind 26: 0.500 	ind 27: 0.441 	ind 28: 0.467 	ind 29: 0.647 	ind 30: 0.487 	ind 31: 0.493 	ind 32: 0.475 	ind 33: 0.670 	ind 34: 0.474 	ind 35: 0.493 	ind 36: 0.520 	ind 37: 0.502 	ind 38: 0.502 	ind 39: 0.492 	ind 40: 0.486 	ind 41: 0.490 	ind 42: 0.467 	ind 43: 0.489 	ind 44: 0.483 	ind 45: 0.520 	ind 46: 0.498 	ind 47: 0.502 	ind 48: 0.511 	ind 49: 0.533 	
2025-05-15 14:20:55,182 - modnet - INFO - Generation number 18

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:13<56:49, 13.69s/it]
  1%|          | 2/250 [00:20<38:42,  9.37s/it]
  1%|          | 3/250 [00:20<22:06,  5.37s/it]
  2%|▏         | 4/250 [00:21<13:58,  3.41s/it]
  2%|▏         | 5/250 [00:26<17:33,  4.30s/it]
  2%|▏         | 6/250 [00:30<16:04,  3.95s/it]
  3%|▎         | 7/250 [00:31<11:52,  2.93s/it]
  3%|▎         | 8/250 [00:32<09:57,  2.47s/it]
  4%|▎         | 9/250 [00:33<08:17,  2.06s/it]
  4%|▍         | 11/250 [00:36<06:34,  1.65s/it]
  5%|▍         | 12/250 [00:39<08:24,  2.12s/it]
  5%|▌         | 13/250 [00:43<09:57,  2.52s/it]
  6%|▌         | 14/250 [00:46<10:55,  2.78s/it]
  6%|▌         | 15/250 [00:50<12:33,  3.20s/it]
  6%|▋         | 16/250 [00:51<09:07,  2.34s/it]
  7%|▋         | 17/250 [00:51<06:49,  1.76s/it]
  7%|▋         | 18/250 [00:53<07:36,  1.97s/it]
  8%|▊         | 19/250 [00:54<05:35,  1.45s/it]
  8%|▊         | 20/250 [00:54<04:10,  1.09s/it]
  8%|▊         | 21/250 [00:54<03:07,  1.22it/s]
  9%|▉         | 22/250 [00:55<02:52,  1.32it/s]
  9%|▉         | 23/250 [00:57<04:13,  1.12s/it]
 10%|▉         | 24/250 [00:58<04:32,  1.21s/it]
 10%|█         | 25/250 [01:00<05:13,  1.40s/it]
 10%|█         | 26/250 [01:00<04:13,  1.13s/it]
 11%|█         | 27/250 [01:07<10:24,  2.80s/it]
 11%|█         | 28/250 [01:09<09:41,  2.62s/it]
 12%|█▏        | 29/250 [01:10<07:09,  1.94s/it]
 12%|█▏        | 30/250 [01:13<08:19,  2.27s/it]
 12%|█▏        | 31/250 [01:14<07:05,  1.94s/it]
 13%|█▎        | 32/250 [01:14<05:12,  1.43s/it]
 13%|█▎        | 33/250 [01:14<03:54,  1.08s/it]
 14%|█▎        | 34/250 [01:15<02:55,  1.23it/s]
 14%|█▍        | 35/250 [01:15<02:17,  1.57it/s]
 14%|█▍        | 36/250 [01:15<02:04,  1.72it/s]
 15%|█▍        | 37/250 [01:16<02:07,  1.66it/s]
 15%|█▌        | 38/250 [01:17<02:40,  1.32it/s]
 16%|█▌        | 39/250 [01:18<03:04,  1.15it/s]
 16%|█▌        | 40/250 [01:19<02:29,  1.41it/s]
 17%|█▋        | 42/250 [01:19<01:32,  2.26it/s]
 17%|█▋        | 43/250 [01:19<01:42,  2.02it/s]
 18%|█▊        | 44/250 [01:20<01:24,  2.44it/s]
 18%|█▊        | 45/250 [01:22<03:12,  1.07it/s]
 18%|█▊        | 46/250 [01:23<02:56,  1.15it/s]
 19%|█▉        | 47/250 [01:25<04:28,  1.32s/it]
 19%|█▉        | 48/250 [01:28<05:37,  1.67s/it]
 20%|█▉        | 49/250 [01:29<04:54,  1.47s/it]
 20%|██        | 50/250 [01:29<03:36,  1.08s/it]
 20%|██        | 51/250 [01:30<03:30,  1.06s/it]
 21%|██        | 52/250 [01:30<02:52,  1.15it/s]
 21%|██        | 53/250 [01:31<02:27,  1.33it/s]
 22%|██▏       | 54/250 [01:34<04:41,  1.43s/it]
 22%|██▏       | 55/250 [01:41<10:04,  3.10s/it]
 22%|██▏       | 56/250 [01:51<17:25,  5.39s/it]
 23%|██▎       | 57/250 [02:00<20:30,  6.37s/it]
 23%|██▎       | 58/250 [02:03<16:36,  5.19s/it]
 24%|██▎       | 59/250 [02:04<12:30,  3.93s/it]
 24%|██▍       | 60/250 [02:04<09:28,  2.99s/it]
 25%|██▍       | 62/250 [02:05<05:19,  1.70s/it]
 25%|██▌       | 63/250 [02:05<04:06,  1.32s/it]
 26%|██▌       | 64/250 [02:07<04:41,  1.51s/it]
 26%|██▌       | 65/250 [02:11<06:48,  2.21s/it]
 26%|██▋       | 66/250 [02:12<05:57,  1.94s/it]
 27%|██▋       | 67/250 [02:13<05:05,  1.67s/it]
 27%|██▋       | 68/250 [02:15<04:45,  1.57s/it]
 28%|██▊       | 69/250 [02:15<04:09,  1.38s/it]
 28%|██▊       | 70/250 [02:17<04:07,  1.37s/it]
 28%|██▊       | 71/250 [02:17<03:00,  1.01s/it]
 29%|██▉       | 72/250 [02:20<05:10,  1.74s/it]
 29%|██▉       | 73/250 [02:21<04:27,  1.51s/it]
 30%|██▉       | 74/250 [02:22<03:33,  1.21s/it]
 30%|███       | 75/250 [02:22<02:52,  1.01it/s]
 30%|███       | 76/250 [02:23<02:09,  1.34it/s]
 31%|███       | 77/250 [02:23<01:55,  1.50it/s]
 31%|███       | 78/250 [02:23<01:31,  1.87it/s]
 32%|███▏      | 79/250 [02:24<01:49,  1.56it/s]
 32%|███▏      | 80/250 [02:26<02:38,  1.08it/s]
 32%|███▏      | 81/250 [02:26<02:09,  1.31it/s]
 33%|███▎      | 82/250 [02:26<01:41,  1.66it/s]
 33%|███▎      | 83/250 [02:29<03:02,  1.09s/it]
 34%|███▎      | 84/250 [02:29<02:47,  1.01s/it]
 34%|███▍      | 85/250 [02:30<02:38,  1.04it/s]
 34%|███▍      | 86/250 [02:31<02:42,  1.01it/s]
 35%|███▍      | 87/250 [02:33<02:53,  1.07s/it]
 35%|███▌      | 88/250 [02:35<03:42,  1.37s/it]
 36%|███▌      | 89/250 [02:35<03:01,  1.13s/it]
 36%|███▌      | 90/250 [02:42<07:50,  2.94s/it]
 36%|███▋      | 91/250 [02:43<05:42,  2.15s/it]
 37%|███▋      | 93/250 [02:43<03:11,  1.22s/it]
 38%|███▊      | 94/250 [02:44<02:59,  1.15s/it]
 38%|███▊      | 95/250 [02:45<03:04,  1.19s/it]
 38%|███▊      | 96/250 [02:48<04:23,  1.71s/it]
 39%|███▉      | 97/250 [02:49<03:26,  1.35s/it]
 39%|███▉      | 98/250 [02:52<04:35,  1.81s/it]
 40%|███▉      | 99/250 [02:53<04:26,  1.77s/it]
 40%|████      | 100/250 [02:55<04:17,  1.71s/it]
 40%|████      | 101/250 [02:58<05:11,  2.09s/it]
 41%|████      | 102/250 [02:59<04:32,  1.84s/it]
 41%|████      | 103/250 [03:00<03:33,  1.45s/it]
 42%|████▏     | 104/250 [03:01<03:11,  1.31s/it]
 42%|████▏     | 105/250 [03:02<03:05,  1.28s/it]
 42%|████▏     | 106/250 [03:03<03:14,  1.35s/it]
 43%|████▎     | 107/250 [03:04<02:51,  1.20s/it]
 43%|████▎     | 108/250 [03:08<04:53,  2.07s/it]
 44%|████▎     | 109/250 [03:10<04:29,  1.91s/it]
 44%|████▍     | 110/250 [03:12<04:13,  1.81s/it]
 44%|████▍     | 111/250 [03:12<03:03,  1.32s/it]
 45%|████▍     | 112/250 [03:13<03:16,  1.42s/it]
 45%|████▌     | 113/250 [03:14<02:47,  1.22s/it]
 46%|████▌     | 114/250 [03:16<03:00,  1.33s/it]
 46%|████▌     | 115/250 [03:17<02:54,  1.30s/it]
 46%|████▋     | 116/250 [03:19<03:36,  1.62s/it]
 47%|████▋     | 117/250 [03:19<02:39,  1.20s/it]
 47%|████▋     | 118/250 [03:21<03:09,  1.44s/it]
 48%|████▊     | 119/250 [03:22<02:24,  1.10s/it]
 48%|████▊     | 120/250 [03:25<03:31,  1.63s/it]
 48%|████▊     | 121/250 [03:28<04:18,  2.00s/it]
 49%|████▉     | 122/250 [03:32<05:41,  2.67s/it]
 49%|████▉     | 123/250 [03:32<04:17,  2.03s/it]
 50%|████▉     | 124/250 [03:35<04:31,  2.16s/it]
 50%|█████     | 125/250 [03:35<03:15,  1.57s/it]
 50%|█████     | 126/250 [03:36<02:47,  1.35s/it]
 51%|█████     | 127/250 [03:37<02:45,  1.35s/it]
 51%|█████     | 128/250 [03:45<06:52,  3.38s/it]
 52%|█████▏    | 129/250 [03:47<05:41,  2.82s/it]
 52%|█████▏    | 130/250 [03:48<04:28,  2.24s/it]
 52%|█████▏    | 131/250 [03:49<03:48,  1.92s/it]
 53%|█████▎    | 132/250 [03:51<03:58,  2.02s/it]
 53%|█████▎    | 133/250 [03:56<05:37,  2.88s/it]
 54%|█████▎    | 134/250 [03:57<04:24,  2.28s/it]
 54%|█████▍    | 135/250 [03:59<04:01,  2.10s/it]
 54%|█████▍    | 136/250 [03:59<02:59,  1.57s/it]
 55%|█████▍    | 137/250 [04:04<04:53,  2.60s/it]
 55%|█████▌    | 138/250 [04:06<04:23,  2.35s/it]
 56%|█████▌    | 139/250 [04:08<04:35,  2.48s/it]
 56%|█████▌    | 140/250 [04:13<05:49,  3.18s/it]
 56%|█████▋    | 141/250 [04:15<04:58,  2.74s/it]
 57%|█████▋    | 142/250 [04:15<03:34,  1.99s/it]
 57%|█████▋    | 143/250 [04:17<03:13,  1.81s/it]
 58%|█████▊    | 144/250 [04:20<03:49,  2.17s/it]
 58%|█████▊    | 145/250 [04:20<02:56,  1.68s/it]
 58%|█████▊    | 146/250 [04:26<05:11,  3.00s/it]
 59%|█████▉    | 147/250 [04:28<04:47,  2.79s/it]
 59%|█████▉    | 148/250 [04:29<03:45,  2.21s/it]
 60%|█████▉    | 149/250 [04:35<05:19,  3.17s/it]
 60%|██████    | 150/250 [04:37<04:55,  2.95s/it]
 60%|██████    | 151/250 [04:41<05:28,  3.32s/it]
 61%|██████    | 152/250 [04:45<05:33,  3.40s/it]
 61%|██████    | 153/250 [04:45<03:58,  2.46s/it]
 62%|██████▏   | 154/250 [04:46<02:57,  1.85s/it]
 62%|██████▏   | 155/250 [04:47<02:38,  1.67s/it]
 62%|██████▏   | 156/250 [04:47<02:06,  1.35s/it]
 63%|██████▎   | 157/250 [04:49<02:19,  1.50s/it]
 63%|██████▎   | 158/250 [04:51<02:11,  1.42s/it]
 64%|██████▎   | 159/250 [04:51<01:34,  1.03s/it]
 64%|██████▍   | 160/250 [04:51<01:14,  1.21it/s]
 64%|██████▍   | 161/250 [04:51<01:03,  1.41it/s]
 65%|██████▍   | 162/250 [04:52<00:54,  1.63it/s]
 65%|██████▌   | 163/250 [04:57<02:59,  2.07s/it]
 66%|██████▌   | 164/250 [04:58<02:19,  1.62s/it]
 66%|██████▌   | 165/250 [05:00<02:18,  1.62s/it]
 66%|██████▋   | 166/250 [05:02<02:41,  1.92s/it]
 67%|██████▋   | 167/250 [05:03<02:09,  1.56s/it]
 67%|██████▋   | 168/250 [05:07<03:14,  2.37s/it]
 68%|██████▊   | 169/250 [05:07<02:22,  1.76s/it]
 68%|██████▊   | 170/250 [05:08<01:43,  1.29s/it]
 68%|██████▊   | 171/250 [05:11<02:23,  1.82s/it]
 69%|██████▉   | 172/250 [05:13<02:24,  1.86s/it]
 69%|██████▉   | 173/250 [05:17<03:16,  2.55s/it]
 70%|██████▉   | 174/250 [05:18<02:51,  2.25s/it]
 70%|███████   | 175/250 [05:21<03:02,  2.43s/it]
 70%|███████   | 176/250 [05:23<02:54,  2.36s/it]
 71%|███████   | 177/250 [05:24<02:05,  1.72s/it]
 71%|███████   | 178/250 [05:24<01:30,  1.26s/it]
 72%|███████▏  | 179/250 [05:24<01:07,  1.06it/s]
 72%|███████▏  | 180/250 [05:26<01:34,  1.35s/it]
 72%|███████▏  | 181/250 [05:29<02:08,  1.86s/it]
 73%|███████▎  | 182/250 [05:30<01:37,  1.43s/it]
 73%|███████▎  | 183/250 [05:30<01:19,  1.18s/it]
 74%|███████▎  | 184/250 [05:31<00:59,  1.10it/s]
 74%|███████▍  | 185/250 [05:32<01:04,  1.01it/s]
 74%|███████▍  | 186/250 [05:33<01:13,  1.15s/it]
 75%|███████▍  | 187/250 [05:34<00:53,  1.17it/s]
 75%|███████▌  | 188/250 [05:36<01:17,  1.26s/it]
 76%|███████▌  | 189/250 [05:39<01:49,  1.79s/it]
 76%|███████▌  | 190/250 [05:39<01:18,  1.31s/it]
 76%|███████▋  | 191/250 [05:40<01:17,  1.32s/it]
 77%|███████▋  | 192/250 [05:43<01:40,  1.73s/it]
 77%|███████▋  | 193/250 [05:48<02:30,  2.65s/it]
 78%|███████▊  | 195/250 [05:48<01:25,  1.55s/it]
 78%|███████▊  | 196/250 [05:48<01:04,  1.19s/it]
 79%|███████▉  | 197/250 [05:54<02:02,  2.30s/it]
 79%|███████▉  | 198/250 [05:56<01:59,  2.29s/it]
 80%|███████▉  | 199/250 [05:59<02:10,  2.56s/it]
 80%|████████  | 200/250 [06:00<01:34,  1.89s/it]
 81%|████████  | 202/250 [06:01<01:04,  1.35s/it]
 81%|████████  | 203/250 [06:02<00:55,  1.17s/it]
 82%|████████▏ | 204/250 [06:02<00:41,  1.10it/s]
 82%|████████▏ | 205/250 [06:04<00:59,  1.33s/it]
 82%|████████▏ | 206/250 [06:08<01:23,  1.90s/it]
 83%|████████▎ | 207/250 [06:10<01:26,  2.02s/it]
 83%|████████▎ | 208/250 [06:10<01:02,  1.48s/it]
 84%|████████▎ | 209/250 [06:11<00:51,  1.25s/it]
 84%|████████▍ | 210/250 [06:12<00:52,  1.30s/it]
 84%|████████▍ | 211/250 [06:13<00:40,  1.05s/it]
 85%|████████▌ | 213/250 [06:13<00:22,  1.66it/s]
 86%|████████▌ | 214/250 [06:13<00:22,  1.60it/s]
 86%|████████▌ | 215/250 [06:14<00:21,  1.66it/s]
 86%|████████▋ | 216/250 [06:14<00:17,  1.90it/s]
 87%|████████▋ | 217/250 [06:15<00:18,  1.75it/s]
 87%|████████▋ | 218/250 [06:17<00:27,  1.16it/s]
 88%|████████▊ | 219/250 [06:17<00:22,  1.35it/s]
 88%|████████▊ | 220/250 [06:18<00:21,  1.40it/s]
 88%|████████▊ | 221/250 [06:20<00:30,  1.05s/it]
 89%|████████▉ | 222/250 [06:20<00:26,  1.07it/s]
 89%|████████▉ | 223/250 [06:22<00:28,  1.04s/it]
 90%|████████▉ | 224/250 [06:25<00:46,  1.78s/it]
 90%|█████████ | 225/250 [06:33<01:30,  3.61s/it]
 90%|█████████ | 226/250 [06:34<01:07,  2.82s/it]
 91%|█████████ | 227/250 [06:34<00:48,  2.09s/it]
 91%|█████████ | 228/250 [06:35<00:35,  1.62s/it]
 92%|█████████▏| 229/250 [06:36<00:30,  1.45s/it]
 92%|█████████▏| 230/250 [06:36<00:22,  1.10s/it]
 92%|█████████▏| 231/250 [06:37<00:21,  1.15s/it]
 93%|█████████▎| 232/250 [06:38<00:17,  1.02it/s]
 93%|█████████▎| 233/250 [06:40<00:19,  1.16s/it]
 94%|█████████▍| 235/250 [06:42<00:17,  1.18s/it]
 94%|█████████▍| 236/250 [06:45<00:22,  1.64s/it]
 95%|█████████▍| 237/250 [06:46<00:17,  1.34s/it]
 95%|█████████▌| 238/250 [06:53<00:34,  2.90s/it]
 96%|█████████▌| 239/250 [06:57<00:36,  3.29s/it]
 96%|█████████▌| 240/250 [06:59<00:30,  3.02s/it]
 96%|█████████▋| 241/250 [07:00<00:20,  2.24s/it]
 97%|█████████▋| 242/250 [07:04<00:23,  2.98s/it]
 97%|█████████▋| 243/250 [07:07<00:20,  2.93s/it]
 98%|█████████▊| 244/250 [07:09<00:15,  2.51s/it]
 98%|█████████▊| 245/250 [07:10<00:10,  2.04s/it]
 98%|█████████▊| 246/250 [07:11<00:07,  1.85s/it]
 99%|█████████▉| 247/250 [07:12<00:04,  1.48s/it]
 99%|█████████▉| 248/250 [07:15<00:03,  1.92s/it]
100%|█████████▉| 249/250 [07:15<00:01,  1.44s/it]
100%|██████████| 250/250 [07:17<00:00,  1.62s/it]
100%|██████████| 250/250 [07:17<00:00,  1.75s/it]
2025-05-15 14:28:12,690 - modnet - INFO - Loss per individual: ind 0: 0.491 	ind 1: 0.491 	ind 2: 0.524 	ind 3: 0.489 	ind 4: 0.480 	ind 5: 0.467 	ind 6: 0.455 	ind 7: 0.522 	ind 8: 0.517 	ind 9: 0.510 	ind 10: 0.532 	ind 11: 0.481 	ind 12: 0.510 	ind 13: 0.511 	ind 14: 0.517 	ind 15: 0.479 	ind 16: 0.528 	ind 17: 0.511 	ind 18: 0.464 	ind 19: 0.487 	ind 20: 0.495 	ind 21: 0.509 	ind 22: 0.498 	ind 23: 0.507 	ind 24: 0.485 	ind 25: 0.507 	ind 26: 0.512 	ind 27: 0.488 	ind 28: 0.475 	ind 29: 0.448 	ind 30: 0.507 	ind 31: 0.483 	ind 32: 0.565 	ind 33: 0.485 	ind 34: 0.506 	ind 35: 0.494 	ind 36: 0.541 	ind 37: 0.560 	ind 38: 0.471 	ind 39: 0.478 	ind 40: 0.508 	ind 41: 0.532 	ind 42: 0.501 	ind 43: 0.489 	ind 44: 0.490 	ind 45: 0.436 	ind 46: 0.492 	ind 47: 0.504 	ind 48: 0.484 	ind 49: 0.501 	
2025-05-15 14:28:12,693 - modnet - INFO - Early stopping: same best model for 8 consecutive generations
2025-05-15 14:28:12,693 - modnet - INFO - Early stopping at generation number 18
2025-05-15 14:28:16,752 - modnet - INFO - Model successfully saved as results/matbench_mp_gap_best_model_fold_5.pkl!
Saved best model for fold 5 to results/matbench_mp_gap_best_model_fold_5.pkl
2025-05-15 14:28:17.056669: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-15 14:28:17.069329: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445380000 Hz
/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py:168: RuntimeWarning: divide by zero encountered in true_divide
  mape = metrics["mape"] = np.mean(np.abs((target - pred) / target)) * 100
[Fold 5] MAE = 0.432 , MedianAE = 0.180 , MAPE = inf, √MSE = 0.757 
[Fold 5] MaxAE = 6.927 , slope = 0.80, R = 0.88
Fold 5 metrics saved.
Saved fold 5 complete results to results/matbench_mp_gap_fold_5_results.pkl
Final score =  0.4291514881927224
Saved average target metrics to results/matbench_mp_gap_average_target_metrics.json
Fold predictions saved to results/matbench_mp_gap_fold_predictions.csv
2025-05-15 14:28:50,783 - modnet - INFO - Model successfully saved as final_model/matbench_mp_gap_model!
Final score =  0.4291514881927224
Job finished on Thu May 15 14:28:55 CEST 2025

Resources Used

Total Memory used                        - MEM              : 54GiB
Total CPU Time                           - CPU_Time         : 2-18:57:36
Execution Time                           - Wall_Time        : 02:05:33
total programme cpu time                 - Total_CPU        : 2-04:37:07
Total_CPU / CPU_Time  (%)                - ETA              : 78%
Number of alloc CPU                      - NCPUS            : 32
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 48
Mobilized Resources x Execution Time     - R_Wall_Time      : 4-04:26:24
CPU_Time / R_Wall_Time (%)               - ALPHA            : 66%
Energy (Joules)                                             : 1508669

