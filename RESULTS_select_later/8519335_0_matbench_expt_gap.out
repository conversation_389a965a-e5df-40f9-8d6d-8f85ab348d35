Job started on Fri May 16 04:34:04 CEST 2025
Running on node(s): cnm021
2025-05-16 04:34:06.303091: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 32 jobs
Preparing nested CV run for task 'matbench_expt_gap_matminer_sisso_roost_lmp'
Found 1 matching files for matbench_expt_gap_matminer_sisso_roost_lmp
2025-05-16 04:34:08,736 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fd46bd0dd00> object, created with modnet version 0.1.13
Traceback (most recent call last):
  File "run_benchmark.py", line 866, in <module>
    setup_threading()
  File "run_benchmark.py", line 44, in setup_threading
    os.environ['OPENBLAS_NUM_THREADS'] = '1'
UnboundLocalError: local variable 'os' referenced before assignment
Job finished on Fri May 16 04:34:09 CEST 2025

Resources Used

Total Memory used                        - MEM              : 0B
Total CPU Time                           - CPU_Time         : 00:03:12
Execution Time                           - Wall_Time        : 00:00:06
total programme cpu time                 - Total_CPU        : 00:04.364
Total_CPU / CPU_Time  (%)                - ETA              : 2%
Number of alloc CPU                      - NCPUS            : 32
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 48
Mobilized Resources x Execution Time     - R_Wall_Time      : 00:04:48
CPU_Time / R_Wall_Time (%)               - ALPHA            : 66%
Energy (Joules)                                             : 250

