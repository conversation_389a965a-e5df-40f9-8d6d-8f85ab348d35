Job started on Fri May 16 04:34:40 CEST 2025
Running on node(s): cnm021
2025-05-16 04:34:42.113480: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 32 jobs
Preparing nested CV run for task 'matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso'
Found 1 matching files for matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso
2025-05-16 04:34:44,585 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fbef4f2d9d0> object, created with modnet version 0.1.13
Traceback (most recent call last):
  File "run_benchmark.py", line 866, in <module>
    setup_threading()
  File "run_benchmark.py", line 44, in setup_threading
    os.environ['OPENBLAS_NUM_THREADS'] = '1'
UnboundLocalError: local variable 'os' referenced before assignment
Job finished on Fri May 16 04:34:45 CEST 2025

Resources Used

Total Memory used                        - MEM              : 0B
Total CPU Time                           - CPU_Time         : 00:04:48
Execution Time                           - Wall_Time        : 00:00:09
total programme cpu time                 - Total_CPU        : 00:07.563
Total_CPU / CPU_Time  (%)                - ETA              : 2%
Number of alloc CPU                      - NCPUS            : 32
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 48
Mobilized Resources x Execution Time     - R_Wall_Time      : 00:07:12
CPU_Time / R_Wall_Time (%)               - ALPHA            : 66%
Energy (Joules)                                             : 6800

