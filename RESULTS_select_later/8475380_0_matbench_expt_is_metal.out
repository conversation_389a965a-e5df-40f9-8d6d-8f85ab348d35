Job started on Wed May 14 10:30:10 CEST 2025
Running on node(s): cnm022
2025-05-14 10:30:27.907426: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 32 jobs
Preparing nested CV run for task 'matbench_expt_gap'
Found 0 matching files for matbench_expt_gap
They are []
Fetching matbench_expt_is_metal.json.gz from https://ml.materialsproject.org/projects/matbench_expt_is_metal.json.gz to /gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/matminer/datasets/matbench_expt_is_metal.json.gz
2025-05-14 10:30:44,881 - modnet - INFO - Loaded CompositionOnlyFeaturizer featurizer.
2025-05-14 10:30:44,883 - modnet - INFO - Computing features, this can take time...
2025-05-14 10:30:44,884 - modnet - INFO - Applying composition featurizers...
2025-05-14 10:30:44,887 - modnet - INFO - Applying featurizers (AtomicOrbitals(), AtomicPackingEfficiency(), BandCenter(), ElementFraction(), ElementProperty(data_source=<matminer.utils.data.MagpieData object at 0x7f8869b1a910>,
                features=['Number', 'MendeleevNumber', 'AtomicWeight',
                          'MeltingT', 'Column', 'Row', 'CovalentRadius',
                          'Electronegativity', 'NsValence', 'NpValence',
                          'NdValence', 'NfValence', 'NValence', 'NsUnfilled',
                          'NpUnfilled', 'NdUnfilled', 'NfUnfilled', 'NUnfilled',
                          'GSvolume_pa', 'GSbandgap', 'GSmagmom',
                          'SpaceGroupNumber'],
                stats=['minimum', 'maximum', 'range', 'mean', 'avg_dev',
                       'mode']), IonProperty(), Miedema(ss_types=['min'], struct_types=['inter', 'amor', 'ss']), Stoichiometry(), TMetalFraction(), ValenceOrbital(), YangSolidSolution()) to column 'composition'.

MultipleFeaturizer:   0%|          | 0/4921 [00:00<?, ?it/s]
MultipleFeaturizer:   0%|          | 1/4921 [00:00<18:18,  4.48it/s]
MultipleFeaturizer:   9%|▊         | 429/4921 [00:00<00:02, 1589.89it/s]
MultipleFeaturizer:  14%|█▍        | 702/4921 [00:00<00:02, 1918.32it/s]
MultipleFeaturizer:  19%|█▉        | 937/4921 [00:00<00:02, 1932.86it/s]
MultipleFeaturizer:  24%|██▍       | 1170/4921 [00:00<00:01, 2010.55it/s]
MultipleFeaturizer:  28%|██▊       | 1389/4921 [00:06<00:31, 110.69it/s] 
MultipleFeaturizer:  31%|███       | 1535/4921 [00:09<00:35, 94.44it/s] 
MultipleFeaturizer:  33%|███▎      | 1636/4921 [00:09<00:33, 99.04it/s]
MultipleFeaturizer:  35%|███▍      | 1709/4921 [00:10<00:32, 99.54it/s]
MultipleFeaturizer:  36%|███▌      | 1762/4921 [00:11<00:32, 96.49it/s]
MultipleFeaturizer:  37%|███▋      | 1802/4921 [00:11<00:29, 106.11it/s]
MultipleFeaturizer:  37%|███▋      | 1839/4921 [00:11<00:27, 111.65it/s]
MultipleFeaturizer:  39%|███▉      | 1911/4921 [00:11<00:21, 139.55it/s]
MultipleFeaturizer:  40%|███▉      | 1950/4921 [00:12<00:22, 129.19it/s]
MultipleFeaturizer:  40%|████      | 1989/4921 [00:12<00:20, 142.51it/s]
MultipleFeaturizer:  41%|████      | 2028/4921 [00:12<00:20, 142.56it/s]
MultipleFeaturizer:  42%|████▏     | 2067/4921 [00:13<00:26, 109.73it/s]
MultipleFeaturizer:  43%|████▎     | 2106/4921 [00:13<00:25, 112.24it/s]
MultipleFeaturizer:  44%|████▎     | 2145/4921 [00:13<00:25, 107.81it/s]
MultipleFeaturizer:  45%|████▌     | 2223/4921 [00:14<00:15, 169.84it/s]
MultipleFeaturizer:  46%|████▌     | 2262/4921 [00:14<00:21, 121.93it/s]
MultipleFeaturizer:  47%|████▋     | 2301/4921 [00:15<00:24, 108.11it/s]
MultipleFeaturizer:  48%|████▊     | 2379/4921 [00:15<00:15, 166.46it/s]
MultipleFeaturizer:  49%|████▉     | 2418/4921 [00:15<00:19, 129.62it/s]
MultipleFeaturizer:  51%|█████     | 2496/4921 [00:16<00:22, 109.72it/s]
MultipleFeaturizer:  52%|█████▏    | 2535/4921 [00:16<00:18, 129.76it/s]
MultipleFeaturizer:  53%|█████▎    | 2613/4921 [00:17<00:24, 96.07it/s] 
MultipleFeaturizer:  54%|█████▍    | 2652/4921 [00:18<00:25, 88.48it/s]
MultipleFeaturizer:  55%|█████▍    | 2691/4921 [00:18<00:23, 93.16it/s]
MultipleFeaturizer:  55%|█████▌    | 2730/4921 [00:19<00:30, 72.49it/s]
MultipleFeaturizer:  57%|█████▋    | 2808/4921 [00:24<01:15, 27.95it/s]
MultipleFeaturizer:  58%|█████▊    | 2847/4921 [00:25<01:08, 30.13it/s]
MultipleFeaturizer:  59%|█████▊    | 2886/4921 [00:26<00:59, 34.26it/s]
MultipleFeaturizer:  59%|█████▉    | 2925/4921 [00:26<00:48, 41.49it/s]
MultipleFeaturizer:  60%|██████    | 2964/4921 [00:27<00:39, 49.19it/s]
MultipleFeaturizer:  61%|██████    | 3003/4921 [00:27<00:29, 64.85it/s]
MultipleFeaturizer:  62%|██████▏   | 3042/4921 [00:28<00:34, 54.63it/s]
MultipleFeaturizer:  63%|██████▎   | 3081/4921 [00:28<00:25, 71.11it/s]
MultipleFeaturizer:  63%|██████▎   | 3120/4921 [00:29<00:37, 47.59it/s]
MultipleFeaturizer:  64%|██████▍   | 3159/4921 [00:30<00:27, 63.07it/s]
MultipleFeaturizer:  65%|██████▍   | 3198/4921 [00:31<00:34, 49.47it/s]
MultipleFeaturizer:  67%|██████▋   | 3276/4921 [00:31<00:19, 85.39it/s]
MultipleFeaturizer:  67%|██████▋   | 3315/4921 [00:32<00:20, 79.68it/s]
MultipleFeaturizer:  69%|██████▉   | 3393/4921 [00:32<00:13, 115.09it/s]
MultipleFeaturizer:  71%|███████   | 3471/4921 [00:32<00:08, 163.69it/s]
MultipleFeaturizer:  71%|███████▏  | 3510/4921 [00:32<00:10, 133.35it/s]
MultipleFeaturizer:  72%|███████▏  | 3549/4921 [00:33<00:10, 126.68it/s]
MultipleFeaturizer:  73%|███████▎  | 3588/4921 [00:33<00:11, 121.08it/s]
MultipleFeaturizer:  74%|███████▎  | 3627/4921 [00:34<00:13, 95.00it/s] 
MultipleFeaturizer:  74%|███████▍  | 3666/4921 [00:34<00:11, 111.50it/s]
MultipleFeaturizer:  76%|███████▌  | 3744/4921 [00:34<00:09, 128.37it/s]
MultipleFeaturizer:  78%|███████▊  | 3822/4921 [00:35<00:09, 113.97it/s]
MultipleFeaturizer:  78%|███████▊  | 3861/4921 [00:35<00:08, 131.97it/s]
MultipleFeaturizer:  79%|███████▉  | 3900/4921 [00:36<00:08, 114.79it/s]
MultipleFeaturizer:  80%|████████  | 3939/4921 [00:36<00:08, 113.66it/s]
MultipleFeaturizer:  81%|████████  | 3978/4921 [00:37<00:11, 81.45it/s] 
MultipleFeaturizer:  82%|████████▏ | 4056/4921 [00:37<00:06, 125.85it/s]
MultipleFeaturizer:  84%|████████▍ | 4134/4921 [00:38<00:07, 104.26it/s]
MultipleFeaturizer:  86%|████████▌ | 4212/4921 [00:38<00:04, 142.81it/s]
MultipleFeaturizer:  87%|████████▋ | 4290/4921 [00:39<00:04, 141.34it/s]
MultipleFeaturizer:  89%|████████▉ | 4368/4921 [00:40<00:04, 114.33it/s]
MultipleFeaturizer:  90%|████████▉ | 4407/4921 [00:40<00:04, 113.74it/s]
MultipleFeaturizer:  91%|█████████ | 4485/4921 [00:41<00:03, 131.23it/s]
MultipleFeaturizer:  92%|█████████▏| 4524/4921 [00:41<00:03, 132.02it/s]
MultipleFeaturizer:  93%|█████████▎| 4563/4921 [00:41<00:02, 143.39it/s]
MultipleFeaturizer:  94%|█████████▎| 4602/4921 [00:42<00:02, 110.55it/s]
MultipleFeaturizer:  95%|█████████▌| 4680/4921 [00:42<00:01, 131.50it/s]
MultipleFeaturizer:  97%|█████████▋| 4758/4921 [00:43<00:01, 144.31it/s]
MultipleFeaturizer:  97%|█████████▋| 4797/4921 [00:43<00:00, 143.03it/s]
MultipleFeaturizer:  99%|█████████▉| 4875/4921 [00:44<00:00, 132.23it/s]
MultipleFeaturizer: 100%|██████████| 4921/4921 [00:44<00:00, 111.42it/s]
2025-05-14 10:32:12,848 - modnet - INFO - Data has successfully been featurized!
2025-05-14 10:32:14,507 - modnet - INFO - Data successfully saved as ./precomputed/matbench_expt_is_metal_moddata.pkl.gz!
Preparing fold 1 ...
2025-05-14 10:32:14,791 - modnet - INFO - Multiprocessing on 32 workers.
2025-05-14 10:32:14,791 - modnet - INFO - Computing "self" MI (i.e. information entropy) of features

  0%|          | 0/270 [00:00<?, ?it/s]
  0%|          | 1/270 [00:02<09:15,  2.07s/it]
 26%|██▋       | 71/270 [00:02<00:06, 30.42it/s]
 63%|██████▎   | 171/270 [00:03<00:01, 81.06it/s]
100%|██████████| 270/270 [00:03<00:00, 85.13it/s]
2025-05-14 10:32:17,981 - modnet - INFO - Computing cross NMI between all features...

  0%|          | 0/19306 [00:00<?, ?it/s]
  0%|          | 1/19306 [00:04<25:46:00,  4.81s/it]
  1%|          | 101/19306 [00:05<11:28, 27.88it/s] 
  2%|▏         | 301/19306 [00:05<03:09, 100.45it/s]
  3%|▎         | 501/19306 [00:05<01:43, 181.83it/s]
  3%|▎         | 601/19306 [00:05<01:40, 185.73it/s]
  4%|▎         | 701/19306 [00:06<01:22, 224.88it/s]
  5%|▍         | 901/19306 [00:06<00:55, 329.62it/s]
  6%|▌         | 1180/19306 [00:06<00:32, 561.47it/s]
  7%|▋         | 1311/19306 [00:07<00:50, 359.81it/s]
  7%|▋         | 1406/19306 [00:07<00:51, 345.41it/s]
  8%|▊         | 1501/19306 [00:08<01:17, 228.92it/s]
  9%|▉         | 1701/19306 [00:09<01:31, 193.28it/s]
  9%|▉         | 1801/19306 [00:10<01:20, 217.29it/s]
 10%|▉         | 1901/19306 [00:10<01:34, 184.19it/s]
 10%|█         | 2001/19306 [00:11<01:33, 185.66it/s]
 11%|█▏        | 2188/19306 [00:11<00:58, 292.69it/s]
 12%|█▏        | 2301/19306 [00:11<00:53, 315.75it/s]
 13%|█▎        | 2601/19306 [00:12<00:38, 438.98it/s]
 14%|█▍        | 2701/19306 [00:12<00:44, 370.79it/s]
 15%|█▌        | 2901/19306 [00:12<00:34, 482.29it/s]
 16%|█▌        | 3001/19306 [00:13<00:34, 468.86it/s]
 17%|█▋        | 3201/19306 [00:13<00:28, 556.30it/s]
 18%|█▊        | 3401/19306 [00:13<00:22, 697.11it/s]
 18%|█▊        | 3501/19306 [00:13<00:21, 720.08it/s]
 20%|█▉        | 3801/19306 [00:13<00:16, 953.52it/s]
 20%|██        | 3909/19306 [00:13<00:16, 922.32it/s]
 21%|██        | 4009/19306 [00:14<00:17, 896.22it/s]
 21%|██▏       | 4103/19306 [00:14<00:21, 701.82it/s]
 22%|██▏       | 4201/19306 [00:14<00:26, 568.67it/s]
 22%|██▏       | 4301/19306 [00:14<00:35, 425.23it/s]
 23%|██▎       | 4401/19306 [00:15<00:53, 278.72it/s]
 23%|██▎       | 4501/19306 [00:15<00:47, 312.27it/s]
 24%|██▍       | 4601/19306 [00:16<01:00, 243.21it/s]
 24%|██▍       | 4701/19306 [00:16<00:50, 287.36it/s]
 25%|██▌       | 4901/19306 [00:16<00:31, 458.44it/s]
 26%|██▌       | 5067/19306 [00:16<00:23, 611.52it/s]
 27%|██▋       | 5170/19306 [00:17<00:24, 567.08it/s]
 27%|██▋       | 5256/19306 [00:17<00:29, 468.92it/s]
 28%|██▊       | 5325/19306 [00:19<01:38, 142.46it/s]
 28%|██▊       | 5401/19306 [00:19<01:32, 149.98it/s]
 28%|██▊       | 5501/19306 [00:19<01:11, 193.42it/s]
 30%|██▉       | 5701/19306 [00:20<01:02, 218.24it/s]
 30%|███       | 5801/19306 [00:20<00:50, 269.50it/s]
 31%|███       | 5901/19306 [00:21<00:45, 297.40it/s]
 31%|███       | 6001/19306 [00:21<00:38, 347.84it/s]
 32%|███▏      | 6101/19306 [00:21<00:32, 403.30it/s]
 33%|███▎      | 6301/19306 [00:21<00:24, 526.96it/s]
 33%|███▎      | 6401/19306 [00:21<00:26, 489.52it/s]
 35%|███▍      | 6701/19306 [00:22<00:23, 527.99it/s]
 36%|███▌      | 6957/19306 [00:22<00:16, 757.82it/s]
 37%|███▋      | 7101/19306 [00:22<00:14, 823.29it/s]
 38%|███▊      | 7301/19306 [00:22<00:13, 862.70it/s]
 38%|███▊      | 7412/19306 [00:23<00:17, 688.18it/s]
 39%|███▉      | 7501/19306 [00:23<00:27, 431.90it/s]
 40%|███▉      | 7701/19306 [00:24<00:27, 424.28it/s]
 41%|████      | 7928/19306 [00:24<00:18, 612.94it/s]
 42%|████▏     | 8038/19306 [00:24<00:20, 538.43it/s]
 42%|████▏     | 8126/19306 [00:24<00:20, 539.55it/s]
 42%|████▏     | 8204/19306 [00:24<00:25, 443.16it/s]
 43%|████▎     | 8301/19306 [00:25<00:24, 455.09it/s]
 45%|████▍     | 8601/19306 [00:25<00:17, 595.22it/s]
 46%|████▌     | 8801/19306 [00:25<00:15, 695.18it/s]
 46%|████▌     | 8901/19306 [00:26<00:19, 520.63it/s]
 47%|████▋     | 9101/19306 [00:26<00:15, 662.05it/s]
 48%|████▊     | 9301/19306 [00:26<00:11, 843.27it/s]
 49%|████▉     | 9414/19306 [00:26<00:13, 738.80it/s]
 49%|████▉     | 9508/19306 [00:26<00:15, 614.42it/s]
 50%|████▉     | 9601/19306 [00:26<00:15, 611.40it/s]
 51%|█████     | 9801/19306 [00:27<00:11, 793.46it/s]
 52%|█████▏    | 10001/19306 [00:27<00:12, 733.60it/s]
 52%|█████▏    | 10101/19306 [00:27<00:19, 468.03it/s]
 53%|█████▎    | 10210/19306 [00:28<00:16, 543.86it/s]
 54%|█████▍    | 10401/19306 [00:28<00:12, 733.32it/s]
 54%|█████▍    | 10507/19306 [00:28<00:15, 561.82it/s]
 55%|█████▌    | 10701/19306 [00:28<00:12, 670.40it/s]
 56%|█████▌    | 10801/19306 [00:29<00:16, 521.15it/s]
 56%|█████▋    | 10901/19306 [00:29<00:20, 416.54it/s]
 58%|█████▊    | 11101/19306 [00:29<00:16, 489.14it/s]
 58%|█████▊    | 11201/19306 [00:29<00:16, 485.85it/s]
 60%|█████▉    | 11501/19306 [00:30<00:10, 726.23it/s]
 60%|██████    | 11601/19306 [00:30<00:15, 498.60it/s]
 61%|██████    | 11701/19306 [00:30<00:14, 513.49it/s]
 62%|██████▏   | 12001/19306 [00:30<00:09, 792.41it/s]
 63%|██████▎   | 12104/19306 [00:31<00:09, 800.20it/s]
 63%|██████▎   | 12202/19306 [00:31<00:11, 625.33it/s]
 64%|██████▎   | 12301/19306 [00:31<00:13, 510.10it/s]
 65%|██████▌   | 12601/19306 [00:31<00:08, 813.70it/s]
 66%|██████▌   | 12708/19306 [00:32<00:09, 689.68it/s]
 66%|██████▋   | 12801/19306 [00:32<00:12, 510.64it/s]
 68%|██████▊   | 13053/19306 [00:32<00:07, 783.70it/s]
 68%|██████▊   | 13177/19306 [00:32<00:07, 811.47it/s]
 69%|██████▉   | 13292/19306 [00:32<00:08, 680.70it/s]
 69%|██████▉   | 13401/19306 [00:33<00:09, 634.33it/s]
 70%|██████▉   | 13501/19306 [00:33<00:12, 472.85it/s]
 70%|███████   | 13601/19306 [00:33<00:10, 518.92it/s]
 71%|███████   | 13701/19306 [00:33<00:10, 540.01it/s]
 71%|███████▏  | 13801/19306 [00:33<00:09, 611.04it/s]
 72%|███████▏  | 13901/19306 [00:34<00:13, 388.48it/s]
 73%|███████▎  | 14101/19306 [00:34<00:11, 439.01it/s]
 74%|███████▎  | 14201/19306 [00:35<00:11, 447.93it/s]
 74%|███████▍  | 14301/19306 [00:35<00:10, 465.24it/s]
 75%|███████▍  | 14401/19306 [00:35<00:10, 456.24it/s]
 75%|███████▌  | 14501/19306 [00:35<00:10, 450.48it/s]
 76%|███████▌  | 14601/19306 [00:36<00:14, 327.21it/s]
 76%|███████▌  | 14701/19306 [00:36<00:11, 399.81it/s]
 77%|███████▋  | 14901/19306 [00:36<00:07, 582.73it/s]
 78%|███████▊  | 15101/19306 [00:36<00:07, 561.35it/s]
 79%|███████▉  | 15301/19306 [00:36<00:05, 750.33it/s]
 80%|████████  | 15501/19306 [00:37<00:04, 884.04it/s]
 81%|████████  | 15616/19306 [00:37<00:04, 745.06it/s]
 82%|████████▏ | 15801/19306 [00:37<00:04, 713.84it/s]
 83%|████████▎ | 16001/19306 [00:38<00:06, 528.47it/s]
 84%|████████▍ | 16201/19306 [00:38<00:04, 626.17it/s]
 85%|████████▌ | 16446/19306 [00:38<00:03, 858.81it/s]
 86%|████████▋ | 16663/19306 [00:38<00:02, 1060.86it/s]
 87%|████████▋ | 16819/19306 [00:38<00:02, 973.79it/s] 
 88%|████████▊ | 16952/19306 [00:39<00:04, 523.55it/s]
 88%|████████▊ | 17051/19306 [00:39<00:04, 467.84it/s]
 89%|████████▉ | 17211/19306 [00:39<00:03, 600.18it/s]
 90%|████████▉ | 17315/19306 [00:40<00:03, 515.49it/s]
 90%|█████████ | 17407/19306 [00:40<00:03, 490.89it/s]
 91%|█████████ | 17507/19306 [00:40<00:03, 537.59it/s]
 92%|█████████▏| 17707/19306 [00:41<00:03, 410.96it/s]
 93%|█████████▎| 17907/19306 [00:41<00:02, 497.42it/s]
 93%|█████████▎| 18007/19306 [00:41<00:02, 516.29it/s]
 94%|█████████▍| 18107/19306 [00:41<00:02, 571.71it/s]
 94%|█████████▍| 18207/19306 [00:41<00:01, 617.67it/s]
 95%|█████████▌| 18407/19306 [00:41<00:01, 768.02it/s]
 96%|█████████▋| 18607/19306 [00:42<00:00, 882.51it/s]
 97%|█████████▋| 18707/19306 [00:42<00:00, 702.55it/s]
 98%|█████████▊| 19007/19306 [00:42<00:00, 865.11it/s]
 99%|█████████▉| 19207/19306 [00:43<00:00, 607.53it/s]
100%|██████████| 19306/19306 [00:43<00:00, 447.09it/s]
2025-05-14 10:33:01,585 - modnet - INFO - Starting target 1/1: is_metal ...
2025-05-14 10:33:01,585 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 10:33:19,439 - modnet - INFO - Computing optimal features...
2025-05-14 10:33:22,484 - modnet - INFO - Selected 50/197 features...
2025-05-14 10:33:24,728 - modnet - INFO - Selected 100/197 features...
2025-05-14 10:33:26,097 - modnet - INFO - Selected 150/197 features...
2025-05-14 10:33:26,569 - modnet - INFO - Done with target 1/1: is_metal.
2025-05-14 10:33:26,569 - modnet - INFO - Merging all features...
2025-05-14 10:33:26,569 - modnet - INFO - Done.
2025-05-14 10:33:26,591 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_train_moddata_f1!
2025-05-14 10:33:26,597 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_test_moddata_f1!
Preparing fold 2 ...
2025-05-14 10:33:26,888 - modnet - INFO - Multiprocessing on 32 workers.
2025-05-14 10:33:26,888 - modnet - INFO - Computing "self" MI (i.e. information entropy) of features

  0%|          | 0/270 [00:00<?, ?it/s]
  0%|          | 1/270 [00:02<09:17,  2.07s/it]
 26%|██▋       | 71/270 [00:03<00:06, 29.20it/s]
 63%|██████▎   | 171/270 [00:03<00:01, 82.49it/s]
100%|██████████| 270/270 [00:03<00:00, 85.19it/s]
2025-05-14 10:33:30,070 - modnet - INFO - Computing cross NMI between all features...

  0%|          | 0/18528 [00:00<?, ?it/s]
  0%|          | 1/18528 [00:05<27:18:14,  5.31s/it]
  1%|          | 201/18528 [00:05<05:58, 51.09it/s] 
  2%|▏         | 401/18528 [00:05<02:45, 109.67it/s]
  3%|▎         | 501/18528 [00:06<02:20, 128.08it/s]
  4%|▍         | 701/18528 [00:06<01:23, 213.16it/s]
  5%|▌         | 961/18528 [00:06<00:47, 368.69it/s]
  6%|▌         | 1091/18528 [00:06<00:42, 412.30it/s]
  6%|▋         | 1201/18528 [00:07<00:38, 449.89it/s]
  7%|▋         | 1301/18528 [00:07<00:36, 475.05it/s]
  8%|▊         | 1401/18528 [00:07<00:38, 447.76it/s]
  8%|▊         | 1501/18528 [00:07<00:43, 390.66it/s]
  9%|▉         | 1701/18528 [00:07<00:28, 585.46it/s]
 10%|█         | 1901/18528 [00:08<00:22, 725.09it/s]
 11%|█         | 2003/18528 [00:08<00:26, 635.28it/s]
 11%|█▏        | 2101/18528 [00:08<00:25, 635.10it/s]
 12%|█▏        | 2301/18528 [00:09<00:36, 439.22it/s]
 15%|█▍        | 2693/18528 [00:09<00:19, 827.55it/s]
 15%|█▌        | 2856/18528 [00:11<01:06, 236.60it/s]
 16%|█▌        | 2972/18528 [00:11<00:57, 270.29it/s]
 17%|█▋        | 3073/18528 [00:11<00:54, 281.69it/s]
 17%|█▋        | 3154/18528 [00:12<00:49, 309.11it/s]
 17%|█▋        | 3227/18528 [00:12<00:45, 334.31it/s]
 18%|█▊        | 3301/18528 [00:12<00:48, 314.19it/s]
 18%|█▊        | 3401/18528 [00:13<01:14, 203.59it/s]
 19%|█▉        | 3501/18528 [00:14<01:20, 187.33it/s]
 21%|██        | 3801/18528 [00:14<00:46, 317.40it/s]
 21%|██        | 3901/18528 [00:15<00:55, 265.70it/s]
 22%|██▏       | 4101/18528 [00:15<00:44, 323.29it/s]
 23%|██▎       | 4301/18528 [00:15<00:35, 397.27it/s]
 24%|██▍       | 4401/18528 [00:16<00:42, 334.35it/s]
 24%|██▍       | 4501/18528 [00:16<00:36, 388.71it/s]
 25%|██▌       | 4701/18528 [00:16<00:31, 432.94it/s]
 26%|██▌       | 4801/18528 [00:16<00:30, 454.92it/s]
 26%|██▋       | 4901/18528 [00:17<00:31, 430.89it/s]
 27%|██▋       | 5001/18528 [00:17<00:29, 465.28it/s]
 28%|██▊       | 5201/18528 [00:17<00:19, 679.59it/s]
 29%|██▉       | 5401/18528 [00:17<00:15, 821.44it/s]
 30%|███       | 5601/18528 [00:17<00:13, 928.47it/s]
 31%|███       | 5711/18528 [00:18<00:17, 722.12it/s]
 32%|███▏      | 5901/18528 [00:18<00:23, 532.87it/s]
 33%|███▎      | 6201/18528 [00:18<00:19, 635.68it/s]
 34%|███▍      | 6301/18528 [00:20<00:51, 236.39it/s]
 35%|███▍      | 6401/18528 [00:20<00:44, 271.45it/s]
 35%|███▌      | 6501/18528 [00:21<01:01, 195.99it/s]
 36%|███▌      | 6701/18528 [00:21<00:39, 299.44it/s]
 37%|███▋      | 6801/18528 [00:22<00:40, 291.79it/s]
 38%|███▊      | 7001/18528 [00:22<00:28, 405.11it/s]
 39%|███▉      | 7201/18528 [00:22<00:20, 553.73it/s]
 40%|███▉      | 7401/18528 [00:22<00:16, 693.51it/s]
 41%|████      | 7518/18528 [00:23<00:18, 580.99it/s]
 42%|████▏     | 7701/18528 [00:23<00:17, 617.56it/s]
 42%|████▏     | 7801/18528 [00:23<00:18, 591.73it/s]
 43%|████▎     | 7901/18528 [00:23<00:16, 633.19it/s]
 44%|████▍     | 8147/18528 [00:23<00:11, 942.79it/s]
 45%|████▍     | 8301/18528 [00:23<00:09, 1025.80it/s]
 46%|████▌     | 8431/18528 [00:23<00:09, 1067.37it/s]
 46%|████▋     | 8601/18528 [00:24<00:14, 699.04it/s] 
 48%|████▊     | 8801/18528 [00:24<00:15, 624.73it/s]
 49%|████▉     | 9068/18528 [00:24<00:10, 901.89it/s]
 50%|████▉     | 9209/18528 [00:25<00:11, 840.63it/s]
 50%|█████     | 9328/18528 [00:25<00:15, 601.30it/s]
 52%|█████▏    | 9601/18528 [00:26<00:27, 324.88it/s]
 52%|█████▏    | 9701/18528 [00:27<00:24, 358.78it/s]
 53%|█████▎    | 9801/18528 [00:27<00:23, 366.09it/s]
 53%|█████▎    | 9901/18528 [00:27<00:29, 297.46it/s]
 54%|█████▍    | 10001/18528 [00:28<00:26, 317.17it/s]
 55%|█████▍    | 10101/18528 [00:28<00:29, 285.34it/s]
 55%|█████▌    | 10201/18528 [00:28<00:26, 316.13it/s]
 57%|█████▋    | 10501/18528 [00:28<00:14, 560.42it/s]
 58%|█████▊    | 10801/18528 [00:29<00:09, 776.59it/s]
 59%|█████▉    | 11001/18528 [00:29<00:07, 943.27it/s]
 60%|██████    | 11132/18528 [00:29<00:08, 830.76it/s]
 62%|██████▏   | 11401/18528 [00:29<00:07, 992.06it/s]
 62%|██████▏   | 11521/18528 [00:29<00:07, 897.84it/s]
 63%|██████▎   | 11624/18528 [00:29<00:07, 910.00it/s]
 64%|██████▎   | 11801/18528 [00:30<00:07, 940.46it/s]
 65%|██████▍   | 12001/18528 [00:30<00:05, 1141.39it/s]
 66%|██████▌   | 12201/18528 [00:30<00:05, 1063.62it/s]
 66%|██████▋   | 12320/18528 [00:30<00:08, 773.77it/s] 
 67%|██████▋   | 12415/18528 [00:30<00:07, 800.73it/s]
 68%|██████▊   | 12509/18528 [00:31<00:12, 491.99it/s]
 68%|██████▊   | 12601/18528 [00:31<00:14, 398.98it/s]
 69%|██████▊   | 12701/18528 [00:31<00:15, 377.68it/s]
 69%|██████▉   | 12801/18528 [00:32<00:20, 284.91it/s]
 70%|██████▉   | 12901/18528 [00:32<00:19, 289.54it/s]
 70%|███████   | 13001/18528 [00:33<00:28, 193.55it/s]
 71%|███████   | 13101/18528 [00:34<00:26, 207.36it/s]
 72%|███████▏  | 13301/18528 [00:34<00:15, 344.34it/s]
 72%|███████▏  | 13401/18528 [00:34<00:13, 373.60it/s]
 73%|███████▎  | 13601/18528 [00:34<00:08, 561.32it/s]
 74%|███████▍  | 13706/18528 [00:34<00:07, 618.70it/s]
 75%|███████▍  | 13807/18528 [00:34<00:08, 589.84it/s]
 75%|███████▌  | 13901/18528 [00:35<00:09, 472.25it/s]
 76%|███████▌  | 14001/18528 [00:35<00:08, 549.18it/s]
 77%|███████▋  | 14301/18528 [00:35<00:04, 877.68it/s]
 78%|███████▊  | 14501/18528 [00:35<00:04, 949.05it/s]
 79%|███████▉  | 14611/18528 [00:35<00:04, 949.90it/s]
 80%|████████  | 14859/18528 [00:35<00:02, 1248.06it/s]
 81%|████████  | 15003/18528 [00:36<00:05, 637.65it/s] 
 82%|████████▏ | 15273/18528 [00:36<00:03, 920.01it/s]
 83%|████████▎ | 15430/18528 [00:37<00:06, 502.51it/s]
 84%|████████▍ | 15547/18528 [00:37<00:07, 418.20it/s]
 84%|████████▍ | 15636/18528 [00:38<00:07, 377.60it/s]
 85%|████████▍ | 15707/18528 [00:38<00:07, 367.36it/s]
 86%|████████▌ | 15929/18528 [00:38<00:04, 528.13it/s]
 87%|████████▋ | 16029/18528 [00:38<00:05, 487.10it/s]
 87%|████████▋ | 16129/18528 [00:38<00:04, 502.55it/s]
 88%|████████▊ | 16229/18528 [00:39<00:03, 575.10it/s]
 88%|████████▊ | 16329/18528 [00:39<00:05, 409.57it/s]
 89%|████████▊ | 16429/18528 [00:39<00:04, 463.59it/s]
 91%|█████████ | 16829/18528 [00:39<00:01, 928.27it/s]
 92%|█████████▏| 16972/18528 [00:39<00:01, 1012.85it/s]
 92%|█████████▏| 17129/18528 [00:39<00:01, 1117.42it/s]
 93%|█████████▎| 17265/18528 [00:40<00:02, 485.97it/s] 
 94%|█████████▍| 17429/18528 [00:40<00:01, 593.49it/s]
 95%|█████████▍| 17536/18528 [00:41<00:01, 601.91it/s]
 95%|█████████▌| 17630/18528 [00:41<00:01, 597.23it/s]
 96%|█████████▌| 17729/18528 [00:41<00:01, 463.60it/s]
 96%|█████████▌| 17829/18528 [00:42<00:02, 324.97it/s]
 97%|█████████▋| 17929/18528 [00:42<00:01, 358.88it/s]
 97%|█████████▋| 18029/18528 [00:42<00:01, 395.74it/s]
 98%|█████████▊| 18129/18528 [00:42<00:01, 373.09it/s]
 98%|█████████▊| 18229/18528 [00:43<00:00, 408.62it/s]
 99%|█████████▉| 18329/18528 [00:43<00:00, 310.84it/s]
 99%|█████████▉| 18429/18528 [00:45<00:00, 132.83it/s]
100%|██████████| 18528/18528 [00:45<00:00, 408.98it/s]
2025-05-14 10:34:15,611 - modnet - INFO - Starting target 1/1: is_metal ...
2025-05-14 10:34:15,611 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 10:34:33,664 - modnet - INFO - Computing optimal features...
2025-05-14 10:34:36,633 - modnet - INFO - Selected 50/193 features...
2025-05-14 10:34:38,796 - modnet - INFO - Selected 100/193 features...
2025-05-14 10:34:40,093 - modnet - INFO - Selected 150/193 features...
2025-05-14 10:34:40,492 - modnet - INFO - Done with target 1/1: is_metal.
2025-05-14 10:34:40,492 - modnet - INFO - Merging all features...
2025-05-14 10:34:40,493 - modnet - INFO - Done.
2025-05-14 10:34:40,520 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_train_moddata_f2!
2025-05-14 10:34:40,527 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_test_moddata_f2!
Preparing fold 3 ...
2025-05-14 10:34:40,847 - modnet - INFO - Multiprocessing on 32 workers.
2025-05-14 10:34:40,847 - modnet - INFO - Computing "self" MI (i.e. information entropy) of features

  0%|          | 0/270 [00:00<?, ?it/s]
  0%|          | 1/270 [00:02<09:28,  2.11s/it]
 26%|██▋       | 71/270 [00:02<00:06, 30.01it/s]
 63%|██████▎   | 171/270 [00:03<00:01, 83.53it/s]
100%|██████████| 270/270 [00:03<00:00, 85.97it/s]
2025-05-14 10:34:44,010 - modnet - INFO - Computing cross NMI between all features...

  0%|          | 0/19306 [00:00<?, ?it/s]
  0%|          | 1/19306 [00:06<36:05:48,  6.73s/it]
  1%|          | 101/19306 [00:07<18:21, 17.43it/s] 
  1%|          | 201/19306 [00:08<08:17, 38.36it/s]
  2%|▏         | 301/19306 [00:08<04:56, 64.14it/s]
  2%|▏         | 401/19306 [00:08<03:11, 98.47it/s]
  3%|▎         | 501/19306 [00:08<02:16, 137.45it/s]
  3%|▎         | 601/19306 [00:09<01:41, 184.86it/s]
  4%|▎         | 701/19306 [00:09<01:24, 220.05it/s]
  4%|▍         | 801/19306 [00:09<01:06, 277.88it/s]
  5%|▍         | 923/19306 [00:09<00:47, 383.53it/s]
  6%|▌         | 1189/19306 [00:09<00:25, 697.91it/s]
  7%|▋         | 1320/19306 [00:09<00:25, 707.78it/s]
  8%|▊         | 1501/19306 [00:10<00:32, 546.77it/s]
  8%|▊         | 1601/19306 [00:10<00:30, 577.11it/s]
  9%|▉         | 1701/19306 [00:11<00:49, 356.54it/s]
  9%|▉         | 1801/19306 [00:11<00:43, 399.35it/s]
 10%|█         | 2001/19306 [00:11<00:43, 401.52it/s]
 11%|█         | 2101/19306 [00:12<01:04, 267.08it/s]
 11%|█▏        | 2201/19306 [00:12<00:53, 318.31it/s]
 12%|█▏        | 2357/19306 [00:12<00:38, 443.16it/s]
 13%|█▎        | 2441/19306 [00:12<00:37, 448.95it/s]
 13%|█▎        | 2514/19306 [00:13<00:44, 373.48it/s]
 13%|█▎        | 2601/19306 [00:13<00:54, 304.19it/s]
 14%|█▍        | 2701/19306 [00:13<00:43, 384.46it/s]
 15%|█▍        | 2801/19306 [00:14<01:10, 234.95it/s]
 16%|█▌        | 3001/19306 [00:14<00:47, 345.01it/s]
 16%|█▌        | 3101/19306 [00:15<00:51, 316.50it/s]
 17%|█▋        | 3201/19306 [00:15<01:05, 246.77it/s]
 17%|█▋        | 3301/19306 [00:17<02:01, 132.13it/s]
 18%|█▊        | 3401/19306 [00:18<01:49, 145.12it/s]
 18%|█▊        | 3501/19306 [00:18<01:26, 181.68it/s]
 19%|█▊        | 3601/19306 [00:18<01:14, 210.07it/s]
 19%|█▉        | 3701/19306 [00:18<00:58, 265.99it/s]
 20%|█▉        | 3801/19306 [00:19<00:54, 284.54it/s]
 21%|██        | 4001/19306 [00:19<00:34, 446.59it/s]
 21%|██        | 4101/19306 [00:19<00:36, 420.71it/s]
 23%|██▎       | 4345/19306 [00:19<00:21, 680.94it/s]
 23%|██▎       | 4461/19306 [00:20<00:30, 486.94it/s]
 24%|██▍       | 4601/19306 [00:20<00:35, 419.66it/s]
 24%|██▍       | 4701/19306 [00:20<00:34, 426.84it/s]
 25%|██▍       | 4801/19306 [00:20<00:29, 494.49it/s]
 27%|██▋       | 5167/19306 [00:20<00:14, 964.22it/s]
 28%|██▊       | 5328/19306 [00:21<00:14, 982.86it/s]
 28%|██▊       | 5472/19306 [00:22<00:41, 331.75it/s]
 29%|██▉       | 5601/19306 [00:23<00:47, 287.09it/s]
 30%|██▉       | 5701/19306 [00:23<00:45, 300.44it/s]
 31%|███       | 5901/19306 [00:23<00:30, 434.53it/s]
 31%|███       | 6001/19306 [00:23<00:32, 410.88it/s]
 32%|███▏      | 6101/19306 [00:23<00:30, 430.94it/s]
 32%|███▏      | 6201/19306 [00:24<00:43, 300.66it/s]
 33%|███▎      | 6301/19306 [00:25<00:50, 258.50it/s]
 33%|███▎      | 6401/19306 [00:25<00:43, 297.10it/s]
 34%|███▎      | 6501/19306 [00:25<00:40, 312.55it/s]
 34%|███▍      | 6601/19306 [00:26<01:06, 190.51it/s]
 35%|███▍      | 6701/19306 [00:27<01:07, 187.19it/s]
 35%|███▌      | 6801/19306 [00:27<01:00, 207.26it/s]
 36%|███▌      | 6901/19306 [00:27<00:46, 268.92it/s]
 36%|███▋      | 7001/19306 [00:28<01:02, 196.94it/s]
 37%|███▋      | 7201/19306 [00:28<00:40, 296.04it/s]
 38%|███▊      | 7301/19306 [00:28<00:35, 338.53it/s]
 39%|███▉      | 7601/19306 [00:29<00:20, 579.41it/s]
 40%|████      | 7801/19306 [00:29<00:16, 697.66it/s]
 42%|████▏     | 8166/19306 [00:29<00:10, 1113.01it/s]
 43%|████▎     | 8345/19306 [00:29<00:11, 961.77it/s] 
 45%|████▍     | 8601/19306 [00:29<00:12, 889.39it/s]
 45%|████▌     | 8726/19306 [00:30<00:13, 806.93it/s]
 46%|████▌     | 8901/19306 [00:30<00:19, 523.17it/s]
 48%|████▊     | 9201/19306 [00:31<00:14, 680.67it/s]
 48%|████▊     | 9363/19306 [00:31<00:12, 788.79it/s]
 50%|████▉     | 9601/19306 [00:31<00:11, 825.84it/s]
 51%|█████     | 9801/19306 [00:31<00:12, 732.72it/s]
 51%|█████▏    | 9901/19306 [00:32<00:20, 467.08it/s]
 52%|█████▏    | 10001/19306 [00:33<00:30, 300.33it/s]
 52%|█████▏    | 10101/19306 [00:33<00:27, 329.35it/s]
 53%|█████▎    | 10201/19306 [00:33<00:28, 323.73it/s]
 53%|█████▎    | 10301/19306 [00:34<00:32, 274.50it/s]
 55%|█████▍    | 10587/19306 [00:34<00:17, 507.54it/s]
 56%|█████▌    | 10801/19306 [00:34<00:13, 634.87it/s]
 57%|█████▋    | 11001/19306 [00:34<00:11, 740.03it/s]
 58%|█████▊    | 11117/19306 [00:34<00:10, 800.69it/s]
 58%|█████▊    | 11232/19306 [00:34<00:09, 811.31it/s]
 59%|█████▊    | 11338/19306 [00:35<00:13, 607.00it/s]
 59%|█████▉    | 11422/19306 [00:35<00:13, 598.02it/s]
 60%|██████    | 11601/19306 [00:35<00:11, 659.19it/s]
 61%|██████    | 11701/19306 [00:35<00:12, 605.41it/s]
 61%|██████    | 11801/19306 [00:35<00:12, 609.17it/s]
 62%|██████▏   | 11901/19306 [00:36<00:11, 668.08it/s]
 63%|██████▎   | 12201/19306 [00:36<00:07, 889.48it/s]
 64%|██████▍   | 12401/19306 [00:36<00:09, 735.87it/s]
 65%|██████▍   | 12501/19306 [00:36<00:09, 726.93it/s]
 65%|██████▌   | 12601/19306 [00:36<00:08, 746.47it/s]
 66%|██████▌   | 12701/19306 [00:37<00:12, 540.32it/s]
 66%|██████▋   | 12801/19306 [00:37<00:10, 591.39it/s]
 67%|██████▋   | 12901/19306 [00:37<00:11, 554.59it/s]
 67%|██████▋   | 13001/19306 [00:37<00:13, 479.85it/s]
 68%|██████▊   | 13201/19306 [00:38<00:08, 688.55it/s]
 69%|██████▉   | 13301/19306 [00:38<00:08, 682.16it/s]
 69%|██████▉   | 13401/19306 [00:39<00:18, 315.35it/s]
 70%|██████▉   | 13501/19306 [00:39<00:15, 370.24it/s]
 70%|███████   | 13601/19306 [00:39<00:13, 427.48it/s]
 71%|███████   | 13701/19306 [00:39<00:14, 392.12it/s]
 72%|███████▏  | 13901/19306 [00:39<00:10, 493.00it/s]
 73%|███████▎  | 14001/19306 [00:40<00:14, 363.13it/s]
 73%|███████▎  | 14101/19306 [00:40<00:12, 408.63it/s]
 74%|███████▍  | 14301/19306 [00:40<00:09, 550.14it/s]
 75%|███████▍  | 14401/19306 [00:41<00:10, 466.85it/s]
 75%|███████▌  | 14501/19306 [00:41<00:15, 317.20it/s]
 77%|███████▋  | 14786/19306 [00:41<00:07, 574.00it/s]
 77%|███████▋  | 14903/19306 [00:41<00:07, 594.48it/s]
 78%|███████▊  | 15006/19306 [00:42<00:07, 580.24it/s]
 78%|███████▊  | 15101/19306 [00:42<00:06, 619.99it/s]
 79%|███████▉  | 15241/19306 [00:42<00:05, 750.38it/s]
 80%|████████  | 15501/19306 [00:42<00:03, 1115.62it/s]
 81%|████████  | 15649/19306 [00:42<00:03, 1091.10it/s]
 82%|████████▏ | 15784/19306 [00:42<00:04, 826.75it/s] 
 82%|████████▏ | 15901/19306 [00:43<00:04, 757.67it/s]
 83%|████████▎ | 16043/19306 [00:43<00:03, 879.99it/s]
 84%|████████▍ | 16195/19306 [00:43<00:03, 1013.46it/s]
 85%|████████▍ | 16317/19306 [00:43<00:03, 832.66it/s] 
 85%|████████▌ | 16419/19306 [00:44<00:06, 422.45it/s]
 86%|████████▌ | 16507/19306 [00:44<00:05, 471.44it/s]
 86%|████████▌ | 16607/19306 [00:44<00:05, 514.79it/s]
 87%|████████▋ | 16707/19306 [00:45<00:10, 256.94it/s]
 88%|████████▊ | 16907/19306 [00:45<00:06, 394.87it/s]
 88%|████████▊ | 17007/19306 [00:45<00:05, 445.87it/s]
 89%|████████▉ | 17207/19306 [00:45<00:03, 628.04it/s]
 90%|█████████ | 17407/19306 [00:45<00:02, 757.13it/s]
 91%|█████████ | 17512/19306 [00:46<00:03, 504.74it/s]
 91%|█████████ | 17607/19306 [00:47<00:05, 322.91it/s]
 92%|█████████▏| 17807/19306 [00:47<00:03, 384.82it/s]
 94%|█████████▍| 18107/19306 [00:47<00:02, 541.43it/s]
 94%|█████████▍| 18207/19306 [00:47<00:02, 538.39it/s]
 96%|█████████▋| 18607/19306 [00:48<00:00, 909.09it/s]
 97%|█████████▋| 18737/19306 [00:48<00:00, 884.14it/s]
 98%|█████████▊| 18907/19306 [00:48<00:00, 857.13it/s]
 99%|█████████▉| 19065/19306 [00:48<00:00, 971.96it/s]
 99%|█████████▉| 19185/19306 [00:48<00:00, 826.38it/s]
100%|█████████▉| 19286/19306 [00:49<00:00, 361.10it/s]
100%|██████████| 19306/19306 [00:49<00:00, 389.31it/s]
2025-05-14 10:35:33,819 - modnet - INFO - Starting target 1/1: is_metal ...
2025-05-14 10:35:33,819 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 10:35:52,970 - modnet - INFO - Computing optimal features...
2025-05-14 10:35:55,971 - modnet - INFO - Selected 50/197 features...
2025-05-14 10:35:58,178 - modnet - INFO - Selected 100/197 features...
2025-05-14 10:35:59,530 - modnet - INFO - Selected 150/197 features...
2025-05-14 10:35:59,997 - modnet - INFO - Done with target 1/1: is_metal.
2025-05-14 10:35:59,997 - modnet - INFO - Merging all features...
2025-05-14 10:35:59,997 - modnet - INFO - Done.
2025-05-14 10:36:00,020 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_train_moddata_f3!
2025-05-14 10:36:00,027 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_test_moddata_f3!
Preparing fold 4 ...
2025-05-14 10:36:00,353 - modnet - INFO - Multiprocessing on 32 workers.
2025-05-14 10:36:00,354 - modnet - INFO - Computing "self" MI (i.e. information entropy) of features

  0%|          | 0/270 [00:00<?, ?it/s]
  0%|          | 1/270 [00:02<09:13,  2.06s/it]
 26%|██▋       | 71/270 [00:02<00:06, 30.13it/s]
 63%|██████▎   | 171/270 [00:03<00:01, 77.05it/s]
100%|██████████| 270/270 [00:03<00:00, 82.52it/s]
2025-05-14 10:36:03,653 - modnet - INFO - Computing cross NMI between all features...

  0%|          | 0/19306 [00:00<?, ?it/s]
  0%|          | 1/19306 [00:06<32:16:10,  6.02s/it]
  1%|          | 101/19306 [00:06<14:11, 22.56it/s] 
  1%|          | 201/19306 [00:06<06:07, 51.95it/s]
  2%|▏         | 301/19306 [00:06<03:41, 85.67it/s]
  2%|▏         | 401/19306 [00:06<02:23, 131.56it/s]
  3%|▎         | 501/19306 [00:06<01:41, 185.32it/s]
  4%|▍         | 801/19306 [00:07<00:49, 372.89it/s]
  5%|▍         | 901/19306 [00:07<00:46, 392.37it/s]
  6%|▌         | 1101/19306 [00:07<00:39, 463.82it/s]
  6%|▌         | 1201/19306 [00:08<00:42, 429.67it/s]
  7%|▋         | 1301/19306 [00:08<00:47, 375.78it/s]
  7%|▋         | 1401/19306 [00:08<00:54, 325.93it/s]
  8%|▊         | 1501/19306 [00:09<00:59, 298.28it/s]
  8%|▊         | 1601/19306 [00:09<00:48, 368.23it/s]
 10%|▉         | 1901/19306 [00:09<00:32, 533.48it/s]
 11%|█         | 2101/19306 [00:09<00:28, 600.69it/s]
 11%|█▏        | 2201/19306 [00:10<00:27, 626.93it/s]
 12%|█▏        | 2301/19306 [00:10<00:35, 478.72it/s]
 12%|█▏        | 2401/19306 [00:10<00:43, 391.23it/s]
 13%|█▎        | 2501/19306 [00:11<01:07, 247.78it/s]
 13%|█▎        | 2601/19306 [00:11<00:55, 300.00it/s]
 14%|█▍        | 2701/19306 [00:12<01:15, 218.82it/s]
 15%|█▍        | 2801/19306 [00:12<01:05, 251.78it/s]
 15%|█▌        | 2901/19306 [00:13<00:51, 318.47it/s]
 16%|█▌        | 3001/19306 [00:13<00:52, 312.02it/s]
 16%|█▌        | 3101/19306 [00:13<01:02, 261.17it/s]
 17%|█▋        | 3201/19306 [00:14<01:05, 245.80it/s]
 17%|█▋        | 3301/19306 [00:14<00:57, 277.15it/s]
 18%|█▊        | 3401/19306 [00:14<00:53, 297.26it/s]
 18%|█▊        | 3501/19306 [00:15<01:15, 208.43it/s]
 19%|█▊        | 3601/19306 [00:15<01:05, 240.54it/s]
 19%|█▉        | 3701/19306 [00:16<01:11, 217.62it/s]
 20%|█▉        | 3801/19306 [00:16<00:56, 274.48it/s]
 20%|██        | 3901/19306 [00:16<00:51, 299.57it/s]
 21%|██        | 4002/19306 [00:17<00:40, 380.27it/s]
 22%|██▏       | 4201/19306 [00:17<00:29, 512.53it/s]
 22%|██▏       | 4301/19306 [00:17<00:26, 569.46it/s]
 23%|██▎       | 4401/19306 [00:17<00:36, 412.82it/s]
 23%|██▎       | 4501/19306 [00:18<00:39, 370.23it/s]
 24%|██▍       | 4701/19306 [00:18<00:38, 378.71it/s]
 25%|██▍       | 4801/19306 [00:19<00:42, 341.50it/s]
 25%|██▌       | 4901/19306 [00:19<00:54, 262.00it/s]
 26%|██▋       | 5101/19306 [00:19<00:35, 397.35it/s]
 27%|██▋       | 5201/19306 [00:20<00:47, 297.02it/s]
 28%|██▊       | 5401/19306 [00:20<00:39, 355.45it/s]
 28%|██▊       | 5501/19306 [00:21<00:40, 338.47it/s]
 30%|██▉       | 5701/19306 [00:22<00:46, 293.37it/s]
 31%|███       | 5901/19306 [00:22<00:35, 380.76it/s]
 31%|███       | 6001/19306 [00:22<00:30, 432.25it/s]
 32%|███▏      | 6201/19306 [00:23<00:34, 380.36it/s]
 33%|███▎      | 6301/19306 [00:23<00:33, 387.04it/s]
 33%|███▎      | 6401/19306 [00:24<00:56, 230.14it/s]
 34%|███▎      | 6501/19306 [00:24<00:57, 221.43it/s]
 35%|███▍      | 6701/19306 [00:25<00:44, 286.06it/s]
 35%|███▌      | 6801/19306 [00:25<00:38, 328.91it/s]
 36%|███▌      | 6901/19306 [00:25<00:38, 323.75it/s]
 36%|███▋      | 7001/19306 [00:26<00:40, 306.29it/s]
 37%|███▋      | 7201/19306 [00:26<00:45, 267.76it/s]
 38%|███▊      | 7401/19306 [00:27<00:30, 387.77it/s]
 39%|███▉      | 7501/19306 [00:27<00:29, 396.43it/s]
 39%|███▉      | 7601/19306 [00:27<00:27, 427.56it/s]
 40%|████      | 7801/19306 [00:28<00:28, 399.61it/s]
 41%|████      | 7901/19306 [00:28<00:25, 446.18it/s]
 41%|████▏     | 8001/19306 [00:28<00:28, 401.50it/s]
 42%|████▏     | 8101/19306 [00:28<00:24, 462.12it/s]
 43%|████▎     | 8301/19306 [00:28<00:19, 576.46it/s]
 44%|████▍     | 8463/19306 [00:28<00:14, 730.14it/s]
 44%|████▍     | 8559/19306 [00:29<00:15, 703.54it/s]
 45%|████▌     | 8701/19306 [00:29<00:12, 821.03it/s]
 46%|████▌     | 8901/19306 [00:29<00:10, 991.19it/s]
 47%|████▋     | 9015/19306 [00:30<00:22, 451.40it/s]
 47%|████▋     | 9101/19306 [00:30<00:20, 486.25it/s]
 48%|████▊     | 9301/19306 [00:30<00:14, 689.04it/s]
 49%|████▉     | 9501/19306 [00:30<00:11, 827.91it/s]
 50%|█████     | 9701/19306 [00:30<00:09, 1013.07it/s]
 51%|█████     | 9835/19306 [00:31<00:15, 606.07it/s] 
 51%|█████▏    | 9937/19306 [00:31<00:18, 505.05it/s]
 52%|█████▏    | 10043/19306 [00:31<00:16, 576.65it/s]
 52%|█████▏    | 10131/19306 [00:31<00:17, 529.82it/s]
 53%|█████▎    | 10205/19306 [00:31<00:22, 405.46it/s]
 53%|█████▎    | 10301/19306 [00:32<00:19, 458.35it/s]
 54%|█████▍    | 10401/19306 [00:32<00:19, 459.53it/s]
 54%|█████▍    | 10501/19306 [00:32<00:21, 414.20it/s]
 55%|█████▍    | 10601/19306 [00:33<00:27, 312.54it/s]
 56%|█████▌    | 10801/19306 [00:33<00:17, 487.80it/s]
 56%|█████▋    | 10901/19306 [00:33<00:16, 523.46it/s]
 57%|█████▋    | 11001/19306 [00:33<00:22, 371.00it/s]
 58%|█████▊    | 11101/19306 [00:34<00:34, 235.72it/s]
 58%|█████▊    | 11201/19306 [00:35<00:30, 263.63it/s]
 59%|█████▊    | 11301/19306 [00:35<00:27, 290.45it/s]
 60%|█████▉    | 11501/19306 [00:35<00:19, 405.46it/s]
 61%|██████    | 11701/19306 [00:35<00:12, 588.51it/s]
 62%|██████▏   | 11901/19306 [00:35<00:10, 677.79it/s]
 63%|██████▎   | 12101/19306 [00:35<00:08, 853.44it/s]
 63%|██████▎   | 12220/19306 [00:36<00:08, 809.34it/s]
 64%|██████▍   | 12324/19306 [00:36<00:08, 785.72it/s]
 64%|██████▍   | 12418/19306 [00:36<00:08, 784.49it/s]
 65%|██████▍   | 12508/19306 [00:36<00:12, 544.66it/s]
 65%|██████▌   | 12601/19306 [00:36<00:11, 606.43it/s]
 66%|██████▋   | 12801/19306 [00:37<00:16, 404.02it/s]
 67%|██████▋   | 12901/19306 [00:37<00:13, 462.37it/s]
 68%|██████▊   | 13201/19306 [00:38<00:11, 547.41it/s]
 69%|██████▉   | 13401/19306 [00:38<00:09, 642.78it/s]
 70%|██████▉   | 13501/19306 [00:38<00:11, 517.29it/s]
 70%|███████   | 13601/19306 [00:38<00:12, 458.51it/s]
 71%|███████▏  | 13763/19306 [00:39<00:09, 600.10it/s]
 72%|███████▏  | 13850/19306 [00:39<00:14, 365.28it/s]
 72%|███████▏  | 13915/19306 [00:39<00:16, 332.68it/s]
 73%|███████▎  | 14001/19306 [00:40<00:21, 247.76it/s]
 74%|███████▍  | 14301/19306 [00:41<00:15, 326.52it/s]
 75%|███████▌  | 14501/19306 [00:41<00:11, 407.46it/s]
 76%|███████▌  | 14601/19306 [00:41<00:10, 449.20it/s]
 77%|███████▋  | 14943/19306 [00:41<00:05, 767.25it/s]
 78%|███████▊  | 15101/19306 [00:41<00:04, 845.14it/s]
 79%|███████▉  | 15228/19306 [00:42<00:06, 645.01it/s]
 79%|███████▉  | 15327/19306 [00:42<00:06, 596.39it/s]
 80%|███████▉  | 15410/19306 [00:42<00:07, 487.54it/s]
 80%|████████  | 15501/19306 [00:42<00:07, 523.09it/s]
 81%|████████▏ | 15714/19306 [00:43<00:04, 778.13it/s]
 82%|████████▏ | 15825/19306 [00:43<00:06, 564.88it/s]
 83%|████████▎ | 16001/19306 [00:43<00:04, 716.18it/s]
 84%|████████▍ | 16201/19306 [00:43<00:04, 757.54it/s]
 84%|████████▍ | 16301/19306 [00:43<00:04, 683.33it/s]
 85%|████████▍ | 16384/19306 [00:44<00:05, 496.10it/s]
 86%|████████▌ | 16507/19306 [00:44<00:05, 549.37it/s]
 87%|████████▋ | 16707/19306 [00:45<00:06, 393.38it/s]
 87%|████████▋ | 16807/19306 [00:45<00:07, 331.32it/s]
 88%|████████▊ | 17007/19306 [00:45<00:04, 479.60it/s]
 89%|████████▊ | 17107/19306 [00:45<00:04, 483.34it/s]
 89%|████████▉ | 17207/19306 [00:46<00:03, 533.28it/s]
 90%|████████▉ | 17307/19306 [00:46<00:03, 512.54it/s]
 91%|█████████ | 17507/19306 [00:46<00:02, 740.37it/s]
 91%|█████████ | 17610/19306 [00:46<00:03, 487.24it/s]
 92%|█████████▏| 17707/19306 [00:47<00:04, 327.62it/s]
 93%|█████████▎| 17981/19306 [00:47<00:02, 578.99it/s]
 94%|█████████▍| 18106/19306 [00:47<00:01, 605.84it/s]
 94%|█████████▍| 18221/19306 [00:47<00:01, 684.23it/s]
 95%|█████████▍| 18332/19306 [00:48<00:01, 673.58it/s]
 95%|█████████▌| 18429/19306 [00:48<00:01, 724.74it/s]
 96%|█████████▋| 18607/19306 [00:48<00:00, 936.84it/s]
 97%|█████████▋| 18807/19306 [00:48<00:00, 654.18it/s]
 99%|█████████▉| 19102/19306 [00:48<00:00, 1001.47it/s]
100%|█████████▉| 19259/19306 [00:49<00:00, 461.82it/s] 
100%|██████████| 19306/19306 [00:49<00:00, 388.73it/s]
2025-05-14 10:36:53,600 - modnet - INFO - Starting target 1/1: is_metal ...
2025-05-14 10:36:53,600 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 10:37:12,020 - modnet - INFO - Computing optimal features...
2025-05-14 10:37:15,091 - modnet - INFO - Selected 50/197 features...
2025-05-14 10:37:17,351 - modnet - INFO - Selected 100/197 features...
2025-05-14 10:37:18,732 - modnet - INFO - Selected 150/197 features...
2025-05-14 10:37:19,208 - modnet - INFO - Done with target 1/1: is_metal.
2025-05-14 10:37:19,208 - modnet - INFO - Merging all features...
2025-05-14 10:37:19,208 - modnet - INFO - Done.
2025-05-14 10:37:19,231 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_train_moddata_f4!
2025-05-14 10:37:19,238 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_test_moddata_f4!
Preparing fold 5 ...
2025-05-14 10:37:19,568 - modnet - INFO - Multiprocessing on 32 workers.
2025-05-14 10:37:19,568 - modnet - INFO - Computing "self" MI (i.e. information entropy) of features

  0%|          | 0/270 [00:00<?, ?it/s]
  0%|          | 1/270 [00:02<09:24,  2.10s/it]
 26%|██▋       | 71/270 [00:02<00:06, 30.82it/s]
 63%|██████▎   | 171/270 [00:03<00:01, 82.62it/s]
100%|██████████| 270/270 [00:03<00:00, 86.06it/s]
2025-05-14 10:37:22,732 - modnet - INFO - Computing cross NMI between all features...

  0%|          | 0/18915 [00:00<?, ?it/s]
  0%|          | 1/18915 [00:05<29:07:01,  5.54s/it]
  1%|          | 192/18915 [00:05<06:28, 48.15it/s] 
  2%|▏         | 401/18915 [00:05<02:36, 118.46it/s]
  4%|▎         | 688/18915 [00:05<01:13, 247.75it/s]
  5%|▍         | 890/18915 [00:06<01:12, 247.79it/s]
  5%|▌         | 1029/18915 [00:06<01:03, 279.52it/s]
  6%|▋         | 1201/18915 [00:08<01:27, 202.99it/s]
  7%|▋         | 1301/18915 [00:08<01:13, 241.04it/s]
  7%|▋         | 1401/18915 [00:08<01:02, 282.01it/s]
  8%|▊         | 1601/18915 [00:09<00:54, 316.73it/s]
 10%|▉         | 1801/18915 [00:09<00:43, 391.47it/s]
 10%|█         | 1901/18915 [00:09<00:40, 425.28it/s]
 11%|█         | 2001/18915 [00:09<00:40, 418.14it/s]
 12%|█▏        | 2201/18915 [00:09<00:27, 606.33it/s]
 12%|█▏        | 2302/18915 [00:10<00:38, 432.49it/s]
 13%|█▎        | 2401/18915 [00:11<01:05, 253.43it/s]
 13%|█▎        | 2501/18915 [00:11<00:57, 286.63it/s]
 14%|█▍        | 2601/18915 [00:13<01:48, 150.91it/s]
 14%|█▍        | 2701/18915 [00:13<01:33, 173.30it/s]
 15%|█▍        | 2801/18915 [00:14<01:38, 163.43it/s]
 16%|█▌        | 3001/18915 [00:14<01:18, 202.60it/s]
 16%|█▋        | 3101/18915 [00:15<01:17, 204.21it/s]
 17%|█▋        | 3201/18915 [00:15<01:03, 248.69it/s]
 17%|█▋        | 3301/18915 [00:16<01:43, 151.06it/s]
 18%|█▊        | 3401/18915 [00:18<02:18, 111.71it/s]
 19%|█▊        | 3501/18915 [00:18<01:59, 129.10it/s]
 20%|█▉        | 3701/18915 [00:18<01:12, 209.82it/s]
 20%|██        | 3801/18915 [00:19<01:00, 250.76it/s]
 21%|██        | 4001/18915 [00:19<00:39, 379.34it/s]
 22%|██▏       | 4201/18915 [00:19<00:28, 522.78it/s]
 23%|██▎       | 4401/18915 [00:19<00:25, 570.06it/s]
 24%|██▍       | 4501/18915 [00:20<00:29, 481.70it/s]
 24%|██▍       | 4601/18915 [00:20<00:33, 425.49it/s]
 25%|██▍       | 4701/18915 [00:20<00:30, 465.31it/s]
 26%|██▌       | 4901/18915 [00:21<00:31, 441.22it/s]
 26%|██▋       | 5001/18915 [00:21<00:31, 438.48it/s]
 27%|██▋       | 5101/18915 [00:21<00:48, 285.09it/s]
 27%|██▋       | 5201/18915 [00:22<00:46, 296.32it/s]
 28%|██▊       | 5301/18915 [00:22<00:44, 307.52it/s]
 29%|██▊       | 5401/18915 [00:22<00:38, 354.97it/s]
 29%|██▉       | 5501/18915 [00:23<00:41, 319.61it/s]
 30%|██▉       | 5601/18915 [00:23<00:36, 365.26it/s]
 30%|███       | 5701/18915 [00:23<00:37, 355.53it/s]
 31%|███       | 5901/18915 [00:24<00:32, 405.53it/s]
 32%|███▏      | 6001/18915 [00:24<00:32, 399.68it/s]
 32%|███▏      | 6101/18915 [00:25<00:54, 236.87it/s]
 33%|███▎      | 6201/18915 [00:25<00:51, 246.86it/s]
 33%|███▎      | 6301/18915 [00:25<00:48, 258.29it/s]
 34%|███▍      | 6401/18915 [00:26<00:52, 236.40it/s]
 34%|███▍      | 6501/18915 [00:26<00:50, 248.27it/s]
 35%|███▌      | 6701/18915 [00:27<00:43, 278.70it/s]
 36%|███▋      | 6901/18915 [00:28<00:43, 278.27it/s]
 37%|███▋      | 7001/18915 [00:29<01:06, 179.40it/s]
 38%|███▊      | 7201/18915 [00:29<00:42, 272.43it/s]
 39%|███▊      | 7301/18915 [00:30<00:48, 239.65it/s]
 39%|███▉      | 7401/18915 [00:30<00:42, 271.00it/s]
 40%|███▉      | 7501/18915 [00:30<00:45, 248.17it/s]
 40%|████      | 7630/18915 [00:30<00:33, 333.51it/s]
 41%|████      | 7801/18915 [00:31<00:30, 369.73it/s]
 42%|████▏     | 7901/18915 [00:31<00:32, 337.11it/s]
 42%|████▏     | 8001/18915 [00:31<00:29, 374.64it/s]
 43%|████▎     | 8201/18915 [00:32<00:27, 387.96it/s]
 44%|████▍     | 8301/18915 [00:32<00:25, 413.13it/s]
 44%|████▍     | 8401/18915 [00:32<00:26, 400.50it/s]
 45%|████▍     | 8501/18915 [00:32<00:24, 427.66it/s]
 45%|████▌     | 8601/18915 [00:33<00:20, 493.57it/s]
 46%|████▌     | 8743/18915 [00:33<00:15, 638.73it/s]
 47%|████▋     | 8826/18915 [00:33<00:17, 569.18it/s]
 47%|████▋     | 8901/18915 [00:33<00:17, 567.85it/s]
 48%|████▊     | 9001/18915 [00:33<00:20, 488.39it/s]
 49%|████▊     | 9201/18915 [00:33<00:14, 689.51it/s]
 49%|████▉     | 9301/18915 [00:34<00:13, 717.09it/s]
 50%|████▉     | 9401/18915 [00:34<00:15, 605.52it/s]
 51%|█████     | 9601/18915 [00:34<00:21, 429.18it/s]
 51%|█████▏    | 9701/18915 [00:35<00:28, 318.65it/s]
 52%|█████▏    | 9801/18915 [00:36<00:41, 220.71it/s]
 52%|█████▏    | 9901/18915 [00:36<00:36, 246.35it/s]
 54%|█████▍    | 10201/18915 [00:37<00:22, 379.23it/s]
 54%|█████▍    | 10301/18915 [00:37<00:20, 423.92it/s]
 55%|█████▍    | 10401/18915 [00:37<00:23, 365.23it/s]
 56%|█████▌    | 10634/18915 [00:37<00:14, 569.29it/s]
 57%|█████▋    | 10736/18915 [00:38<00:16, 508.81it/s]
 57%|█████▋    | 10818/18915 [00:38<00:17, 474.28it/s]
 58%|█████▊    | 11001/18915 [00:38<00:13, 575.51it/s]
 59%|█████▊    | 11101/18915 [00:38<00:13, 584.06it/s]
 59%|█████▉    | 11201/18915 [00:39<00:18, 425.37it/s]
 60%|██████    | 11401/18915 [00:39<00:13, 544.02it/s]
 61%|██████    | 11501/18915 [00:39<00:16, 444.21it/s]
 61%|██████▏   | 11601/18915 [00:39<00:16, 455.93it/s]
 62%|██████▏   | 11701/18915 [00:39<00:14, 491.48it/s]
 62%|██████▏   | 11801/18915 [00:40<00:23, 304.13it/s]
 63%|██████▎   | 12001/18915 [00:40<00:17, 403.95it/s]
 64%|██████▍   | 12101/18915 [00:41<00:16, 410.20it/s]
 65%|██████▍   | 12201/18915 [00:41<00:14, 477.15it/s]
 65%|██████▌   | 12301/18915 [00:41<00:17, 384.85it/s]
 66%|██████▌   | 12401/18915 [00:41<00:15, 419.32it/s]
 66%|██████▌   | 12501/18915 [00:41<00:13, 478.85it/s]
 67%|██████▋   | 12701/18915 [00:42<00:10, 588.64it/s]
 68%|██████▊   | 12901/18915 [00:42<00:10, 596.83it/s]
 69%|██████▊   | 13001/18915 [00:43<00:20, 284.81it/s]
 69%|██████▉   | 13101/18915 [00:43<00:19, 298.91it/s]
 70%|██████▉   | 13201/18915 [00:43<00:16, 347.90it/s]
 70%|███████   | 13301/18915 [00:44<00:13, 406.80it/s]
 71%|███████▏  | 13501/18915 [00:44<00:08, 617.29it/s]
 72%|███████▏  | 13601/18915 [00:44<00:09, 534.10it/s]
 72%|███████▏  | 13701/18915 [00:44<00:09, 573.94it/s]
 73%|███████▎  | 13801/18915 [00:44<00:09, 521.18it/s]
 73%|███████▎  | 13901/18915 [00:44<00:08, 580.61it/s]
 74%|███████▍  | 14001/18915 [00:45<00:14, 328.63it/s]
 75%|███████▍  | 14101/18915 [00:45<00:15, 320.72it/s]
 75%|███████▌  | 14201/18915 [00:46<00:15, 305.01it/s]
 76%|███████▌  | 14301/18915 [00:46<00:15, 300.44it/s]
 76%|███████▌  | 14401/18915 [00:47<00:17, 259.28it/s]
 77%|███████▋  | 14501/18915 [00:47<00:15, 289.98it/s]
 78%|███████▊  | 14701/18915 [00:47<00:09, 464.11it/s]
 78%|███████▊  | 14801/18915 [00:47<00:07, 515.90it/s]
 79%|███████▉  | 14901/18915 [00:47<00:08, 493.96it/s]
 79%|███████▉  | 15001/18915 [00:48<00:08, 469.54it/s]
 80%|███████▉  | 15101/18915 [00:48<00:10, 380.46it/s]
 81%|████████▏ | 15401/18915 [00:48<00:05, 639.79it/s]
 82%|████████▏ | 15501/18915 [00:48<00:05, 598.91it/s]
 82%|████████▏ | 15601/18915 [00:49<00:06, 511.63it/s]
 84%|████████▎ | 15801/18915 [00:49<00:04, 722.36it/s]
 85%|████████▍ | 16001/18915 [00:49<00:04, 587.79it/s]
 85%|████████▌ | 16101/18915 [00:49<00:04, 574.62it/s]
 86%|████████▌ | 16201/18915 [00:50<00:07, 350.89it/s]
 86%|████████▌ | 16301/18915 [00:50<00:06, 411.75it/s]
 87%|████████▋ | 16401/18915 [00:50<00:05, 447.54it/s]
 87%|████████▋ | 16516/18915 [00:51<00:04, 544.30it/s]
 88%|████████▊ | 16716/18915 [00:52<00:06, 319.80it/s]
 89%|████████▉ | 16816/18915 [00:52<00:05, 355.67it/s]
 89%|████████▉ | 16916/18915 [00:52<00:06, 306.91it/s]
 90%|█████████ | 17116/18915 [00:52<00:03, 452.44it/s]
 92%|█████████▏| 17416/18915 [00:53<00:02, 640.76it/s]
 93%|█████████▎| 17516/18915 [00:53<00:02, 636.11it/s]
 93%|█████████▎| 17616/18915 [00:53<00:01, 652.99it/s]
 95%|█████████▍| 17916/18915 [00:53<00:01, 703.13it/s]
 96%|█████████▋| 18216/18915 [00:54<00:00, 822.84it/s]
 97%|█████████▋| 18316/18915 [00:54<00:00, 777.14it/s]
 97%|█████████▋| 18416/18915 [00:54<00:00, 578.93it/s]
 98%|█████████▊| 18516/18915 [00:54<00:00, 624.45it/s]
 98%|█████████▊| 18616/18915 [00:54<00:00, 542.46it/s]
 99%|█████████▉| 18716/18915 [00:55<00:00, 315.80it/s]
 99%|█████████▉| 18816/18915 [00:56<00:00, 304.05it/s]
100%|██████████| 18915/18915 [00:56<00:00, 337.68it/s]
2025-05-14 10:38:19,046 - modnet - INFO - Starting target 1/1: is_metal ...
2025-05-14 10:38:19,046 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 10:38:37,118 - modnet - INFO - Computing optimal features...
2025-05-14 10:38:40,087 - modnet - INFO - Selected 50/195 features...
2025-05-14 10:38:42,273 - modnet - INFO - Selected 100/195 features...
2025-05-14 10:38:43,588 - modnet - INFO - Selected 150/195 features...
2025-05-14 10:38:44,018 - modnet - INFO - Done with target 1/1: is_metal.
2025-05-14 10:38:44,018 - modnet - INFO - Merging all features...
2025-05-14 10:38:44,018 - modnet - INFO - Done.
2025-05-14 10:38:44,041 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_train_moddata_f5!
2025-05-14 10:38:44,048 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_test_moddata_f5!
Training fold 1 ...
Processing fold 1 ...
2025-05-14 10:38:44.558126: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:44.611566: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:44.642934: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:44.643035: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:44.643833: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:44,913 - modnet - INFO - Targets:
2025-05-14 10:38:44,914 - modnet - INFO - 1)is_metal: regression
2025-05-14 10:38:45,607 - modnet - INFO - Multiprocessing on 32 cores. Total of 128 cores available.
2025-05-14 10:38:45,608 - modnet - INFO - Generation number 0

  0%|          | 0/250 [00:00<?, ?it/s]2025-05-14 10:38:48.859136: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.859238: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.859303: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.859493: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.859574: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.859629: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.859724: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.859738: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.862923: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.862977: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.863134: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.863218: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.863221: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.863256: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.867580: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.867601: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.867624: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.867789: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.867930: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.867955: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.867992: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.873631: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.873632: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.873632: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.923018: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.923012: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.923024: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.923013: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.964758: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.964760: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.964919: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:48.964919: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 10:38:53.336698: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.337179: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.337211: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.337248: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.337608: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.364058: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.364578: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.364607: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.364644: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.365511: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.377983: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.378447: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.378765: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.378803: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.379058: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.399721: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.400276: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.400310: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.400352: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.400745: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.403466: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.419916: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.420383: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.420418: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.420454: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.420826: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.430658: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.432470: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.435532: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.441408: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.441941: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.442228: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.442266: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.442542: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.442637: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.445478: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.461984: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.465801: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.466270: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.466302: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.466337: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.466697: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.475554: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.482233: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.484315: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.484759: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.484789: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.484821: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.485126: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.494569: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.506006: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.506561: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.507028: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.507065: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.507533: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.518698: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.519567: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.530961: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.531465: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.531495: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.531526: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.531864: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.544220: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.545134: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.553987: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.554489: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.554517: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.554549: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.554846: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.560310: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.561355: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.579234: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.579768: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.579797: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.579836: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.580173: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.582657: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.594540: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.597561: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.598083: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.598115: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.598152: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.598543: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.605045: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.612951: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.617521: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.619049: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.619581: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.619614: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.619978: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.620379: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.625529: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.638800: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.639251: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.639282: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.639319: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.639600: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.639910: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.640920: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.656713: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.657511: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.665096: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.665672: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.665715: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.665754: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.666104: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.685622: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.686447: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.690220: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.690725: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.690976: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.691021: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.691423: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.697451: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.698259: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.704465: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.704886: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.704917: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.704951: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.705241: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.726970: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.727476: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.727515: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.727560: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.728014: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.747758: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.748203: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.749028: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.749076: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.749406: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.751637: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.752446: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.767969: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.768504: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.768533: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.768566: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.768889: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.779946: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.780808: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.786736: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.787372: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.792825: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.793337: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.793365: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.793415: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.793762: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.800961: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.801824: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.811683: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.812253: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.812281: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.812314: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.812679: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.821475: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.822286: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.840452: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.841234: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.841763: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.842187: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.842213: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.842242: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.842505: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.872959: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.873841: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.876125: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.876570: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.876601: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.876825: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.877234: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.885192: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.886024: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.896957: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.897408: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.897437: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.897472: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.897813: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.910382: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.911162: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.916495: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.916975: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.917002: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.917034: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.917345: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.955330: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.955998: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.956166: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.956203: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.956640: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.961979: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.962932: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.966999: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.967648: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.970138: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.970693: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.970723: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.970765: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.971210: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.985818: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:53.986666: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:53.994342: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:53.995094: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:53.995127: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:53.995177: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:53.995644: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:54.011283: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:54.012246: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:54.013519: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:54.013601: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:54.014393: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:54.060892: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:54.061524: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 10:38:54.061559: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 10:38:54.061597: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm022.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 10:38:54.061918: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 10:38:54.062669: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:54.063702: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:54.072243: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:54.073002: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:54.076074: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:54.076969: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:54.098943: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:54.099845: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
2025-05-14 10:38:54.138269: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 10:38:54.139222: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz

  0%|          | 1/250 [00:12<53:56, 13.00s/it]
  1%|          | 2/250 [00:13<22:48,  5.52s/it]
  1%|          | 3/250 [00:13<12:34,  3.06s/it]
  2%|▏         | 5/250 [00:13<05:40,  1.39s/it]
  2%|▏         | 6/250 [00:13<04:15,  1.05s/it]
  3%|▎         | 8/250 [00:14<02:33,  1.57it/s]
  4%|▍         | 11/250 [00:14<01:41,  2.35it/s]
  5%|▌         | 13/250 [00:15<01:52,  2.11it/s]
  6%|▌         | 14/250 [00:16<01:47,  2.19it/s]
  6%|▌         | 15/250 [00:17<02:35,  1.51it/s]
  6%|▋         | 16/250 [00:19<03:14,  1.21it/s]
  7%|▋         | 18/250 [00:19<02:16,  1.69it/s]
  8%|▊         | 19/250 [00:20<02:25,  1.59it/s]
  8%|▊         | 20/250 [00:20<02:21,  1.63it/s]
  8%|▊         | 21/250 [00:27<08:23,  2.20s/it]
  9%|▉         | 22/250 [00:30<09:11,  2.42s/it]
  9%|▉         | 23/250 [00:33<09:26,  2.50s/it]
 10%|▉         | 24/250 [00:34<07:58,  2.12s/it]
 10%|█         | 25/250 [00:34<05:46,  1.54s/it]
 10%|█         | 26/250 [00:36<05:52,  1.57s/it]
 11%|█         | 27/250 [00:37<05:19,  1.43s/it]
 12%|█▏        | 29/250 [00:38<03:39,  1.01it/s]
 12%|█▏        | 30/250 [00:39<03:20,  1.10it/s]
 12%|█▏        | 31/250 [00:39<03:08,  1.16it/s]
 13%|█▎        | 32/250 [00:40<03:19,  1.09it/s]
 13%|█▎        | 33/250 [00:41<02:41,  1.34it/s]
 14%|█▎        | 34/250 [00:42<03:40,  1.02s/it]
 14%|█▍        | 35/250 [00:43<03:35,  1.00s/it]
 14%|█▍        | 36/250 [00:44<02:48,  1.27it/s]
 15%|█▍        | 37/250 [00:44<02:05,  1.69it/s]
 15%|█▌        | 38/250 [00:45<02:23,  1.48it/s]
 16%|█▌        | 39/250 [00:45<02:05,  1.68it/s]
 16%|█▋        | 41/250 [00:46<02:10,  1.61it/s]
 18%|█▊        | 44/250 [00:47<01:14,  2.75it/s]
 18%|█▊        | 45/250 [00:48<01:48,  1.88it/s]
 18%|█▊        | 46/250 [00:48<01:35,  2.14it/s]
 19%|█▉        | 47/250 [00:48<01:27,  2.32it/s]
 19%|█▉        | 48/250 [00:50<02:11,  1.54it/s]
 20%|██        | 50/250 [00:51<02:04,  1.60it/s]
 21%|██        | 52/250 [00:52<01:49,  1.81it/s]
 21%|██        | 53/250 [00:52<01:42,  1.92it/s]
 22%|██▏       | 54/250 [00:53<02:12,  1.48it/s]
 22%|██▏       | 55/250 [00:54<02:09,  1.51it/s]
 22%|██▏       | 56/250 [00:55<02:18,  1.40it/s]
 23%|██▎       | 58/250 [00:56<02:02,  1.57it/s]
 24%|██▍       | 60/250 [00:56<01:23,  2.27it/s]
 24%|██▍       | 61/250 [00:56<01:19,  2.37it/s]
 25%|██▍       | 62/250 [00:57<01:07,  2.77it/s]
 26%|██▌       | 64/250 [00:57<00:46,  3.98it/s]
 26%|██▌       | 65/250 [00:57<00:47,  3.91it/s]
 26%|██▋       | 66/250 [01:00<02:33,  1.20it/s]
 27%|██▋       | 67/250 [01:00<02:02,  1.50it/s]
 27%|██▋       | 68/250 [01:00<01:42,  1.77it/s]
 28%|██▊       | 69/250 [01:00<01:23,  2.17it/s]
 28%|██▊       | 71/250 [01:01<00:59,  3.02it/s]
 29%|██▉       | 72/250 [01:01<01:15,  2.37it/s]
 29%|██▉       | 73/250 [01:02<01:39,  1.77it/s]
 30%|███       | 75/250 [01:04<01:46,  1.65it/s]
 30%|███       | 76/250 [01:04<01:29,  1.93it/s]
 31%|███       | 77/250 [01:05<01:43,  1.66it/s]
 31%|███       | 78/250 [01:05<01:30,  1.90it/s]
 32%|███▏      | 79/250 [01:08<03:05,  1.09s/it]
 32%|███▏      | 80/250 [01:10<03:54,  1.38s/it]
 32%|███▏      | 81/250 [01:10<02:54,  1.03s/it]
 33%|███▎      | 82/250 [01:10<02:23,  1.17it/s]
 33%|███▎      | 83/250 [01:11<02:03,  1.36it/s]
 34%|███▎      | 84/250 [01:11<01:48,  1.53it/s]
 34%|███▍      | 85/250 [01:12<01:32,  1.78it/s]
 34%|███▍      | 86/250 [01:12<01:16,  2.15it/s]
 35%|███▌      | 88/250 [01:12<00:55,  2.90it/s]
 36%|███▌      | 89/250 [01:12<00:46,  3.43it/s]
 36%|███▌      | 90/250 [01:14<01:38,  1.62it/s]
 36%|███▋      | 91/250 [01:16<02:36,  1.01it/s]
 37%|███▋      | 92/250 [01:16<02:01,  1.30it/s]
 38%|███▊      | 94/250 [01:17<01:28,  1.76it/s]
 38%|███▊      | 95/250 [01:18<01:38,  1.57it/s]
 39%|███▉      | 98/250 [01:18<00:58,  2.61it/s]
 40%|███▉      | 99/250 [01:18<00:50,  2.99it/s]
 40%|████      | 100/250 [01:18<00:47,  3.14it/s]
 40%|████      | 101/250 [01:19<00:56,  2.65it/s]
 41%|████      | 102/250 [01:20<01:14,  1.97it/s]
 41%|████      | 103/250 [01:21<01:24,  1.75it/s]
 42%|████▏     | 104/250 [01:24<02:59,  1.23s/it]
 42%|████▏     | 105/250 [01:25<03:15,  1.34s/it]
 42%|████▏     | 106/250 [01:25<02:27,  1.03s/it]
 43%|████▎     | 108/250 [01:26<01:23,  1.70it/s]
 44%|████▎     | 109/250 [01:26<01:27,  1.61it/s]
 44%|████▍     | 110/250 [01:26<01:08,  2.04it/s]
 45%|████▍     | 112/250 [01:27<01:05,  2.09it/s]
 45%|████▌     | 113/250 [01:27<00:54,  2.54it/s]
 46%|████▌     | 115/250 [01:28<00:44,  3.02it/s]
 47%|████▋     | 117/250 [01:28<00:34,  3.86it/s]
 48%|████▊     | 119/250 [01:31<01:18,  1.67it/s]
 48%|████▊     | 121/250 [01:32<01:15,  1.72it/s]
 49%|████▉     | 122/250 [01:33<01:22,  1.56it/s]
 49%|████▉     | 123/250 [01:34<01:27,  1.46it/s]
 50%|████▉     | 124/250 [01:34<01:19,  1.59it/s]
 50%|█████     | 125/250 [01:35<01:20,  1.56it/s]
 50%|█████     | 126/250 [01:35<01:19,  1.56it/s]
 51%|█████     | 127/250 [01:36<01:13,  1.67it/s]
 52%|█████▏    | 129/250 [01:36<00:46,  2.61it/s]
 52%|█████▏    | 130/250 [01:37<01:02,  1.92it/s]
 52%|█████▏    | 131/250 [01:37<00:58,  2.02it/s]
 53%|█████▎    | 132/250 [01:38<01:08,  1.73it/s]
 53%|█████▎    | 133/250 [01:38<00:57,  2.02it/s]
 54%|█████▎    | 134/250 [01:39<00:46,  2.48it/s]
 54%|█████▍    | 136/250 [01:39<00:39,  2.88it/s]
 55%|█████▍    | 137/250 [01:40<00:38,  2.93it/s]
 55%|█████▌    | 138/250 [01:40<00:35,  3.14it/s]
 56%|█████▌    | 139/250 [01:41<00:50,  2.20it/s]
 56%|█████▌    | 140/250 [01:41<00:58,  1.88it/s]
 57%|█████▋    | 142/250 [01:43<01:23,  1.30it/s]
 57%|█████▋    | 143/250 [01:44<01:17,  1.39it/s]
 58%|█████▊    | 144/250 [01:45<01:12,  1.46it/s]
 58%|█████▊    | 145/250 [01:53<04:37,  2.65s/it]
 58%|█████▊    | 146/250 [01:55<04:27,  2.57s/it]
 59%|█████▉    | 147/250 [01:56<03:34,  2.09s/it]
 59%|█████▉    | 148/250 [01:59<03:49,  2.25s/it]
 60%|█████▉    | 149/250 [01:59<02:47,  1.65s/it]
 60%|██████    | 150/250 [02:00<02:42,  1.62s/it]
 60%|██████    | 151/250 [02:01<02:02,  1.24s/it]
 61%|██████    | 153/250 [02:01<01:20,  1.21it/s]
 62%|██████▏   | 154/250 [02:01<01:04,  1.50it/s]
 62%|██████▏   | 155/250 [02:02<01:07,  1.41it/s]
 62%|██████▏   | 156/250 [02:03<01:11,  1.32it/s]
 63%|██████▎   | 157/250 [02:04<00:59,  1.57it/s]
 63%|██████▎   | 158/250 [02:05<01:17,  1.19it/s]
 64%|██████▎   | 159/250 [02:05<01:08,  1.33it/s]
 64%|██████▍   | 160/250 [02:07<01:19,  1.13it/s]
 64%|██████▍   | 161/250 [02:07<01:01,  1.46it/s]
 65%|██████▍   | 162/250 [02:07<00:51,  1.70it/s]
 65%|██████▌   | 163/250 [02:08<00:53,  1.61it/s]
 66%|██████▌   | 164/250 [02:09<01:13,  1.18it/s]
 66%|██████▌   | 165/250 [02:10<00:57,  1.47it/s]
 67%|██████▋   | 167/250 [02:10<00:32,  2.52it/s]
 67%|██████▋   | 168/250 [02:10<00:34,  2.36it/s]
 68%|██████▊   | 169/250 [02:10<00:31,  2.59it/s]
 68%|██████▊   | 171/250 [02:12<00:39,  2.02it/s]
 69%|██████▉   | 172/250 [02:12<00:39,  1.97it/s]
 69%|██████▉   | 173/250 [02:12<00:32,  2.39it/s]
 70%|██████▉   | 174/250 [02:13<00:31,  2.41it/s]
 70%|███████   | 175/250 [02:14<00:42,  1.79it/s]
 70%|███████   | 176/250 [02:15<01:00,  1.23it/s]
 71%|███████   | 177/250 [02:15<00:44,  1.63it/s]
 72%|███████▏  | 179/250 [02:16<00:38,  1.82it/s]
 72%|███████▏  | 180/250 [02:17<00:45,  1.53it/s]
 73%|███████▎  | 182/250 [02:22<01:31,  1.34s/it]
 73%|███████▎  | 183/250 [02:23<01:28,  1.32s/it]
 74%|███████▎  | 184/250 [02:23<01:10,  1.06s/it]
 74%|███████▍  | 185/250 [02:24<01:06,  1.02s/it]
 74%|███████▍  | 186/250 [02:25<00:55,  1.16it/s]
 75%|███████▍  | 187/250 [02:25<00:42,  1.49it/s]
 75%|███████▌  | 188/250 [02:26<00:46,  1.32it/s]
 76%|███████▌  | 190/250 [02:26<00:28,  2.10it/s]
 76%|███████▋  | 191/250 [02:27<00:26,  2.23it/s]
 77%|███████▋  | 192/250 [02:27<00:23,  2.48it/s]
 77%|███████▋  | 193/250 [02:28<00:28,  1.99it/s]
 78%|███████▊  | 194/250 [02:29<00:40,  1.38it/s]
 78%|███████▊  | 195/250 [02:30<00:47,  1.15it/s]
 78%|███████▊  | 196/250 [02:31<00:41,  1.30it/s]
 79%|███████▉  | 198/250 [02:32<00:33,  1.57it/s]
 80%|███████▉  | 199/250 [02:32<00:26,  1.89it/s]
 80%|████████  | 200/250 [02:32<00:21,  2.29it/s]
 80%|████████  | 201/250 [02:32<00:19,  2.51it/s]
 81%|████████  | 202/250 [02:33<00:23,  2.07it/s]
 81%|████████  | 203/250 [02:33<00:20,  2.31it/s]
 82%|████████▏ | 205/250 [02:36<00:35,  1.29it/s]
 82%|████████▏ | 206/250 [02:36<00:31,  1.40it/s]
 83%|████████▎ | 207/250 [02:40<01:00,  1.41s/it]
 83%|████████▎ | 208/250 [02:41<00:55,  1.32s/it]
 84%|████████▎ | 209/250 [02:41<00:40,  1.02it/s]
 84%|████████▍ | 210/250 [02:42<00:46,  1.16s/it]
 84%|████████▍ | 211/250 [02:44<00:49,  1.27s/it]
 85%|████████▍ | 212/250 [02:45<00:40,  1.07s/it]
 85%|████████▌ | 213/250 [02:45<00:34,  1.08it/s]
 86%|████████▌ | 214/250 [02:45<00:26,  1.38it/s]
 86%|████████▌ | 215/250 [02:46<00:23,  1.46it/s]
 86%|████████▋ | 216/250 [02:47<00:23,  1.43it/s]
 87%|████████▋ | 218/250 [02:47<00:17,  1.80it/s]
 88%|████████▊ | 219/250 [02:48<00:14,  2.11it/s]
 88%|████████▊ | 220/250 [02:48<00:13,  2.22it/s]
 88%|████████▊ | 221/250 [02:48<00:12,  2.24it/s]
 89%|████████▉ | 222/250 [02:49<00:12,  2.31it/s]
 89%|████████▉ | 223/250 [02:49<00:12,  2.17it/s]
 90%|████████▉ | 224/250 [02:50<00:09,  2.79it/s]
 90%|█████████ | 226/250 [02:50<00:05,  4.52it/s]
 91%|█████████ | 227/250 [02:50<00:04,  4.96it/s]
 92%|█████████▏| 229/250 [02:50<00:03,  5.57it/s]
 92%|█████████▏| 231/250 [02:50<00:03,  5.80it/s]
 93%|█████████▎| 232/250 [02:51<00:03,  5.06it/s]
 94%|█████████▍| 235/250 [02:51<00:02,  5.78it/s]
 94%|█████████▍| 236/250 [02:51<00:02,  5.41it/s]
 95%|█████████▍| 237/250 [02:52<00:02,  5.43it/s]
 95%|█████████▌| 238/250 [02:53<00:05,  2.27it/s]
 96%|█████████▌| 239/250 [02:54<00:05,  1.91it/s]
 96%|█████████▋| 241/250 [02:54<00:03,  2.44it/s]
 97%|█████████▋| 242/250 [02:57<00:07,  1.01it/s]
 97%|█████████▋| 243/250 [03:01<00:12,  1.73s/it]
 98%|█████████▊| 244/250 [03:04<00:12,  2.09s/it]
 98%|█████████▊| 245/250 [03:05<00:08,  1.71s/it]
 98%|█████████▊| 246/250 [03:09<00:09,  2.46s/it]
 99%|█████████▉| 247/250 [03:12<00:07,  2.48s/it]
 99%|█████████▉| 248/250 [03:12<00:03,  1.93s/it]
100%|█████████▉| 249/250 [03:14<00:01,  1.73s/it]
100%|██████████| 250/250 [03:14<00:00,  1.32s/it]
100%|██████████| 250/250 [03:14<00:00,  1.29it/s]
2025-05-14 10:42:00,160 - modnet - INFO - Loss per individual: ind 0: 0.505 	ind 1: 0.097 	ind 2: 0.497 	ind 3: 0.380 	ind 4: 0.134 	ind 5: 0.189 	ind 6: 0.269 	ind 7: 0.100 	ind 8: 0.135 	ind 9: 0.311 	ind 10: 0.113 	ind 11: 0.121 	ind 12: 0.126 	ind 13: 0.367 	ind 14: 0.775 	ind 15: 0.994 	ind 16: 0.510 	ind 17: 0.501 	ind 18: 0.125 	ind 19: 0.114 	ind 20: 1.317 	ind 21: 0.209 	ind 22: 0.138 	ind 23: 0.197 	ind 24: 0.110 	ind 25: 0.109 	ind 26: 0.505 	ind 27: 0.108 	ind 28: 0.160 	ind 29: 0.110 	ind 30: 0.119 	ind 31: 0.116 	ind 32: 0.098 	ind 33: 0.128 	ind 34: 0.101 	ind 35: 0.104 	ind 36: 0.450 	ind 37: 0.105 	ind 38: 0.103 	ind 39: 0.134 	ind 40: 0.118 	ind 41: 0.111 	ind 42: 0.101 	ind 43: 0.108 	ind 44: 0.209 	ind 45: 0.101 	ind 46: 0.108 	ind 47: 0.114 	ind 48: 0.315 	ind 49: 0.441 	
2025-05-14 10:42:00,162 - modnet - INFO - Generation number 1

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:11<48:31, 11.69s/it]
  1%|          | 2/250 [00:11<20:12,  4.89s/it]
  1%|          | 3/250 [00:14<16:52,  4.10s/it]
  2%|▏         | 4/250 [00:15<11:49,  2.88s/it]
  2%|▏         | 5/250 [00:16<07:42,  1.89s/it]
  2%|▏         | 6/250 [00:16<05:14,  1.29s/it]
  3%|▎         | 7/250 [00:18<05:57,  1.47s/it]
  3%|▎         | 8/250 [00:18<04:15,  1.06s/it]
  4%|▎         | 9/250 [00:19<04:00,  1.00it/s]
  4%|▍         | 10/250 [00:21<05:15,  1.32s/it]
  5%|▍         | 12/250 [00:22<03:52,  1.02it/s]
  5%|▌         | 13/250 [00:23<03:33,  1.11it/s]
  6%|▌         | 14/250 [00:23<03:10,  1.24it/s]
  6%|▌         | 15/250 [00:24<02:49,  1.39it/s]
  6%|▋         | 16/250 [00:24<02:16,  1.71it/s]
  7%|▋         | 17/250 [00:24<02:21,  1.65it/s]
  7%|▋         | 18/250 [00:26<03:28,  1.11it/s]
  8%|▊         | 19/250 [00:27<03:01,  1.28it/s]
  8%|▊         | 21/250 [00:28<02:51,  1.34it/s]
  9%|▉         | 22/250 [00:29<02:53,  1.31it/s]
  9%|▉         | 23/250 [00:30<03:23,  1.12it/s]
 10%|▉         | 24/250 [00:32<04:05,  1.08s/it]
 10%|█         | 25/250 [00:32<03:26,  1.09it/s]
 10%|█         | 26/250 [00:35<05:38,  1.51s/it]
 11%|█         | 27/250 [00:35<04:11,  1.13s/it]
 11%|█         | 28/250 [00:36<04:01,  1.09s/it]
 12%|█▏        | 29/250 [00:43<10:25,  2.83s/it]
 12%|█▏        | 30/250 [00:44<07:33,  2.06s/it]
 12%|█▏        | 31/250 [00:44<06:14,  1.71s/it]
 13%|█▎        | 32/250 [00:45<04:48,  1.32s/it]
 13%|█▎        | 33/250 [00:48<06:58,  1.93s/it]
 14%|█▎        | 34/250 [00:49<05:25,  1.51s/it]
 14%|█▍        | 35/250 [00:49<04:06,  1.15s/it]
 14%|█▍        | 36/250 [00:52<06:15,  1.76s/it]
 15%|█▍        | 37/250 [00:53<05:25,  1.53s/it]
 15%|█▌        | 38/250 [00:54<04:08,  1.17s/it]
 16%|█▌        | 39/250 [00:55<04:17,  1.22s/it]
 16%|█▋        | 41/250 [00:56<03:07,  1.11it/s]
 17%|█▋        | 42/250 [00:57<03:24,  1.02it/s]
 18%|█▊        | 44/250 [01:01<04:26,  1.29s/it]
 18%|█▊        | 45/250 [01:02<04:16,  1.25s/it]
 18%|█▊        | 46/250 [01:02<03:35,  1.05s/it]
 19%|█▉        | 47/250 [01:03<03:36,  1.07s/it]
 20%|█▉        | 49/250 [01:04<02:40,  1.25it/s]
 20%|██        | 50/250 [01:05<02:25,  1.37it/s]
 20%|██        | 51/250 [01:05<02:19,  1.43it/s]
 21%|██        | 52/250 [01:06<01:59,  1.66it/s]
 21%|██        | 53/250 [01:07<02:20,  1.40it/s]
 22%|██▏       | 55/250 [01:07<01:45,  1.84it/s]
 22%|██▏       | 56/250 [01:10<03:20,  1.04s/it]
 23%|██▎       | 58/250 [01:11<02:41,  1.19it/s]
 24%|██▎       | 59/250 [01:13<03:43,  1.17s/it]
 24%|██▍       | 61/250 [01:16<03:46,  1.20s/it]
 25%|██▍       | 62/250 [01:17<03:48,  1.22s/it]
 25%|██▌       | 63/250 [01:20<04:45,  1.53s/it]
 26%|██▌       | 64/250 [01:21<05:00,  1.62s/it]
 26%|██▌       | 65/250 [01:22<04:25,  1.43s/it]
 26%|██▋       | 66/250 [01:23<03:21,  1.10s/it]
 27%|██▋       | 68/250 [01:24<03:02,  1.00s/it]
 28%|██▊       | 69/250 [01:26<03:31,  1.17s/it]
 28%|██▊       | 70/250 [01:27<03:15,  1.09s/it]
 29%|██▉       | 72/250 [01:27<02:00,  1.48it/s]
 29%|██▉       | 73/250 [01:28<02:02,  1.45it/s]
 30%|██▉       | 74/250 [01:28<01:43,  1.69it/s]
 30%|███       | 75/250 [01:29<01:32,  1.90it/s]
 31%|███       | 77/250 [01:29<00:57,  3.00it/s]
 31%|███       | 78/250 [01:29<00:49,  3.49it/s]
 32%|███▏      | 79/250 [01:29<00:57,  2.97it/s]
 32%|███▏      | 81/250 [01:30<00:48,  3.48it/s]
 33%|███▎      | 82/250 [01:31<01:17,  2.18it/s]
 33%|███▎      | 83/250 [01:32<01:57,  1.42it/s]
 34%|███▎      | 84/250 [01:33<02:09,  1.28it/s]
 34%|███▍      | 85/250 [01:34<01:51,  1.48it/s]
 34%|███▍      | 86/250 [01:34<01:30,  1.80it/s]
 35%|███▍      | 87/250 [01:36<03:07,  1.15s/it]
 35%|███▌      | 88/250 [01:42<06:26,  2.38s/it]
 36%|███▌      | 89/250 [01:45<06:41,  2.50s/it]
 36%|███▋      | 91/250 [01:47<04:53,  1.85s/it]
 37%|███▋      | 92/250 [01:50<05:33,  2.11s/it]
 37%|███▋      | 93/250 [01:50<04:30,  1.72s/it]
 38%|███▊      | 94/250 [01:51<03:46,  1.45s/it]
 38%|███▊      | 95/250 [01:52<03:26,  1.33s/it]
 38%|███▊      | 96/250 [01:59<07:22,  2.87s/it]
 39%|███▉      | 97/250 [01:59<05:33,  2.18s/it]
 39%|███▉      | 98/250 [02:00<04:47,  1.89s/it]
 40%|███▉      | 99/250 [02:01<03:57,  1.57s/it]
 40%|████      | 101/250 [02:03<03:13,  1.30s/it]
 41%|████      | 102/250 [02:05<03:14,  1.32s/it]
 41%|████      | 103/250 [02:05<02:31,  1.03s/it]
 42%|████▏     | 104/250 [02:05<02:10,  1.12it/s]
 42%|████▏     | 105/250 [02:06<01:52,  1.29it/s]
 42%|████▏     | 106/250 [02:07<01:52,  1.28it/s]
 43%|████▎     | 107/250 [02:07<01:32,  1.55it/s]
 44%|████▎     | 109/250 [02:09<01:45,  1.33it/s]
 44%|████▍     | 111/250 [02:09<01:21,  1.71it/s]
 45%|████▍     | 112/250 [02:12<02:08,  1.07it/s]
 45%|████▌     | 113/250 [02:14<02:45,  1.21s/it]
 46%|████▌     | 114/250 [02:16<03:17,  1.46s/it]
 46%|████▌     | 115/250 [02:16<02:45,  1.22s/it]
 46%|████▋     | 116/250 [02:17<02:30,  1.13s/it]
 47%|████▋     | 117/250 [02:19<02:49,  1.28s/it]
 47%|████▋     | 118/250 [02:21<03:18,  1.50s/it]
 48%|████▊     | 119/250 [02:23<03:50,  1.76s/it]
 48%|████▊     | 120/250 [02:25<03:46,  1.74s/it]
 48%|████▊     | 121/250 [02:25<02:43,  1.27s/it]
 49%|████▉     | 122/250 [02:30<05:09,  2.42s/it]
 49%|████▉     | 123/250 [02:32<04:23,  2.08s/it]
 50%|████▉     | 124/250 [02:32<03:29,  1.66s/it]
 50%|█████     | 126/250 [02:32<01:54,  1.08it/s]
 51%|█████     | 127/250 [02:33<01:39,  1.24it/s]
 51%|█████     | 128/250 [02:34<01:42,  1.20it/s]
 52%|█████▏    | 129/250 [02:34<01:20,  1.50it/s]
 52%|█████▏    | 130/250 [02:34<01:06,  1.81it/s]
 52%|█████▏    | 131/250 [02:34<00:52,  2.25it/s]
 53%|█████▎    | 132/250 [02:36<01:42,  1.16it/s]
 53%|█████▎    | 133/250 [02:37<01:47,  1.08it/s]
 54%|█████▎    | 134/250 [02:38<01:44,  1.11it/s]
 54%|█████▍    | 135/250 [02:39<01:41,  1.13it/s]
 54%|█████▍    | 136/250 [02:40<01:38,  1.16it/s]
 55%|█████▌    | 138/250 [02:40<01:01,  1.82it/s]
 56%|█████▌    | 139/250 [02:40<00:51,  2.17it/s]
 56%|█████▌    | 140/250 [02:41<00:50,  2.17it/s]
 56%|█████▋    | 141/250 [02:41<00:46,  2.33it/s]
 57%|█████▋    | 142/250 [02:43<01:26,  1.25it/s]
 57%|█████▋    | 143/250 [02:44<01:21,  1.31it/s]
 58%|█████▊    | 144/250 [02:45<01:38,  1.07it/s]
 58%|█████▊    | 145/250 [02:46<01:42,  1.03it/s]
 58%|█████▊    | 146/250 [02:48<01:56,  1.12s/it]
 59%|█████▉    | 147/250 [02:49<02:00,  1.17s/it]
 59%|█████▉    | 148/250 [02:50<02:02,  1.20s/it]
 60%|█████▉    | 149/250 [02:50<01:32,  1.09it/s]
 60%|██████    | 150/250 [02:52<01:40,  1.01s/it]
 60%|██████    | 151/250 [02:52<01:14,  1.33it/s]
 61%|██████    | 153/250 [02:52<00:44,  2.20it/s]
 62%|██████▏   | 154/250 [02:53<00:57,  1.67it/s]
 62%|██████▏   | 155/250 [02:54<01:00,  1.56it/s]
 62%|██████▏   | 156/250 [02:54<01:01,  1.53it/s]
 63%|██████▎   | 157/250 [02:57<01:39,  1.07s/it]
 63%|██████▎   | 158/250 [03:00<02:27,  1.60s/it]
 64%|██████▎   | 159/250 [03:00<01:48,  1.19s/it]
 64%|██████▍   | 161/250 [03:00<01:00,  1.48it/s]
 65%|██████▍   | 162/250 [03:00<00:47,  1.87it/s]
 65%|██████▌   | 163/250 [03:00<00:47,  1.84it/s]
 66%|██████▌   | 164/250 [03:02<01:06,  1.29it/s]
 66%|██████▌   | 165/250 [03:02<00:53,  1.60it/s]
 66%|██████▋   | 166/250 [03:03<00:51,  1.62it/s]
 67%|██████▋   | 167/250 [03:04<01:14,  1.11it/s]
 67%|██████▋   | 168/250 [03:05<00:58,  1.41it/s]
 68%|██████▊   | 169/250 [03:09<02:28,  1.84s/it]
 68%|██████▊   | 170/250 [03:09<01:47,  1.34s/it]
 68%|██████▊   | 171/250 [03:11<01:44,  1.32s/it]
 69%|██████▉   | 172/250 [03:11<01:21,  1.04s/it]
 69%|██████▉   | 173/250 [03:13<01:47,  1.40s/it]
 70%|██████▉   | 174/250 [03:14<01:30,  1.19s/it]
 70%|███████   | 175/250 [03:15<01:22,  1.10s/it]
 70%|███████   | 176/250 [03:17<01:48,  1.47s/it]
 71%|███████   | 177/250 [03:19<02:02,  1.68s/it]
 71%|███████   | 178/250 [03:22<02:32,  2.12s/it]
 72%|███████▏  | 180/250 [03:24<01:41,  1.45s/it]
 72%|███████▏  | 181/250 [03:26<01:57,  1.70s/it]
 73%|███████▎  | 182/250 [03:28<01:48,  1.60s/it]
 73%|███████▎  | 183/250 [03:28<01:33,  1.40s/it]
 74%|███████▎  | 184/250 [03:34<02:50,  2.58s/it]
 74%|███████▍  | 185/250 [03:34<02:05,  1.93s/it]
 74%|███████▍  | 186/250 [03:36<01:50,  1.73s/it]
 75%|███████▍  | 187/250 [03:36<01:22,  1.31s/it]
 75%|███████▌  | 188/250 [03:36<01:08,  1.11s/it]
 76%|███████▌  | 189/250 [03:37<00:52,  1.16it/s]
 76%|███████▌  | 190/250 [03:37<00:42,  1.43it/s]
 76%|███████▋  | 191/250 [03:38<00:46,  1.28it/s]
 77%|███████▋  | 192/250 [03:38<00:38,  1.50it/s]
 77%|███████▋  | 193/250 [03:39<00:44,  1.28it/s]
 78%|███████▊  | 194/250 [03:40<00:35,  1.58it/s]
 78%|███████▊  | 195/250 [03:41<00:38,  1.44it/s]
 78%|███████▊  | 196/250 [03:41<00:34,  1.58it/s]
 79%|███████▉  | 197/250 [03:41<00:26,  1.98it/s]
 80%|███████▉  | 199/250 [03:42<00:18,  2.72it/s]
 80%|████████  | 200/250 [03:42<00:21,  2.28it/s]
 80%|████████  | 201/250 [03:43<00:20,  2.41it/s]
 81%|████████  | 202/250 [03:44<00:33,  1.45it/s]
 81%|████████  | 203/250 [03:44<00:27,  1.70it/s]
 82%|████████▏ | 204/250 [03:45<00:27,  1.69it/s]
 82%|████████▏ | 205/250 [03:46<00:31,  1.45it/s]
 82%|████████▏ | 206/250 [03:47<00:29,  1.50it/s]
 83%|████████▎ | 207/250 [03:47<00:22,  1.94it/s]
 83%|████████▎ | 208/250 [03:47<00:17,  2.44it/s]
 84%|████████▎ | 209/250 [03:48<00:19,  2.08it/s]
 84%|████████▍ | 211/250 [03:48<00:12,  3.12it/s]
 85%|████████▍ | 212/250 [03:49<00:17,  2.13it/s]
 86%|████████▌ | 214/250 [03:49<00:10,  3.30it/s]
 86%|████████▌ | 215/250 [03:49<00:10,  3.22it/s]
 86%|████████▋ | 216/250 [03:50<00:12,  2.77it/s]
 88%|████████▊ | 219/250 [03:50<00:07,  4.11it/s]
 88%|████████▊ | 220/250 [03:50<00:07,  4.28it/s]
 88%|████████▊ | 221/250 [03:51<00:08,  3.27it/s]
 89%|████████▉ | 222/250 [03:54<00:25,  1.11it/s]
 89%|████████▉ | 223/250 [03:54<00:20,  1.34it/s]
 90%|████████▉ | 224/250 [03:56<00:31,  1.21s/it]
 90%|█████████ | 225/250 [03:57<00:24,  1.00it/s]
 90%|█████████ | 226/250 [03:57<00:19,  1.24it/s]
 91%|█████████ | 227/250 [03:58<00:15,  1.50it/s]
 91%|█████████ | 228/250 [04:00<00:25,  1.14s/it]
 92%|█████████▏| 229/250 [04:02<00:29,  1.42s/it]
 92%|█████████▏| 230/250 [04:06<00:45,  2.26s/it]
 92%|█████████▏| 231/250 [04:10<00:49,  2.58s/it]
 93%|█████████▎| 232/250 [04:10<00:34,  1.90s/it]
 93%|█████████▎| 233/250 [04:12<00:32,  1.89s/it]
 94%|█████████▎| 234/250 [04:13<00:26,  1.65s/it]
 94%|█████████▍| 235/250 [04:13<00:18,  1.20s/it]
 94%|█████████▍| 236/250 [04:13<00:12,  1.09it/s]
 95%|█████████▌| 238/250 [04:13<00:06,  1.80it/s]
 96%|█████████▌| 240/250 [04:14<00:03,  2.78it/s]
 96%|█████████▋| 241/250 [04:15<00:04,  2.02it/s]
 97%|█████████▋| 242/250 [04:15<00:04,  1.97it/s]
 97%|█████████▋| 243/250 [04:16<00:03,  1.81it/s]
 98%|█████████▊| 244/250 [04:17<00:03,  1.51it/s]
 98%|█████████▊| 245/250 [04:19<00:05,  1.01s/it]
 98%|█████████▊| 246/250 [04:19<00:03,  1.29it/s]
100%|█████████▉| 249/250 [04:21<00:00,  1.30it/s]
100%|██████████| 250/250 [04:22<00:00,  1.41it/s]
100%|██████████| 250/250 [04:22<00:00,  1.05s/it]
2025-05-14 10:46:22,353 - modnet - INFO - Loss per individual: ind 0: 0.107 	ind 1: 0.108 	ind 2: 0.104 	ind 3: 0.109 	ind 4: 0.102 	ind 5: 0.100 	ind 6: 0.102 	ind 7: 0.102 	ind 8: 0.096 	ind 9: 0.100 	ind 10: 0.133 	ind 11: 0.151 	ind 12: 0.109 	ind 13: 0.114 	ind 14: 0.117 	ind 15: 0.132 	ind 16: 0.116 	ind 17: 0.148 	ind 18: 0.105 	ind 19: 0.106 	ind 20: 0.131 	ind 21: 0.106 	ind 22: 0.109 	ind 23: 0.113 	ind 24: 0.103 	ind 25: 0.098 	ind 26: 0.101 	ind 27: 0.102 	ind 28: 0.096 	ind 29: 0.101 	ind 30: 0.095 	ind 31: 0.141 	ind 32: 0.114 	ind 33: 0.110 	ind 34: 0.107 	ind 35: 0.102 	ind 36: 0.108 	ind 37: 0.107 	ind 38: 0.106 	ind 39: 0.120 	ind 40: 0.109 	ind 41: 0.121 	ind 42: 0.104 	ind 43: 0.103 	ind 44: 0.103 	ind 45: 0.103 	ind 46: 0.133 	ind 47: 0.104 	ind 48: 0.101 	ind 49: 0.100 	
2025-05-14 10:46:22,354 - modnet - INFO - Generation number 2

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:13<55:02, 13.26s/it]
  1%|          | 2/250 [00:13<23:23,  5.66s/it]
  1%|          | 3/250 [00:13<13:17,  3.23s/it]
  2%|▏         | 4/250 [00:15<10:29,  2.56s/it]
  2%|▏         | 5/250 [00:16<08:52,  2.17s/it]
  3%|▎         | 7/250 [00:17<04:33,  1.12s/it]
  3%|▎         | 8/250 [00:17<03:54,  1.03it/s]
  4%|▍         | 10/250 [00:18<03:14,  1.24it/s]
  5%|▍         | 12/250 [00:19<02:06,  1.89it/s]
  5%|▌         | 13/250 [00:19<01:52,  2.10it/s]
  6%|▌         | 14/250 [00:19<01:31,  2.57it/s]
  6%|▌         | 15/250 [00:20<02:29,  1.58it/s]
  6%|▋         | 16/250 [00:22<03:07,  1.25it/s]
  7%|▋         | 17/250 [00:24<04:57,  1.28s/it]
  7%|▋         | 18/250 [00:28<07:30,  1.94s/it]
  8%|▊         | 19/250 [00:28<05:30,  1.43s/it]
  8%|▊         | 20/250 [00:28<04:29,  1.17s/it]
  8%|▊         | 21/250 [00:29<03:53,  1.02s/it]
  9%|▉         | 22/250 [00:30<03:44,  1.02it/s]
  9%|▉         | 23/250 [00:31<03:18,  1.14it/s]
 10%|▉         | 24/250 [00:31<02:53,  1.30it/s]
 10%|█         | 25/250 [00:32<03:04,  1.22it/s]
 10%|█         | 26/250 [00:34<03:48,  1.02s/it]
 11%|█         | 27/250 [00:34<03:01,  1.23it/s]
 11%|█         | 28/250 [00:34<02:30,  1.47it/s]
 12%|█▏        | 29/250 [00:35<02:02,  1.81it/s]
 12%|█▏        | 31/250 [00:35<01:40,  2.19it/s]
 13%|█▎        | 32/250 [00:35<01:27,  2.49it/s]
 13%|█▎        | 33/250 [00:36<01:14,  2.92it/s]
 14%|█▎        | 34/250 [00:36<01:11,  3.00it/s]
 14%|█▍        | 35/250 [00:38<03:00,  1.19it/s]
 14%|█▍        | 36/250 [00:38<02:15,  1.58it/s]
 15%|█▍        | 37/250 [00:38<01:42,  2.07it/s]
 15%|█▌        | 38/250 [00:39<01:25,  2.48it/s]
 16%|█▌        | 39/250 [00:39<01:48,  1.94it/s]
 16%|█▌        | 40/250 [00:40<02:14,  1.56it/s]
 16%|█▋        | 41/250 [00:41<02:22,  1.47it/s]
 17%|█▋        | 42/250 [00:44<05:09,  1.49s/it]
 18%|█▊        | 44/250 [00:45<03:11,  1.08it/s]
 18%|█▊        | 45/250 [00:47<04:21,  1.28s/it]
 18%|█▊        | 46/250 [00:50<05:10,  1.52s/it]
 19%|█▉        | 47/250 [00:51<05:11,  1.54s/it]
 20%|█▉        | 49/250 [00:52<03:26,  1.03s/it]
 20%|██        | 50/250 [00:53<03:26,  1.03s/it]
 20%|██        | 51/250 [00:54<03:05,  1.07it/s]
 21%|██        | 52/250 [00:54<02:27,  1.35it/s]
 21%|██        | 53/250 [00:55<02:43,  1.21it/s]
 22%|██▏       | 55/250 [00:55<01:59,  1.64it/s]
 22%|██▏       | 56/250 [00:56<02:01,  1.60it/s]
 23%|██▎       | 57/250 [00:56<01:41,  1.91it/s]
 23%|██▎       | 58/250 [00:58<02:44,  1.16it/s]
 24%|██▎       | 59/250 [00:59<02:49,  1.13it/s]
 24%|██▍       | 60/250 [00:59<02:20,  1.35it/s]
 24%|██▍       | 61/250 [01:00<01:55,  1.64it/s]
 25%|██▍       | 62/250 [01:00<01:46,  1.77it/s]
 25%|██▌       | 63/250 [01:00<01:23,  2.23it/s]
 26%|██▌       | 64/250 [01:02<02:50,  1.09it/s]
 26%|██▌       | 65/250 [01:05<03:56,  1.28s/it]
 26%|██▋       | 66/250 [01:05<03:18,  1.08s/it]
 27%|██▋       | 67/250 [01:06<03:26,  1.13s/it]
 27%|██▋       | 68/250 [01:15<09:45,  3.21s/it]
 28%|██▊       | 69/250 [01:20<11:23,  3.78s/it]
 28%|██▊       | 71/250 [01:21<06:47,  2.27s/it]
 29%|██▉       | 72/250 [01:21<05:19,  1.80s/it]
 29%|██▉       | 73/250 [01:23<05:13,  1.77s/it]
 30%|██▉       | 74/250 [01:24<04:30,  1.54s/it]
 30%|███       | 75/250 [01:24<03:26,  1.18s/it]
 30%|███       | 76/250 [01:33<10:09,  3.50s/it]
 31%|███       | 77/250 [01:35<08:31,  2.95s/it]
 31%|███       | 78/250 [01:36<06:52,  2.40s/it]
 32%|███▏      | 79/250 [01:37<05:50,  2.05s/it]
 32%|███▏      | 80/250 [01:39<05:25,  1.92s/it]
 32%|███▏      | 81/250 [01:39<04:24,  1.56s/it]
 33%|███▎      | 82/250 [01:40<03:23,  1.21s/it]
 33%|███▎      | 83/250 [01:41<03:06,  1.11s/it]
 34%|███▎      | 84/250 [01:43<04:13,  1.53s/it]
 34%|███▍      | 85/250 [01:43<03:12,  1.17s/it]
 35%|███▍      | 87/250 [01:44<01:56,  1.40it/s]
 35%|███▌      | 88/250 [01:44<01:32,  1.76it/s]
 36%|███▌      | 89/250 [01:44<01:12,  2.21it/s]
 36%|███▌      | 90/250 [01:44<01:05,  2.43it/s]
 36%|███▋      | 91/250 [01:47<03:00,  1.14s/it]
 37%|███▋      | 92/250 [01:49<02:58,  1.13s/it]
 37%|███▋      | 93/250 [01:49<02:11,  1.19it/s]
 38%|███▊      | 94/250 [01:50<02:36,  1.00s/it]
 38%|███▊      | 95/250 [01:53<03:53,  1.51s/it]
 38%|███▊      | 96/250 [01:53<03:11,  1.24s/it]
 39%|███▉      | 97/250 [01:56<04:09,  1.63s/it]
 39%|███▉      | 98/250 [01:56<03:15,  1.29s/it]
 40%|████      | 100/250 [01:56<01:48,  1.39it/s]
 40%|████      | 101/250 [01:58<02:02,  1.21it/s]
 41%|████      | 102/250 [01:58<01:53,  1.31it/s]
 41%|████      | 103/250 [01:59<01:47,  1.37it/s]
 42%|████▏     | 104/250 [01:59<01:25,  1.71it/s]
 42%|████▏     | 105/250 [01:59<01:10,  2.05it/s]
 42%|████▏     | 106/250 [02:00<00:57,  2.51it/s]
 43%|████▎     | 107/250 [02:02<02:06,  1.13it/s]
 43%|████▎     | 108/250 [02:02<01:51,  1.27it/s]
 44%|████▎     | 109/250 [02:02<01:27,  1.62it/s]
 44%|████▍     | 110/250 [02:04<02:20,  1.01s/it]
 44%|████▍     | 111/250 [02:07<03:15,  1.41s/it]
 45%|████▍     | 112/250 [02:08<03:03,  1.33s/it]
 45%|████▌     | 113/250 [02:09<02:56,  1.29s/it]
 46%|████▌     | 114/250 [02:10<02:25,  1.07s/it]
 46%|████▌     | 115/250 [02:11<02:25,  1.08s/it]
 46%|████▋     | 116/250 [02:11<01:56,  1.15it/s]
 47%|████▋     | 117/250 [02:11<01:32,  1.44it/s]
 47%|████▋     | 118/250 [02:12<01:13,  1.79it/s]
 48%|████▊     | 119/250 [02:12<01:02,  2.11it/s]
 48%|████▊     | 120/250 [02:13<01:34,  1.37it/s]
 48%|████▊     | 121/250 [02:13<01:11,  1.80it/s]
 49%|████▉     | 122/250 [02:16<02:23,  1.12s/it]
 49%|████▉     | 123/250 [02:16<01:43,  1.23it/s]
 50%|█████     | 125/250 [02:16<01:08,  1.81it/s]
 50%|█████     | 126/250 [02:16<00:57,  2.16it/s]
 51%|█████     | 128/250 [02:17<00:41,  2.93it/s]
 52%|█████▏    | 129/250 [02:17<00:35,  3.42it/s]
 52%|█████▏    | 130/250 [02:18<00:49,  2.45it/s]
 52%|█████▏    | 131/250 [02:19<01:08,  1.73it/s]
 53%|█████▎    | 132/250 [02:23<03:08,  1.60s/it]
 53%|█████▎    | 133/250 [02:25<03:05,  1.59s/it]
 54%|█████▎    | 134/250 [02:26<02:49,  1.46s/it]
 54%|█████▍    | 135/250 [02:27<02:37,  1.37s/it]
 54%|█████▍    | 136/250 [02:29<02:46,  1.46s/it]
 55%|█████▍    | 137/250 [02:30<02:57,  1.57s/it]
 55%|█████▌    | 138/250 [02:31<02:09,  1.16s/it]
 56%|█████▌    | 139/250 [02:31<01:52,  1.01s/it]
 56%|█████▌    | 140/250 [02:32<01:32,  1.18it/s]
 56%|█████▋    | 141/250 [02:33<01:42,  1.07it/s]
 57%|█████▋    | 142/250 [02:34<01:58,  1.10s/it]
 57%|█████▋    | 143/250 [02:35<01:43,  1.04it/s]
 58%|█████▊    | 144/250 [02:37<02:28,  1.40s/it]
 58%|█████▊    | 145/250 [02:38<02:02,  1.16s/it]
 58%|█████▊    | 146/250 [02:40<02:35,  1.50s/it]
 59%|█████▉    | 147/250 [02:41<01:53,  1.10s/it]
 59%|█████▉    | 148/250 [02:41<01:26,  1.18it/s]
 60%|█████▉    | 149/250 [02:41<01:17,  1.30it/s]
 60%|██████    | 150/250 [02:42<01:11,  1.41it/s]
 60%|██████    | 151/250 [02:42<01:00,  1.64it/s]
 61%|██████    | 152/250 [02:43<00:50,  1.94it/s]
 61%|██████    | 153/250 [02:44<01:10,  1.37it/s]
 62%|██████▏   | 154/250 [02:45<01:15,  1.27it/s]
 63%|██████▎   | 157/250 [02:47<01:06,  1.40it/s]
 63%|██████▎   | 158/250 [02:48<01:10,  1.30it/s]
 64%|██████▎   | 159/250 [02:49<01:11,  1.28it/s]
 64%|██████▍   | 160/250 [02:49<01:11,  1.26it/s]
 64%|██████▍   | 161/250 [02:50<00:54,  1.63it/s]
 65%|██████▍   | 162/250 [02:51<01:08,  1.29it/s]
 65%|██████▌   | 163/250 [02:52<01:09,  1.25it/s]
 66%|██████▌   | 165/250 [02:54<01:16,  1.11it/s]
 66%|██████▋   | 166/250 [02:54<01:11,  1.17it/s]
 67%|██████▋   | 167/250 [02:55<00:58,  1.42it/s]
 67%|██████▋   | 168/250 [02:56<01:14,  1.09it/s]
 68%|██████▊   | 170/250 [02:57<01:03,  1.25it/s]
 68%|██████▊   | 171/250 [02:58<00:50,  1.55it/s]
 69%|██████▉   | 173/250 [02:58<00:38,  2.00it/s]
 70%|██████▉   | 174/250 [02:58<00:31,  2.40it/s]
 70%|███████   | 176/250 [03:00<00:44,  1.66it/s]
 71%|███████   | 177/250 [03:01<00:47,  1.53it/s]
 71%|███████   | 178/250 [03:02<01:01,  1.18it/s]
 72%|███████▏  | 179/250 [03:03<00:53,  1.32it/s]
 72%|███████▏  | 180/250 [03:04<00:54,  1.29it/s]
 72%|███████▏  | 181/250 [03:05<01:07,  1.02it/s]
 73%|███████▎  | 182/250 [03:06<01:06,  1.02it/s]
 73%|███████▎  | 183/250 [03:09<01:37,  1.46s/it]
 74%|███████▎  | 184/250 [03:10<01:37,  1.48s/it]
 74%|███████▍  | 185/250 [03:11<01:11,  1.10s/it]
 74%|███████▍  | 186/250 [03:13<01:37,  1.52s/it]
 75%|███████▍  | 187/250 [03:13<01:10,  1.11s/it]
 75%|███████▌  | 188/250 [03:15<01:27,  1.41s/it]
 76%|███████▌  | 189/250 [03:16<01:20,  1.32s/it]
 76%|███████▌  | 190/250 [03:19<01:45,  1.75s/it]
 76%|███████▋  | 191/250 [03:20<01:20,  1.37s/it]
 77%|███████▋  | 192/250 [03:20<01:04,  1.10s/it]
 77%|███████▋  | 193/250 [03:26<02:27,  2.59s/it]
 78%|███████▊  | 194/250 [03:27<01:57,  2.09s/it]
 78%|███████▊  | 195/250 [03:29<01:45,  1.92s/it]
 78%|███████▊  | 196/250 [03:29<01:15,  1.39s/it]
 79%|███████▉  | 197/250 [03:29<00:56,  1.06s/it]
 79%|███████▉  | 198/250 [03:31<01:12,  1.40s/it]
 80%|███████▉  | 199/250 [03:32<01:03,  1.24s/it]
 80%|████████  | 200/250 [03:33<00:54,  1.09s/it]
 80%|████████  | 201/250 [03:33<00:42,  1.16it/s]
 81%|████████  | 202/250 [03:34<00:35,  1.34it/s]
 81%|████████  | 203/250 [03:36<00:56,  1.21s/it]
 82%|████████▏ | 204/250 [03:39<01:22,  1.79s/it]
 82%|████████▏ | 205/250 [03:40<01:10,  1.56s/it]
 82%|████████▏ | 206/250 [03:41<01:04,  1.46s/it]
 83%|████████▎ | 207/250 [03:42<00:47,  1.10s/it]
 83%|████████▎ | 208/250 [03:43<00:44,  1.05s/it]
 84%|████████▎ | 209/250 [03:44<00:44,  1.08s/it]
 85%|████████▍ | 212/250 [03:45<00:24,  1.52it/s]
 85%|████████▌ | 213/250 [03:46<00:25,  1.44it/s]
 86%|████████▌ | 214/250 [03:48<00:42,  1.17s/it]
 86%|████████▌ | 215/250 [03:49<00:36,  1.04s/it]
 86%|████████▋ | 216/250 [03:51<00:41,  1.22s/it]
 87%|████████▋ | 217/250 [03:51<00:33,  1.03s/it]
 87%|████████▋ | 218/250 [03:54<00:45,  1.41s/it]
 88%|████████▊ | 219/250 [03:54<00:34,  1.12s/it]
 88%|████████▊ | 220/250 [03:56<00:39,  1.30s/it]
 88%|████████▊ | 221/250 [03:58<00:44,  1.52s/it]
 89%|████████▉ | 222/250 [04:00<00:49,  1.77s/it]
 89%|████████▉ | 223/250 [04:00<00:36,  1.35s/it]
 90%|████████▉ | 224/250 [04:01<00:25,  1.00it/s]
 90%|█████████ | 225/250 [04:03<00:32,  1.31s/it]
 90%|█████████ | 226/250 [04:07<00:50,  2.11s/it]
 91%|█████████ | 227/250 [04:07<00:37,  1.61s/it]
 91%|█████████ | 228/250 [04:08<00:33,  1.54s/it]
 92%|█████████▏| 229/250 [04:09<00:24,  1.16s/it]
 92%|█████████▏| 230/250 [04:09<00:17,  1.13it/s]
 92%|█████████▏| 231/250 [04:09<00:13,  1.41it/s]
 93%|█████████▎| 232/250 [04:09<00:09,  1.84it/s]
 93%|█████████▎| 233/250 [04:10<00:08,  1.97it/s]
 94%|█████████▎| 234/250 [04:10<00:08,  1.93it/s]
 94%|█████████▍| 235/250 [04:11<00:06,  2.30it/s]
 94%|█████████▍| 236/250 [04:11<00:04,  2.86it/s]
 95%|█████████▍| 237/250 [04:12<00:06,  2.14it/s]
 95%|█████████▌| 238/250 [04:12<00:05,  2.39it/s]
 96%|█████████▌| 239/250 [04:13<00:06,  1.67it/s]
 96%|█████████▌| 240/250 [04:15<00:10,  1.01s/it]
 97%|█████████▋| 242/250 [04:15<00:05,  1.56it/s]
 97%|█████████▋| 243/250 [04:16<00:03,  1.86it/s]
 98%|█████████▊| 244/250 [04:16<00:02,  2.19it/s]
 98%|█████████▊| 245/250 [04:16<00:01,  2.74it/s]
 98%|█████████▊| 246/250 [04:16<00:01,  3.02it/s]
 99%|█████████▉| 247/250 [04:19<00:02,  1.02it/s]
 99%|█████████▉| 248/250 [04:22<00:03,  1.72s/it]
100%|█████████▉| 249/250 [04:22<00:01,  1.27s/it]
100%|██████████| 250/250 [04:30<00:00,  3.04s/it]
100%|██████████| 250/250 [04:30<00:00,  1.08s/it]
2025-05-14 10:50:52,589 - modnet - INFO - Loss per individual: ind 0: 0.108 	ind 1: 0.102 	ind 2: 0.116 	ind 3: 0.111 	ind 4: 0.098 	ind 5: 0.151 	ind 6: 0.107 	ind 7: 0.117 	ind 8: 0.109 	ind 9: 0.098 	ind 10: 0.117 	ind 11: 0.109 	ind 12: 0.140 	ind 13: 0.097 	ind 14: 0.115 	ind 15: 0.134 	ind 16: 0.142 	ind 17: 0.105 	ind 18: 0.095 	ind 19: 0.097 	ind 20: 0.123 	ind 21: 0.140 	ind 22: 0.107 	ind 23: 0.097 	ind 24: 0.098 	ind 25: 0.147 	ind 26: 0.099 	ind 27: 0.131 	ind 28: 0.102 	ind 29: 0.099 	ind 30: 0.116 	ind 31: 0.097 	ind 32: 0.111 	ind 33: 0.106 	ind 34: 0.118 	ind 35: 0.104 	ind 36: 0.099 	ind 37: 0.095 	ind 38: 0.097 	ind 39: 0.100 	ind 40: 0.100 	ind 41: 0.105 	ind 42: 0.104 	ind 43: 0.112 	ind 44: 0.111 	ind 45: 0.105 	ind 46: 0.092 	ind 47: 0.096 	ind 48: 0.129 	ind 49: 0.128 	
2025-05-14 10:50:52,591 - modnet - INFO - Generation number 3

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:09<40:06,  9.66s/it]
  1%|          | 2/250 [00:11<21:17,  5.15s/it]
  1%|          | 3/250 [00:12<12:12,  2.96s/it]
  2%|▏         | 4/250 [00:12<08:01,  1.96s/it]
  2%|▏         | 5/250 [00:13<06:09,  1.51s/it]
  2%|▏         | 6/250 [00:14<06:01,  1.48s/it]
  3%|▎         | 7/250 [00:16<05:58,  1.47s/it]
  3%|▎         | 8/250 [00:18<07:07,  1.77s/it]
  4%|▎         | 9/250 [00:19<05:46,  1.44s/it]
  4%|▍         | 10/250 [00:19<04:59,  1.25s/it]
  4%|▍         | 11/250 [00:20<03:59,  1.00s/it]
  5%|▍         | 12/250 [00:20<02:58,  1.33it/s]
  5%|▌         | 13/250 [00:21<03:08,  1.26it/s]
  6%|▌         | 14/250 [00:22<02:53,  1.36it/s]
  6%|▌         | 15/250 [00:23<03:56,  1.01s/it]
  6%|▋         | 16/250 [00:25<04:28,  1.15s/it]
  8%|▊         | 19/250 [00:26<02:33,  1.51it/s]
  8%|▊         | 20/250 [00:26<02:21,  1.63it/s]
  8%|▊         | 21/250 [00:27<03:02,  1.26it/s]
  9%|▉         | 22/250 [00:30<04:17,  1.13s/it]
  9%|▉         | 23/250 [00:31<04:16,  1.13s/it]
 10%|▉         | 24/250 [00:31<03:37,  1.04it/s]
 10%|█         | 25/250 [00:32<03:27,  1.09it/s]
 10%|█         | 26/250 [00:33<03:13,  1.15it/s]
 11%|█         | 27/250 [00:33<02:30,  1.48it/s]
 11%|█         | 28/250 [00:33<02:08,  1.73it/s]
 12%|█▏        | 29/250 [00:34<02:06,  1.74it/s]
 12%|█▏        | 30/250 [00:34<01:47,  2.05it/s]
 12%|█▏        | 31/250 [00:34<01:32,  2.36it/s]
 13%|█▎        | 32/250 [00:36<03:12,  1.13it/s]
 13%|█▎        | 33/250 [00:37<02:51,  1.27it/s]
 14%|█▎        | 34/250 [00:40<04:59,  1.39s/it]
 14%|█▍        | 35/250 [00:40<03:45,  1.05s/it]
 14%|█▍        | 36/250 [00:40<03:04,  1.16it/s]
 15%|█▍        | 37/250 [00:41<02:19,  1.53it/s]
 15%|█▌        | 38/250 [00:41<02:00,  1.75it/s]
 16%|█▌        | 39/250 [00:41<01:32,  2.28it/s]
 16%|█▌        | 40/250 [00:42<02:21,  1.48it/s]
 16%|█▋        | 41/250 [00:43<01:54,  1.83it/s]
 17%|█▋        | 42/250 [00:43<01:49,  1.89it/s]
 17%|█▋        | 43/250 [00:46<04:41,  1.36s/it]
 18%|█▊        | 44/250 [00:50<07:19,  2.13s/it]
 18%|█▊        | 45/250 [00:51<05:23,  1.58s/it]
 18%|█▊        | 46/250 [00:51<03:59,  1.18s/it]
 19%|█▉        | 47/250 [00:51<03:17,  1.03it/s]
 19%|█▉        | 48/250 [00:51<02:29,  1.35it/s]
 20%|█▉        | 49/250 [00:52<02:17,  1.47it/s]
 20%|██        | 51/250 [00:53<02:06,  1.57it/s]
 21%|██        | 52/250 [00:54<02:18,  1.43it/s]
 21%|██        | 53/250 [00:55<02:06,  1.56it/s]
 22%|██▏       | 54/250 [00:55<01:46,  1.85it/s]
 22%|██▏       | 55/250 [00:56<02:28,  1.31it/s]
 22%|██▏       | 56/250 [00:57<02:04,  1.55it/s]
 23%|██▎       | 57/250 [00:57<02:08,  1.50it/s]
 23%|██▎       | 58/250 [00:58<01:53,  1.70it/s]
 24%|██▍       | 60/250 [00:58<01:06,  2.87it/s]
 24%|██▍       | 61/250 [00:58<00:59,  3.19it/s]
 25%|██▍       | 62/250 [00:59<01:44,  1.80it/s]
 25%|██▌       | 63/250 [00:59<01:23,  2.23it/s]
 26%|██▌       | 64/250 [01:00<01:36,  1.93it/s]
 26%|██▌       | 65/250 [01:02<02:27,  1.25it/s]
 27%|██▋       | 67/250 [01:04<03:01,  1.01it/s]
 27%|██▋       | 68/250 [01:05<03:05,  1.02s/it]
 28%|██▊       | 69/250 [01:06<02:49,  1.07it/s]
 28%|██▊       | 70/250 [01:06<02:20,  1.28it/s]
 28%|██▊       | 71/250 [01:08<02:54,  1.02it/s]
 29%|██▉       | 72/250 [01:09<03:01,  1.02s/it]
 29%|██▉       | 73/250 [01:10<03:11,  1.08s/it]
 30%|██▉       | 74/250 [01:12<04:18,  1.47s/it]
 30%|███       | 75/250 [01:16<06:19,  2.17s/it]
 30%|███       | 76/250 [01:20<07:51,  2.71s/it]
 31%|███       | 77/250 [01:22<06:37,  2.30s/it]
 31%|███       | 78/250 [01:23<05:35,  1.95s/it]
 32%|███▏      | 79/250 [01:24<04:41,  1.64s/it]
 32%|███▏      | 80/250 [01:25<04:40,  1.65s/it]
 33%|███▎      | 82/250 [01:26<02:41,  1.04it/s]
 33%|███▎      | 83/250 [01:27<02:42,  1.03it/s]
 34%|███▎      | 84/250 [01:27<02:06,  1.31it/s]
 34%|███▍      | 85/250 [01:28<02:24,  1.14it/s]
 34%|███▍      | 86/250 [01:28<02:00,  1.36it/s]
 35%|███▍      | 87/250 [01:29<01:42,  1.59it/s]
 36%|███▌      | 89/250 [01:29<01:11,  2.24it/s]
 36%|███▌      | 90/250 [01:29<01:06,  2.42it/s]
 36%|███▋      | 91/250 [01:30<00:53,  2.97it/s]
 37%|███▋      | 92/250 [01:30<00:59,  2.63it/s]
 37%|███▋      | 93/250 [01:31<01:04,  2.43it/s]
 38%|███▊      | 94/250 [01:31<00:59,  2.61it/s]
 38%|███▊      | 95/250 [01:32<01:44,  1.49it/s]
 38%|███▊      | 96/250 [01:32<01:20,  1.92it/s]
 39%|███▉      | 98/250 [01:36<02:51,  1.13s/it]
 40%|███▉      | 99/250 [01:37<02:21,  1.07it/s]
 40%|████      | 100/250 [01:37<02:18,  1.08it/s]
 40%|████      | 101/250 [01:38<02:00,  1.24it/s]
 41%|████      | 102/250 [01:39<02:17,  1.08it/s]
 41%|████      | 103/250 [01:40<02:12,  1.11it/s]
 42%|████▏     | 104/250 [01:41<02:26,  1.00s/it]
 42%|████▏     | 106/250 [01:41<01:23,  1.72it/s]
 43%|████▎     | 108/250 [01:42<01:00,  2.33it/s]
 44%|████▎     | 109/250 [01:42<00:52,  2.68it/s]
 44%|████▍     | 110/250 [01:42<00:47,  2.95it/s]
 44%|████▍     | 111/250 [01:43<00:57,  2.40it/s]
 45%|████▍     | 112/250 [01:44<01:19,  1.73it/s]
 45%|████▌     | 113/250 [01:45<01:47,  1.27it/s]
 46%|████▌     | 114/250 [01:47<02:33,  1.13s/it]
 46%|████▌     | 115/250 [01:48<02:11,  1.02it/s]
 46%|████▋     | 116/250 [01:48<01:42,  1.31it/s]
 47%|████▋     | 117/250 [01:48<01:28,  1.49it/s]
 47%|████▋     | 118/250 [01:49<01:33,  1.41it/s]
 48%|████▊     | 119/250 [01:50<01:26,  1.51it/s]
 48%|████▊     | 121/250 [01:50<00:51,  2.51it/s]
 49%|████▉     | 122/250 [01:50<00:51,  2.50it/s]
 49%|████▉     | 123/250 [01:52<01:18,  1.61it/s]
 50%|████▉     | 124/250 [01:52<01:22,  1.54it/s]
 50%|█████     | 125/250 [01:53<01:34,  1.32it/s]
 50%|█████     | 126/250 [01:54<01:20,  1.54it/s]
 51%|█████     | 127/250 [01:54<01:07,  1.84it/s]
 51%|█████     | 128/250 [01:55<01:07,  1.80it/s]
 52%|█████▏    | 129/250 [01:55<00:51,  2.35it/s]
 52%|█████▏    | 130/250 [01:56<01:23,  1.44it/s]
 52%|█████▏    | 131/250 [01:58<02:18,  1.17s/it]
 53%|█████▎    | 132/250 [02:00<02:26,  1.24s/it]
 53%|█████▎    | 133/250 [02:01<02:13,  1.14s/it]
 54%|█████▎    | 134/250 [02:01<01:35,  1.21it/s]
 54%|█████▍    | 135/250 [02:01<01:13,  1.57it/s]
 54%|█████▍    | 136/250 [02:01<00:59,  1.92it/s]
 55%|█████▍    | 137/250 [02:01<00:49,  2.26it/s]
 55%|█████▌    | 138/250 [02:02<00:56,  1.99it/s]
 56%|█████▌    | 139/250 [02:02<00:44,  2.48it/s]
 56%|█████▌    | 140/250 [02:03<00:42,  2.58it/s]
 56%|█████▋    | 141/250 [02:03<00:47,  2.29it/s]
 57%|█████▋    | 143/250 [02:03<00:27,  3.87it/s]
 58%|█████▊    | 144/250 [02:04<00:32,  3.23it/s]
 58%|█████▊    | 145/250 [02:04<00:36,  2.88it/s]
 58%|█████▊    | 146/250 [02:05<00:50,  2.05it/s]
 59%|█████▉    | 147/250 [02:06<01:06,  1.55it/s]
 59%|█████▉    | 148/250 [02:07<01:11,  1.43it/s]
 60%|█████▉    | 149/250 [02:08<01:31,  1.10it/s]
 60%|██████    | 150/250 [02:10<01:40,  1.01s/it]
 60%|██████    | 151/250 [02:10<01:14,  1.32it/s]
 61%|██████    | 152/250 [02:11<01:40,  1.03s/it]
 62%|██████▏   | 154/250 [02:13<01:20,  1.19it/s]
 62%|██████▏   | 155/250 [02:16<02:19,  1.47s/it]
 63%|██████▎   | 157/250 [02:17<01:30,  1.03it/s]
 63%|██████▎   | 158/250 [02:18<01:30,  1.02it/s]
 64%|██████▎   | 159/250 [02:19<01:46,  1.17s/it]
 64%|██████▍   | 161/250 [02:21<01:26,  1.03it/s]
 65%|██████▍   | 162/250 [02:21<01:20,  1.10it/s]
 65%|██████▌   | 163/250 [02:22<01:04,  1.36it/s]
 66%|██████▌   | 164/250 [02:22<01:05,  1.32it/s]
 66%|██████▌   | 165/250 [02:24<01:30,  1.06s/it]
 67%|██████▋   | 167/250 [02:25<00:55,  1.50it/s]
 67%|██████▋   | 168/250 [02:25<00:49,  1.66it/s]
 68%|██████▊   | 170/250 [02:25<00:30,  2.59it/s]
 68%|██████▊   | 171/250 [02:26<00:36,  2.18it/s]
 69%|██████▉   | 172/250 [02:27<00:38,  2.03it/s]
 69%|██████▉   | 173/250 [02:28<01:07,  1.14it/s]
 70%|██████▉   | 174/250 [02:31<01:45,  1.39s/it]
 70%|███████   | 175/250 [02:31<01:19,  1.06s/it]
 70%|███████   | 176/250 [02:32<01:01,  1.20it/s]
 71%|███████   | 177/250 [02:33<01:16,  1.05s/it]
 71%|███████   | 178/250 [02:35<01:20,  1.12s/it]
 72%|███████▏  | 179/250 [02:35<00:59,  1.19it/s]
 72%|███████▏  | 180/250 [02:35<00:49,  1.41it/s]
 72%|███████▏  | 181/250 [02:37<01:03,  1.08it/s]
 73%|███████▎  | 182/250 [02:37<00:47,  1.43it/s]
 73%|███████▎  | 183/250 [02:38<00:51,  1.29it/s]
 74%|███████▎  | 184/250 [02:38<00:44,  1.50it/s]
 74%|███████▍  | 185/250 [02:43<02:06,  1.95s/it]
 74%|███████▍  | 186/250 [02:44<01:46,  1.66s/it]
 75%|███████▌  | 188/250 [02:45<01:11,  1.15s/it]
 76%|███████▌  | 189/250 [02:46<01:05,  1.07s/it]
 76%|███████▌  | 190/250 [02:47<01:02,  1.03s/it]
 76%|███████▋  | 191/250 [02:47<00:53,  1.11it/s]
 77%|███████▋  | 192/250 [02:49<00:53,  1.08it/s]
 77%|███████▋  | 193/250 [02:49<00:46,  1.23it/s]
 78%|███████▊  | 194/250 [02:50<00:55,  1.01it/s]
 78%|███████▊  | 195/250 [02:51<00:43,  1.28it/s]
 78%|███████▊  | 196/250 [02:51<00:41,  1.31it/s]
 79%|███████▉  | 197/250 [02:52<00:31,  1.67it/s]
 79%|███████▉  | 198/250 [02:52<00:34,  1.51it/s]
 80%|███████▉  | 199/250 [02:53<00:36,  1.39it/s]
 80%|████████  | 200/250 [02:54<00:32,  1.55it/s]
 80%|████████  | 201/250 [02:56<00:59,  1.21s/it]
 81%|████████  | 202/250 [02:57<00:55,  1.16s/it]
 81%|████████  | 203/250 [02:58<00:47,  1.00s/it]
 82%|████████▏ | 204/250 [02:58<00:38,  1.20it/s]
 82%|████████▏ | 205/250 [03:00<00:50,  1.12s/it]
 82%|████████▏ | 206/250 [03:02<00:58,  1.34s/it]
 83%|████████▎ | 207/250 [03:02<00:42,  1.02it/s]
 83%|████████▎ | 208/250 [03:03<00:35,  1.18it/s]
 84%|████████▎ | 209/250 [03:04<00:39,  1.04it/s]
 84%|████████▍ | 210/250 [03:04<00:28,  1.41it/s]
 84%|████████▍ | 211/250 [03:04<00:21,  1.78it/s]
 85%|████████▌ | 213/250 [03:05<00:16,  2.23it/s]
 86%|████████▌ | 214/250 [03:05<00:13,  2.70it/s]
 86%|████████▌ | 215/250 [03:05<00:12,  2.70it/s]
 86%|████████▋ | 216/250 [03:06<00:15,  2.15it/s]
 87%|████████▋ | 217/250 [03:07<00:14,  2.24it/s]
 87%|████████▋ | 218/250 [03:09<00:27,  1.15it/s]
 88%|████████▊ | 219/250 [03:09<00:26,  1.19it/s]
 90%|████████▉ | 224/250 [03:10<00:09,  2.77it/s]
 90%|█████████ | 225/250 [03:11<00:11,  2.21it/s]
 90%|█████████ | 226/250 [03:12<00:12,  1.99it/s]
 91%|█████████ | 227/250 [03:12<00:13,  1.72it/s]
 91%|█████████ | 228/250 [03:14<00:15,  1.42it/s]
 92%|█████████▏| 229/250 [03:15<00:19,  1.10it/s]
 92%|█████████▏| 230/250 [03:16<00:20,  1.03s/it]
 92%|█████████▏| 231/250 [03:17<00:17,  1.09it/s]
 93%|█████████▎| 232/250 [03:18<00:17,  1.01it/s]
 93%|█████████▎| 233/250 [03:19<00:13,  1.27it/s]
 94%|█████████▎| 234/250 [03:19<00:12,  1.29it/s]
 94%|█████████▍| 235/250 [03:20<00:09,  1.57it/s]
 94%|█████████▍| 236/250 [03:20<00:08,  1.65it/s]
 95%|█████████▍| 237/250 [03:21<00:07,  1.82it/s]
 95%|█████████▌| 238/250 [03:21<00:06,  1.89it/s]
 96%|█████████▌| 239/250 [03:21<00:05,  2.11it/s]
 96%|█████████▌| 240/250 [03:22<00:04,  2.15it/s]
 96%|█████████▋| 241/250 [03:22<00:03,  2.65it/s]
 97%|█████████▋| 242/250 [03:22<00:02,  2.83it/s]
 97%|█████████▋| 243/250 [03:23<00:03,  1.82it/s]
 98%|█████████▊| 244/250 [03:24<00:04,  1.37it/s]
 98%|█████████▊| 245/250 [03:25<00:02,  1.82it/s]
 98%|█████████▊| 246/250 [03:26<00:03,  1.24it/s]
 99%|█████████▉| 248/250 [03:28<00:01,  1.25it/s]
100%|█████████▉| 249/250 [03:28<00:00,  1.54it/s]
100%|██████████| 250/250 [03:30<00:00,  1.17s/it]
100%|██████████| 250/250 [03:30<00:00,  1.19it/s]
2025-05-14 10:54:23,546 - modnet - INFO - Loss per individual: ind 0: 0.115 	ind 1: 0.121 	ind 2: 0.105 	ind 3: 0.104 	ind 4: 0.130 	ind 5: 0.099 	ind 6: 0.103 	ind 7: 0.113 	ind 8: 0.105 	ind 9: 0.124 	ind 10: 0.127 	ind 11: 0.107 	ind 12: 0.140 	ind 13: 0.101 	ind 14: 0.103 	ind 15: 0.094 	ind 16: 0.108 	ind 17: 0.116 	ind 18: 0.101 	ind 19: 0.117 	ind 20: 0.114 	ind 21: 0.106 	ind 22: 0.098 	ind 23: 0.101 	ind 24: 0.100 	ind 25: 0.116 	ind 26: 0.111 	ind 27: 0.097 	ind 28: 0.107 	ind 29: 0.111 	ind 30: 0.116 	ind 31: 0.102 	ind 32: 0.096 	ind 33: 0.106 	ind 34: 0.110 	ind 35: 0.134 	ind 36: 0.106 	ind 37: 0.115 	ind 38: 0.094 	ind 39: 0.097 	ind 40: 0.102 	ind 41: 0.130 	ind 42: 0.119 	ind 43: 0.114 	ind 44: 0.109 	ind 45: 0.113 	ind 46: 0.093 	ind 47: 0.112 	ind 48: 0.110 	ind 49: 0.143 	
2025-05-14 10:54:23,548 - modnet - INFO - Generation number 4

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:11<46:27, 11.20s/it]
  1%|          | 2/250 [00:14<26:58,  6.53s/it]
  1%|          | 3/250 [00:17<20:51,  5.07s/it]
  2%|▏         | 4/250 [00:18<12:56,  3.16s/it]
  2%|▏         | 5/250 [00:18<08:55,  2.19s/it]
  2%|▏         | 6/250 [00:22<11:28,  2.82s/it]
  3%|▎         | 8/250 [00:24<07:32,  1.87s/it]
  4%|▎         | 9/250 [00:25<07:16,  1.81s/it]
  4%|▍         | 10/250 [00:26<05:23,  1.35s/it]
  4%|▍         | 11/250 [00:26<04:20,  1.09s/it]
  5%|▍         | 12/250 [00:28<04:51,  1.23s/it]
  5%|▌         | 13/250 [00:28<03:50,  1.03it/s]
  6%|▌         | 14/250 [00:28<03:21,  1.17it/s]
  6%|▌         | 15/250 [00:29<03:25,  1.14it/s]
  6%|▋         | 16/250 [00:30<02:59,  1.30it/s]
  7%|▋         | 18/250 [00:31<02:40,  1.44it/s]
  8%|▊         | 20/250 [00:32<02:35,  1.48it/s]
  8%|▊         | 21/250 [00:35<04:31,  1.19s/it]
  9%|▉         | 22/250 [00:37<05:15,  1.39s/it]
  9%|▉         | 23/250 [00:38<04:22,  1.16s/it]
 10%|▉         | 24/250 [00:38<03:41,  1.02it/s]
 10%|█         | 25/250 [00:40<04:45,  1.27s/it]
 10%|█         | 26/250 [00:43<06:21,  1.71s/it]
 11%|█         | 27/250 [00:45<05:54,  1.59s/it]
 11%|█         | 28/250 [00:46<05:29,  1.48s/it]
 12%|█▏        | 29/250 [00:46<04:02,  1.10s/it]
 12%|█▏        | 30/250 [00:46<02:56,  1.25it/s]
 12%|█▏        | 31/250 [00:46<02:11,  1.66it/s]
 13%|█▎        | 32/250 [00:47<02:07,  1.71it/s]
 13%|█▎        | 33/250 [00:47<01:56,  1.86it/s]
 14%|█▎        | 34/250 [00:47<01:39,  2.17it/s]
 14%|█▍        | 35/250 [00:49<02:35,  1.38it/s]
 15%|█▍        | 37/250 [00:49<01:39,  2.15it/s]
 15%|█▌        | 38/250 [00:50<02:13,  1.59it/s]
 16%|█▌        | 39/250 [00:54<05:09,  1.47s/it]
 16%|█▌        | 40/250 [00:54<03:53,  1.11s/it]
 16%|█▋        | 41/250 [00:56<04:30,  1.29s/it]
 17%|█▋        | 42/250 [00:59<06:27,  1.86s/it]
 17%|█▋        | 43/250 [01:00<05:36,  1.62s/it]
 18%|█▊        | 44/250 [01:01<04:33,  1.33s/it]
 18%|█▊        | 45/250 [01:01<03:27,  1.01s/it]
 18%|█▊        | 46/250 [01:03<04:05,  1.20s/it]
 19%|█▉        | 47/250 [01:03<03:26,  1.02s/it]
 19%|█▉        | 48/250 [01:04<02:52,  1.17it/s]
 20%|█▉        | 49/250 [01:05<02:51,  1.17it/s]
 20%|██        | 50/250 [01:05<02:11,  1.52it/s]
 20%|██        | 51/250 [01:05<01:44,  1.91it/s]
 21%|██        | 52/250 [01:07<02:40,  1.23it/s]
 21%|██        | 53/250 [01:07<02:14,  1.46it/s]
 22%|██▏       | 54/250 [01:11<05:44,  1.76s/it]
 22%|██▏       | 56/250 [01:13<04:21,  1.35s/it]
 23%|██▎       | 57/250 [01:14<04:03,  1.26s/it]
 23%|██▎       | 58/250 [01:16<04:21,  1.36s/it]
 24%|██▎       | 59/250 [01:16<03:48,  1.20s/it]
 24%|██▍       | 60/250 [01:17<03:41,  1.17s/it]
 25%|██▍       | 62/250 [01:18<02:16,  1.37it/s]
 25%|██▌       | 63/250 [01:18<02:00,  1.55it/s]
 26%|██▌       | 64/250 [01:19<02:10,  1.42it/s]
 26%|██▌       | 65/250 [01:22<03:52,  1.26s/it]
 27%|██▋       | 67/250 [01:24<03:40,  1.21s/it]
 27%|██▋       | 68/250 [01:24<02:53,  1.05it/s]
 28%|██▊       | 69/250 [01:25<02:19,  1.30it/s]
 28%|██▊       | 71/250 [01:27<02:55,  1.02it/s]
 29%|██▉       | 72/250 [01:27<02:28,  1.20it/s]
 29%|██▉       | 73/250 [01:28<02:18,  1.28it/s]
 30%|██▉       | 74/250 [01:28<01:58,  1.49it/s]
 30%|███       | 75/250 [01:29<01:45,  1.65it/s]
 30%|███       | 76/250 [01:29<01:39,  1.75it/s]
 31%|███       | 77/250 [01:29<01:18,  2.22it/s]
 31%|███       | 78/250 [01:32<03:25,  1.19s/it]
 32%|███▏      | 79/250 [01:33<02:34,  1.11it/s]
 32%|███▏      | 80/250 [01:34<02:41,  1.05it/s]
 32%|███▏      | 81/250 [01:34<02:25,  1.16it/s]
 33%|███▎      | 82/250 [01:35<02:22,  1.18it/s]
 33%|███▎      | 83/250 [01:37<02:58,  1.07s/it]
 34%|███▎      | 84/250 [01:38<03:12,  1.16s/it]
 34%|███▍      | 85/250 [01:40<03:51,  1.40s/it]
 34%|███▍      | 86/250 [01:43<04:46,  1.75s/it]
 35%|███▍      | 87/250 [01:43<03:49,  1.41s/it]
 35%|███▌      | 88/250 [01:44<02:56,  1.09s/it]
 36%|███▌      | 89/250 [01:44<02:21,  1.14it/s]
 36%|███▌      | 90/250 [01:45<02:03,  1.29it/s]
 36%|███▋      | 91/250 [01:45<01:35,  1.66it/s]
 37%|███▋      | 92/250 [01:45<01:17,  2.03it/s]
 37%|███▋      | 93/250 [01:46<01:45,  1.48it/s]
 38%|███▊      | 94/250 [01:47<01:50,  1.41it/s]
 38%|███▊      | 95/250 [01:50<03:44,  1.45s/it]
 39%|███▉      | 97/250 [01:52<03:03,  1.20s/it]
 39%|███▉      | 98/250 [01:52<02:24,  1.05it/s]
 40%|████      | 100/250 [01:52<01:27,  1.71it/s]
 40%|████      | 101/250 [01:53<01:18,  1.90it/s]
 41%|████      | 102/250 [01:53<01:27,  1.70it/s]
 41%|████      | 103/250 [01:55<02:15,  1.08it/s]
 42%|████▏     | 104/250 [01:55<01:44,  1.40it/s]
 42%|████▏     | 105/250 [01:55<01:19,  1.82it/s]
 42%|████▏     | 106/250 [01:56<01:40,  1.44it/s]
 43%|████▎     | 107/250 [01:57<01:15,  1.89it/s]
 43%|████▎     | 108/250 [01:57<01:08,  2.07it/s]
 44%|████▎     | 109/250 [01:57<00:55,  2.52it/s]
 45%|████▍     | 112/250 [01:58<00:52,  2.65it/s]
 45%|████▌     | 113/250 [02:01<02:10,  1.05it/s]
 46%|████▌     | 114/250 [02:04<02:51,  1.26s/it]
 46%|████▌     | 115/250 [02:04<02:19,  1.03s/it]
 46%|████▋     | 116/250 [02:05<02:16,  1.02s/it]
 47%|████▋     | 117/250 [02:05<01:49,  1.22it/s]
 47%|████▋     | 118/250 [02:05<01:23,  1.57it/s]
 48%|████▊     | 119/250 [02:06<01:11,  1.82it/s]
 48%|████▊     | 120/250 [02:07<01:35,  1.36it/s]
 48%|████▊     | 121/250 [02:07<01:13,  1.76it/s]
 49%|████▉     | 122/250 [02:08<01:13,  1.74it/s]
 49%|████▉     | 123/250 [02:08<01:10,  1.80it/s]
 50%|████▉     | 124/250 [02:09<00:59,  2.12it/s]
 50%|█████     | 125/250 [02:09<01:10,  1.78it/s]
 50%|█████     | 126/250 [02:10<01:30,  1.38it/s]
 51%|█████     | 127/250 [02:14<03:03,  1.49s/it]
 51%|█████     | 128/250 [02:14<02:33,  1.26s/it]
 52%|█████▏    | 129/250 [02:15<02:13,  1.10s/it]
 52%|█████▏    | 130/250 [02:16<01:50,  1.09it/s]
 52%|█████▏    | 131/250 [02:16<01:31,  1.30it/s]
 53%|█████▎    | 132/250 [02:17<01:31,  1.29it/s]
 53%|█████▎    | 133/250 [02:18<01:56,  1.00it/s]
 54%|█████▎    | 134/250 [02:19<01:43,  1.12it/s]
 54%|█████▍    | 135/250 [02:20<01:48,  1.06it/s]
 54%|█████▍    | 136/250 [02:20<01:21,  1.40it/s]
 55%|█████▍    | 137/250 [02:20<01:04,  1.76it/s]
 56%|█████▌    | 140/250 [02:21<00:40,  2.70it/s]
 56%|█████▋    | 141/250 [02:22<00:54,  2.00it/s]
 57%|█████▋    | 142/250 [02:23<01:04,  1.67it/s]
 57%|█████▋    | 143/250 [02:24<01:03,  1.68it/s]
 58%|█████▊    | 144/250 [02:24<00:55,  1.92it/s]
 58%|█████▊    | 146/250 [02:24<00:41,  2.51it/s]
 59%|█████▉    | 147/250 [02:25<00:51,  2.01it/s]
 59%|█████▉    | 148/250 [02:26<00:50,  2.02it/s]
 60%|█████▉    | 149/250 [02:30<02:36,  1.55s/it]
 60%|██████    | 150/250 [02:31<02:19,  1.39s/it]
 60%|██████    | 151/250 [02:32<01:48,  1.10s/it]
 61%|██████    | 152/250 [02:32<01:37,  1.01it/s]
 61%|██████    | 153/250 [02:33<01:40,  1.04s/it]
 62%|██████▏   | 155/250 [02:34<01:05,  1.45it/s]
 62%|██████▏   | 156/250 [02:35<01:10,  1.33it/s]
 63%|██████▎   | 157/250 [02:35<01:03,  1.47it/s]
 63%|██████▎   | 158/250 [02:36<00:52,  1.76it/s]
 64%|██████▎   | 159/250 [02:36<00:50,  1.79it/s]
 64%|██████▍   | 160/250 [02:38<01:11,  1.26it/s]
 64%|██████▍   | 161/250 [02:38<01:07,  1.32it/s]
 65%|██████▍   | 162/250 [02:40<01:25,  1.03it/s]
 65%|██████▌   | 163/250 [02:41<01:36,  1.11s/it]
 66%|██████▌   | 164/250 [02:42<01:40,  1.17s/it]
 66%|██████▌   | 165/250 [02:43<01:22,  1.03it/s]
 66%|██████▋   | 166/250 [02:43<01:08,  1.23it/s]
 67%|██████▋   | 167/250 [02:44<00:58,  1.41it/s]
 67%|██████▋   | 168/250 [02:44<00:51,  1.60it/s]
 68%|██████▊   | 169/250 [02:45<00:57,  1.42it/s]
 68%|██████▊   | 170/250 [02:46<00:47,  1.70it/s]
 68%|██████▊   | 171/250 [02:46<00:35,  2.23it/s]
 69%|██████▉   | 172/250 [02:46<00:35,  2.21it/s]
 69%|██████▉   | 173/250 [02:47<00:41,  1.87it/s]
 70%|██████▉   | 174/250 [02:48<00:55,  1.36it/s]
 70%|███████   | 175/250 [02:48<00:43,  1.71it/s]
 70%|███████   | 176/250 [02:48<00:33,  2.18it/s]
 71%|███████   | 177/250 [02:49<00:27,  2.63it/s]
 71%|███████   | 178/250 [02:49<00:27,  2.63it/s]
 72%|███████▏  | 179/250 [02:50<00:40,  1.77it/s]
 72%|███████▏  | 180/250 [02:51<00:38,  1.80it/s]
 73%|███████▎  | 182/250 [02:51<00:26,  2.54it/s]
 74%|███████▎  | 184/250 [02:51<00:22,  2.88it/s]
 74%|███████▍  | 185/250 [02:53<00:36,  1.77it/s]
 74%|███████▍  | 186/250 [02:55<00:55,  1.15it/s]
 75%|███████▍  | 187/250 [02:57<01:14,  1.19s/it]
 75%|███████▌  | 188/250 [02:59<01:23,  1.34s/it]
 76%|███████▌  | 189/250 [02:59<01:08,  1.13s/it]
 76%|███████▌  | 190/250 [03:00<01:11,  1.18s/it]
 76%|███████▋  | 191/250 [03:01<00:53,  1.10it/s]
 77%|███████▋  | 192/250 [03:02<01:04,  1.11s/it]
 77%|███████▋  | 193/250 [03:05<01:22,  1.45s/it]
 78%|███████▊  | 194/250 [03:05<01:01,  1.10s/it]
 78%|███████▊  | 195/250 [03:05<00:53,  1.02it/s]
 78%|███████▊  | 196/250 [03:09<01:40,  1.86s/it]
 79%|███████▉  | 197/250 [03:12<01:51,  2.10s/it]
 79%|███████▉  | 198/250 [03:12<01:21,  1.56s/it]
 80%|███████▉  | 199/250 [03:13<01:07,  1.33s/it]
 80%|████████  | 200/250 [03:14<00:52,  1.04s/it]
 81%|████████  | 202/250 [03:16<00:52,  1.09s/it]
 81%|████████  | 203/250 [03:16<00:44,  1.06it/s]
 82%|████████▏ | 204/250 [03:19<01:00,  1.31s/it]
 82%|████████▏ | 205/250 [03:19<00:50,  1.11s/it]
 82%|████████▏ | 206/250 [03:20<00:40,  1.09it/s]
 83%|████████▎ | 208/250 [03:21<00:37,  1.13it/s]
 84%|████████▎ | 209/250 [03:22<00:30,  1.33it/s]
 84%|████████▍ | 210/250 [03:22<00:26,  1.49it/s]
 84%|████████▍ | 211/250 [03:23<00:25,  1.54it/s]
 85%|████████▍ | 212/250 [03:23<00:21,  1.74it/s]
 85%|████████▌ | 213/250 [03:23<00:16,  2.26it/s]
 86%|████████▌ | 214/250 [03:26<00:37,  1.05s/it]
 86%|████████▌ | 215/250 [03:26<00:33,  1.05it/s]
 86%|████████▋ | 216/250 [03:27<00:25,  1.32it/s]
 87%|████████▋ | 217/250 [03:27<00:24,  1.36it/s]
 87%|████████▋ | 218/250 [03:28<00:17,  1.82it/s]
 88%|████████▊ | 220/250 [03:28<00:11,  2.51it/s]
 88%|████████▊ | 221/250 [03:28<00:11,  2.53it/s]
 89%|████████▉ | 222/250 [03:29<00:13,  2.05it/s]
 89%|████████▉ | 223/250 [03:30<00:14,  1.81it/s]
 90%|████████▉ | 224/250 [03:31<00:16,  1.62it/s]
 90%|█████████ | 225/250 [03:32<00:17,  1.42it/s]
 91%|█████████ | 227/250 [03:32<00:11,  1.96it/s]
 91%|█████████ | 228/250 [03:33<00:12,  1.75it/s]
 92%|█████████▏| 229/250 [03:36<00:24,  1.17s/it]
 92%|█████████▏| 230/250 [03:36<00:18,  1.11it/s]
 92%|█████████▏| 231/250 [03:36<00:13,  1.37it/s]
 93%|█████████▎| 232/250 [03:36<00:10,  1.78it/s]
 93%|█████████▎| 233/250 [03:37<00:08,  1.90it/s]
 94%|█████████▎| 234/250 [03:37<00:07,  2.17it/s]
 94%|█████████▍| 235/250 [03:38<00:08,  1.86it/s]
 94%|█████████▍| 236/250 [03:39<00:11,  1.27it/s]
 95%|█████████▍| 237/250 [03:40<00:08,  1.51it/s]
 95%|█████████▌| 238/250 [03:40<00:07,  1.58it/s]
 96%|█████████▌| 239/250 [03:41<00:07,  1.45it/s]
 96%|█████████▌| 240/250 [03:48<00:25,  2.51s/it]
 97%|█████████▋| 242/250 [03:48<00:11,  1.42s/it]
 97%|█████████▋| 243/250 [03:50<00:10,  1.48s/it]
 98%|█████████▊| 244/250 [03:56<00:16,  2.70s/it]
 98%|█████████▊| 245/250 [03:57<00:11,  2.37s/it]
 98%|█████████▊| 246/250 [03:58<00:08,  2.04s/it]
 99%|█████████▉| 247/250 [03:59<00:04,  1.62s/it]
 99%|█████████▉| 248/250 [04:01<00:03,  1.63s/it]
100%|█████████▉| 249/250 [04:06<00:02,  2.58s/it]
100%|██████████| 250/250 [04:07<00:00,  2.25s/it]
100%|██████████| 250/250 [04:07<00:00,  1.01it/s]
2025-05-14 10:58:31,156 - modnet - INFO - Loss per individual: ind 0: 0.097 	ind 1: 0.109 	ind 2: 0.105 	ind 3: 0.099 	ind 4: 0.110 	ind 5: 0.152 	ind 6: 0.103 	ind 7: 0.115 	ind 8: 0.098 	ind 9: 0.118 	ind 10: 0.102 	ind 11: 0.103 	ind 12: 0.098 	ind 13: 0.097 	ind 14: 0.104 	ind 15: 0.122 	ind 16: 0.097 	ind 17: 0.097 	ind 18: 0.103 	ind 19: 0.209 	ind 20: 0.129 	ind 21: 0.128 	ind 22: 0.108 	ind 23: 0.135 	ind 24: 0.110 	ind 25: 0.146 	ind 26: 0.100 	ind 27: 0.114 	ind 28: 0.099 	ind 29: 0.100 	ind 30: 0.102 	ind 31: 0.096 	ind 32: 0.114 	ind 33: 0.105 	ind 34: 0.147 	ind 35: 0.146 	ind 36: 0.119 	ind 37: 0.102 	ind 38: 0.110 	ind 39: 0.097 	ind 40: 0.114 	ind 41: 0.118 	ind 42: 0.107 	ind 43: 0.116 	ind 44: 0.116 	ind 45: 0.122 	ind 46: 0.106 	ind 47: 0.103 	ind 48: 0.107 	ind 49: 0.101 	
2025-05-14 10:58:31,157 - modnet - INFO - Generation number 5

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:12<52:32, 12.66s/it]
  1%|          | 2/250 [00:13<23:09,  5.60s/it]
  1%|          | 3/250 [00:14<14:53,  3.62s/it]
  2%|▏         | 5/250 [00:14<06:33,  1.60s/it]
  2%|▏         | 6/250 [00:15<05:58,  1.47s/it]
  3%|▎         | 7/250 [00:16<04:40,  1.15s/it]
  3%|▎         | 8/250 [00:17<04:59,  1.24s/it]
  4%|▎         | 9/250 [00:18<03:58,  1.01it/s]
  4%|▍         | 10/250 [00:18<03:05,  1.29it/s]
  4%|▍         | 11/250 [00:18<02:54,  1.37it/s]
  5%|▍         | 12/250 [00:19<02:36,  1.53it/s]
  5%|▌         | 13/250 [00:19<01:59,  1.99it/s]
  6%|▌         | 14/250 [00:19<01:39,  2.36it/s]
  6%|▌         | 15/250 [00:19<01:19,  2.97it/s]
  6%|▋         | 16/250 [00:20<01:18,  2.99it/s]
  7%|▋         | 17/250 [00:20<01:18,  2.96it/s]
  7%|▋         | 18/250 [00:20<01:17,  3.00it/s]
  8%|▊         | 19/250 [00:21<01:06,  3.50it/s]
  8%|▊         | 20/250 [00:21<01:21,  2.82it/s]
  8%|▊         | 21/250 [00:21<01:10,  3.23it/s]
  9%|▉         | 22/250 [00:22<01:52,  2.02it/s]
  9%|▉         | 23/250 [00:23<01:45,  2.14it/s]
 10%|▉         | 24/250 [00:24<02:38,  1.42it/s]
 10%|█         | 25/250 [00:25<02:36,  1.43it/s]
 11%|█         | 27/250 [00:25<02:05,  1.78it/s]
 11%|█         | 28/250 [00:26<02:07,  1.75it/s]
 12%|█▏        | 29/250 [00:27<02:10,  1.69it/s]
 12%|█▏        | 30/250 [00:28<02:47,  1.32it/s]
 12%|█▏        | 31/250 [00:28<02:32,  1.43it/s]
 13%|█▎        | 32/250 [00:29<02:23,  1.52it/s]
 13%|█▎        | 33/250 [00:32<04:58,  1.37s/it]
 14%|█▎        | 34/250 [00:32<03:37,  1.01s/it]
 14%|█▍        | 35/250 [00:34<04:45,  1.33s/it]
 14%|█▍        | 36/250 [00:36<04:49,  1.35s/it]
 15%|█▍        | 37/250 [00:38<05:39,  1.60s/it]
 15%|█▌        | 38/250 [00:39<05:04,  1.44s/it]
 16%|█▌        | 39/250 [00:42<06:50,  1.94s/it]
 16%|█▌        | 40/250 [00:44<06:31,  1.87s/it]
 16%|█▋        | 41/250 [00:45<05:44,  1.65s/it]
 17%|█▋        | 42/250 [00:46<04:52,  1.41s/it]
 17%|█▋        | 43/250 [00:48<05:15,  1.53s/it]
 18%|█▊        | 44/250 [00:48<03:51,  1.12s/it]
 18%|█▊        | 45/250 [00:48<03:17,  1.04it/s]
 18%|█▊        | 46/250 [00:49<02:46,  1.23it/s]
 19%|█▉        | 47/250 [00:49<02:27,  1.38it/s]
 19%|█▉        | 48/250 [00:51<03:19,  1.02it/s]
 20%|█▉        | 49/250 [00:51<02:41,  1.24it/s]
 20%|██        | 50/250 [00:53<03:23,  1.02s/it]
 20%|██        | 51/250 [00:55<04:52,  1.47s/it]
 21%|██        | 52/250 [00:55<03:29,  1.06s/it]
 22%|██▏       | 54/250 [00:56<01:58,  1.66it/s]
 22%|██▏       | 56/250 [00:56<01:28,  2.18it/s]
 23%|██▎       | 57/250 [00:56<01:13,  2.62it/s]
 23%|██▎       | 58/250 [00:58<02:03,  1.56it/s]
 24%|██▎       | 59/250 [00:59<02:33,  1.25it/s]
 24%|██▍       | 60/250 [01:00<02:59,  1.06it/s]
 24%|██▍       | 61/250 [01:01<02:27,  1.28it/s]
 25%|██▍       | 62/250 [01:01<02:22,  1.32it/s]
 25%|██▌       | 63/250 [01:04<03:47,  1.21s/it]
 26%|██▌       | 64/250 [01:07<05:23,  1.74s/it]
 26%|██▌       | 65/250 [01:11<07:40,  2.49s/it]
 26%|██▋       | 66/250 [01:12<06:07,  2.00s/it]
 27%|██▋       | 67/250 [01:13<05:07,  1.68s/it]
 28%|██▊       | 69/250 [01:13<03:13,  1.07s/it]
 28%|██▊       | 70/250 [01:14<02:36,  1.15it/s]
 28%|██▊       | 71/250 [01:14<02:12,  1.35it/s]
 29%|██▉       | 72/250 [01:14<01:54,  1.56it/s]
 29%|██▉       | 73/250 [01:15<01:34,  1.87it/s]
 30%|██▉       | 74/250 [01:15<01:45,  1.66it/s]
 30%|███       | 75/250 [01:16<01:49,  1.60it/s]
 31%|███       | 77/250 [01:17<01:27,  1.97it/s]
 31%|███       | 78/250 [01:17<01:18,  2.20it/s]
 32%|███▏      | 79/250 [01:18<01:36,  1.77it/s]
 32%|███▏      | 80/250 [01:18<01:17,  2.19it/s]
 32%|███▏      | 81/250 [01:19<01:36,  1.75it/s]
 33%|███▎      | 82/250 [01:20<01:47,  1.56it/s]
 33%|███▎      | 83/250 [01:20<01:33,  1.78it/s]
 34%|███▎      | 84/250 [01:21<01:28,  1.88it/s]
 34%|███▍      | 86/250 [01:22<01:51,  1.47it/s]
 35%|███▍      | 87/250 [01:23<01:29,  1.82it/s]
 35%|███▌      | 88/250 [01:23<01:18,  2.06it/s]
 36%|███▌      | 89/250 [01:25<02:21,  1.14it/s]
 36%|███▌      | 90/250 [01:27<03:12,  1.20s/it]
 36%|███▋      | 91/250 [01:28<03:07,  1.18s/it]
 37%|███▋      | 93/250 [01:28<02:00,  1.31it/s]
 38%|███▊      | 94/250 [01:29<01:41,  1.54it/s]
 38%|███▊      | 96/250 [01:29<01:04,  2.38it/s]
 39%|███▉      | 97/250 [01:43<08:46,  3.44s/it]
 39%|███▉      | 98/250 [01:43<06:48,  2.69s/it]
 40%|███▉      | 99/250 [01:43<05:15,  2.09s/it]
 40%|████      | 100/250 [01:44<04:12,  1.69s/it]
 40%|████      | 101/250 [01:45<03:47,  1.53s/it]
 41%|████      | 102/250 [01:45<02:47,  1.13s/it]
 41%|████      | 103/250 [01:46<02:16,  1.08it/s]
 42%|████▏     | 104/250 [01:48<02:55,  1.20s/it]
 42%|████▏     | 105/250 [01:49<03:00,  1.24s/it]
 42%|████▏     | 106/250 [01:50<02:30,  1.05s/it]
 43%|████▎     | 107/250 [01:50<02:17,  1.04it/s]
 43%|████▎     | 108/250 [01:51<01:50,  1.29it/s]
 44%|████▎     | 109/250 [01:52<02:26,  1.04s/it]
 44%|████▍     | 110/250 [01:53<02:04,  1.13it/s]
 44%|████▍     | 111/250 [01:53<01:34,  1.46it/s]
 45%|████▍     | 112/250 [01:55<02:47,  1.21s/it]
 45%|████▌     | 113/250 [01:58<03:36,  1.58s/it]
 46%|████▌     | 114/250 [02:01<04:28,  1.98s/it]
 46%|████▌     | 115/250 [02:03<04:31,  2.01s/it]
 46%|████▋     | 116/250 [02:03<03:14,  1.45s/it]
 47%|████▋     | 117/250 [02:04<02:42,  1.22s/it]
 48%|████▊     | 119/250 [02:04<01:30,  1.46it/s]
 48%|████▊     | 120/250 [02:05<01:32,  1.41it/s]
 48%|████▊     | 121/250 [02:05<01:20,  1.59it/s]
 49%|████▉     | 122/250 [02:06<01:26,  1.47it/s]
 49%|████▉     | 123/250 [02:06<01:06,  1.92it/s]
 50%|████▉     | 124/250 [02:07<01:09,  1.81it/s]
 50%|█████     | 126/250 [02:07<00:42,  2.90it/s]
 51%|█████     | 127/250 [02:08<01:10,  1.74it/s]
 51%|█████     | 128/250 [02:08<00:58,  2.10it/s]
 52%|█████▏    | 129/250 [02:09<00:51,  2.37it/s]
 52%|█████▏    | 130/250 [02:09<00:47,  2.54it/s]
 52%|█████▏    | 131/250 [02:09<00:46,  2.54it/s]
 53%|█████▎    | 132/250 [02:10<00:43,  2.71it/s]
 53%|█████▎    | 133/250 [02:10<00:53,  2.21it/s]
 54%|█████▎    | 134/250 [02:12<01:35,  1.22it/s]
 54%|█████▍    | 135/250 [02:14<02:29,  1.30s/it]
 54%|█████▍    | 136/250 [02:15<02:06,  1.11s/it]
 55%|█████▍    | 137/250 [02:15<01:37,  1.15it/s]
 55%|█████▌    | 138/250 [02:19<03:07,  1.67s/it]
 56%|█████▌    | 139/250 [02:23<04:28,  2.42s/it]
 56%|█████▌    | 140/250 [02:24<03:50,  2.10s/it]
 56%|█████▋    | 141/250 [02:26<03:18,  1.82s/it]
 57%|█████▋    | 142/250 [02:26<02:42,  1.50s/it]
 57%|█████▋    | 143/250 [02:27<02:19,  1.30s/it]
 58%|█████▊    | 144/250 [02:31<03:27,  1.96s/it]
 58%|█████▊    | 145/250 [02:31<02:43,  1.56s/it]
 58%|█████▊    | 146/250 [02:33<02:38,  1.52s/it]
 59%|█████▉    | 147/250 [02:33<02:09,  1.26s/it]
 59%|█████▉    | 148/250 [02:34<01:40,  1.01it/s]
 60%|█████▉    | 149/250 [02:34<01:28,  1.14it/s]
 60%|██████    | 150/250 [02:37<02:14,  1.34s/it]
 61%|██████    | 152/250 [02:38<01:48,  1.10s/it]
 61%|██████    | 153/250 [02:39<01:33,  1.03it/s]
 62%|██████▏   | 154/250 [02:41<02:04,  1.30s/it]
 62%|██████▏   | 155/250 [02:41<01:34,  1.01it/s]
 63%|██████▎   | 157/250 [02:43<01:24,  1.10it/s]
 63%|██████▎   | 158/250 [02:43<01:10,  1.30it/s]
 64%|██████▎   | 159/250 [02:44<01:02,  1.46it/s]
 64%|██████▍   | 160/250 [02:44<00:48,  1.87it/s]
 64%|██████▍   | 161/250 [02:45<01:06,  1.34it/s]
 65%|██████▍   | 162/250 [02:46<01:02,  1.40it/s]
 66%|██████▌   | 164/250 [02:47<01:01,  1.39it/s]
 66%|██████▋   | 166/250 [02:48<00:53,  1.58it/s]
 67%|██████▋   | 167/250 [02:49<00:54,  1.52it/s]
 67%|██████▋   | 168/250 [02:50<00:54,  1.49it/s]
 68%|██████▊   | 169/250 [02:51<00:58,  1.38it/s]
 68%|██████▊   | 170/250 [02:53<01:26,  1.08s/it]
 68%|██████▊   | 171/250 [02:53<01:12,  1.08it/s]
 69%|██████▉   | 172/250 [02:56<01:47,  1.38s/it]
 69%|██████▉   | 173/250 [02:57<01:55,  1.50s/it]
 70%|██████▉   | 174/250 [02:58<01:23,  1.10s/it]
 70%|███████   | 175/250 [02:58<01:09,  1.08it/s]
 70%|███████   | 176/250 [03:02<02:12,  1.79s/it]
 71%|███████   | 177/250 [03:02<01:42,  1.40s/it]
 71%|███████   | 178/250 [03:03<01:30,  1.26s/it]
 72%|███████▏  | 180/250 [03:03<00:50,  1.40it/s]
 72%|███████▏  | 181/250 [03:11<02:46,  2.42s/it]
 73%|███████▎  | 182/250 [03:12<02:11,  1.94s/it]
 73%|███████▎  | 183/250 [03:12<01:42,  1.53s/it]
 74%|███████▎  | 184/250 [03:12<01:14,  1.13s/it]
 74%|███████▍  | 185/250 [03:13<01:09,  1.07s/it]
 74%|███████▍  | 186/250 [03:14<00:57,  1.12it/s]
 75%|███████▍  | 187/250 [03:15<00:58,  1.07it/s]
 75%|███████▌  | 188/250 [03:15<00:52,  1.19it/s]
 76%|███████▌  | 189/250 [03:16<00:44,  1.38it/s]
 76%|███████▋  | 191/250 [03:18<00:51,  1.14it/s]
 77%|███████▋  | 192/250 [03:19<00:51,  1.12it/s]
 77%|███████▋  | 193/250 [03:19<00:45,  1.24it/s]
 78%|███████▊  | 194/250 [03:20<00:43,  1.30it/s]
 78%|███████▊  | 196/250 [03:21<00:30,  1.74it/s]
 79%|███████▉  | 197/250 [03:21<00:33,  1.56it/s]
 79%|███████▉  | 198/250 [03:22<00:31,  1.66it/s]
 80%|███████▉  | 199/250 [03:23<00:30,  1.68it/s]
 80%|████████  | 200/250 [03:24<00:41,  1.20it/s]
 80%|████████  | 201/250 [03:24<00:32,  1.52it/s]
 81%|████████  | 202/250 [03:25<00:29,  1.63it/s]
 81%|████████  | 203/250 [03:25<00:28,  1.63it/s]
 82%|████████▏ | 204/250 [03:26<00:25,  1.82it/s]
 82%|████████▏ | 205/250 [03:27<00:29,  1.51it/s]
 82%|████████▏ | 206/250 [03:27<00:23,  1.87it/s]
 83%|████████▎ | 207/250 [03:28<00:27,  1.54it/s]
 83%|████████▎ | 208/250 [03:28<00:22,  1.85it/s]
 84%|████████▎ | 209/250 [03:28<00:18,  2.19it/s]
 84%|████████▍ | 210/250 [03:29<00:17,  2.22it/s]
 84%|████████▍ | 211/250 [03:29<00:18,  2.06it/s]
 85%|████████▌ | 213/250 [03:30<00:19,  1.89it/s]
 86%|████████▌ | 214/250 [03:32<00:23,  1.54it/s]
 86%|████████▌ | 215/250 [03:32<00:21,  1.61it/s]
 86%|████████▋ | 216/250 [03:34<00:30,  1.11it/s]
 87%|████████▋ | 217/250 [03:34<00:23,  1.41it/s]
 87%|████████▋ | 218/250 [03:34<00:21,  1.50it/s]
 88%|████████▊ | 219/250 [03:37<00:38,  1.24s/it]
 88%|████████▊ | 220/250 [03:39<00:38,  1.28s/it]
 88%|████████▊ | 221/250 [03:40<00:36,  1.25s/it]
 89%|████████▉ | 222/250 [03:41<00:33,  1.21s/it]
 90%|████████▉ | 224/250 [03:41<00:18,  1.37it/s]
 90%|█████████ | 225/250 [03:41<00:15,  1.60it/s]
 91%|█████████ | 227/250 [03:43<00:14,  1.56it/s]
 91%|█████████ | 228/250 [03:43<00:13,  1.67it/s]
 92%|█████████▏| 229/250 [03:45<00:18,  1.15it/s]
 92%|█████████▏| 230/250 [03:46<00:16,  1.22it/s]
 93%|█████████▎| 232/250 [03:46<00:10,  1.68it/s]
 93%|█████████▎| 233/250 [03:47<00:09,  1.79it/s]
 94%|█████████▎| 234/250 [03:47<00:08,  1.95it/s]
 94%|█████████▍| 235/250 [03:48<00:10,  1.41it/s]
 94%|█████████▍| 236/250 [03:49<00:09,  1.55it/s]
 95%|█████████▍| 237/250 [03:49<00:07,  1.64it/s]
 95%|█████████▌| 238/250 [03:50<00:06,  1.93it/s]
 96%|█████████▌| 239/250 [03:51<00:07,  1.43it/s]
 96%|█████████▌| 240/250 [03:53<00:10,  1.04s/it]
 96%|█████████▋| 241/250 [03:53<00:08,  1.02it/s]
 97%|█████████▋| 242/250 [03:57<00:14,  1.87s/it]
 97%|█████████▋| 243/250 [03:59<00:12,  1.79s/it]
 98%|█████████▊| 244/250 [04:01<00:10,  1.77s/it]
 98%|█████████▊| 245/250 [04:02<00:08,  1.62s/it]
 98%|█████████▊| 246/250 [04:03<00:05,  1.32s/it]
 99%|█████████▉| 247/250 [04:07<00:06,  2.24s/it]
 99%|█████████▉| 248/250 [04:08<00:03,  1.92s/it]
100%|█████████▉| 249/250 [04:09<00:01,  1.49s/it]
100%|██████████| 250/250 [04:10<00:00,  1.55s/it]
100%|██████████| 250/250 [04:10<00:00,  1.00s/it]
2025-05-14 11:02:42,013 - modnet - INFO - Loss per individual: ind 0: 0.100 	ind 1: 0.118 	ind 2: 0.144 	ind 3: 0.135 	ind 4: 0.139 	ind 5: 0.119 	ind 6: 0.118 	ind 7: 0.107 	ind 8: 0.121 	ind 9: 0.121 	ind 10: 0.139 	ind 11: 0.113 	ind 12: 0.099 	ind 13: 0.099 	ind 14: 0.107 	ind 15: 0.107 	ind 16: 0.102 	ind 17: 0.106 	ind 18: 0.101 	ind 19: 0.104 	ind 20: 0.099 	ind 21: 0.102 	ind 22: 0.128 	ind 23: 0.111 	ind 24: 0.110 	ind 25: 0.111 	ind 26: 0.106 	ind 27: 0.131 	ind 28: 0.141 	ind 29: 0.098 	ind 30: 0.114 	ind 31: 0.101 	ind 32: 0.101 	ind 33: 0.121 	ind 34: 0.125 	ind 35: 0.095 	ind 36: 0.096 	ind 37: 0.098 	ind 38: 0.160 	ind 39: 0.095 	ind 40: 0.106 	ind 41: 0.116 	ind 42: 0.105 	ind 43: 0.124 	ind 44: 0.121 	ind 45: 0.098 	ind 46: 0.130 	ind 47: 0.131 	ind 48: 0.118 	ind 49: 0.101 	
2025-05-14 11:02:42,015 - modnet - INFO - Generation number 6

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:09<40:57,  9.87s/it]
  1%|          | 2/250 [00:10<18:19,  4.44s/it]
  1%|          | 3/250 [00:11<11:19,  2.75s/it]
  2%|▏         | 4/250 [00:12<08:46,  2.14s/it]
  2%|▏         | 5/250 [00:12<05:46,  1.42s/it]
  2%|▏         | 6/250 [00:13<04:50,  1.19s/it]
  3%|▎         | 7/250 [00:14<04:32,  1.12s/it]
  3%|▎         | 8/250 [00:16<05:23,  1.34s/it]
  4%|▎         | 9/250 [00:17<04:55,  1.23s/it]
  4%|▍         | 11/250 [00:19<04:26,  1.11s/it]
  5%|▍         | 12/250 [00:21<06:00,  1.52s/it]
  6%|▌         | 14/250 [00:21<03:33,  1.11it/s]
  6%|▌         | 15/250 [00:22<03:21,  1.17it/s]
  6%|▋         | 16/250 [00:23<03:06,  1.25it/s]
  7%|▋         | 17/250 [00:24<04:02,  1.04s/it]
  7%|▋         | 18/250 [00:25<03:21,  1.15it/s]
  8%|▊         | 20/250 [00:26<02:58,  1.29it/s]
  8%|▊         | 21/250 [00:27<03:17,  1.16it/s]
  9%|▉         | 23/250 [00:29<03:02,  1.25it/s]
 10%|▉         | 24/250 [00:29<02:45,  1.36it/s]
 10%|█         | 25/250 [00:30<03:11,  1.18it/s]
 10%|█         | 26/250 [00:32<03:46,  1.01s/it]
 11%|█         | 28/250 [00:34<03:34,  1.03it/s]
 12%|█▏        | 29/250 [00:36<04:29,  1.22s/it]
 12%|█▏        | 31/250 [00:37<03:20,  1.09it/s]
 13%|█▎        | 32/250 [00:38<03:20,  1.08it/s]
 13%|█▎        | 33/250 [00:38<03:07,  1.16it/s]
 14%|█▍        | 35/250 [00:38<01:54,  1.88it/s]
 14%|█▍        | 36/250 [00:39<01:38,  2.17it/s]
 15%|█▍        | 37/250 [00:39<01:54,  1.86it/s]
 15%|█▌        | 38/250 [00:40<02:16,  1.55it/s]
 16%|█▌        | 39/250 [00:41<01:56,  1.81it/s]
 16%|█▌        | 40/250 [00:41<01:34,  2.23it/s]
 16%|█▋        | 41/250 [00:41<01:16,  2.72it/s]
 17%|█▋        | 42/250 [00:42<02:23,  1.45it/s]
 17%|█▋        | 43/250 [00:43<02:07,  1.62it/s]
 18%|█▊        | 44/250 [00:43<01:57,  1.76it/s]
 18%|█▊        | 45/250 [00:45<02:55,  1.17it/s]
 18%|█▊        | 46/250 [00:46<03:32,  1.04s/it]
 19%|█▉        | 47/250 [00:47<03:08,  1.08it/s]
 19%|█▉        | 48/250 [00:48<02:40,  1.26it/s]
 20%|█▉        | 49/250 [00:50<03:57,  1.18s/it]
 20%|██        | 50/250 [00:50<02:57,  1.12it/s]
 20%|██        | 51/250 [00:50<02:12,  1.50it/s]
 21%|██        | 52/250 [00:50<01:59,  1.65it/s]
 21%|██        | 53/250 [00:51<01:36,  2.04it/s]
 22%|██▏       | 54/250 [00:52<02:42,  1.21it/s]
 22%|██▏       | 55/250 [00:53<02:05,  1.55it/s]
 22%|██▏       | 56/250 [00:53<01:44,  1.85it/s]
 23%|██▎       | 57/250 [00:53<01:33,  2.07it/s]
 23%|██▎       | 58/250 [00:55<02:26,  1.31it/s]
 24%|██▍       | 60/250 [00:55<01:24,  2.24it/s]
 25%|██▍       | 62/250 [00:55<01:12,  2.61it/s]
 25%|██▌       | 63/250 [00:57<02:17,  1.36it/s]
 26%|██▌       | 64/250 [00:58<02:11,  1.41it/s]
 26%|██▋       | 66/250 [00:59<01:59,  1.54it/s]
 27%|██▋       | 67/250 [01:00<01:57,  1.56it/s]
 27%|██▋       | 68/250 [01:00<01:37,  1.87it/s]
 28%|██▊       | 69/250 [01:00<01:17,  2.33it/s]
 28%|██▊       | 70/250 [01:02<02:40,  1.12it/s]
 28%|██▊       | 71/250 [01:04<03:07,  1.05s/it]
 29%|██▉       | 72/250 [01:06<04:00,  1.35s/it]
 29%|██▉       | 73/250 [01:06<03:19,  1.13s/it]
 30%|██▉       | 74/250 [01:07<02:55,  1.00it/s]
 30%|███       | 75/250 [01:07<02:21,  1.24it/s]
 30%|███       | 76/250 [01:08<02:22,  1.22it/s]
 31%|███       | 77/250 [01:09<02:11,  1.31it/s]
 31%|███       | 78/250 [01:13<05:05,  1.77s/it]
 32%|███▏      | 79/250 [01:15<05:26,  1.91s/it]
 32%|███▏      | 80/250 [01:17<05:41,  2.01s/it]
 32%|███▏      | 81/250 [01:18<04:11,  1.49s/it]
 33%|███▎      | 82/250 [01:18<03:21,  1.20s/it]
 33%|███▎      | 83/250 [01:19<03:06,  1.12s/it]
 34%|███▎      | 84/250 [01:20<02:31,  1.09it/s]
 34%|███▍      | 85/250 [01:22<03:55,  1.43s/it]
 34%|███▍      | 86/250 [01:23<03:02,  1.12s/it]
 35%|███▍      | 87/250 [01:23<02:33,  1.06it/s]
 36%|███▌      | 89/250 [01:24<01:41,  1.59it/s]
 36%|███▌      | 90/250 [01:24<01:44,  1.53it/s]
 36%|███▋      | 91/250 [01:25<01:23,  1.90it/s]
 37%|███▋      | 92/250 [01:25<01:36,  1.64it/s]
 37%|███▋      | 93/250 [01:25<01:14,  2.10it/s]
 38%|███▊      | 94/250 [01:29<03:19,  1.28s/it]
 38%|███▊      | 95/250 [01:29<02:26,  1.06it/s]
 38%|███▊      | 96/250 [01:32<04:14,  1.65s/it]
 39%|███▉      | 97/250 [01:33<03:33,  1.40s/it]
 39%|███▉      | 98/250 [01:34<03:06,  1.23s/it]
 40%|███▉      | 99/250 [01:35<02:50,  1.13s/it]
 40%|████      | 100/250 [01:35<02:16,  1.10it/s]
 40%|████      | 101/250 [01:36<01:55,  1.29it/s]
 41%|████      | 102/250 [01:37<02:09,  1.15it/s]
 41%|████      | 103/250 [01:37<01:40,  1.46it/s]
 42%|████▏     | 106/250 [01:37<00:48,  2.96it/s]
 43%|████▎     | 107/250 [01:37<00:43,  3.28it/s]
 43%|████▎     | 108/250 [01:39<01:25,  1.67it/s]
 44%|████▍     | 110/250 [01:39<00:55,  2.54it/s]
 44%|████▍     | 111/250 [01:39<00:46,  3.02it/s]
 45%|████▍     | 112/250 [01:39<00:42,  3.26it/s]
 45%|████▌     | 113/250 [01:40<01:03,  2.17it/s]
 46%|████▌     | 114/250 [01:42<01:34,  1.44it/s]
 46%|████▌     | 115/250 [01:42<01:20,  1.67it/s]
 46%|████▋     | 116/250 [01:43<01:46,  1.26it/s]
 47%|████▋     | 117/250 [01:45<02:35,  1.17s/it]
 47%|████▋     | 118/250 [01:47<02:50,  1.29s/it]
 48%|████▊     | 119/250 [01:48<02:48,  1.29s/it]
 48%|████▊     | 120/250 [01:51<03:30,  1.62s/it]
 48%|████▊     | 121/250 [01:51<02:46,  1.29s/it]
 49%|████▉     | 122/250 [01:52<02:27,  1.16s/it]
 49%|████▉     | 123/250 [01:53<02:29,  1.18s/it]
 50%|████▉     | 124/250 [01:54<01:58,  1.06it/s]
 50%|█████     | 126/250 [01:54<01:08,  1.81it/s]
 51%|█████     | 127/250 [01:55<01:18,  1.57it/s]
 51%|█████     | 128/250 [01:55<01:11,  1.71it/s]
 52%|█████▏    | 129/250 [01:56<01:03,  1.91it/s]
 52%|█████▏    | 130/250 [01:59<02:33,  1.28s/it]
 52%|█████▏    | 131/250 [01:59<01:53,  1.05it/s]
 53%|█████▎    | 132/250 [02:01<02:47,  1.42s/it]
 53%|█████▎    | 133/250 [02:03<02:40,  1.38s/it]
 54%|█████▍    | 135/250 [02:03<01:45,  1.09it/s]
 54%|█████▍    | 136/250 [02:04<01:36,  1.18it/s]
 55%|█████▍    | 137/250 [02:05<01:33,  1.20it/s]
 55%|█████▌    | 138/250 [02:05<01:12,  1.54it/s]
 56%|█████▌    | 139/250 [02:06<01:33,  1.19it/s]
 56%|█████▋    | 141/250 [02:08<01:33,  1.16it/s]
 57%|█████▋    | 143/250 [02:10<01:27,  1.22it/s]
 58%|█████▊    | 145/250 [02:10<01:00,  1.73it/s]
 58%|█████▊    | 146/250 [02:10<00:58,  1.78it/s]
 59%|█████▉    | 147/250 [02:12<01:10,  1.46it/s]
 59%|█████▉    | 148/250 [02:12<01:07,  1.52it/s]
 60%|█████▉    | 149/250 [02:12<00:54,  1.85it/s]
 60%|██████    | 150/250 [02:13<01:07,  1.49it/s]
 61%|██████    | 152/250 [02:14<00:58,  1.69it/s]
 61%|██████    | 153/250 [02:15<00:50,  1.90it/s]
 62%|██████▏   | 154/250 [02:15<00:43,  2.19it/s]
 62%|██████▏   | 155/250 [02:15<00:35,  2.64it/s]
 62%|██████▏   | 156/250 [02:15<00:33,  2.83it/s]
 63%|██████▎   | 157/250 [02:18<01:22,  1.13it/s]
 63%|██████▎   | 158/250 [02:18<01:10,  1.31it/s]
 64%|██████▎   | 159/250 [02:19<01:16,  1.18it/s]
 64%|██████▍   | 160/250 [02:20<01:06,  1.36it/s]
 65%|██████▍   | 162/250 [02:21<00:58,  1.50it/s]
 66%|██████▌   | 164/250 [02:21<00:45,  1.87it/s]
 66%|██████▌   | 165/250 [02:22<00:42,  1.99it/s]
 66%|██████▋   | 166/250 [02:22<00:35,  2.39it/s]
 67%|██████▋   | 167/250 [02:23<00:43,  1.91it/s]
 67%|██████▋   | 168/250 [02:24<01:00,  1.36it/s]
 68%|██████▊   | 169/250 [02:25<01:12,  1.11it/s]
 68%|██████▊   | 170/250 [02:26<01:06,  1.20it/s]
 68%|██████▊   | 171/250 [02:26<00:49,  1.59it/s]
 69%|██████▉   | 172/250 [02:27<00:42,  1.85it/s]
 69%|██████▉   | 173/250 [02:27<00:35,  2.19it/s]
 70%|██████▉   | 174/250 [02:27<00:30,  2.46it/s]
 70%|███████   | 175/250 [02:28<00:40,  1.85it/s]
 70%|███████   | 176/250 [02:28<00:39,  1.88it/s]
 71%|███████   | 178/250 [02:30<00:41,  1.75it/s]
 72%|███████▏  | 179/250 [02:32<01:11,  1.01s/it]
 72%|███████▏  | 180/250 [02:32<00:57,  1.22it/s]
 72%|███████▏  | 181/250 [02:32<00:43,  1.58it/s]
 73%|███████▎  | 182/250 [02:40<02:50,  2.50s/it]
 73%|███████▎  | 183/250 [02:42<02:44,  2.46s/it]
 74%|███████▎  | 184/250 [02:43<02:03,  1.87s/it]
 74%|███████▍  | 185/250 [02:43<01:36,  1.49s/it]
 74%|███████▍  | 186/250 [02:44<01:18,  1.23s/it]
 75%|███████▍  | 187/250 [02:45<01:10,  1.12s/it]
 75%|███████▌  | 188/250 [02:45<00:59,  1.04it/s]
 76%|███████▌  | 189/250 [02:46<00:55,  1.10it/s]
 76%|███████▌  | 190/250 [02:48<01:16,  1.27s/it]
 76%|███████▋  | 191/250 [02:49<01:06,  1.13s/it]
 77%|███████▋  | 192/250 [02:50<00:58,  1.02s/it]
 77%|███████▋  | 193/250 [02:50<00:54,  1.04it/s]
 78%|███████▊  | 194/250 [02:56<02:16,  2.44s/it]
 78%|███████▊  | 195/250 [02:57<01:52,  2.04s/it]
 78%|███████▊  | 196/250 [02:58<01:29,  1.67s/it]
 79%|███████▉  | 197/250 [02:59<01:19,  1.50s/it]
 79%|███████▉  | 198/250 [03:01<01:22,  1.59s/it]
 80%|███████▉  | 199/250 [03:05<02:01,  2.37s/it]
 80%|████████  | 200/250 [03:07<01:42,  2.05s/it]
 80%|████████  | 201/250 [03:07<01:13,  1.50s/it]
 81%|████████  | 203/250 [03:08<00:45,  1.03it/s]
 82%|████████▏ | 204/250 [03:08<00:35,  1.31it/s]
 82%|████████▏ | 205/250 [03:08<00:28,  1.55it/s]
 82%|████████▏ | 206/250 [03:09<00:29,  1.51it/s]
 83%|████████▎ | 207/250 [03:09<00:23,  1.79it/s]
 83%|████████▎ | 208/250 [03:10<00:28,  1.46it/s]
 84%|████████▎ | 209/250 [03:10<00:22,  1.85it/s]
 84%|████████▍ | 210/250 [03:11<00:23,  1.73it/s]
 84%|████████▍ | 211/250 [03:12<00:26,  1.47it/s]
 85%|████████▍ | 212/250 [03:12<00:19,  1.92it/s]
 86%|████████▌ | 215/250 [03:13<00:14,  2.39it/s]
 86%|████████▋ | 216/250 [03:13<00:12,  2.77it/s]
 87%|████████▋ | 217/250 [03:13<00:11,  2.96it/s]
 87%|████████▋ | 218/250 [03:14<00:12,  2.66it/s]
 88%|████████▊ | 219/250 [03:15<00:15,  2.02it/s]
 88%|████████▊ | 220/250 [03:15<00:15,  1.93it/s]
 88%|████████▊ | 221/250 [03:18<00:35,  1.21s/it]
 89%|████████▉ | 222/250 [03:18<00:25,  1.09it/s]
 89%|████████▉ | 223/250 [03:19<00:19,  1.39it/s]
 90%|████████▉ | 224/250 [03:19<00:15,  1.72it/s]
 90%|█████████ | 225/250 [03:20<00:16,  1.54it/s]
 91%|█████████ | 228/250 [03:20<00:07,  3.01it/s]
 92%|█████████▏| 229/250 [03:20<00:07,  2.94it/s]
 92%|█████████▏| 230/250 [03:21<00:06,  2.97it/s]
 92%|█████████▏| 231/250 [03:21<00:05,  3.24it/s]
 93%|█████████▎| 232/250 [03:21<00:05,  3.18it/s]
 93%|█████████▎| 233/250 [03:22<00:06,  2.72it/s]
 94%|█████████▎| 234/250 [03:24<00:16,  1.00s/it]
 94%|█████████▍| 235/250 [03:27<00:23,  1.56s/it]
 94%|█████████▍| 236/250 [03:30<00:25,  1.80s/it]
 95%|█████████▍| 237/250 [03:33<00:29,  2.30s/it]
 95%|█████████▌| 238/250 [03:33<00:20,  1.69s/it]
 96%|█████████▌| 239/250 [03:35<00:17,  1.59s/it]
 96%|█████████▌| 240/250 [03:35<00:11,  1.15s/it]
 96%|█████████▋| 241/250 [03:35<00:08,  1.11it/s]
 97%|█████████▋| 242/250 [03:38<00:10,  1.32s/it]
 97%|█████████▋| 243/250 [03:38<00:07,  1.08s/it]
 98%|█████████▊| 244/250 [03:40<00:08,  1.35s/it]
 98%|█████████▊| 245/250 [03:41<00:06,  1.36s/it]
 98%|█████████▊| 246/250 [03:42<00:04,  1.18s/it]
 99%|█████████▉| 247/250 [03:43<00:02,  1.06it/s]
 99%|█████████▉| 248/250 [03:43<00:01,  1.29it/s]
100%|█████████▉| 249/250 [03:43<00:00,  1.44it/s]
100%|██████████| 250/250 [03:46<00:00,  1.19s/it]
100%|██████████| 250/250 [03:46<00:00,  1.10it/s]
2025-05-14 11:06:28,373 - modnet - INFO - Loss per individual: ind 0: 0.115 	ind 1: 0.102 	ind 2: 0.105 	ind 3: 0.114 	ind 4: 0.106 	ind 5: 0.112 	ind 6: 0.100 	ind 7: 0.101 	ind 8: 0.114 	ind 9: 0.132 	ind 10: 0.098 	ind 11: 0.097 	ind 12: 0.103 	ind 13: 0.103 	ind 14: 0.101 	ind 15: 0.100 	ind 16: 0.099 	ind 17: 0.097 	ind 18: 0.101 	ind 19: 0.102 	ind 20: 0.104 	ind 21: 0.096 	ind 22: 0.098 	ind 23: 0.110 	ind 24: 0.108 	ind 25: 0.096 	ind 26: 0.141 	ind 27: 0.098 	ind 28: 0.113 	ind 29: 0.098 	ind 30: 0.112 	ind 31: 0.112 	ind 32: 0.097 	ind 33: 0.100 	ind 34: 0.144 	ind 35: 0.098 	ind 36: 0.102 	ind 37: 0.114 	ind 38: 0.097 	ind 39: 0.109 	ind 40: 0.097 	ind 41: 0.101 	ind 42: 0.105 	ind 43: 0.129 	ind 44: 0.103 	ind 45: 0.104 	ind 46: 0.156 	ind 47: 0.132 	ind 48: 0.099 	ind 49: 0.103 	
2025-05-14 11:06:28,374 - modnet - INFO - Generation number 7

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:19<1:21:53, 19.73s/it]
  1%|          | 2/250 [00:20<35:36,  8.61s/it]  
  1%|          | 3/250 [00:20<19:47,  4.81s/it]
  2%|▏         | 4/250 [00:21<12:28,  3.04s/it]
  2%|▏         | 5/250 [00:24<12:43,  3.12s/it]
  3%|▎         | 7/250 [00:25<07:19,  1.81s/it]
  3%|▎         | 8/250 [00:25<05:41,  1.41s/it]
  4%|▎         | 9/250 [00:26<05:05,  1.27s/it]
  4%|▍         | 11/250 [00:26<02:55,  1.37it/s]
  5%|▌         | 13/250 [00:27<01:55,  2.06it/s]
  6%|▌         | 14/250 [00:29<03:24,  1.16it/s]
  6%|▌         | 15/250 [00:29<02:51,  1.37it/s]
  6%|▋         | 16/250 [00:30<02:55,  1.34it/s]
  7%|▋         | 17/250 [00:30<02:30,  1.55it/s]
  7%|▋         | 18/250 [00:32<03:54,  1.01s/it]
  8%|▊         | 19/250 [00:34<04:50,  1.26s/it]
  8%|▊         | 20/250 [00:34<03:49,  1.00it/s]
  8%|▊         | 21/250 [00:35<02:58,  1.28it/s]
  9%|▉         | 22/250 [00:35<02:27,  1.55it/s]
  9%|▉         | 23/250 [00:35<01:52,  2.01it/s]
 10%|▉         | 24/250 [00:36<02:29,  1.51it/s]
 10%|█         | 25/250 [00:38<03:14,  1.16it/s]
 10%|█         | 26/250 [00:39<04:14,  1.14s/it]
 11%|█         | 27/250 [00:41<04:44,  1.28s/it]
 11%|█         | 28/250 [00:41<03:25,  1.08it/s]
 12%|█▏        | 29/250 [00:42<03:49,  1.04s/it]
 12%|█▏        | 30/250 [00:43<03:55,  1.07s/it]
 12%|█▏        | 31/250 [00:46<05:16,  1.45s/it]
 13%|█▎        | 32/250 [00:46<04:13,  1.16s/it]
 13%|█▎        | 33/250 [00:49<06:01,  1.66s/it]
 14%|█▎        | 34/250 [00:49<04:24,  1.23s/it]
 14%|█▍        | 35/250 [00:51<04:36,  1.29s/it]
 14%|█▍        | 36/250 [00:51<03:35,  1.01s/it]
 15%|█▍        | 37/250 [00:51<02:43,  1.30it/s]
 15%|█▌        | 38/250 [00:51<02:01,  1.74it/s]
 16%|█▌        | 39/250 [00:52<02:30,  1.41it/s]
 16%|█▌        | 40/250 [00:53<02:32,  1.38it/s]
 17%|█▋        | 42/250 [00:53<01:30,  2.29it/s]
 17%|█▋        | 43/250 [00:54<01:28,  2.35it/s]
 18%|█▊        | 44/250 [00:54<01:23,  2.46it/s]
 18%|█▊        | 45/250 [00:54<01:18,  2.62it/s]
 18%|█▊        | 46/250 [00:57<02:54,  1.17it/s]
 19%|█▉        | 47/250 [00:57<02:37,  1.29it/s]
 19%|█▉        | 48/250 [01:03<07:12,  2.14s/it]
 20%|█▉        | 49/250 [01:03<05:33,  1.66s/it]
 20%|██        | 50/250 [01:04<04:28,  1.34s/it]
 21%|██        | 52/250 [01:04<02:45,  1.20it/s]
 21%|██        | 53/250 [01:04<02:12,  1.49it/s]
 22%|██▏       | 55/250 [01:05<01:51,  1.75it/s]
 22%|██▏       | 56/250 [01:06<01:42,  1.89it/s]
 23%|██▎       | 57/250 [01:06<01:57,  1.64it/s]
 23%|██▎       | 58/250 [01:07<01:45,  1.82it/s]
 24%|██▎       | 59/250 [01:08<01:52,  1.69it/s]
 24%|██▍       | 60/250 [01:10<03:13,  1.02s/it]
 24%|██▍       | 61/250 [01:11<03:10,  1.01s/it]
 25%|██▍       | 62/250 [01:11<02:27,  1.27it/s]
 25%|██▌       | 63/250 [01:11<02:00,  1.55it/s]
 26%|██▌       | 64/250 [01:12<02:12,  1.40it/s]
 26%|██▌       | 65/250 [01:12<01:54,  1.61it/s]
 26%|██▋       | 66/250 [01:14<02:55,  1.05it/s]
 27%|██▋       | 67/250 [01:14<02:08,  1.43it/s]
 27%|██▋       | 68/250 [01:15<02:03,  1.47it/s]
 28%|██▊       | 69/250 [01:16<01:58,  1.52it/s]
 28%|██▊       | 70/250 [01:16<02:03,  1.46it/s]
 28%|██▊       | 71/250 [01:17<02:21,  1.27it/s]
 29%|██▉       | 72/250 [01:19<03:34,  1.20s/it]
 29%|██▉       | 73/250 [01:20<03:03,  1.04s/it]
 30%|██▉       | 74/250 [01:23<04:51,  1.66s/it]
 30%|███       | 76/250 [01:26<04:18,  1.48s/it]
 31%|███       | 77/250 [01:27<04:18,  1.49s/it]
 31%|███       | 78/250 [01:27<03:17,  1.15s/it]
 32%|███▏      | 79/250 [01:28<03:03,  1.07s/it]
 32%|███▏      | 80/250 [01:29<03:04,  1.08s/it]
 32%|███▏      | 81/250 [01:30<02:15,  1.25it/s]
 33%|███▎      | 82/250 [01:31<02:45,  1.01it/s]
 33%|███▎      | 83/250 [01:31<02:01,  1.37it/s]
 34%|███▎      | 84/250 [01:32<01:44,  1.58it/s]
 34%|███▍      | 85/250 [01:32<01:52,  1.47it/s]
 34%|███▍      | 86/250 [01:33<01:27,  1.88it/s]
 35%|███▍      | 87/250 [01:33<01:12,  2.26it/s]
 35%|███▌      | 88/250 [01:33<01:15,  2.15it/s]
 36%|███▌      | 89/250 [01:34<01:50,  1.46it/s]
 36%|███▌      | 90/250 [01:36<02:17,  1.16it/s]
 36%|███▋      | 91/250 [01:36<02:02,  1.30it/s]
 37%|███▋      | 92/250 [01:37<02:19,  1.13it/s]
 37%|███▋      | 93/250 [01:39<02:36,  1.00it/s]
 38%|███▊      | 95/250 [01:39<01:50,  1.40it/s]
 39%|███▉      | 97/250 [01:40<01:09,  2.20it/s]
 39%|███▉      | 98/250 [01:40<00:57,  2.65it/s]
 40%|███▉      | 99/250 [01:41<01:41,  1.48it/s]
 40%|████      | 100/250 [01:45<03:35,  1.43s/it]
 41%|████      | 102/250 [01:45<02:12,  1.11it/s]
 41%|████      | 103/250 [01:46<01:54,  1.29it/s]
 42%|████▏     | 104/250 [01:46<01:45,  1.38it/s]
 42%|████▏     | 105/250 [01:47<01:37,  1.49it/s]
 42%|████▏     | 106/250 [01:47<01:35,  1.51it/s]
 43%|████▎     | 107/250 [01:48<01:20,  1.78it/s]
 43%|████▎     | 108/250 [01:48<01:09,  2.03it/s]
 44%|████▍     | 110/250 [01:50<01:32,  1.51it/s]
 44%|████▍     | 111/250 [01:51<01:45,  1.32it/s]
 45%|████▍     | 112/250 [01:54<02:57,  1.29s/it]
 45%|████▌     | 113/250 [01:56<03:23,  1.48s/it]
 46%|████▌     | 114/250 [01:58<03:48,  1.68s/it]
 46%|████▌     | 115/250 [02:00<03:50,  1.71s/it]
 46%|████▋     | 116/250 [02:02<03:57,  1.77s/it]
 47%|████▋     | 117/250 [02:02<03:05,  1.39s/it]
 48%|████▊     | 119/250 [02:06<03:43,  1.71s/it]
 48%|████▊     | 120/250 [02:06<02:57,  1.36s/it]
 49%|████▉     | 122/250 [02:08<02:14,  1.05s/it]
 49%|████▉     | 123/250 [02:08<01:47,  1.18it/s]
 50%|████▉     | 124/250 [02:09<01:53,  1.11it/s]
 50%|█████     | 125/250 [02:10<01:50,  1.13it/s]
 50%|█████     | 126/250 [02:11<02:03,  1.00it/s]
 51%|█████     | 127/250 [02:13<02:36,  1.27s/it]
 51%|█████     | 128/250 [02:16<03:36,  1.77s/it]
 52%|█████▏    | 129/250 [02:17<02:55,  1.45s/it]
 52%|█████▏    | 130/250 [02:19<03:18,  1.66s/it]
 52%|█████▏    | 131/250 [02:22<03:55,  1.98s/it]
 53%|█████▎    | 132/250 [02:22<02:50,  1.45s/it]
 53%|█████▎    | 133/250 [02:23<02:47,  1.43s/it]
 54%|█████▎    | 134/250 [02:25<02:58,  1.54s/it]
 54%|█████▍    | 135/250 [02:25<02:20,  1.22s/it]
 54%|█████▍    | 136/250 [02:26<01:48,  1.05it/s]
 55%|█████▍    | 137/250 [02:27<01:46,  1.06it/s]
 56%|█████▌    | 139/250 [02:27<01:02,  1.76it/s]
 56%|█████▌    | 140/250 [02:27<00:53,  2.07it/s]
 56%|█████▋    | 141/250 [02:29<01:41,  1.07it/s]
 57%|█████▋    | 142/250 [02:30<01:42,  1.06it/s]
 57%|█████▋    | 143/250 [02:31<01:25,  1.25it/s]
 58%|█████▊    | 144/250 [02:31<01:04,  1.64it/s]
 58%|█████▊    | 145/250 [02:32<01:04,  1.62it/s]
 58%|█████▊    | 146/250 [02:32<01:06,  1.57it/s]
 59%|█████▉    | 147/250 [02:33<01:22,  1.24it/s]
 59%|█████▉    | 148/250 [02:35<01:53,  1.12s/it]
 60%|█████▉    | 149/250 [02:36<01:40,  1.01it/s]
 60%|██████    | 150/250 [02:37<01:25,  1.17it/s]
 60%|██████    | 151/250 [02:37<01:18,  1.26it/s]
 61%|██████    | 153/250 [02:37<00:46,  2.08it/s]
 62%|██████▏   | 155/250 [02:38<00:32,  2.91it/s]
 63%|██████▎   | 157/250 [02:39<00:39,  2.35it/s]
 63%|██████▎   | 158/250 [02:39<00:34,  2.69it/s]
 64%|██████▎   | 159/250 [02:40<00:44,  2.04it/s]
 64%|██████▍   | 160/250 [02:40<00:36,  2.49it/s]
 64%|██████▍   | 161/250 [02:40<00:32,  2.73it/s]
 65%|██████▍   | 162/250 [02:41<00:42,  2.09it/s]
 65%|██████▌   | 163/250 [02:41<00:35,  2.45it/s]
 66%|██████▌   | 164/250 [02:42<00:41,  2.10it/s]
 66%|██████▌   | 165/250 [02:42<00:33,  2.52it/s]
 66%|██████▋   | 166/250 [02:43<00:33,  2.53it/s]
 67%|██████▋   | 167/250 [02:45<01:24,  1.02s/it]
 67%|██████▋   | 168/250 [02:46<01:11,  1.15it/s]
 68%|██████▊   | 169/250 [02:47<01:21,  1.00s/it]
 68%|██████▊   | 170/250 [02:48<01:19,  1.01it/s]
 68%|██████▊   | 171/250 [02:49<01:28,  1.12s/it]
 69%|██████▉   | 172/250 [02:50<01:08,  1.14it/s]
 69%|██████▉   | 173/250 [02:50<01:04,  1.19it/s]
 70%|██████▉   | 174/250 [02:50<00:47,  1.60it/s]
 70%|███████   | 175/250 [02:51<00:43,  1.74it/s]
 71%|███████   | 178/250 [02:52<00:29,  2.43it/s]
 72%|███████▏  | 179/250 [02:52<00:26,  2.64it/s]
 72%|███████▏  | 180/250 [02:53<00:32,  2.15it/s]
 72%|███████▏  | 181/250 [02:53<00:30,  2.26it/s]
 73%|███████▎  | 182/250 [02:54<00:36,  1.88it/s]
 73%|███████▎  | 183/250 [02:55<00:40,  1.64it/s]
 74%|███████▎  | 184/250 [02:58<01:26,  1.31s/it]
 74%|███████▍  | 185/250 [02:58<01:06,  1.03s/it]
 74%|███████▍  | 186/250 [03:00<01:18,  1.22s/it]
 75%|███████▍  | 187/250 [03:02<01:34,  1.50s/it]
 75%|███████▌  | 188/250 [03:04<01:36,  1.56s/it]
 76%|███████▌  | 189/250 [03:04<01:09,  1.15s/it]
 76%|███████▌  | 190/250 [03:04<00:52,  1.14it/s]
 76%|███████▋  | 191/250 [03:04<00:38,  1.52it/s]
 77%|███████▋  | 192/250 [03:05<00:34,  1.67it/s]
 78%|███████▊  | 194/250 [03:05<00:22,  2.47it/s]
 78%|███████▊  | 195/250 [03:06<00:24,  2.22it/s]
 79%|███████▉  | 197/250 [03:06<00:20,  2.61it/s]
 79%|███████▉  | 198/250 [03:07<00:29,  1.76it/s]
 80%|███████▉  | 199/250 [03:08<00:32,  1.55it/s]
 80%|████████  | 200/250 [03:09<00:29,  1.72it/s]
 80%|████████  | 201/250 [03:10<00:35,  1.39it/s]
 81%|████████  | 202/250 [03:12<00:56,  1.17s/it]
 81%|████████  | 203/250 [03:13<00:53,  1.15s/it]
 82%|████████▏ | 204/250 [03:15<01:04,  1.40s/it]
 82%|████████▏ | 205/250 [03:16<00:47,  1.06s/it]
 83%|████████▎ | 207/250 [03:17<00:37,  1.13it/s]
 83%|████████▎ | 208/250 [03:17<00:29,  1.41it/s]
 84%|████████▎ | 209/250 [03:18<00:30,  1.33it/s]
 84%|████████▍ | 210/250 [03:18<00:27,  1.43it/s]
 84%|████████▍ | 211/250 [03:20<00:36,  1.07it/s]
 85%|████████▌ | 213/250 [03:22<00:33,  1.10it/s]
 86%|████████▌ | 214/250 [03:23<00:36,  1.02s/it]
 86%|████████▌ | 215/250 [03:24<00:33,  1.05it/s]
 86%|████████▋ | 216/250 [03:24<00:25,  1.35it/s]
 87%|████████▋ | 217/250 [03:24<00:19,  1.69it/s]
 87%|████████▋ | 218/250 [03:25<00:16,  1.98it/s]
 88%|████████▊ | 219/250 [03:25<00:12,  2.54it/s]
 88%|████████▊ | 220/250 [03:25<00:09,  3.10it/s]
 88%|████████▊ | 221/250 [03:25<00:11,  2.51it/s]
 89%|████████▉ | 222/250 [03:26<00:13,  2.08it/s]
 89%|████████▉ | 223/250 [03:26<00:11,  2.35it/s]
 90%|█████████ | 225/250 [03:27<00:08,  2.86it/s]
 90%|█████████ | 226/250 [03:27<00:08,  2.85it/s]
 91%|█████████ | 227/250 [03:28<00:08,  2.75it/s]
 91%|█████████ | 228/250 [03:29<00:15,  1.41it/s]
 92%|█████████▏| 229/250 [03:29<00:11,  1.84it/s]
 92%|█████████▏| 230/250 [03:30<00:13,  1.49it/s]
 92%|█████████▏| 231/250 [03:31<00:09,  1.94it/s]
 93%|█████████▎| 232/250 [03:31<00:10,  1.71it/s]
 93%|█████████▎| 233/250 [03:32<00:08,  1.99it/s]
 94%|█████████▎| 234/250 [03:34<00:18,  1.15s/it]
 94%|█████████▍| 235/250 [03:37<00:22,  1.52s/it]
 94%|█████████▍| 236/250 [03:37<00:16,  1.21s/it]
 96%|█████████▌| 239/250 [03:37<00:06,  1.76it/s]
 96%|█████████▋| 241/250 [03:38<00:03,  2.54it/s]
 97%|█████████▋| 242/250 [03:38<00:03,  2.03it/s]
 97%|█████████▋| 243/250 [03:39<00:03,  1.78it/s]
 98%|█████████▊| 244/250 [03:40<00:03,  1.82it/s]
 98%|█████████▊| 245/250 [03:40<00:02,  1.84it/s]
 98%|█████████▊| 246/250 [03:47<00:09,  2.34s/it]
 99%|█████████▉| 247/250 [03:49<00:06,  2.06s/it]
 99%|█████████▉| 248/250 [03:50<00:03,  1.75s/it]
100%|█████████▉| 249/250 [03:50<00:01,  1.45s/it]
100%|██████████| 250/250 [03:51<00:00,  1.31s/it]
100%|██████████| 250/250 [03:51<00:00,  1.08it/s]
2025-05-14 11:10:20,397 - modnet - INFO - Loss per individual: ind 0: 0.104 	ind 1: 0.129 	ind 2: 0.095 	ind 3: 0.136 	ind 4: 0.146 	ind 5: 0.102 	ind 6: 0.099 	ind 7: 0.100 	ind 8: 0.101 	ind 9: 0.105 	ind 10: 0.099 	ind 11: 0.113 	ind 12: 0.107 	ind 13: 0.101 	ind 14: 0.103 	ind 15: 0.105 	ind 16: 0.097 	ind 17: 0.105 	ind 18: 0.123 	ind 19: 0.106 	ind 20: 0.123 	ind 21: 0.107 	ind 22: 0.097 	ind 23: 0.127 	ind 24: 0.132 	ind 25: 0.101 	ind 26: 0.119 	ind 27: 0.095 	ind 28: 0.101 	ind 29: 0.122 	ind 30: 0.098 	ind 31: 0.100 	ind 32: 0.104 	ind 33: 0.105 	ind 34: 0.102 	ind 35: 0.111 	ind 36: 0.108 	ind 37: 0.099 	ind 38: 0.129 	ind 39: 0.105 	ind 40: 0.095 	ind 41: 0.101 	ind 42: 0.108 	ind 43: 0.113 	ind 44: 0.104 	ind 45: 0.097 	ind 46: 0.105 	ind 47: 0.103 	ind 48: 0.101 	ind 49: 0.127 	
2025-05-14 11:10:20,401 - modnet - INFO - Generation number 8

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:11<48:36, 11.71s/it]
  1%|          | 2/250 [00:12<20:41,  5.01s/it]
  1%|          | 3/250 [00:12<12:23,  3.01s/it]
  2%|▏         | 4/250 [00:14<10:05,  2.46s/it]
  2%|▏         | 5/250 [00:14<06:46,  1.66s/it]
  2%|▏         | 6/250 [00:16<06:32,  1.61s/it]
  3%|▎         | 7/250 [00:18<07:44,  1.91s/it]
  3%|▎         | 8/250 [00:18<05:47,  1.44s/it]
  4%|▎         | 9/250 [00:19<04:21,  1.08s/it]
  4%|▍         | 10/250 [00:21<05:55,  1.48s/it]
  4%|▍         | 11/250 [00:23<06:25,  1.61s/it]
  5%|▍         | 12/250 [00:23<04:53,  1.23s/it]
  6%|▌         | 14/250 [00:24<03:09,  1.24it/s]
  6%|▌         | 15/250 [00:24<02:35,  1.51it/s]
  6%|▋         | 16/250 [00:27<04:41,  1.20s/it]
  7%|▋         | 17/250 [00:30<06:44,  1.73s/it]
  7%|▋         | 18/250 [00:31<05:36,  1.45s/it]
  8%|▊         | 19/250 [00:32<04:42,  1.22s/it]
  8%|▊         | 20/250 [00:32<03:42,  1.03it/s]
  8%|▊         | 21/250 [00:32<02:59,  1.28it/s]
  9%|▉         | 23/250 [00:32<01:44,  2.18it/s]
 10%|▉         | 24/250 [00:32<01:25,  2.66it/s]
 10%|█         | 25/250 [00:33<01:56,  1.92it/s]
 10%|█         | 26/250 [00:34<01:32,  2.42it/s]
 11%|█         | 27/250 [00:34<01:14,  2.98it/s]
 11%|█         | 28/250 [00:35<02:24,  1.54it/s]
 12%|█▏        | 29/250 [00:36<03:06,  1.18it/s]
 12%|█▏        | 30/250 [00:40<06:19,  1.72s/it]
 12%|█▏        | 31/250 [00:40<04:33,  1.25s/it]
 13%|█▎        | 32/250 [00:41<03:31,  1.03it/s]
 14%|█▎        | 34/250 [00:41<02:07,  1.70it/s]
 14%|█▍        | 35/250 [00:42<02:05,  1.71it/s]
 14%|█▍        | 36/250 [00:42<01:46,  2.00it/s]
 15%|█▍        | 37/250 [00:42<01:37,  2.18it/s]
 15%|█▌        | 38/250 [00:43<01:43,  2.06it/s]
 16%|█▌        | 39/250 [00:43<01:26,  2.45it/s]
 16%|█▌        | 40/250 [00:44<02:16,  1.54it/s]
 16%|█▋        | 41/250 [00:44<01:44,  1.99it/s]
 17%|█▋        | 43/250 [00:45<01:50,  1.88it/s]
 18%|█▊        | 44/250 [00:49<04:16,  1.24s/it]
 18%|█▊        | 45/250 [00:50<04:28,  1.31s/it]
 18%|█▊        | 46/250 [00:51<03:42,  1.09s/it]
 19%|█▉        | 47/250 [00:53<04:25,  1.31s/it]
 19%|█▉        | 48/250 [00:54<04:00,  1.19s/it]
 20%|█▉        | 49/250 [00:55<03:40,  1.10s/it]
 20%|██        | 50/250 [00:59<06:46,  2.03s/it]
 20%|██        | 51/250 [00:59<05:08,  1.55s/it]
 21%|██        | 52/250 [01:00<04:02,  1.23s/it]
 21%|██        | 53/250 [01:01<03:46,  1.15s/it]
 22%|██▏       | 54/250 [01:01<02:51,  1.15it/s]
 22%|██▏       | 55/250 [01:01<02:22,  1.37it/s]
 22%|██▏       | 56/250 [01:02<02:48,  1.15it/s]
 23%|██▎       | 57/250 [01:03<02:06,  1.52it/s]
 23%|██▎       | 58/250 [01:03<01:41,  1.90it/s]
 24%|██▎       | 59/250 [01:03<01:26,  2.21it/s]
 24%|██▍       | 60/250 [01:04<01:22,  2.30it/s]
 24%|██▍       | 61/250 [01:04<01:24,  2.22it/s]
 25%|██▍       | 62/250 [01:05<01:29,  2.09it/s]
 25%|██▌       | 63/250 [01:05<01:28,  2.10it/s]
 26%|██▌       | 64/250 [01:06<01:32,  2.02it/s]
 26%|██▌       | 65/250 [01:09<04:03,  1.31s/it]
 26%|██▋       | 66/250 [01:09<03:10,  1.03s/it]
 27%|██▋       | 67/250 [01:10<03:22,  1.11s/it]
 27%|██▋       | 68/250 [01:11<03:04,  1.01s/it]
 28%|██▊       | 69/250 [01:13<03:25,  1.14s/it]
 28%|██▊       | 70/250 [01:15<04:04,  1.36s/it]
 28%|██▊       | 71/250 [01:15<03:40,  1.23s/it]
 29%|██▉       | 72/250 [01:16<03:15,  1.10s/it]
 29%|██▉       | 73/250 [01:17<02:38,  1.12it/s]
 30%|███       | 75/250 [01:17<01:30,  1.94it/s]
 30%|███       | 76/250 [01:17<01:31,  1.90it/s]
 31%|███       | 77/250 [01:18<01:57,  1.47it/s]
 31%|███       | 78/250 [01:19<01:34,  1.81it/s]
 32%|███▏      | 80/250 [01:19<01:05,  2.60it/s]
 32%|███▏      | 81/250 [01:19<00:58,  2.90it/s]
 33%|███▎      | 82/250 [01:20<01:02,  2.69it/s]
 33%|███▎      | 83/250 [01:20<00:58,  2.86it/s]
 34%|███▎      | 84/250 [01:20<01:01,  2.70it/s]
 34%|███▍      | 85/250 [01:21<01:24,  1.96it/s]
 35%|███▍      | 87/250 [01:23<01:34,  1.72it/s]
 35%|███▌      | 88/250 [01:23<01:27,  1.86it/s]
 36%|███▌      | 89/250 [01:23<01:11,  2.27it/s]
 36%|███▌      | 90/250 [01:24<01:18,  2.04it/s]
 36%|███▋      | 91/250 [01:24<01:19,  2.00it/s]
 37%|███▋      | 93/250 [01:25<01:21,  1.94it/s]
 38%|███▊      | 94/250 [01:26<01:13,  2.11it/s]
 38%|███▊      | 95/250 [01:26<01:02,  2.48it/s]
 38%|███▊      | 96/250 [01:26<00:55,  2.76it/s]
 39%|███▉      | 97/250 [01:27<00:57,  2.64it/s]
 39%|███▉      | 98/250 [01:27<01:06,  2.28it/s]
 40%|███▉      | 99/250 [01:28<01:05,  2.31it/s]
 40%|████      | 101/250 [01:32<02:57,  1.19s/it]
 41%|████      | 102/250 [01:33<02:47,  1.13s/it]
 41%|████      | 103/250 [01:33<02:18,  1.06it/s]
 42%|████▏     | 104/250 [01:42<07:10,  2.95s/it]
 42%|████▏     | 105/250 [01:43<05:50,  2.42s/it]
 42%|████▏     | 106/250 [01:45<05:43,  2.39s/it]
 43%|████▎     | 108/250 [01:46<03:51,  1.63s/it]
 44%|████▍     | 110/250 [01:47<02:46,  1.19s/it]
 44%|████▍     | 111/250 [01:47<02:13,  1.04it/s]
 45%|████▌     | 113/250 [01:49<02:00,  1.14it/s]
 46%|████▌     | 114/250 [01:49<01:38,  1.39it/s]
 46%|████▌     | 115/250 [01:52<02:36,  1.16s/it]
 46%|████▋     | 116/250 [01:52<02:11,  1.02it/s]
 47%|████▋     | 117/250 [01:54<02:45,  1.24s/it]
 47%|████▋     | 118/250 [01:55<02:14,  1.02s/it]
 48%|████▊     | 119/250 [01:55<01:54,  1.14it/s]
 48%|████▊     | 121/250 [01:56<01:29,  1.45it/s]
 49%|████▉     | 122/250 [01:56<01:17,  1.65it/s]
 49%|████▉     | 123/250 [01:57<01:24,  1.50it/s]
 50%|████▉     | 124/250 [01:59<01:50,  1.14it/s]
 50%|█████     | 125/250 [01:59<01:23,  1.49it/s]
 50%|█████     | 126/250 [02:01<02:31,  1.23s/it]
 51%|█████     | 127/250 [02:02<02:20,  1.14s/it]
 51%|█████     | 128/250 [02:02<01:44,  1.17it/s]
 52%|█████▏    | 129/250 [02:03<01:50,  1.10it/s]
 52%|█████▏    | 130/250 [02:04<01:26,  1.39it/s]
 53%|█████▎    | 132/250 [02:06<01:46,  1.11it/s]
 53%|█████▎    | 133/250 [02:07<01:49,  1.06it/s]
 54%|█████▎    | 134/250 [02:07<01:24,  1.37it/s]
 54%|█████▍    | 135/250 [02:09<02:09,  1.12s/it]
 55%|█████▍    | 137/250 [02:10<01:30,  1.25it/s]
 55%|█████▌    | 138/250 [02:10<01:12,  1.54it/s]
 56%|█████▌    | 139/250 [02:11<01:12,  1.52it/s]
 56%|█████▌    | 140/250 [02:12<01:19,  1.38it/s]
 56%|█████▋    | 141/250 [02:13<01:28,  1.24it/s]
 57%|█████▋    | 142/250 [02:14<01:47,  1.00it/s]
 57%|█████▋    | 143/250 [02:15<01:20,  1.33it/s]
 58%|█████▊    | 144/250 [02:15<01:00,  1.76it/s]
 58%|█████▊    | 145/250 [02:15<01:03,  1.67it/s]
 58%|█████▊    | 146/250 [02:16<01:03,  1.63it/s]
 59%|█████▉    | 147/250 [02:16<00:50,  2.04it/s]
 59%|█████▉    | 148/250 [02:20<02:21,  1.38s/it]
 60%|█████▉    | 149/250 [02:24<03:45,  2.24s/it]
 60%|██████    | 150/250 [02:27<03:59,  2.39s/it]
 60%|██████    | 151/250 [02:29<03:44,  2.26s/it]
 61%|██████    | 152/250 [02:29<02:42,  1.66s/it]
 61%|██████    | 153/250 [02:31<02:53,  1.78s/it]
 62%|██████▏   | 154/250 [02:33<03:08,  1.96s/it]
 62%|██████▏   | 155/250 [02:35<02:48,  1.78s/it]
 62%|██████▏   | 156/250 [02:35<02:13,  1.42s/it]
 63%|██████▎   | 157/250 [02:39<03:10,  2.05s/it]
 63%|██████▎   | 158/250 [02:40<02:39,  1.73s/it]
 64%|██████▍   | 160/250 [02:41<01:59,  1.32s/it]
 64%|██████▍   | 161/250 [02:44<02:22,  1.60s/it]
 65%|██████▍   | 162/250 [02:44<01:52,  1.28s/it]
 65%|██████▌   | 163/250 [02:47<02:22,  1.64s/it]
 66%|██████▌   | 164/250 [02:48<02:16,  1.59s/it]
 66%|██████▋   | 166/250 [02:49<01:27,  1.05s/it]
 67%|██████▋   | 167/250 [02:50<01:23,  1.00s/it]
 67%|██████▋   | 168/250 [02:51<01:13,  1.12it/s]
 68%|██████▊   | 169/250 [02:53<01:43,  1.28s/it]
 68%|██████▊   | 170/250 [02:53<01:21,  1.02s/it]
 68%|██████▊   | 171/250 [02:54<01:17,  1.03it/s]
 69%|██████▉   | 172/250 [02:55<01:10,  1.11it/s]
 70%|██████▉   | 174/250 [02:55<00:45,  1.68it/s]
 70%|███████   | 176/250 [02:55<00:28,  2.56it/s]
 71%|███████   | 177/250 [02:56<00:29,  2.48it/s]
 71%|███████   | 178/250 [02:57<00:34,  2.08it/s]
 72%|███████▏  | 179/250 [02:58<00:54,  1.31it/s]
 72%|███████▏  | 180/250 [03:00<01:15,  1.08s/it]
 72%|███████▏  | 181/250 [03:01<01:12,  1.05s/it]
 73%|███████▎  | 182/250 [03:02<01:03,  1.07it/s]
 73%|███████▎  | 183/250 [03:02<00:46,  1.43it/s]
 74%|███████▎  | 184/250 [03:04<01:22,  1.25s/it]
 74%|███████▍  | 185/250 [03:10<02:49,  2.60s/it]
 74%|███████▍  | 186/250 [03:11<02:08,  2.01s/it]
 75%|███████▍  | 187/250 [03:14<02:19,  2.21s/it]
 75%|███████▌  | 188/250 [03:15<02:09,  2.08s/it]
 76%|███████▌  | 190/250 [03:17<01:24,  1.41s/it]
 76%|███████▋  | 191/250 [03:17<01:09,  1.18s/it]
 77%|███████▋  | 192/250 [03:17<00:53,  1.09it/s]
 77%|███████▋  | 193/250 [03:18<00:43,  1.32it/s]
 78%|███████▊  | 194/250 [03:18<00:38,  1.46it/s]
 78%|███████▊  | 195/250 [03:19<00:44,  1.24it/s]
 78%|███████▊  | 196/250 [03:19<00:35,  1.54it/s]
 79%|███████▉  | 197/250 [03:20<00:35,  1.49it/s]
 79%|███████▉  | 198/250 [03:20<00:29,  1.79it/s]
 80%|███████▉  | 199/250 [03:21<00:28,  1.80it/s]
 80%|████████  | 201/250 [03:21<00:16,  2.95it/s]
 81%|████████  | 202/250 [03:21<00:14,  3.30it/s]
 82%|████████▏ | 204/250 [03:22<00:14,  3.27it/s]
 82%|████████▏ | 205/250 [03:22<00:14,  3.05it/s]
 82%|████████▏ | 206/250 [03:23<00:14,  3.00it/s]
 83%|████████▎ | 207/250 [03:24<00:27,  1.57it/s]
 83%|████████▎ | 208/250 [03:26<00:33,  1.24it/s]
 84%|████████▎ | 209/250 [03:26<00:31,  1.31it/s]
 84%|████████▍ | 210/250 [03:27<00:27,  1.44it/s]
 84%|████████▍ | 211/250 [03:28<00:31,  1.22it/s]
 85%|████████▍ | 212/250 [03:29<00:35,  1.08it/s]
 85%|████████▌ | 213/250 [03:30<00:33,  1.09it/s]
 86%|████████▌ | 214/250 [03:32<00:50,  1.41s/it]
 86%|████████▌ | 215/250 [03:33<00:38,  1.09s/it]
 86%|████████▋ | 216/250 [03:33<00:30,  1.13it/s]
 87%|████████▋ | 217/250 [03:34<00:29,  1.13it/s]
 87%|████████▋ | 218/250 [03:34<00:21,  1.52it/s]
 88%|████████▊ | 219/250 [03:35<00:17,  1.79it/s]
 88%|████████▊ | 220/250 [03:36<00:25,  1.17it/s]
 88%|████████▊ | 221/250 [03:36<00:19,  1.47it/s]
 89%|████████▉ | 222/250 [03:38<00:29,  1.05s/it]
 89%|████████▉ | 223/250 [03:38<00:20,  1.31it/s]
 90%|████████▉ | 224/250 [03:39<00:19,  1.32it/s]
 90%|█████████ | 225/250 [03:40<00:21,  1.18it/s]
 90%|█████████ | 226/250 [03:40<00:15,  1.51it/s]
 91%|█████████ | 227/250 [03:41<00:12,  1.91it/s]
 91%|█████████ | 228/250 [03:41<00:11,  1.95it/s]
 92%|█████████▏| 229/250 [03:42<00:14,  1.43it/s]
 92%|█████████▏| 230/250 [03:43<00:12,  1.67it/s]
 92%|█████████▏| 231/250 [03:43<00:12,  1.58it/s]
 93%|█████████▎| 233/250 [03:43<00:06,  2.66it/s]
 94%|█████████▎| 234/250 [03:44<00:08,  1.91it/s]
 94%|█████████▍| 235/250 [03:46<00:12,  1.22it/s]
 94%|█████████▍| 236/250 [03:47<00:10,  1.31it/s]
 95%|█████████▍| 237/250 [03:47<00:07,  1.65it/s]
 95%|█████████▌| 238/250 [03:47<00:05,  2.08it/s]
 96%|█████████▌| 239/250 [03:47<00:04,  2.38it/s]
 96%|█████████▌| 240/250 [03:48<00:04,  2.47it/s]
 96%|█████████▋| 241/250 [03:48<00:02,  3.02it/s]
 97%|█████████▋| 242/250 [03:49<00:03,  2.10it/s]
 97%|█████████▋| 243/250 [03:50<00:05,  1.39it/s]
 98%|█████████▊| 244/250 [03:51<00:05,  1.14it/s]
 98%|█████████▊| 245/250 [03:52<00:04,  1.07it/s]
 98%|█████████▊| 246/250 [03:52<00:02,  1.44it/s]
 99%|█████████▉| 247/250 [03:53<00:01,  1.78it/s]
 99%|█████████▉| 248/250 [03:55<00:02,  1.01s/it]
100%|█████████▉| 249/250 [03:56<00:01,  1.17s/it]
100%|██████████| 250/250 [03:56<00:00,  1.06it/s]
2025-05-14 11:14:17,303 - modnet - INFO - Loss per individual: ind 0: 0.098 	ind 1: 0.101 	ind 2: 0.104 	ind 3: 0.107 	ind 4: 0.104 	ind 5: 0.109 	ind 6: 0.112 	ind 7: 0.107 	ind 8: 0.115 	ind 9: 0.107 	ind 10: 0.133 	ind 11: 0.097 	ind 12: 0.102 	ind 13: 0.137 	ind 14: 0.099 	ind 15: 0.168 	ind 16: 0.141 	ind 17: 0.134 	ind 18: 0.104 	ind 19: 0.102 	ind 20: 0.094 	ind 21: 0.110 	ind 22: 0.118 	ind 23: 0.099 	ind 24: 0.115 	ind 25: 0.117 	ind 26: 0.137 	ind 27: 0.105 	ind 28: 0.107 	ind 29: 0.117 	ind 30: 0.100 	ind 31: 0.103 	ind 32: 0.099 	ind 33: 0.099 	ind 34: 0.100 	ind 35: 0.109 	ind 36: 0.094 	ind 37: 0.111 	ind 38: 0.098 	ind 39: 0.099 	ind 40: 0.103 	ind 41: 0.130 	ind 42: 0.103 	ind 43: 0.103 	ind 44: 0.112 	ind 45: 0.106 	ind 46: 0.122 	ind 47: 0.125 	ind 48: 0.113 	ind 49: 0.106 	
2025-05-14 11:14:17,306 - modnet - INFO - Generation number 9

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:12<51:14, 12.35s/it]
  1%|          | 2/250 [00:15<29:40,  7.18s/it]
  1%|          | 3/250 [00:16<16:16,  3.95s/it]
  2%|▏         | 4/250 [00:16<11:02,  2.69s/it]
  2%|▏         | 5/250 [00:17<08:22,  2.05s/it]
  3%|▎         | 7/250 [00:18<05:17,  1.31s/it]
  3%|▎         | 8/250 [00:19<04:53,  1.21s/it]
  4%|▎         | 9/250 [00:20<03:52,  1.04it/s]
  4%|▍         | 10/250 [00:20<03:02,  1.31it/s]
  4%|▍         | 11/250 [00:20<02:43,  1.46it/s]
  5%|▍         | 12/250 [00:21<02:13,  1.78it/s]
  5%|▌         | 13/250 [00:21<02:00,  1.96it/s]
  6%|▌         | 14/250 [00:22<02:02,  1.92it/s]
  6%|▋         | 16/250 [00:23<02:21,  1.65it/s]
  8%|▊         | 19/250 [00:24<01:37,  2.38it/s]
  8%|▊         | 20/250 [00:25<02:14,  1.71it/s]
  8%|▊         | 21/250 [00:25<02:00,  1.90it/s]
  9%|▉         | 22/250 [00:25<01:39,  2.29it/s]
  9%|▉         | 23/250 [00:26<02:06,  1.80it/s]
 10%|▉         | 24/250 [00:27<01:50,  2.04it/s]
 10%|█         | 25/250 [00:30<05:12,  1.39s/it]
 10%|█         | 26/250 [00:31<04:11,  1.12s/it]
 11%|█         | 27/250 [00:32<04:37,  1.24s/it]
 11%|█         | 28/250 [00:34<04:40,  1.26s/it]
 12%|█▏        | 29/250 [00:34<04:00,  1.09s/it]
 12%|█▏        | 31/250 [00:37<04:42,  1.29s/it]
 13%|█▎        | 32/250 [00:38<03:59,  1.10s/it]
 13%|█▎        | 33/250 [00:41<05:32,  1.53s/it]
 14%|█▎        | 34/250 [00:43<05:58,  1.66s/it]
 14%|█▍        | 35/250 [00:43<04:38,  1.30s/it]
 14%|█▍        | 36/250 [00:44<04:07,  1.15s/it]
 15%|█▌        | 38/250 [00:44<02:22,  1.49it/s]
 16%|█▌        | 39/250 [00:44<02:05,  1.69it/s]
 16%|█▌        | 40/250 [00:45<01:40,  2.08it/s]
 16%|█▋        | 41/250 [00:45<01:21,  2.58it/s]
 17%|█▋        | 42/250 [00:45<01:14,  2.81it/s]
 17%|█▋        | 43/250 [00:46<01:42,  2.02it/s]
 18%|█▊        | 44/250 [00:46<01:30,  2.29it/s]
 18%|█▊        | 45/250 [00:49<03:33,  1.04s/it]
 18%|█▊        | 46/250 [00:50<03:49,  1.13s/it]
 19%|█▉        | 47/250 [00:54<06:16,  1.86s/it]
 19%|█▉        | 48/250 [00:56<07:14,  2.15s/it]
 20%|█▉        | 49/250 [00:57<05:40,  1.69s/it]
 20%|██        | 50/250 [00:58<04:50,  1.45s/it]
 20%|██        | 51/250 [00:59<04:38,  1.40s/it]
 21%|██        | 52/250 [00:59<03:22,  1.02s/it]
 21%|██        | 53/250 [01:00<02:47,  1.17it/s]
 22%|██▏       | 54/250 [01:00<02:17,  1.43it/s]
 22%|██▏       | 55/250 [01:01<02:10,  1.50it/s]
 22%|██▏       | 56/250 [01:01<02:04,  1.56it/s]
 23%|██▎       | 57/250 [01:02<02:00,  1.61it/s]
 23%|██▎       | 58/250 [01:03<02:04,  1.54it/s]
 24%|██▎       | 59/250 [01:03<01:32,  2.06it/s]
 24%|██▍       | 61/250 [01:03<01:04,  2.94it/s]
 25%|██▍       | 62/250 [01:04<01:28,  2.14it/s]
 26%|██▌       | 64/250 [01:04<00:55,  3.38it/s]
 26%|██▌       | 65/250 [01:05<01:08,  2.69it/s]
 26%|██▋       | 66/250 [01:05<00:56,  3.27it/s]
 27%|██▋       | 67/250 [01:07<02:16,  1.34it/s]
 27%|██▋       | 68/250 [01:07<01:59,  1.52it/s]
 28%|██▊       | 69/250 [01:09<02:41,  1.12it/s]
 28%|██▊       | 71/250 [01:09<01:46,  1.68it/s]
 29%|██▉       | 72/250 [01:09<01:28,  2.02it/s]
 29%|██▉       | 73/250 [01:10<01:23,  2.12it/s]
 30%|██▉       | 74/250 [01:12<02:58,  1.01s/it]
 30%|███       | 75/250 [01:17<05:52,  2.01s/it]
 30%|███       | 76/250 [01:18<05:17,  1.82s/it]
 31%|███       | 77/250 [01:18<03:59,  1.38s/it]
 31%|███       | 78/250 [01:19<03:03,  1.07s/it]
 32%|███▏      | 79/250 [01:19<02:36,  1.09it/s]
 32%|███▏      | 80/250 [01:20<02:37,  1.08it/s]
 32%|███▏      | 81/250 [01:21<02:12,  1.27it/s]
 33%|███▎      | 82/250 [01:21<01:41,  1.66it/s]
 33%|███▎      | 83/250 [01:21<01:25,  1.96it/s]
 34%|███▎      | 84/250 [01:22<01:21,  2.03it/s]
 34%|███▍      | 85/250 [01:22<01:38,  1.67it/s]
 34%|███▍      | 86/250 [01:23<01:33,  1.75it/s]
 35%|███▍      | 87/250 [01:23<01:10,  2.31it/s]
 35%|███▌      | 88/250 [01:24<01:55,  1.40it/s]
 36%|███▌      | 89/250 [01:25<01:34,  1.71it/s]
 36%|███▌      | 90/250 [01:25<01:13,  2.19it/s]
 36%|███▋      | 91/250 [01:25<00:59,  2.66it/s]
 37%|███▋      | 92/250 [01:25<00:49,  3.20it/s]
 37%|███▋      | 93/250 [01:25<00:46,  3.37it/s]
 38%|███▊      | 94/250 [01:26<00:56,  2.76it/s]
 38%|███▊      | 95/250 [01:27<01:41,  1.52it/s]
 38%|███▊      | 96/250 [01:28<01:47,  1.43it/s]
 39%|███▉      | 97/250 [01:29<01:54,  1.33it/s]
 39%|███▉      | 98/250 [01:33<04:30,  1.78s/it]
 40%|████      | 100/250 [01:34<02:59,  1.20s/it]
 40%|████      | 101/250 [01:34<02:26,  1.02it/s]
 41%|████      | 102/250 [01:35<02:11,  1.12it/s]
 41%|████      | 103/250 [01:36<02:00,  1.22it/s]
 42%|████▏     | 104/250 [01:37<01:56,  1.25it/s]
 42%|████▏     | 105/250 [01:37<01:34,  1.54it/s]
 42%|████▏     | 106/250 [01:38<01:55,  1.25it/s]
 43%|████▎     | 108/250 [01:38<01:05,  2.15it/s]
 44%|████▎     | 109/250 [01:38<00:53,  2.64it/s]
 44%|████▍     | 110/250 [01:39<00:54,  2.59it/s]
 44%|████▍     | 111/250 [01:39<00:54,  2.57it/s]
 45%|████▍     | 112/250 [01:40<01:22,  1.68it/s]
 45%|████▌     | 113/250 [01:40<01:03,  2.17it/s]
 46%|████▌     | 114/250 [01:40<00:52,  2.59it/s]
 46%|████▌     | 115/250 [01:41<00:44,  3.06it/s]
 46%|████▋     | 116/250 [01:41<00:37,  3.60it/s]
 47%|████▋     | 117/250 [01:41<00:44,  2.99it/s]
 47%|████▋     | 118/250 [01:42<01:13,  1.80it/s]
 48%|████▊     | 120/250 [01:43<00:57,  2.27it/s]
 48%|████▊     | 121/250 [01:44<01:07,  1.90it/s]
 49%|████▉     | 122/250 [01:44<01:13,  1.73it/s]
 49%|████▉     | 123/250 [01:46<01:36,  1.32it/s]
 50%|████▉     | 124/250 [01:48<02:24,  1.15s/it]
 50%|█████     | 125/250 [01:54<05:13,  2.51s/it]
 50%|█████     | 126/250 [02:01<07:53,  3.82s/it]
 51%|█████     | 127/250 [02:02<06:03,  2.96s/it]
 51%|█████     | 128/250 [02:03<04:55,  2.42s/it]
 52%|█████▏    | 129/250 [02:03<03:37,  1.80s/it]
 52%|█████▏    | 130/250 [02:04<02:51,  1.43s/it]
 52%|█████▏    | 131/250 [02:06<03:31,  1.77s/it]
 53%|█████▎    | 132/250 [02:07<03:06,  1.58s/it]
 53%|█████▎    | 133/250 [02:08<02:24,  1.23s/it]
 54%|█████▎    | 134/250 [02:09<02:19,  1.20s/it]
 54%|█████▍    | 136/250 [02:09<01:22,  1.38it/s]
 55%|█████▍    | 137/250 [02:10<01:15,  1.50it/s]
 55%|█████▌    | 138/250 [02:12<01:52,  1.01s/it]
 56%|█████▌    | 139/250 [02:12<01:28,  1.26it/s]
 56%|█████▌    | 140/250 [02:12<01:07,  1.63it/s]
 56%|█████▋    | 141/250 [02:12<00:53,  2.05it/s]
 57%|█████▋    | 142/250 [02:14<01:24,  1.28it/s]
 57%|█████▋    | 143/250 [02:15<01:25,  1.26it/s]
 58%|█████▊    | 145/250 [02:15<00:51,  2.05it/s]
 59%|█████▉    | 147/250 [02:15<00:37,  2.78it/s]
 59%|█████▉    | 148/250 [02:16<00:37,  2.70it/s]
 60%|█████▉    | 149/250 [02:16<00:42,  2.40it/s]
 60%|██████    | 150/250 [02:16<00:37,  2.68it/s]
 60%|██████    | 151/250 [02:20<02:02,  1.24s/it]
 61%|██████    | 152/250 [02:22<02:15,  1.39s/it]
 61%|██████    | 153/250 [02:22<01:53,  1.17s/it]
 62%|██████▏   | 154/250 [02:25<02:28,  1.54s/it]
 62%|██████▏   | 155/250 [02:26<02:12,  1.40s/it]
 63%|██████▎   | 157/250 [02:28<02:01,  1.30s/it]
 64%|██████▍   | 160/250 [02:28<01:02,  1.45it/s]
 65%|██████▌   | 163/250 [02:29<00:37,  2.32it/s]
 66%|██████▋   | 166/250 [02:29<00:28,  2.93it/s]
 67%|██████▋   | 167/250 [02:30<00:28,  2.90it/s]
 67%|██████▋   | 168/250 [02:30<00:27,  3.02it/s]
 68%|██████▊   | 169/250 [02:31<00:32,  2.51it/s]
 68%|██████▊   | 170/250 [02:31<00:27,  2.91it/s]
 69%|██████▉   | 172/250 [02:31<00:18,  4.22it/s]
 69%|██████▉   | 173/250 [02:31<00:22,  3.47it/s]
 70%|███████   | 175/250 [02:33<00:35,  2.12it/s]
 70%|███████   | 176/250 [02:33<00:30,  2.41it/s]
 71%|███████   | 177/250 [02:34<00:38,  1.91it/s]
 71%|███████   | 178/250 [02:37<01:15,  1.05s/it]
 72%|███████▏  | 179/250 [02:37<01:07,  1.05it/s]
 72%|███████▏  | 180/250 [02:38<00:58,  1.19it/s]
 72%|███████▏  | 181/250 [02:38<00:51,  1.34it/s]
 73%|███████▎  | 183/250 [02:42<01:18,  1.17s/it]
 74%|███████▎  | 184/250 [02:43<01:15,  1.14s/it]
 74%|███████▍  | 185/250 [02:44<01:24,  1.29s/it]
 74%|███████▍  | 186/250 [02:45<01:12,  1.13s/it]
 75%|███████▍  | 187/250 [02:47<01:31,  1.45s/it]
 75%|███████▌  | 188/250 [02:48<01:11,  1.15s/it]
 76%|███████▌  | 189/250 [02:49<01:12,  1.19s/it]
 76%|███████▌  | 190/250 [02:49<00:57,  1.05it/s]
 76%|███████▋  | 191/250 [02:50<00:53,  1.11it/s]
 77%|███████▋  | 192/250 [02:51<00:52,  1.11it/s]
 77%|███████▋  | 193/250 [02:52<00:43,  1.30it/s]
 78%|███████▊  | 194/250 [02:52<00:38,  1.45it/s]
 78%|███████▊  | 195/250 [02:52<00:32,  1.71it/s]
 78%|███████▊  | 196/250 [02:53<00:29,  1.84it/s]
 79%|███████▉  | 197/250 [02:53<00:25,  2.06it/s]
 79%|███████▉  | 198/250 [02:54<00:32,  1.58it/s]
 80%|███████▉  | 199/250 [02:56<00:50,  1.01it/s]
 80%|████████  | 200/250 [03:00<01:38,  1.96s/it]
 80%|████████  | 201/250 [03:01<01:24,  1.72s/it]
 81%|████████  | 202/250 [03:02<01:10,  1.46s/it]
 81%|████████  | 203/250 [03:03<00:51,  1.10s/it]
 82%|████████▏ | 205/250 [03:04<00:38,  1.17it/s]
 82%|████████▏ | 206/250 [03:06<00:57,  1.31s/it]
 83%|████████▎ | 207/250 [03:07<00:53,  1.24s/it]
 83%|████████▎ | 208/250 [03:08<00:40,  1.04it/s]
 84%|████████▍ | 210/250 [03:08<00:22,  1.77it/s]
 84%|████████▍ | 211/250 [03:09<00:31,  1.23it/s]
 85%|████████▍ | 212/250 [03:10<00:30,  1.26it/s]
 85%|████████▌ | 213/250 [03:11<00:32,  1.15it/s]
 86%|████████▌ | 214/250 [03:11<00:25,  1.44it/s]
 86%|████████▌ | 215/250 [03:12<00:22,  1.57it/s]
 86%|████████▋ | 216/250 [03:12<00:17,  1.92it/s]
 87%|████████▋ | 218/250 [03:12<00:10,  3.14it/s]
 88%|████████▊ | 219/250 [03:13<00:12,  2.52it/s]
 88%|████████▊ | 220/250 [03:14<00:13,  2.16it/s]
 88%|████████▊ | 221/250 [03:15<00:22,  1.31it/s]
 89%|████████▉ | 222/250 [03:16<00:21,  1.33it/s]
 89%|████████▉ | 223/250 [03:17<00:25,  1.06it/s]
 90%|████████▉ | 224/250 [03:18<00:22,  1.13it/s]
 90%|█████████ | 225/250 [03:19<00:21,  1.14it/s]
 90%|█████████ | 226/250 [03:20<00:21,  1.12it/s]
 91%|█████████ | 227/250 [03:20<00:16,  1.39it/s]
 91%|█████████ | 228/250 [03:21<00:14,  1.55it/s]
 92%|█████████▏| 229/250 [03:21<00:10,  1.98it/s]
 92%|█████████▏| 230/250 [03:21<00:10,  1.96it/s]
 92%|█████████▏| 231/250 [03:22<00:09,  1.99it/s]
 93%|█████████▎| 233/250 [03:22<00:05,  2.84it/s]
 94%|█████████▍| 235/250 [03:22<00:03,  4.27it/s]
 94%|█████████▍| 236/250 [03:22<00:03,  4.53it/s]
 95%|█████████▍| 237/250 [03:24<00:06,  2.04it/s]
 96%|█████████▌| 240/250 [03:24<00:02,  3.41it/s]
 96%|█████████▋| 241/250 [03:25<00:02,  3.11it/s]
 97%|█████████▋| 242/250 [03:25<00:03,  2.34it/s]
 97%|█████████▋| 243/250 [03:26<00:02,  2.38it/s]
 98%|█████████▊| 244/250 [03:26<00:02,  2.26it/s]
 98%|█████████▊| 245/250 [03:27<00:02,  2.41it/s]
 98%|█████████▊| 246/250 [03:28<00:03,  1.24it/s]
 99%|█████████▉| 248/250 [03:29<00:01,  1.78it/s]
100%|█████████▉| 249/250 [03:29<00:00,  1.80it/s]
100%|██████████| 250/250 [03:32<00:00,  1.00it/s]
100%|██████████| 250/250 [03:32<00:00,  1.18it/s]
2025-05-14 11:17:49,607 - modnet - INFO - Loss per individual: ind 0: 0.129 	ind 1: 0.104 	ind 2: 0.105 	ind 3: 0.135 	ind 4: 0.101 	ind 5: 0.126 	ind 6: 0.130 	ind 7: 0.101 	ind 8: 0.144 	ind 9: 0.102 	ind 10: 0.100 	ind 11: 0.094 	ind 12: 0.128 	ind 13: 0.096 	ind 14: 0.109 	ind 15: 0.101 	ind 16: 0.104 	ind 17: 0.098 	ind 18: 0.133 	ind 19: 0.113 	ind 20: 0.104 	ind 21: 0.103 	ind 22: 0.148 	ind 23: 0.104 	ind 24: 0.112 	ind 25: 0.101 	ind 26: 0.095 	ind 27: 0.095 	ind 28: 0.130 	ind 29: 0.107 	ind 30: 0.104 	ind 31: 0.098 	ind 32: 0.106 	ind 33: 0.095 	ind 34: 0.099 	ind 35: 0.099 	ind 36: 0.100 	ind 37: 0.100 	ind 38: 0.101 	ind 39: 0.125 	ind 40: 0.111 	ind 41: 0.108 	ind 42: 0.114 	ind 43: 0.119 	ind 44: 0.132 	ind 45: 0.124 	ind 46: 0.120 	ind 47: 0.110 	ind 48: 0.105 	ind 49: 0.103 	
2025-05-14 11:17:49,610 - modnet - INFO - Early stopping: same best model for 8 consecutive generations
2025-05-14 11:17:49,610 - modnet - INFO - Early stopping at generation number 9
2025-05-14 11:17:55,977 - modnet - INFO - Model successfully saved as results/matbench_expt_gap_best_model_fold_1.pkl!
Saved best model for fold 1 to results/matbench_expt_gap_best_model_fold_1.pkl
2025-05-14 11:17:56.024768: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 11:17:56.037527: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445365000 Hz
Traceback (most recent call last):
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 323, in train_fold
    pred_results = model.predict(test_data, **predict_kwargs)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/models/ensemble.py", line 157, in predict
    p = self.model[i].predict(test_data, return_prob=return_prob)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/models/vanilla.py", line 624, in predict
    yrange = self.max_y - self.min_y
TypeError: numpy boolean subtract, the `-` operator, is not supported, use the bitwise_xor, the `^` operator, or the logical_xor function instead.
Something went wrong benchmarking this model.
Traceback (most recent call last):
  File "run_benchmark.py", line 861, in <module>
    results = benchmark(data, settings, n_jobs=n_jobs, fast=fast)
  File "run_benchmark.py", line 140, in benchmark
    return matbench_benchmark(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 137, in matbench_benchmark
    fold_results.append(train_fold(fold, *args, **model_kwargs))
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 365, in train_fold
    if stds is not None:
UnboundLocalError: local variable 'stds' referenced before assignment
Job finished on Wed May 14 11:17:58 CEST 2025

Resources Used

Total Memory used                        - MEM              : 14GiB
Total CPU Time                           - CPU_Time         : 1-01:29:36
Execution Time                           - Wall_Time        : 00:47:48
total programme cpu time                 - Total_CPU        : 17:31:57
Total_CPU / CPU_Time  (%)                - ETA              : 68%
Number of alloc CPU                      - NCPUS            : 32
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 48
Mobilized Resources x Execution Time     - R_Wall_Time      : 1-14:14:24
CPU_Time / R_Wall_Time (%)               - ALPHA            : 66%
Energy (Joules)                                             : 574455

