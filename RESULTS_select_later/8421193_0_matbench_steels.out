Job started on Sun May 11 02:15:27 CEST 2025
Running on node(s): cnm021
2025-05-11 02:15:36.800165: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 32 jobs
Preparing nested CV run for task 'matbench_steels'
2025-05-11 02:15:46,095 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f98d9b039d0> object, created with modnet version 0.1.13
Preparing fold 1 ...
2025-05-11 02:15:46,118 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f98d92446a0> object, created with modnet version 0.1.13
2025-05-11 02:15:46,128 - modnet - INFO - Data successfully saved as folds/train_moddata_f1!
Preparing fold 2 ...
2025-05-11 02:15:46,144 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f98d9043940> object, created with modnet version 0.1.13
2025-05-11 02:15:46,153 - modnet - INFO - Data successfully saved as folds/train_moddata_f2!
Preparing fold 3 ...
2025-05-11 02:15:46,170 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f98d91c2fa0> object, created with modnet version 0.1.13
2025-05-11 02:15:46,179 - modnet - INFO - Data successfully saved as folds/train_moddata_f3!
Preparing fold 4 ...
2025-05-11 02:15:46,195 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f98d9043880> object, created with modnet version 0.1.13
2025-05-11 02:15:46,206 - modnet - INFO - Data successfully saved as folds/train_moddata_f4!
Preparing fold 5 ...
2025-05-11 02:15:46,223 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f98d9003b50> object, created with modnet version 0.1.13
2025-05-11 02:15:46,232 - modnet - INFO - Data successfully saved as folds/train_moddata_f5!
Training fold 1 ...
Processing fold 1 ...
2025-05-11 02:15:46.256565: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-11 02:15:46.257023: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/8.4.1.50-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-11 02:15:46.257049: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-11 02:15:46.257079: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm021.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-11 02:15:46.257308: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-11 02:15:46,351 - modnet - INFO - Proceeding with grid search: archs: [(64, [[128], [32], [8], [8]]), (64, [[64], [32], [8], [8]]), (64, [[32], [16], [8], [8]]), (91, [[182], [45], [11], [11]]), (91, [[91], [45], [11], [11]]), (91, [[45], [22], [11], [11]]), (118, [[236], [59], [14], [14]]), (118, [[118], [59], [14], [14]]), (118, [[59], [29], [14], [14]])], batch sizes: [32, 64], learning_rates: [0.001, 0.005, 0.01]
Using 2 presets
2025-05-11 02:15:46,779 - modnet - INFO - Multiprocessing on 32 cores. Total of 128 cores available.

  0%|          | 0/10 [00:00<?, ?it/s]2025-05-11 02:15:50,571 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:15:50,579 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:15:50,586 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:15:50,593 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:15:50,607 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:15:50,614 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:15:50,646 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:15:50,653 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:15:50,678 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:15:50,685 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:15:50,689 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:15:50,697 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:15:50,702 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:15:50,710 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:15:50,740 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:15:50,748 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:15:50,756 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:15:50,763 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:15:50,782 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:15:50,790 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:16:10,735 - modnet - INFO - loss: 75.9590	mae: 75.9590	val_loss: 169.0456	val_mae: 169.0456	
2025-05-11 02:16:10,735 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:16:11,107 - modnet - INFO - loss: 74.7669	mae: 74.7669	val_loss: 106.8153	val_mae: 106.8153	
2025-05-11 02:16:11,107 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:16:11,182 - modnet - INFO - loss: 49.0608	mae: 49.0608	val_loss: 147.8933	val_mae: 147.8933	
2025-05-11 02:16:11,183 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:16:11,221 - modnet - INFO - loss: 70.7149	mae: 70.7149	val_loss: 98.7803	val_mae: 98.7803	
2025-05-11 02:16:11,221 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:16:11,291 - modnet - INFO - loss: 65.0062	mae: 65.0062	val_loss: 150.3177	val_mae: 150.3177	
2025-05-11 02:16:11,292 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:16:11,317 - modnet - INFO - loss: 68.2918	mae: 68.2918	val_loss: 171.9693	val_mae: 171.9693	
2025-05-11 02:16:11,317 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:16:11,329 - modnet - INFO - loss: 54.2911	mae: 54.2911	val_loss: 141.0007	val_mae: 141.0007	
2025-05-11 02:16:11,329 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:16:11,561 - modnet - INFO - loss: 63.1250	mae: 63.1250	val_loss: 124.6023	val_mae: 124.6023	
2025-05-11 02:16:11,561 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:16:11,676 - modnet - INFO - loss: 67.8943	mae: 67.8943	val_loss: 144.1880	val_mae: 144.1880	
2025-05-11 02:16:11,676 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:16:11,855 - modnet - INFO - loss: 47.5663	mae: 47.5663	val_loss: 146.7467	val_mae: 146.7467	
2025-05-11 02:16:11,855 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:16:29,868 - modnet - INFO - loss: 78.9970	mae: 78.9970	val_loss: 166.0016	val_mae: 166.0016	
2025-05-11 02:16:29,868 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:16:30,057 - modnet - INFO - loss: 64.3837	mae: 64.3837	val_loss: 140.6235	val_mae: 140.6235	
2025-05-11 02:16:30,057 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:16:30,462 - modnet - INFO - loss: 85.0577	mae: 85.0577	val_loss: 163.5574	val_mae: 163.5574	
2025-05-11 02:16:30,462 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:16:30,520 - modnet - INFO - loss: 82.2796	mae: 82.2796	val_loss: 99.2759	val_mae: 99.2759	
2025-05-11 02:16:30,520 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:16:30,763 - modnet - INFO - loss: 45.0475	mae: 45.0475	val_loss: 151.3233	val_mae: 151.3233	
2025-05-11 02:16:30,763 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:16:30,775 - modnet - INFO - loss: 57.3751	mae: 57.3751	val_loss: 97.3243	val_mae: 97.3243	
2025-05-11 02:16:30,775 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:16:30,833 - modnet - INFO - loss: 62.4302	mae: 62.4302	val_loss: 123.4181	val_mae: 123.4181	
2025-05-11 02:16:30,834 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:16:30,868 - modnet - INFO - loss: 44.6600	mae: 44.6600	val_loss: 143.8630	val_mae: 143.8630	
2025-05-11 02:16:30,868 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:16:31,464 - modnet - INFO - loss: 68.8193	mae: 68.8193	val_loss: 147.1567	val_mae: 147.1567	
2025-05-11 02:16:31,464 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:16:31,771 - modnet - INFO - loss: 60.5801	mae: 60.5801	val_loss: 126.0969	val_mae: 126.0969	
2025-05-11 02:16:31,771 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:16:49,444 - modnet - INFO - loss: 45.8074	mae: 45.8074	val_loss: 141.3651	val_mae: 141.3651	
2025-05-11 02:16:49,444 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:16:49,458 - modnet - INFO - loss: 74.6039	mae: 74.6039	val_loss: 162.8897	val_mae: 162.8897	
2025-05-11 02:16:49,458 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:16:49,844 - modnet - INFO - loss: 60.9541	mae: 60.9541	val_loss: 108.6043	val_mae: 108.6043	
2025-05-11 02:16:49,844 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:16:49,888 - modnet - INFO - loss: 72.5675	mae: 72.5675	val_loss: 169.9137	val_mae: 169.9137	
2025-05-11 02:16:49,888 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:16:50,310 - modnet - INFO - loss: 57.1565	mae: 57.1565	val_loss: 96.6652	val_mae: 96.6652	
2025-05-11 02:16:50,310 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:16:50,344 - modnet - INFO - loss: 57.0564	mae: 57.0564	val_loss: 135.6661	val_mae: 135.6661	
2025-05-11 02:16:50,344 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:16:50,494 - modnet - INFO - loss: 52.5676	mae: 52.5676	val_loss: 151.1980	val_mae: 151.1980	
2025-05-11 02:16:50,494 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:16:50,639 - modnet - INFO - loss: 72.1588	mae: 72.1588	val_loss: 146.8769	val_mae: 146.8769	
2025-05-11 02:16:50,639 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:16:50,913 - modnet - INFO - loss: 57.9434	mae: 57.9434	val_loss: 146.7650	val_mae: 146.7650	
2025-05-11 02:16:50,913 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:16:51,570 - modnet - INFO - loss: 49.7637	mae: 49.7637	val_loss: 144.2252	val_mae: 144.2252	
2025-05-11 02:16:51,570 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:17:09,700 - modnet - INFO - loss: 70.3835	mae: 70.3835	val_loss: 175.2231	val_mae: 175.2231	
2025-05-11 02:17:09,700 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:17:09,803 - modnet - INFO - loss: 70.9416	mae: 70.9416	val_loss: 146.5989	val_mae: 146.5989	
2025-05-11 02:17:09,803 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:17:10,037 - modnet - INFO - loss: 44.9932	mae: 44.9932	val_loss: 157.8428	val_mae: 157.8428	
2025-05-11 02:17:10,037 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:17:10,365 - modnet - INFO - loss: 71.4339	mae: 71.4339	val_loss: 168.5562	val_mae: 168.5562	
2025-05-11 02:17:10,365 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:17:10,710 - modnet - INFO - loss: 62.9541	mae: 62.9541	val_loss: 124.6603	val_mae: 124.6603	
2025-05-11 02:17:10,710 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:17:10,773 - modnet - INFO - loss: 57.7492	mae: 57.7492	val_loss: 101.8336	val_mae: 101.8336	
2025-05-11 02:17:10,773 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:17:11,240 - modnet - INFO - loss: 45.4543	mae: 45.4543	val_loss: 148.9993	val_mae: 148.9993	
2025-05-11 02:17:11,240 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:17:11,465 - modnet - INFO - loss: 59.1541	mae: 59.1541	val_loss: 140.7511	val_mae: 140.7511	
2025-05-11 02:17:11,465 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:17:11,707 - modnet - INFO - loss: 71.1239	mae: 71.1239	val_loss: 98.5245	val_mae: 98.5245	
2025-05-11 02:17:11,707 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:17:13,306 - modnet - INFO - loss: 55.7007	mae: 55.7007	val_loss: 127.5507	val_mae: 127.5507	
2025-05-11 02:17:13,306 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:17:29,364 - modnet - INFO - loss: 71.9471	mae: 71.9471	val_loss: 171.1712	val_mae: 171.1712	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f63dc604ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:17:29,602 - modnet - INFO - Preset #1 fitting finished, loss: 168.86625461425783

 10%|█         | 1/10 [01:42<15:26, 102.92s/it]2025-05-11 02:17:29,771 - modnet - INFO - loss: 54.6813	mae: 54.6813	val_loss: 104.5367	val_mae: 104.5367	
2025-05-11 02:17:29,902 - modnet - INFO - loss: 62.8264	mae: 62.8264	val_loss: 163.6792	val_mae: 163.6792	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f0be4e07ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:17:30,009 - modnet - INFO - Preset #1 fitting finished, loss: 102.21584184570312
2025-05-11 02:17:30,063 - modnet - INFO - loss: 47.6880	mae: 47.6880	val_loss: 143.1864	val_mae: 143.1864	
2025-05-11 02:17:30,092 - modnet - INFO - loss: 58.9516	mae: 58.9516	val_loss: 135.2779	val_mae: 135.2779	

 20%|██        | 2/10 [01:43<05:40, 42.62s/it] WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9858476040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:17:30,139 - modnet - INFO - Preset #0 fitting finished, loss: 167.53518974609375

 30%|███       | 3/10 [01:43<02:42, 23.22s/it]WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7efd0cf98ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:17:30,295 - modnet - INFO - Preset #1 fitting finished, loss: 147.02800746372768
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f2a0de59ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.

 40%|████      | 4/10 [01:43<01:24, 14.11s/it]2025-05-11 02:17:30,398 - modnet - INFO - Preset #1 fitting finished, loss: 143.93894658203126
2025-05-11 02:17:30,438 - modnet - INFO - loss: 44.7518	mae: 44.7518	val_loss: 145.2258	val_mae: 145.2258	

 50%|█████     | 5/10 [01:43<00:45,  9.06s/it]2025-05-11 02:17:30,537 - modnet - INFO - loss: 49.4209	mae: 49.4209	val_loss: 139.1233	val_mae: 139.1233	
2025-05-11 02:17:30,582 - modnet - INFO - loss: 57.3432	mae: 57.3432	val_loss: 129.8013	val_mae: 129.8013	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f2694228040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:17:30,679 - modnet - INFO - Preset #0 fitting finished, loss: 148.50072644292095
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7fae70425ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.

 60%|██████    | 6/10 [01:43<00:24,  6.07s/it]2025-05-11 02:17:30,780 - modnet - INFO - Preset #0 fitting finished, loss: 143.59679301757816
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f6a9c686ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.

 70%|███████   | 7/10 [01:44<00:12,  4.12s/it]2025-05-11 02:17:30,881 - modnet - INFO - Preset #0 fitting finished, loss: 127.62960441894532
2025-05-11 02:17:31,889 - modnet - INFO - loss: 79.8973	mae: 79.8973	val_loss: 99.2597	val_mae: 99.2597	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f6e5c086ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:17:32,120 - modnet - INFO - Preset #0 fitting finished, loss: 100.10813540039064

 90%|█████████ | 9/10 [01:45<00:02,  2.45s/it]2025-05-11 02:17:33,491 - modnet - INFO - loss: 59.1277	mae: 59.1277	val_loss: 125.2481	val_mae: 125.2481	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7fa28d5d8040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:17:33,722 - modnet - INFO - Preset #1 fitting finished, loss: 132.82431721191406

100%|██████████| 10/10 [01:47<00:00,  2.24s/it]
100%|██████████| 10/10 [01:47<00:00, 10.70s/it]
2025-05-11 02:17:35,032 - modnet - INFO - Preset #1 resulted in lowest validation loss with params {'train_data': <modnet.preprocessing.MODData object at 0x7f98d6e6eaf0>, 'targets': [[['yield_strength']]], 'weights': {'yield_strength': 1}, 'n_models': 5, 'num_classes': {'yield_strength': 0}, 'n_feat': 64, 'num_neurons': [[128], [32], [8], [8]], 'lr': 0.001, 'batch_size': 32, 'epochs': 1000, 'loss': 'mae', 'act': 'elu', 'out_act': 'linear', 'callbacks': [<tensorflow.python.keras.callbacks.EarlyStopping object at 0x7f98d90ee8e0>], 'preset_id': 0, 'fold_id': 2, 'verbose': 0, 'val_data': <modnet.preprocessing.MODData object at 0x7f98d6e93bb0>}
2025-05-11 02:17:36,217 - modnet - INFO - Model successfully saved as results/best_model_fold_1.pkl!
Saved best model for fold 1 to results/best_model_fold_1.pkl
2025-05-11 02:17:36.241988: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-11 02:17:36.254064: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445300000 Hz
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977235160> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99771e59d0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977235b80> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997c0038b0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977212ca0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99771d5280> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977184820> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99771d5940> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997bfb8310> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99771335e0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99771051f0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977105ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997bfb8700> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977235310> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99770b94c0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977069160> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997709b700> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977069820> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977069a60> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997704c280> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976fa2040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976fa2dc0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977212670> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977133a60> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976fd2430> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f7e8b0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f324c0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f7e5e0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997709baf0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f09040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f093a0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976eb8b80> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f09550> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99770b95e0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976e69af0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976e94430> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976deb0d0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976e94310> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f328b0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f09430> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976da01f0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976dce940> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976da0940> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976e69a60> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976d815e0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976d530d0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
[Fold 1] MAE = 112.965 , MedianAE = 78.753 , MAPE = 100000000000000000000.000, √MSE = 169.118 
[Fold 1] MaxAE = 684.494 , slope = 0.68, R = 0.82
Fold 1 metrics saved.
Saved fold 1 complete results to results/fold_1_results.pkl
Training fold 2 ...
Processing fold 2 ...
2025-05-11 02:17:38,389 - modnet - INFO - Proceeding with grid search: archs: [(64, [[128], [32], [8], [8]]), (64, [[64], [32], [8], [8]]), (64, [[32], [16], [8], [8]]), (91, [[182], [45], [11], [11]]), (91, [[91], [45], [11], [11]]), (91, [[45], [22], [11], [11]]), (118, [[236], [59], [14], [14]]), (118, [[118], [59], [14], [14]]), (118, [[59], [29], [14], [14]])], batch sizes: [32, 64], learning_rates: [0.001, 0.005, 0.01]
Using 2 presets
2025-05-11 02:17:38,858 - modnet - INFO - Multiprocessing on 32 cores. Total of 128 cores available.

  0%|          | 0/10 [00:00<?, ?it/s]2025-05-11 02:17:42,530 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:17:42,538 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:17:42,545 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:17:42,553 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:17:42,565 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:17:42,573 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:17:42,597 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:17:42,605 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:17:42,622 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:17:42,630 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:17:42,643 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:17:42,651 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:17:42,691 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:17:42,695 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:17:42,698 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:17:42,702 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:17:42,720 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:17:42,726 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:17:42,727 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:17:42,733 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:18:03,251 - modnet - INFO - loss: 77.4947	mae: 77.4947	val_loss: 147.6773	val_mae: 147.6773	
2025-05-11 02:18:03,252 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:18:03,256 - modnet - INFO - loss: 57.0911	mae: 57.0911	val_loss: 125.7074	val_mae: 125.7074	
2025-05-11 02:18:03,256 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:18:03,276 - modnet - INFO - loss: 62.3618	mae: 62.3618	val_loss: 126.5534	val_mae: 126.5534	
2025-05-11 02:18:03,277 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:18:03,406 - modnet - INFO - loss: 64.4612	mae: 64.4612	val_loss: 154.8937	val_mae: 154.8937	
2025-05-11 02:18:03,406 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:18:03,611 - modnet - INFO - loss: 51.6613	mae: 51.6613	val_loss: 146.4148	val_mae: 146.4148	
2025-05-11 02:18:03,611 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:18:03,628 - modnet - INFO - loss: 71.0640	mae: 71.0640	val_loss: 118.1272	val_mae: 118.1272	
2025-05-11 02:18:03,628 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:18:03,837 - modnet - INFO - loss: 61.3770	mae: 61.3770	val_loss: 147.9284	val_mae: 147.9284	
2025-05-11 02:18:03,837 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:18:04,083 - modnet - INFO - loss: 51.1047	mae: 51.1047	val_loss: 141.2283	val_mae: 141.2283	
2025-05-11 02:18:04,083 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:18:04,167 - modnet - INFO - loss: 70.1262	mae: 70.1262	val_loss: 139.2813	val_mae: 139.2813	
2025-05-11 02:18:04,168 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:18:04,386 - modnet - INFO - loss: 69.3305	mae: 69.3305	val_loss: 123.6707	val_mae: 123.6707	
2025-05-11 02:18:04,386 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:18:21,760 - modnet - INFO - loss: 72.4897	mae: 72.4897	val_loss: 152.3997	val_mae: 152.3997	
2025-05-11 02:18:21,760 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:18:22,480 - modnet - INFO - loss: 71.3267	mae: 71.3267	val_loss: 119.6682	val_mae: 119.6682	
2025-05-11 02:18:22,481 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:18:22,681 - modnet - INFO - loss: 58.5071	mae: 58.5071	val_loss: 143.1904	val_mae: 143.1904	
2025-05-11 02:18:22,681 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:18:22,765 - modnet - INFO - loss: 52.8230	mae: 52.8230	val_loss: 146.4326	val_mae: 146.4326	
2025-05-11 02:18:22,765 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:18:22,912 - modnet - INFO - loss: 59.7784	mae: 59.7784	val_loss: 129.5445	val_mae: 129.5445	
2025-05-11 02:18:22,912 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:18:23,026 - modnet - INFO - loss: 56.7198	mae: 56.7198	val_loss: 142.2563	val_mae: 142.2563	
2025-05-11 02:18:23,026 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:18:23,229 - modnet - INFO - loss: 59.4068	mae: 59.4068	val_loss: 150.0438	val_mae: 150.0438	
2025-05-11 02:18:23,229 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:18:23,368 - modnet - INFO - loss: 57.2695	mae: 57.2695	val_loss: 121.9759	val_mae: 121.9759	
2025-05-11 02:18:23,369 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:18:23,816 - modnet - INFO - loss: 59.3411	mae: 59.3411	val_loss: 150.0915	val_mae: 150.0915	
2025-05-11 02:18:23,816 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:18:24,795 - modnet - INFO - loss: 70.7312	mae: 70.7312	val_loss: 119.5431	val_mae: 119.5431	
2025-05-11 02:18:24,795 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:18:40,306 - modnet - INFO - loss: 63.6634	mae: 63.6634	val_loss: 145.9626	val_mae: 145.9626	
2025-05-11 02:18:40,306 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:18:41,316 - modnet - INFO - loss: 71.1520	mae: 71.1520	val_loss: 123.8303	val_mae: 123.8303	
2025-05-11 02:18:41,316 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:18:42,076 - modnet - INFO - loss: 69.1936	mae: 69.1936	val_loss: 139.9335	val_mae: 139.9335	
2025-05-11 02:18:42,076 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:18:42,365 - modnet - INFO - loss: 51.0165	mae: 51.0165	val_loss: 141.8557	val_mae: 141.8557	
2025-05-11 02:18:42,365 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:18:42,664 - modnet - INFO - loss: 55.6387	mae: 55.6387	val_loss: 145.1116	val_mae: 145.1116	
2025-05-11 02:18:42,664 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:18:42,708 - modnet - INFO - loss: 63.8195	mae: 63.8195	val_loss: 153.6288	val_mae: 153.6288	
2025-05-11 02:18:42,708 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:18:42,865 - modnet - INFO - loss: 62.5736	mae: 62.5736	val_loss: 125.0039	val_mae: 125.0039	
2025-05-11 02:18:42,865 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:18:42,937 - modnet - INFO - loss: 75.1153	mae: 75.1153	val_loss: 144.1076	val_mae: 144.1076	
2025-05-11 02:18:42,938 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:18:43,469 - modnet - INFO - loss: 59.7779	mae: 59.7779	val_loss: 152.1253	val_mae: 152.1253	
2025-05-11 02:18:43,469 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:18:44,526 - modnet - INFO - loss: 68.9937	mae: 68.9937	val_loss: 119.8829	val_mae: 119.8829	
2025-05-11 02:18:44,526 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:18:59,544 - modnet - INFO - loss: 67.4764	mae: 67.4764	val_loss: 149.0697	val_mae: 149.0697	
2025-05-11 02:18:59,545 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:19:01,231 - modnet - INFO - loss: 70.7668	mae: 70.7668	val_loss: 124.8414	val_mae: 124.8414	
2025-05-11 02:19:01,232 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:19:02,064 - modnet - INFO - loss: 62.0769	mae: 62.0769	val_loss: 129.4137	val_mae: 129.4137	
2025-05-11 02:19:02,064 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:19:02,278 - modnet - INFO - loss: 54.5085	mae: 54.5085	val_loss: 142.9908	val_mae: 142.9908	
2025-05-11 02:19:02,278 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:19:02,285 - modnet - INFO - loss: 49.9600	mae: 49.9600	val_loss: 132.8058	val_mae: 132.8058	
2025-05-11 02:19:02,285 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:19:02,685 - modnet - INFO - loss: 66.7606	mae: 66.7606	val_loss: 160.0015	val_mae: 160.0015	
2025-05-11 02:19:02,685 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:19:02,705 - modnet - INFO - loss: 59.3977	mae: 59.3977	val_loss: 155.2266	val_mae: 155.2266	
2025-05-11 02:19:02,705 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:19:03,325 - modnet - INFO - loss: 60.1555	mae: 60.1555	val_loss: 119.3544	val_mae: 119.3544	
2025-05-11 02:19:03,325 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:19:04,279 - modnet - INFO - loss: 58.9907	mae: 58.9907	val_loss: 150.0717	val_mae: 150.0717	
2025-05-11 02:19:04,279 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:19:06,475 - modnet - INFO - loss: 68.5617	mae: 68.5617	val_loss: 123.7357	val_mae: 123.7357	
2025-05-11 02:19:06,475 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:19:18,274 - modnet - INFO - loss: 61.4655	mae: 61.4655	val_loss: 140.4879	val_mae: 140.4879	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7fd93ddf9ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:19:18,522 - modnet - INFO - Preset #1 fitting finished, loss: 147.1194544921875

 10%|█         | 1/10 [01:39<14:57, 99.77s/it]2025-05-11 02:19:20,545 - modnet - INFO - loss: 70.8998	mae: 70.8998	val_loss: 122.0080	val_mae: 122.0080	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f96f1947ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:19:20,786 - modnet - INFO - Preset #1 fitting finished, loss: 121.69499033203127

 20%|██        | 2/10 [01:42<05:39, 42.41s/it]2025-05-11 02:19:21,300 - modnet - INFO - loss: 69.0714	mae: 69.0714	val_loss: 131.7450	val_mae: 131.7450	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f2ae0b05ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:19:21,537 - modnet - INFO - Preset #1 fitting finished, loss: 129.92427375637754
2025-05-11 02:19:21,542 - modnet - INFO - loss: 50.2411	mae: 50.2411	val_loss: 140.2236	val_mae: 140.2236	
2025-05-11 02:19:21,570 - modnet - INFO - loss: 63.4769	mae: 63.4769	val_loss: 145.1115	val_mae: 145.1115	

 30%|███       | 3/10 [01:42<02:43, 23.39s/it]2025-05-11 02:19:21,661 - modnet - INFO - loss: 58.2682	mae: 58.2682	val_loss: 154.3406	val_mae: 154.3406	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f67f4117040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:19:21,777 - modnet - INFO - Preset #0 fitting finished, loss: 142.197669128418
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7fbdadd68ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.

 40%|████      | 4/10 [01:43<01:25, 14.25s/it]2025-05-11 02:19:21,880 - modnet - INFO - Preset #0 fitting finished, loss: 145.19667001953127
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f8532088ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:19:21,980 - modnet - INFO - Preset #1 fitting finished, loss: 153.1886358001709

 60%|██████    | 6/10 [01:43<00:26,  6.75s/it]2025-05-11 02:19:22,191 - modnet - INFO - loss: 51.8651	mae: 51.8651	val_loss: 147.4980	val_mae: 147.4980	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f5098ab6040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:19:22,427 - modnet - INFO - Preset #1 fitting finished, loss: 143.************
2025-05-11 02:19:22,452 - modnet - INFO - loss: 68.3580	mae: 68.3580	val_loss: 138.0885	val_mae: 138.0885	

 70%|███████   | 7/10 [01:43<00:15,  5.02s/it]WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f42140b6ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:19:22,683 - modnet - INFO - Preset #0 fitting finished, loss: 127.53973702566964

 80%|████████  | 8/10 [01:43<00:07,  3.68s/it]2025-05-11 02:19:23,616 - modnet - INFO - loss: 61.0801	mae: 61.0801	val_loss: 150.8303	val_mae: 150.8303	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f3950075ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:19:23,851 - modnet - INFO - Preset #0 fitting finished, loss: 151.60253004894258

 90%|█████████ | 9/10 [01:45<00:02,  2.96s/it]2025-05-11 02:19:25,132 - modnet - INFO - loss: 72.9441	mae: 72.9441	val_loss: 127.7881	val_mae: 127.7881	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f92bc587040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:19:25,368 - modnet - INFO - Preset #0 fitting finished, loss: 122.92411074218751

100%|██████████| 10/10 [01:46<00:00,  2.61s/it]
100%|██████████| 10/10 [01:46<00:00, 10.69s/it]
2025-05-11 02:19:26,964 - modnet - INFO - Preset #1 resulted in lowest validation loss with params {'train_data': <modnet.preprocessing.MODData object at 0x7f9976d98250>, 'targets': [[['yield_strength']]], 'weights': {'yield_strength': 1}, 'n_models': 5, 'num_classes': {'yield_strength': 0}, 'n_feat': 64, 'num_neurons': [[128], [32], [8], [8]], 'lr': 0.001, 'batch_size': 32, 'epochs': 1000, 'loss': 'mae', 'act': 'elu', 'out_act': 'linear', 'callbacks': [<tensorflow.python.keras.callbacks.EarlyStopping object at 0x7f9976d454f0>], 'preset_id': 0, 'fold_id': 1, 'verbose': 0, 'val_data': <modnet.preprocessing.MODData object at 0x7f9976d989d0>}
2025-05-11 02:19:28,016 - modnet - INFO - Model successfully saved as results/best_model_fold_2.pkl!
Saved best model for fold 2 to results/best_model_fold_2.pkl
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976e69af0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976e69790> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99770b94c0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997c04bd30> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997bfb83a0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997bfb8b80> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997c0c33a0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99770b9160> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976cfb4c0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e7351f0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e735ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99770b9ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e9198b0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976e06430> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976d901f0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e61d5e0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976d90670> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976d90430> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e8381f0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e8383a0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976dd1ca0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99770b9820> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976cfb5e0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997ea33430> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e940670> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976e533a0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e9403a0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e61d8b0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e8384c0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f6f310> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997bf9aaf0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f6f4c0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99770b9ca0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976eba9d0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976ff43a0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997709a040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997709a310> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976ff4430> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f6fdc0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99771241f0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f98d42d68b0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977124a60> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976ebaca0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f43670> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977056040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977056ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997709a670> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997709a820> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99771124c0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99772451f0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
[Fold 2] MAE = 97.244 , MedianAE = 64.384 , MAPE = 100000000000000000000.000, √MSE = 148.351 
[Fold 2] MaxAE = 572.420 , slope = 0.84, R = 0.87
Fold 2 metrics saved.
Saved fold 2 complete results to results/fold_2_results.pkl
Training fold 3 ...
Processing fold 3 ...
2025-05-11 02:19:30,115 - modnet - INFO - Proceeding with grid search: archs: [(64, [[128], [32], [8], [8]]), (64, [[64], [32], [8], [8]]), (64, [[32], [16], [8], [8]]), (91, [[182], [45], [11], [11]]), (91, [[91], [45], [11], [11]]), (91, [[45], [22], [11], [11]]), (118, [[236], [59], [14], [14]]), (118, [[118], [59], [14], [14]]), (118, [[59], [29], [14], [14]])], batch sizes: [32, 64], learning_rates: [0.001, 0.005, 0.01]
Using 2 presets
2025-05-11 02:19:30,590 - modnet - INFO - Multiprocessing on 32 cores. Total of 128 cores available.

  0%|          | 0/10 [00:00<?, ?it/s]2025-05-11 02:19:34,228 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:19:34,235 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:19:34,265 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:19:34,273 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:19:34,301 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:19:34,302 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:19:34,308 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:19:34,310 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:19:34,331 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:19:34,338 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:19:34,342 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:19:34,349 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:19:34,371 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:19:34,378 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:19:34,388 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:19:34,395 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:19:34,405 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:19:34,412 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:19:34,424 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:19:34,431 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:19:54,238 - modnet - INFO - loss: 49.6315	mae: 49.6315	val_loss: 165.1480	val_mae: 165.1480	
2025-05-11 02:19:54,238 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:19:54,351 - modnet - INFO - loss: 52.8660	mae: 52.8660	val_loss: 165.4960	val_mae: 165.4960	
2025-05-11 02:19:54,351 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:19:54,367 - modnet - INFO - loss: 48.9905	mae: 48.9905	val_loss: 143.9226	val_mae: 143.9226	
2025-05-11 02:19:54,367 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:19:54,571 - modnet - INFO - loss: 49.8730	mae: 49.8730	val_loss: 143.5802	val_mae: 143.5802	
2025-05-11 02:19:54,572 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:19:54,608 - modnet - INFO - loss: 53.1923	mae: 53.1923	val_loss: 155.1015	val_mae: 155.1015	
2025-05-11 02:19:54,608 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:19:54,615 - modnet - INFO - loss: 52.9017	mae: 52.9017	val_loss: 133.7401	val_mae: 133.7401	
2025-05-11 02:19:54,615 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:19:54,625 - modnet - INFO - loss: 54.9640	mae: 54.9640	val_loss: 129.8332	val_mae: 129.8332	
2025-05-11 02:19:54,625 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:19:54,907 - modnet - INFO - loss: 43.5780	mae: 43.5780	val_loss: 136.7053	val_mae: 136.7053	
2025-05-11 02:19:54,907 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:19:54,932 - modnet - INFO - loss: 61.3860	mae: 61.3860	val_loss: 113.3398	val_mae: 113.3398	
2025-05-11 02:19:54,932 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:19:55,084 - modnet - INFO - loss: 49.1941	mae: 49.1941	val_loss: 118.6432	val_mae: 118.6432	
2025-05-11 02:19:55,085 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:20:12,896 - modnet - INFO - loss: 56.1009	mae: 56.1009	val_loss: 144.9597	val_mae: 144.9597	
2025-05-11 02:20:12,897 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:20:13,017 - modnet - INFO - loss: 78.2869	mae: 78.2869	val_loss: 133.6689	val_mae: 133.6689	
2025-05-11 02:20:13,017 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:20:13,134 - modnet - INFO - loss: 55.0302	mae: 55.0302	val_loss: 162.1325	val_mae: 162.1325	
2025-05-11 02:20:13,134 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:20:13,409 - modnet - INFO - loss: 57.3465	mae: 57.3465	val_loss: 129.0384	val_mae: 129.0384	
2025-05-11 02:20:13,409 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:20:13,630 - modnet - INFO - loss: 47.9024	mae: 47.9024	val_loss: 145.1320	val_mae: 145.1320	
2025-05-11 02:20:13,630 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:20:13,872 - modnet - INFO - loss: 51.4251	mae: 51.4251	val_loss: 169.8015	val_mae: 169.8015	
2025-05-11 02:20:13,872 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:20:13,876 - modnet - INFO - loss: 48.3976	mae: 48.3976	val_loss: 144.6992	val_mae: 144.6992	
2025-05-11 02:20:13,876 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:20:14,024 - modnet - INFO - loss: 47.9522	mae: 47.9522	val_loss: 140.2151	val_mae: 140.2151	
2025-05-11 02:20:14,024 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:20:14,606 - modnet - INFO - loss: 49.0633	mae: 49.0633	val_loss: 118.2338	val_mae: 118.2338	
2025-05-11 02:20:14,606 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:20:14,675 - modnet - INFO - loss: 58.8827	mae: 58.8827	val_loss: 114.4789	val_mae: 114.4789	
2025-05-11 02:20:14,676 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:20:31,340 - modnet - INFO - loss: 48.2647	mae: 48.2647	val_loss: 150.1874	val_mae: 150.1874	
2025-05-11 02:20:31,340 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:20:31,867 - modnet - INFO - loss: 62.1192	mae: 62.1192	val_loss: 134.6818	val_mae: 134.6818	
2025-05-11 02:20:31,867 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:20:31,900 - modnet - INFO - loss: 75.5923	mae: 75.5923	val_loss: 131.2381	val_mae: 131.2381	
2025-05-11 02:20:31,900 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:20:32,216 - modnet - INFO - loss: 53.6969	mae: 53.6969	val_loss: 166.9659	val_mae: 166.9659	
2025-05-11 02:20:32,216 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:20:32,352 - modnet - INFO - loss: 50.0379	mae: 50.0379	val_loss: 143.9238	val_mae: 143.9238	
2025-05-11 02:20:32,352 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:20:32,851 - modnet - INFO - loss: 45.8405	mae: 45.8405	val_loss: 142.1483	val_mae: 142.1483	
2025-05-11 02:20:32,851 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:20:33,206 - modnet - INFO - loss: 49.6784	mae: 49.6784	val_loss: 152.0452	val_mae: 152.0452	
2025-05-11 02:20:33,206 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:20:33,498 - modnet - INFO - loss: 52.8727	mae: 52.8727	val_loss: 170.9801	val_mae: 170.9801	
2025-05-11 02:20:33,498 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:20:34,250 - modnet - INFO - loss: 53.2563	mae: 53.2563	val_loss: 119.3487	val_mae: 119.3487	
2025-05-11 02:20:34,251 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:20:34,419 - modnet - INFO - loss: 48.4440	mae: 48.4440	val_loss: 123.1765	val_mae: 123.1765	
2025-05-11 02:20:34,419 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:20:50,363 - modnet - INFO - loss: 50.6076	mae: 50.6076	val_loss: 149.4670	val_mae: 149.4670	
2025-05-11 02:20:50,363 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:20:51,611 - modnet - INFO - loss: 65.8226	mae: 65.8226	val_loss: 128.8956	val_mae: 128.8956	
2025-05-11 02:20:51,611 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:20:51,674 - modnet - INFO - loss: 61.7586	mae: 61.7586	val_loss: 129.8293	val_mae: 129.8293	
2025-05-11 02:20:51,674 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:20:51,879 - modnet - INFO - loss: 52.1510	mae: 52.1510	val_loss: 168.9151	val_mae: 168.9151	
2025-05-11 02:20:51,879 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:20:52,036 - modnet - INFO - loss: 45.8868	mae: 45.8868	val_loss: 147.8768	val_mae: 147.8768	
2025-05-11 02:20:52,037 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:20:52,733 - modnet - INFO - loss: 46.7601	mae: 46.7601	val_loss: 144.1550	val_mae: 144.1550	
2025-05-11 02:20:52,733 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:20:53,641 - modnet - INFO - loss: 54.2189	mae: 54.2189	val_loss: 151.6635	val_mae: 151.6635	
2025-05-11 02:20:53,642 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:20:54,094 - modnet - INFO - loss: 52.4931	mae: 52.4931	val_loss: 172.2188	val_mae: 172.2188	
2025-05-11 02:20:54,095 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:20:54,116 - modnet - INFO - loss: 49.2147	mae: 49.2147	val_loss: 122.3607	val_mae: 122.3607	
2025-05-11 02:20:54,116 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:20:54,250 - modnet - INFO - loss: 57.9336	mae: 57.9336	val_loss: 119.9944	val_mae: 119.9944	
2025-05-11 02:20:54,251 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:21:09,133 - modnet - INFO - loss: 54.8631	mae: 54.8631	val_loss: 138.5246	val_mae: 138.5246	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f359dda8ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:21:09,372 - modnet - INFO - Preset #1 fitting finished, loss: 145.41228866271973

 10%|█         | 1/10 [01:38<14:49, 98.86s/it]2025-05-11 02:21:10,441 - modnet - INFO - loss: 51.8183	mae: 51.8183	val_loss: 125.3748	val_mae: 125.3748	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7fc49cb39040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:21:10,684 - modnet - INFO - Preset #0 fitting finished, loss: 131.27223955078125

 20%|██        | 2/10 [01:40<05:31, 41.47s/it]2025-05-11 02:21:11,008 - modnet - INFO - loss: 53.2293	mae: 53.2293	val_loss: 169.5982	val_mae: 169.5982	
2025-05-11 02:21:11,016 - modnet - INFO - loss: 60.2784	mae: 60.2784	val_loss: 128.8976	val_mae: 128.8976	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f260e14aee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:21:11,243 - modnet - INFO - Preset #1 fitting finished, loss: 166.55195136718754
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f5f09ea7ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.

 30%|███       | 3/10 [01:40<02:39, 22.79s/it]2025-05-11 02:21:11,349 - modnet - INFO - Preset #1 fitting finished, loss: 129.76731220703127
2025-05-11 02:21:11,400 - modnet - INFO - loss: 46.5737	mae: 46.5737	val_loss: 143.3029	val_mae: 143.3029	

 40%|████      | 4/10 [01:40<01:23, 13.83s/it]WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f49b4f79040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:21:11,638 - modnet - INFO - Preset #1 fitting finished, loss: 144.76313930664065

 50%|█████     | 5/10 [01:41<00:44,  8.95s/it]2025-05-11 02:21:11,812 - modnet - INFO - loss: 46.8758	mae: 46.8758	val_loss: 143.1715	val_mae: 143.1715	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f8eec786ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:21:12,041 - modnet - INFO - Preset #0 fitting finished, loss: 141.2790205078125

 60%|██████    | 6/10 [01:41<00:24,  6.04s/it]2025-05-11 02:21:12,603 - modnet - INFO - loss: 47.6560	mae: 47.6560	val_loss: 150.1058	val_mae: 150.1058	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f88bd23b040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:21:12,837 - modnet - INFO - Preset #0 fitting finished, loss: 150.7230587380171

 70%|███████   | 7/10 [01:42<00:12,  4.33s/it]2025-05-11 02:21:13,410 - modnet - INFO - loss: 54.3630	mae: 54.3630	val_loss: 165.5549	val_mae: 165.5549	
2025-05-11 02:21:13,496 - modnet - INFO - loss: 53.8204	mae: 53.8204	val_loss: 121.5671	val_mae: 121.5671	
2025-05-11 02:21:13,502 - modnet - INFO - loss: 59.0071	mae: 59.0071	val_loss: 117.1196	val_mae: 117.1196	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f97de2fc040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:21:13,641 - modnet - INFO - Preset #0 fitting finished, loss: 168.8102618652344
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f19a4745ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f685c7f6ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.

 80%|████████  | 8/10 [01:43<00:06,  3.21s/it]2025-05-11 02:21:13,747 - modnet - INFO - Preset #1 fitting finished, loss: 118.21904143066406
2025-05-11 02:21:13,845 - modnet - INFO - Preset #0 fitting finished, loss: 119.43349052734375

100%|██████████| 10/10 [01:43<00:00,  1.73s/it]
100%|██████████| 10/10 [01:43<00:00, 10.33s/it]
2025-05-11 02:21:15,171 - modnet - INFO - Preset #2 resulted in lowest validation loss with params {'train_data': <modnet.preprocessing.MODData object at 0x7f9977236dc0>, 'targets': [[['yield_strength']]], 'weights': {'yield_strength': 1}, 'n_models': 5, 'num_classes': {'yield_strength': 0}, 'n_feat': 64, 'num_neurons': [[128], [32], [8], [8]], 'lr': 0.001, 'batch_size': 32, 'epochs': 1000, 'loss': 'mae', 'act': 'elu', 'out_act': 'linear', 'callbacks': [<tensorflow.python.keras.callbacks.EarlyStopping object at 0x7f98d44df1c0>], 'preset_id': 1, 'fold_id': 4, 'verbose': 0, 'val_data': <modnet.preprocessing.MODData object at 0x7f99772365e0>}
2025-05-11 02:21:16,524 - modnet - INFO - Model successfully saved as results/best_model_fold_3.pkl!
Saved best model for fold 3 to results/best_model_fold_3.pkl
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e838280> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e838f70> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e61d0d0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976d81550> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976cfb820> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976cfbf70> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e919ca0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976d81a60> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997bfb8310> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997c0c35e0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99770b9ca0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997c04bb80> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997c0c3430> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99770b93a0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976e69ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976d53280> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f43b80> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976e69040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99771121f0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f98d4366280> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976cb4040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976cb4310> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f98d43660d0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f43670> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f98d46480d0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f98d4153700> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f98d46488b0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977112550> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e774280> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f98d6ed5040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f98d6ed5dc0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976cb4550> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976cb4820> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997c193430> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99771da160> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e3dc4c0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99771da5e0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f98d41539d0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976b6d040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976b6d310> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f98d4519b80> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976b6d5e0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f43700> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e9bdaf0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e8d7430> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e7b3280> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e8d74c0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e3dc8b0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976b6d3a0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e7e2160> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
[Fold 3] MAE = 102.580 , MedianAE = 64.210 , MAPE = 100000000000000000000.000, √MSE = 145.415 
[Fold 3] MaxAE = 378.347 , slope = 0.77, R = 0.87
Fold 3 metrics saved.
Saved fold 3 complete results to results/fold_3_results.pkl
Training fold 4 ...
Processing fold 4 ...
2025-05-11 02:21:18,604 - modnet - INFO - Proceeding with grid search: archs: [(64, [[128], [32], [8], [8]]), (64, [[64], [32], [8], [8]]), (64, [[32], [16], [8], [8]]), (91, [[182], [45], [11], [11]]), (91, [[91], [45], [11], [11]]), (91, [[45], [22], [11], [11]]), (118, [[236], [59], [14], [14]]), (118, [[118], [59], [14], [14]]), (118, [[59], [29], [14], [14]])], batch sizes: [32, 64], learning_rates: [0.001, 0.005, 0.01]
Using 2 presets
2025-05-11 02:21:19,084 - modnet - INFO - Multiprocessing on 32 cores. Total of 128 cores available.

  0%|          | 0/10 [00:00<?, ?it/s]2025-05-11 02:21:22,752 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:21:22,759 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:21:22,768 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:21:22,776 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:21:22,789 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:21:22,798 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:21:22,821 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:21:22,828 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:21:22,847 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:21:22,855 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:21:22,871 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:21:22,879 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:21:22,893 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:21:22,900 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:21:22,934 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:21:22,943 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:21:22,947 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:21:22,955 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:21:22,956 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:21:22,963 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:21:42,807 - modnet - INFO - loss: 61.4112	mae: 61.4112	val_loss: 133.4469	val_mae: 133.4469	
2025-05-11 02:21:42,807 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:21:43,066 - modnet - INFO - loss: 69.0185	mae: 69.0185	val_loss: 142.9987	val_mae: 142.9987	
2025-05-11 02:21:43,066 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:21:43,146 - modnet - INFO - loss: 54.6529	mae: 54.6529	val_loss: 153.6270	val_mae: 153.6270	
2025-05-11 02:21:43,146 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:21:43,154 - modnet - INFO - loss: 47.2157	mae: 47.2157	val_loss: 126.3589	val_mae: 126.3589	
2025-05-11 02:21:43,154 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:21:43,219 - modnet - INFO - loss: 60.4279	mae: 60.4279	val_loss: 161.1210	val_mae: 161.1210	
2025-05-11 02:21:43,219 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:21:43,600 - modnet - INFO - loss: 52.9489	mae: 52.9489	val_loss: 134.3744	val_mae: 134.3744	
2025-05-11 02:21:43,600 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:21:43,713 - modnet - INFO - loss: 67.3680	mae: 67.3680	val_loss: 147.5005	val_mae: 147.5005	
2025-05-11 02:21:43,713 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:21:44,071 - modnet - INFO - loss: 65.9795	mae: 65.9795	val_loss: 150.4317	val_mae: 150.4317	
2025-05-11 02:21:44,071 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:21:44,080 - modnet - INFO - loss: 42.8745	mae: 42.8745	val_loss: 134.1248	val_mae: 134.1248	
2025-05-11 02:21:44,081 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:21:44,134 - modnet - INFO - loss: 58.8699	mae: 58.8699	val_loss: 142.8446	val_mae: 142.8446	
2025-05-11 02:21:44,134 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:22:01,716 - modnet - INFO - loss: 68.3149	mae: 68.3149	val_loss: 138.2204	val_mae: 138.2204	
2025-05-11 02:22:01,716 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:22:01,910 - modnet - INFO - loss: 41.1515	mae: 41.1515	val_loss: 121.2488	val_mae: 121.2488	
2025-05-11 02:22:01,911 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:22:02,059 - modnet - INFO - loss: 71.5923	mae: 71.5923	val_loss: 134.1945	val_mae: 134.1945	
2025-05-11 02:22:02,060 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:22:02,792 - modnet - INFO - loss: 58.8083	mae: 58.8083	val_loss: 156.8905	val_mae: 156.8905	
2025-05-11 02:22:02,792 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:22:02,812 - modnet - INFO - loss: 57.3206	mae: 57.3206	val_loss: 157.6015	val_mae: 157.6015	
2025-05-11 02:22:02,812 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:22:02,962 - modnet - INFO - loss: 58.1765	mae: 58.1765	val_loss: 142.8516	val_mae: 142.8516	
2025-05-11 02:22:02,962 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:22:03,042 - modnet - INFO - loss: 61.7554	mae: 61.7554	val_loss: 149.2056	val_mae: 149.2056	
2025-05-11 02:22:03,042 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:22:03,258 - modnet - INFO - loss: 60.7050	mae: 60.7050	val_loss: 133.4818	val_mae: 133.4818	
2025-05-11 02:22:03,258 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:22:03,405 - modnet - INFO - loss: 45.9547	mae: 45.9547	val_loss: 136.0245	val_mae: 136.0245	
2025-05-11 02:22:03,405 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:22:04,024 - modnet - INFO - loss: 68.4600	mae: 68.4600	val_loss: 145.9855	val_mae: 145.9855	
2025-05-11 02:22:04,024 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:22:20,677 - modnet - INFO - loss: 68.9608	mae: 68.9608	val_loss: 143.1710	val_mae: 143.1710	
2025-05-11 02:22:20,677 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:22:20,818 - modnet - INFO - loss: 45.0902	mae: 45.0902	val_loss: 125.0989	val_mae: 125.0989	
2025-05-11 02:22:20,818 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:22:21,551 - modnet - INFO - loss: 69.5998	mae: 69.5998	val_loss: 139.8824	val_mae: 139.8824	
2025-05-11 02:22:21,552 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:22:21,824 - modnet - INFO - loss: 67.1917	mae: 67.1917	val_loss: 154.5248	val_mae: 154.5248	
2025-05-11 02:22:21,824 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:22:21,944 - modnet - INFO - loss: 58.9178	mae: 58.9178	val_loss: 144.8662	val_mae: 144.8662	
2025-05-11 02:22:21,944 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:22:22,143 - modnet - INFO - loss: 60.1940	mae: 60.1940	val_loss: 159.2986	val_mae: 159.2986	
2025-05-11 02:22:22,143 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:22:22,375 - modnet - INFO - loss: 57.4471	mae: 57.4471	val_loss: 143.2047	val_mae: 143.2047	
2025-05-11 02:22:22,376 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:22:22,428 - modnet - INFO - loss: 46.7227	mae: 46.7227	val_loss: 124.8636	val_mae: 124.8636	
2025-05-11 02:22:22,429 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:22:22,684 - modnet - INFO - loss: 56.6506	mae: 56.6506	val_loss: 159.9579	val_mae: 159.9579	
2025-05-11 02:22:22,684 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:22:23,933 - modnet - INFO - loss: 56.7102	mae: 56.7102	val_loss: 148.6546	val_mae: 148.6546	
2025-05-11 02:22:23,933 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:22:39,804 - modnet - INFO - loss: 69.9453	mae: 69.9453	val_loss: 137.0272	val_mae: 137.0272	
2025-05-11 02:22:39,804 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:22:40,866 - modnet - INFO - loss: 44.6923	mae: 44.6923	val_loss: 125.2683	val_mae: 125.2683	
2025-05-11 02:22:40,866 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:22:41,051 - modnet - INFO - loss: 70.1112	mae: 70.1112	val_loss: 136.1465	val_mae: 136.1465	
2025-05-11 02:22:41,051 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:22:41,953 - modnet - INFO - loss: 57.9207	mae: 57.9207	val_loss: 140.9209	val_mae: 140.9209	
2025-05-11 02:22:41,954 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:22:42,002 - modnet - INFO - loss: 74.0876	mae: 74.0876	val_loss: 159.0112	val_mae: 159.0112	
2025-05-11 02:22:42,003 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:22:42,087 - modnet - INFO - loss: 60.6314	mae: 60.6314	val_loss: 147.5178	val_mae: 147.5178	
2025-05-11 02:22:42,087 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:22:42,799 - modnet - INFO - loss: 57.4144	mae: 57.4144	val_loss: 157.3199	val_mae: 157.3199	
2025-05-11 02:22:42,799 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:22:43,269 - modnet - INFO - loss: 45.7749	mae: 45.7749	val_loss: 130.6361	val_mae: 130.6361	
2025-05-11 02:22:43,269 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:22:43,474 - modnet - INFO - loss: 54.2821	mae: 54.2821	val_loss: 154.2719	val_mae: 154.2719	
2025-05-11 02:22:43,474 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:22:44,718 - modnet - INFO - loss: 59.4788	mae: 59.4788	val_loss: 141.5465	val_mae: 141.5465	
2025-05-11 02:22:44,718 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:22:59,422 - modnet - INFO - loss: 40.9510	mae: 40.9510	val_loss: 125.5937	val_mae: 125.5937	
2025-05-11 02:22:59,557 - modnet - INFO - loss: 70.3897	mae: 70.3897	val_loss: 140.7843	val_mae: 140.7843	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f65b0937040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:22:59,669 - modnet - INFO - Preset #0 fitting finished, loss: 124.71371816406251

 10%|█         | 1/10 [01:40<15:06, 100.69s/it]WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f47b00a4ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:22:59,799 - modnet - INFO - Preset #1 fitting finished, loss: 138.52994267578126

 20%|██        | 2/10 [01:40<05:32, 41.53s/it] 2025-05-11 02:23:00,355 - modnet - INFO - loss: 63.4733	mae: 63.4733	val_loss: 130.2050	val_mae: 130.2050	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f1ff8548040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:23:00,595 - modnet - INFO - Preset #0 fitting finished, loss: 136.68541552734376

 30%|███       | 3/10 [01:41<02:40, 22.93s/it]2025-05-11 02:23:01,021 - modnet - INFO - loss: 60.4302	mae: 60.4302	val_loss: 144.4194	val_mae: 144.4194	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7fdd41386ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:23:01,272 - modnet - INFO - Preset #1 fitting finished, loss: 142.29367500000004

 40%|████      | 4/10 [01:42<01:24, 14.15s/it]2025-05-11 02:23:01,500 - modnet - INFO - loss: 55.8356	mae: 55.8356	val_loss: 145.2596	val_mae: 145.2596	
2025-05-11 02:23:01,548 - modnet - INFO - loss: 65.4684	mae: 65.4684	val_loss: 145.8324	val_mae: 145.8324	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f39c4786ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:23:01,734 - modnet - INFO - Preset #0 fitting finished, loss: 141.65454470214846
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f29a8505ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.

 50%|█████     | 5/10 [01:42<00:46,  9.21s/it]2025-05-11 02:23:01,843 - modnet - INFO - Preset #0 fitting finished, loss: 151.80112197265626
2025-05-11 02:23:02,499 - modnet - INFO - loss: 60.5808	mae: 60.5808	val_loss: 156.0761	val_mae: 156.0761	
2025-05-11 02:23:02,710 - modnet - INFO - loss: 59.4759	mae: 59.4759	val_loss: 160.0259	val_mae: 160.0259	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f7278148040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:23:02,728 - modnet - INFO - Preset #0 fitting finished, loss: 156.16467875976565

 70%|███████   | 7/10 [01:43<00:14,  4.79s/it]2025-05-11 02:23:02,849 - modnet - INFO - loss: 48.1322	mae: 48.1322	val_loss: 114.5110	val_mae: 114.5110	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f5462a4aee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:23:02,950 - modnet - INFO - Preset #1 fitting finished, loss: 159.07338066406254

 80%|████████  | 8/10 [01:43<00:07,  3.57s/it]WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f540a2c8ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:23:03,082 - modnet - INFO - Preset #1 fitting finished, loss: 128.0319958496094

 90%|█████████ | 9/10 [01:44<00:02,  2.63s/it]2025-05-11 02:23:04,408 - modnet - INFO - loss: 55.8835	mae: 55.8835	val_loss: 139.5998	val_mae: 139.5998	
WARNING:tensorflow:5 out of the last 9 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7fbf68c06ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 02:23:04,643 - modnet - INFO - Preset #1 fitting finished, loss: 144.6574081542969

100%|██████████| 10/10 [01:45<00:00,  2.32s/it]
100%|██████████| 10/10 [01:45<00:00, 10.56s/it]
2025-05-11 02:23:05,979 - modnet - INFO - Preset #1 resulted in lowest validation loss with params {'train_data': <modnet.preprocessing.MODData object at 0x7f997ea7bcd0>, 'targets': [[['yield_strength']]], 'weights': {'yield_strength': 1}, 'n_models': 5, 'num_classes': {'yield_strength': 0}, 'n_feat': 64, 'num_neurons': [[128], [32], [8], [8]], 'lr': 0.001, 'batch_size': 32, 'epochs': 1000, 'loss': 'mae', 'act': 'elu', 'out_act': 'linear', 'callbacks': [<tensorflow.python.keras.callbacks.EarlyStopping object at 0x7f997ea7b040>], 'preset_id': 0, 'fold_id': 0, 'verbose': 0, 'val_data': <modnet.preprocessing.MODData object at 0x7f997ea7b6d0>}
2025-05-11 02:23:07,289 - modnet - INFO - Model successfully saved as results/best_model_fold_4.pkl!
Saved best model for fold 4 to results/best_model_fold_4.pkl
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99771dab80> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f98d6eac4c0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976cb4700> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977112af0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976d53310> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976e69f70> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99771124c0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976cb44c0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99770b9ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99770b9d30> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997c0c3160> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99770b91f0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9977112040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f43700> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997bfb81f0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e919f70> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976d539d0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f99770b9940> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976cfb1f0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976cfb4c0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976d81430> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e919d30> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976f43310> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e838550> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e7b31f0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976b6d040> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e838670> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976cfbca0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e7e2ee0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976d9a280> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976d9a0d0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e7e28b0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e7b33a0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997c24f1f0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997eaeea60> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997eaee3a0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997c24f430> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e8ed820> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976e17280> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976fac160> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976e17430> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997e7e2a60> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976fac5e0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976b991f0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997c095790> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f997c0954c0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976fac8b0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f98d6ed1310> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976fee0d0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
WARNING:tensorflow:6 out of the last 11 calls to <function Model.make_predict_function.<locals>.predict_function at 0x7f9976fee3a0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has experimental_relax_shapes=True option that relaxes argument shapes that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
[Fold 4] MAE = 114.271 , MedianAE = 77.353 , MAPE = 100000000000000000000.000, √MSE = 173.194 
[Fold 4] MaxAE = 786.747 , slope = 0.95, R = 0.89
Fold 4 metrics saved.
Saved fold 4 complete results to results/fold_4_results.pkl
Training fold 5 ...
Processing fold 5 ...
2025-05-11 02:23:09,365 - modnet - INFO - Proceeding with grid search: archs: [(64, [[128], [32], [8], [8]]), (64, [[64], [32], [8], [8]]), (64, [[32], [16], [8], [8]]), (91, [[182], [45], [11], [11]]), (91, [[91], [45], [11], [11]]), (91, [[45], [22], [11], [11]]), (118, [[236], [59], [14], [14]]), (118, [[118], [59], [14], [14]]), (118, [[59], [29], [14], [14]])], batch sizes: [32, 64], learning_rates: [0.001, 0.005, 0.01]
Using 2 presets
2025-05-11 02:23:09,887 - modnet - INFO - Multiprocessing on 32 cores. Total of 128 cores available.

  0%|          | 0/10 [00:00<?, ?it/s]2025-05-11 02:23:13,405 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:23:13,413 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:23:13,502 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:23:13,509 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:23:13,524 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:23:13,530 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:23:13,532 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:23:13,538 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:23:13,556 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:23:13,564 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:23:13,591 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:23:13,598 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:23:13,601 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:23:13,608 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:23:13,634 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:23:13,641 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:23:13,658 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:23:13,666 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:23:13,674 - modnet - INFO - Generating bootstrap data...
2025-05-11 02:23:13,681 - modnet - INFO - Bootstrap fitting model #1/5
2025-05-11 02:23:32,951 - modnet - INFO - loss: 60.1237	mae: 60.1237	val_loss: 236.5303	val_mae: 236.5303	
2025-05-11 02:23:32,951 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:23:33,429 - modnet - INFO - loss: 66.2751	mae: 66.2751	val_loss: 104.1918	val_mae: 104.1918	
2025-05-11 02:23:33,430 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:23:33,430 - modnet - INFO - loss: 33.9846	mae: 33.9846	val_loss: 115.0757	val_mae: 115.0757	
2025-05-11 02:23:33,430 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:23:33,538 - modnet - INFO - loss: 38.2714	mae: 38.2714	val_loss: 122.5143	val_mae: 122.5143	
2025-05-11 02:23:33,538 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:23:33,712 - modnet - INFO - loss: 72.1372	mae: 72.1372	val_loss: 149.5964	val_mae: 149.5964	
2025-05-11 02:23:33,713 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:23:33,847 - modnet - INFO - loss: 67.1831	mae: 67.1831	val_loss: 145.5048	val_mae: 145.5048	
2025-05-11 02:23:33,848 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:23:34,137 - modnet - INFO - loss: 58.9294	mae: 58.9294	val_loss: 224.5610	val_mae: 224.5610	
2025-05-11 02:23:34,137 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:23:34,356 - modnet - INFO - loss: 62.4458	mae: 62.4458	val_loss: 107.0797	val_mae: 107.0797	
2025-05-11 02:23:34,356 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:23:34,456 - modnet - INFO - loss: 42.3555	mae: 42.3555	val_loss: 128.2067	val_mae: 128.2067	
2025-05-11 02:23:34,456 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:23:34,537 - modnet - INFO - loss: 47.7574	mae: 47.7574	val_loss: 125.9849	val_mae: 125.9849	
2025-05-11 02:23:34,537 - modnet - INFO - Bootstrap fitting model #2/5
2025-05-11 02:23:51,470 - modnet - INFO - loss: 63.3577	mae: 63.3577	val_loss: 232.4294	val_mae: 232.4294	
2025-05-11 02:23:51,471 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:23:51,910 - modnet - INFO - loss: 36.7473	mae: 36.7473	val_loss: 117.4544	val_mae: 117.4544	
2025-05-11 02:23:51,910 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:23:52,327 - modnet - INFO - loss: 62.9397	mae: 62.9397	val_loss: 106.5309	val_mae: 106.5309	
2025-05-11 02:23:52,327 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:23:52,645 - modnet - INFO - loss: 35.1054	mae: 35.1054	val_loss: 122.9098	val_mae: 122.9098	
2025-05-11 02:23:52,645 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:23:52,910 - modnet - INFO - loss: 72.2151	mae: 72.2151	val_loss: 148.9702	val_mae: 148.9702	
2025-05-11 02:23:52,911 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:23:53,051 - modnet - INFO - loss: 60.7364	mae: 60.7364	val_loss: 111.4960	val_mae: 111.4960	
2025-05-11 02:23:53,052 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:23:53,097 - modnet - INFO - loss: 51.7118	mae: 51.7118	val_loss: 236.0533	val_mae: 236.0533	
2025-05-11 02:23:53,097 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:23:53,308 - modnet - INFO - loss: 58.5134	mae: 58.5134	val_loss: 131.0352	val_mae: 131.0352	
2025-05-11 02:23:53,308 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:23:53,852 - modnet - INFO - loss: 43.1981	mae: 43.1981	val_loss: 126.9097	val_mae: 126.9097	
2025-05-11 02:23:53,852 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:23:54,353 - modnet - INFO - loss: 42.1663	mae: 42.1663	val_loss: 135.8989	val_mae: 135.8989	
2025-05-11 02:23:54,353 - modnet - INFO - Bootstrap fitting model #3/5
2025-05-11 02:24:10,339 - modnet - INFO - loss: 57.0383	mae: 57.0383	val_loss: 222.2959	val_mae: 222.2959	
2025-05-11 02:24:10,340 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:24:10,676 - modnet - INFO - loss: 35.1255	mae: 35.1255	val_loss: 116.6859	val_mae: 116.6859	
2025-05-11 02:24:10,677 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:24:11,285 - modnet - INFO - loss: 63.5055	mae: 63.5055	val_loss: 128.0042	val_mae: 128.0042	
2025-05-11 02:24:11,285 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:24:11,541 - modnet - INFO - loss: 52.8274	mae: 52.8274	val_loss: 114.1419	val_mae: 114.1419	
2025-05-11 02:24:11,541 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:24:11,738 - modnet - INFO - loss: 33.1162	mae: 33.1162	val_loss: 120.8546	val_mae: 120.8546	
2025-05-11 02:24:11,738 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:24:12,229 - modnet - INFO - loss: 57.8429	mae: 57.8429	val_loss: 219.7335	val_mae: 219.7335	
2025-05-11 02:24:12,229 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:24:12,387 - modnet - INFO - loss: 67.6570	mae: 67.6570	val_loss: 109.6295	val_mae: 109.6295	
2025-05-11 02:24:12,387 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:24:12,458 - modnet - INFO - loss: 72.6200	mae: 72.6200	val_loss: 145.4838	val_mae: 145.4838	
2025-05-11 02:24:12,459 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:24:13,040 - modnet - INFO - loss: 50.1831	mae: 50.1831	val_loss: 124.5326	val_mae: 124.5326	
2025-05-11 02:24:13,040 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:24:13,778 - modnet - INFO - loss: 43.4483	mae: 43.4483	val_loss: 133.6947	val_mae: 133.6947	
2025-05-11 02:24:13,778 - modnet - INFO - Bootstrap fitting model #4/5
2025-05-11 02:24:29,327 - modnet - INFO - loss: 57.7155	mae: 57.7155	val_loss: 224.7677	val_mae: 224.7677	
2025-05-11 02:24:29,327 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:24:29,948 - modnet - INFO - loss: 37.4674	mae: 37.4674	val_loss: 118.4669	val_mae: 118.4669	
2025-05-11 02:24:29,948 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:24:30,930 - modnet - INFO - loss: 42.3262	mae: 42.3262	val_loss: 120.9689	val_mae: 120.9689	
2025-05-11 02:24:30,930 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:24:31,134 - modnet - INFO - loss: 69.5778	mae: 69.5778	val_loss: 112.6720	val_mae: 112.6720	
2025-05-11 02:24:31,135 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:24:31,235 - modnet - INFO - loss: 65.9799	mae: 65.9799	val_loss: 137.3981	val_mae: 137.3981	
2025-05-11 02:24:31,235 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:24:32,003 - modnet - INFO - loss: 61.7021	mae: 61.7021	val_loss: 222.0323	val_mae: 222.0323	
2025-05-11 02:24:32,003 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:24:32,494 - modnet - INFO - loss: 55.7778	mae: 55.7778	val_loss: 113.4386	val_mae: 113.4386	
2025-05-11 02:24:32,495 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:24:32,841 - modnet - INFO - loss: 41.6496	mae: 41.6496	val_loss: 124.2914	val_mae: 124.2914	
2025-05-11 02:24:32,841 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:24:33,102 - modnet - INFO - loss: 69.4101	mae: 69.4101	val_loss: 133.3003	val_mae: 133.3003	
2025-05-11 02:24:33,102 - modnet - INFO - Bootstrap fitting model #5/5
2025-05-11 02:24:34,258 - modnet - INFO - loss: 47.4037	mae: 47.4037	val_loss: 126.8849	val_mae: 126.8849	
2025-05-11 02:24:34,258 - modnet - INFO - Bootstrap fitting model #5/5
slurmstepd: error: *** JOB 8421193 ON cnm021 CANCELLED AT 2025-05-11T02:24:41 ***

Resources Used

Total Memory used                        - MEM              : 9.7GiB
Total CPU Time                           - CPU_Time         : 05:13:04
Execution Time                           - Wall_Time        : 00:09:47
total programme cpu time                 - Total_CPU        : 01:37:33
Total_CPU / CPU_Time  (%)                - ETA              : 31%
Number of alloc CPU                      - NCPUS            : 32
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 32
Mobilized Resources x Execution Time     - R_Wall_Time      : 05:13:04
CPU_Time / R_Wall_Time (%)               - ALPHA            : 100%
Energy (Joules)                                             : unknown

