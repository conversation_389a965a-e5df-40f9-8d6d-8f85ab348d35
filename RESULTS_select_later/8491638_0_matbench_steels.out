Job started on Thu May 15 12:32:01 CEST 2025
Running on node(s): cnm030
2025-05-15 12:32:04.485771: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 32 jobs
Preparing nested CV run for task 'matbench_steels_matminer_roost_lmp'
Found 1 matching files for matbench_steels_matminer_roost_lmp
2025-05-15 12:32:08,447 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff51d563b80> object, created with modnet version 0.1.13
Preparing fold 1 ...
2025-05-15 12:32:08,469 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff522dfc190> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_steels_matminer_roost_lmp_train_moddata_f1
2025-05-15 12:32:08,494 - modnet - INFO - Data successfully saved as folds/matbench_steels_matminer_roost_lmp_train_moddata_f1!
Preparing fold 2 ...
2025-05-15 12:32:08,516 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff51d304190> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_steels_matminer_roost_lmp_train_moddata_f2
2025-05-15 12:32:08,541 - modnet - INFO - Data successfully saved as folds/matbench_steels_matminer_roost_lmp_train_moddata_f2!
Preparing fold 3 ...
2025-05-15 12:32:08,564 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff51d3a2640> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_steels_matminer_roost_lmp_train_moddata_f3
2025-05-15 12:32:08,695 - modnet - INFO - Data successfully saved as folds/matbench_steels_matminer_roost_lmp_train_moddata_f3!
Preparing fold 4 ...
2025-05-15 12:32:08,716 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff51d391070> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_steels_matminer_roost_lmp_train_moddata_f4
2025-05-15 12:32:08,742 - modnet - INFO - Data successfully saved as folds/matbench_steels_matminer_roost_lmp_train_moddata_f4!
Preparing fold 5 ...
2025-05-15 12:32:08,763 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7ff51c10cb20> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_steels_matminer_roost_lmp_train_moddata_f5
2025-05-15 12:32:08,789 - modnet - INFO - Data successfully saved as folds/matbench_steels_matminer_roost_lmp_train_moddata_f5!
Training fold 1 ...
Fold 1 already completed. Loading saved results.
2025-05-15 12:32:08.824428: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:32:08.824921: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/8.4.1.50-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-15 12:32:08.824948: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-15 12:32:08.824977: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm030.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-15 12:32:08.825198: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-15 12:32:09,795 - modnet - INFO - Loaded <modnet.models.ensemble.EnsembleMODNetModel object at 0x7ff51d563eb0> object, created with modnet version 0.1.13
Loaded saved model from results/matbench_steels_matminer_roost_lmp_best_model_fold_1.pkl
Training fold 2 ...
Fold 2 already completed. Loading saved results.
2025-05-15 12:32:10,919 - modnet - INFO - Loaded <modnet.models.ensemble.EnsembleMODNetModel object at 0x7ff51957fac0> object, created with modnet version 0.1.13
Loaded saved model from results/matbench_steels_matminer_roost_lmp_best_model_fold_2.pkl
Training fold 3 ...
Fold 3 already completed. Loading saved results.
2025-05-15 12:32:11,916 - modnet - INFO - Loaded <modnet.models.ensemble.EnsembleMODNetModel object at 0x7ff518e0bc40> object, created with modnet version 0.1.13
Loaded saved model from results/matbench_steels_matminer_roost_lmp_best_model_fold_3.pkl
Training fold 4 ...
Fold 4 already completed. Loading saved results.
2025-05-15 12:32:12,915 - modnet - INFO - Loaded <modnet.models.ensemble.EnsembleMODNetModel object at 0x7ff5186e9cd0> object, created with modnet version 0.1.13
Loaded saved model from results/matbench_steels_matminer_roost_lmp_best_model_fold_4.pkl
Training fold 5 ...
Fold 5 already completed. Loading saved results.
2025-05-15 12:32:13,918 - modnet - INFO - Loaded <modnet.models.ensemble.EnsembleMODNetModel object at 0x7ff51b72a9a0> object, created with modnet version 0.1.13
Loaded saved model from results/matbench_steels_matminer_roost_lmp_best_model_fold_5.pkl
Final score =  92.20303799755685
Saved average target metrics to results/matbench_steels_matminer_roost_lmp_average_target_metrics.json
Fold predictions saved to results/matbench_steels_matminer_roost_lmp_fold_predictions.csv
2025-05-15 12:32:19,532 - modnet - INFO - Model successfully saved as final_model/matbench_steels_matminer_roost_lmp_model!
Final score =  92.20303799755685
Job finished on Thu May 15 12:32:20 CEST 2025

Resources Used

Total Memory used                        - MEM              : 0B
Total CPU Time                           - CPU_Time         : 00:10:08
Execution Time                           - Wall_Time        : 00:00:19
total programme cpu time                 - Total_CPU        : 00:15.701
Total_CPU / CPU_Time  (%)                - ETA              : 2%
Number of alloc CPU                      - NCPUS            : 32
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 48
Mobilized Resources x Execution Time     - R_Wall_Time      : 00:15:12
CPU_Time / R_Wall_Time (%)               - ALPHA            : 66%
Energy (Joules)                                             : 14129

