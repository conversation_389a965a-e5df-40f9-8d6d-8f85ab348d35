Job started on Tue May 20 15:19:14 CEST 2025
Running on node(s): cnm027
2025-05-20 15:19:28.612376: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 32 jobs
Preparing nested CV run for task 'matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso'
Found 1 matching files for matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso
2025-05-20 15:19:42,631 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fc4e3be6be0> object, created with modnet version 0.1.13
GA settings:
    Population size: 50
    Number of generations: 20
    Early stopping: 8
    Refit: False
Preparing fold 1 ...
2025-05-20 15:19:42,673 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fc4e3bf1100> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f1
2025-05-20 15:19:42,709 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f1!
Preparing fold 2 ...
2025-05-20 15:19:42,745 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fc4e3a00310> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f2
2025-05-20 15:19:42,890 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f2!
Preparing fold 3 ...
2025-05-20 15:19:42,926 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fc4e3300850> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f3
2025-05-20 15:19:42,960 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f3!
Preparing fold 4 ...
2025-05-20 15:19:42,995 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fc4e38060a0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f4
2025-05-20 15:19:43,030 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f4!
Preparing fold 5 ...
2025-05-20 15:19:43,065 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fc4e2eabc10> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f5
2025-05-20 15:19:43,099 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f5!
Training fold 1 ...
Processing fold 1 ...
2025-05-20 15:19:43.788228: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-20 15:19:43.905646: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/8.4.1.50-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-20 15:19:43.954540: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-20 15:19:43.954617: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm027.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-20 15:19:43.977694: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-20 15:19:44,287 - modnet - INFO - Targets:
2025-05-20 15:19:44,287 - modnet - INFO - 1) target: regression
Traceback (most recent call last):
  File "run_benchmark.py", line 869, in <module>
    results = benchmark(data, settings, n_jobs=n_jobs, fast=fast)
  File "run_benchmark.py", line 146, in benchmark
    return matbench_benchmark(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 147, in matbench_benchmark
    fold_results.append(train_fold(fold, *args, **model_kwargs))
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 291, in train_fold
    model = ga.run(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 694, in run
    self.calculate_rf_optimal_descriptors()
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 353, in calculate_rf_optimal_descriptors
    rf_model.fit(self.train_data.df_featurized, self.train_data.df_targets)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/ensemble/_forest.py", line 303, in fit
    X, y = self._validate_data(X, y, multi_output=True,
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/base.py", line 432, in _validate_data
    X, y = check_X_y(X, y, **check_params)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/utils/validation.py", line 72, in inner_f
    return f(**kwargs)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/utils/validation.py", line 795, in check_X_y
    X = check_array(X, accept_sparse=accept_sparse,
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/utils/validation.py", line 72, in inner_f
    return f(**kwargs)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/utils/validation.py", line 644, in check_array
    _assert_all_finite(array,
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/utils/validation.py", line 96, in _assert_all_finite
    raise ValueError(
ValueError: Input contains NaN, infinity or a value too large for dtype('float32').
Job finished on Tue May 20 15:19:45 CEST 2025

Resources Used

Total Memory used                        - MEM              : 0B
Total CPU Time                           - CPU_Time         : 00:17:04
Execution Time                           - Wall_Time        : 00:00:32
total programme cpu time                 - Total_CPU        : 00:05.786
Total_CPU / CPU_Time  (%)                - ETA              : 0%
Number of alloc CPU                      - NCPUS            : 32
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 48
Mobilized Resources x Execution Time     - R_Wall_Time      : 00:25:36
CPU_Time / R_Wall_Time (%)               - ALPHA            : 66%
Energy (Joules)                                             : 1550

