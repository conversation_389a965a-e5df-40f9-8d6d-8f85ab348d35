Job started on Wed May 14 04:59:31 CEST 2025
Running on node(s): cnm025
2025-05-14 04:59:43.164122: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 32 jobs
Preparing nested CV run for task 'matbench_jdft2d'
Found 1 matching files for matbench_jdft2d
They are ['./precomputed/matbench_jdft2d_matminer_featurizedMM2020Struct.pkl.gz']
2025-05-14 04:59:52,384 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7faf9ce6eee0> object, created with modnet version 0.1.13
Preparing fold 1 ...
2025-05-14 04:59:52,388 - modnet - INFO - Loading cross NMI from 'Features_cross' file.
2025-05-14 04:59:52,504 - modnet - WARNING - Feature mismatch between precomputed `Features_cross` and `df_featurized`. Missing columns: {'XRDPowderPattern|xrd_46', 'XRDPowderPattern|xrd_72', 'BondFractions|Al - H bond frac.', 'BondFractions|As - Rb bond frac.', 'BondFractions|Br - Ca bond frac.', 'BondFractions|As - Li bond frac.', 'BondFractions|Ag - P bond frac.', 'BondFractions|Au - Cl bond frac.', 'BondFractions|C - Ta bond frac.', 'BondFractions|Cd - Cd bond frac.', 'BondFractions|Al - Te bond frac.', 'XRDPowderPattern|xrd_84', 'BondFractions|S - Sm bond frac.', 'BondFractions|B - Dy bond frac.', 'BondFractions|Au - Se bond frac.', 'BondFractions|Ba - O bond frac.', 'XRDPowderPattern|xrd_82', 'BondFractions|Br - Rh bond frac.', 'BondFractions|Br - Br bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 28', 'BondFractions|Al - Sr bond frac.', 'XRDPowderPattern|xrd_18', 'SineCoulombMatrix|sine coulomb matrix eig 23', 'BondFractions|Br - Cr bond frac.', 'BondFractions|Cl - Th bond frac.', 'BondFractions|Au - Sn bond frac.', 'BondFractions|Br - F bond frac.', 'BondFractions|Bi - P bond frac.', 'BondFractions|Br - O bond frac.', 'BondFractions|Ag - Ag bond frac.', 'BondFractions|B - Br bond frac.', 'XRDPowderPattern|xrd_2', 'BondFractions|Ag - N bond frac.', 'BondFractions|Ag - Sc bond frac.', 'XRDPowderPattern|xrd_70', 'XRDPowderPattern|xrd_64', 'BondFractions|Bi - Ge bond frac.', 'BondFractions|Nb - S bond frac.', 'BondFractions|Au - C bond frac.', 'XRDPowderPattern|xrd_90', 'BondFractions|Au - Rb bond frac.', 'BondFractions|Br - Pd bond frac.', 'BondFractions|As - Si bond frac.', 'BondFractions|Al - I bond frac.', 'BondFractions|Br - Mo bond frac.', 'BondFractions|Br - Mg bond frac.', 'BondFractions|B - Ba bond frac.', 'XRDPowderPattern|xrd_98', 'BondFractions|As - Zn bond frac.', 'BondFractions|Ba - Se bond frac.', 'BondFractions|Br - Lu bond frac.', 'BondFractions|Bi - Br bond frac.', 'BondFractions|Au - Au bond frac.', 'BondFractions|Au - K bond frac.', 'XRDPowderPattern|xrd_32', 'BondFractions|Ca - I bond frac.', 'BondFractions|Ag - Re bond frac.', 'BondFractions|Bi - Sr bond frac.', 'BondFractions|As - Te bond frac.', 'BondFractions|As - S bond frac.', 'BondFractions|Ni - Te bond frac.', 'BondFractions|Bi - In bond frac.', 'BondFractions|Bi - Cu bond frac.', 'BondFractions|As - Se bond frac.', 'BondFractions|Au - Br bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 29', 'XRDPowderPattern|xrd_62', 'BondFractions|Ca - Pb bond frac.', 'Miedema|Miedema_deltaH_amor', 'BondFractions|Br - Cu bond frac.', 'BondFractions|Ca - Sn bond frac.', 'BondFractions|Ba - F bond frac.', 'BondFractions|Br - Dy bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 26', 'XRDPowderPattern|xrd_68', 'BondFractions|C - Tb bond frac.', 'BondFractions|Cl - Y bond frac.', 'XRDPowderPattern|xrd_58', 'SineCoulombMatrix|sine coulomb matrix eig 33', 'BondFractions|C - Ho bond frac.', 'BondFractions|Ag - I bond frac.', 'BondFractions|Br - Nd bond frac.', 'XRDPowderPattern|xrd_48', 'BondFractions|Se - Ta bond frac.', 'BondFractions|Br - Ho bond frac.', 'BondFractions|Ba - Cl bond frac.', 'XRDPowderPattern|xrd_26', 'BondFractions|Br - Hg bond frac.', 'BondFractions|Bi - Cl bond frac.', 'BondFractions|As - Cl bond frac.', 'XRDPowderPattern|xrd_24', 'SineCoulombMatrix|sine coulomb matrix eig 30', 'BondFractions|Ag - Ga bond frac.', 'BondFractions|Te - W bond frac.', 'BondFractions|Br - U bond frac.', 'BondFractions|Br - Nb bond frac.', 'XRDPowderPattern|xrd_14', 'BondFractions|Ba - Ba bond frac.', 'BondFractions|Au - P bond frac.', 'BondFractions|Ca - H bond frac.', 'BondFractions|Ba - Br bond frac.', 'BondFractions|Ag - O bond frac.', 'BondFractions|Ag - Se bond frac.', 'BondFractions|Au - Mg bond frac.', 'BondFractions|As - O bond frac.', 'BondFractions|Br - Pr bond frac.', 'BondFractions|Bi - Bi bond frac.', 'BondFractions|Br - N bond frac.', 'BondFractions|Br - Ru bond frac.', 'BondFractions|I - Sb bond frac.', 'BondFractions|Al - Se bond frac.', 'BondFractions|Br - H bond frac.', 'BondFractions|Au - Te bond frac.', 'BondFractions|Pd - Se bond frac.', 'XRDPowderPattern|xrd_16', 'BondFractions|Ba - Sb bond frac.', 'BondFractions|Ag - K bond frac.', 'BondFractions|Au - S bond frac.', 'XRDPowderPattern|xrd_66', 'BondFractions|As - Hg bond frac.', 'XRDPowderPattern|xrd_8', 'BondFractions|Br - Hf bond frac.', 'XRDPowderPattern|xrd_86', 'BondFractions|Br - P bond frac.', 'XRDPowderPattern|xrd_34', 'XRDPowderPattern|xrd_12', 'XRDPowderPattern|xrd_76', 'BondFractions|C - Y bond frac.', 'XRDPowderPattern|xrd_6', 'Miedema|Miedema_deltaH_ss_min', 'SineCoulombMatrix|sine coulomb matrix eig 19', 'XRDPowderPattern|xrd_40', 'BondFractions|P - Sn bond frac.', 'XRDPowderPattern|xrd_28', 'BondFractions|B - Se bond frac.', 'BondFractions|Br - C bond frac.', 'BondFractions|Bi - Te bond frac.', 'BondFractions|Bi - F bond frac.', 'BondFractions|As - Ce bond frac.', 'BondFractions|As - Mn bond frac.', 'BondFractions|As - Br bond frac.', 'BondFractions|Br - Ir bond frac.', 'XRDPowderPattern|xrd_52', 'XRDPowderPattern|xrd_10', 'BondFractions|Se - Se bond frac.', 'BondFractions|As - Na bond frac.', 'BondFractions|Ba - I bond frac.', 'BondFractions|Ge - I bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 32', 'BondFractions|Br - Rb bond frac.', 'BondFractions|Ag - Tm bond frac.', 'BondFractions|Br - Co bond frac.', 'BondFractions|I - Nb bond frac.', 'BondFractions|Br - Pu bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 25', 'BondFractions|Br - Fe bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 24', 'BondFractions|Br - Te bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 27', 'BondFractions|Bi - Pt bond frac.', 'BondFractions|Br - Ni bond frac.', 'XRDPowderPattern|xrd_22', 'BondFractions|Ag - Cl bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 34', 'BondFractions|Br - In bond frac.', 'BondFractions|As - As bond frac.', 'BondFractions|Au - I bond frac.', 'BondFractions|Ag - Cr bond frac.', 'BondFractions|B - I bond frac.', 'BondFractions|Bi - I bond frac.', 'BondFractions|Bi - Se bond frac.', 'BondFractions|Br - Cd bond frac.', 'BondFractions|Ag - In bond frac.', 'BondFractions|Br - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 21', 'BondFractions|Ag - Er bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 22', 'BondFractions|Br - Yb bond frac.', 'XRDPowderPattern|xrd_38', 'SineCoulombMatrix|sine coulomb matrix eig 31', 'BondFractions|As - Sn bond frac.', 'BondFractions|Sb - Se bond frac.', 'XRDPowderPattern|xrd_60', 'BondFractions|As - K bond frac.', 'BondFractions|Bi - Pb bond frac.', 'BondFractions|Bi - S bond frac.', 'XRDPowderPattern|xrd_78', 'XRDPowderPattern|xrd_74', 'BondFractions|As - I bond frac.', 'XRDPowderPattern|xrd_94', 'BondFractions|Br - Pb bond frac.', 'XRDPowderPattern|xrd_88', 'BondFractions|Rh - Te bond frac.', 'XRDPowderPattern|xrd_50', 'BondFractions|Ba - Zr bond frac.', 'BondFractions|Bi - O bond frac.', 'BondFractions|Ag - Bi bond frac.', 'BondFractions|Ag - V bond frac.', 'BondFractions|As - Tl bond frac.', 'BondFractions|Bi - Rb bond frac.', 'XRDPowderPattern|xrd_36', 'BondFractions|Ag - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 20', 'XRDPowderPattern|xrd_80', 'BondFractions|Ag - Sn bond frac.', 'BondFractions|S - Ta bond frac.', 'XRDPowderPattern|xrd_92', 'BondFractions|Al - Br bond frac.', 'BondFractions|Ba - N bond frac.', 'BondFractions|Bi - Li bond frac.', 'XRDPowderPattern|xrd_4', 'BondFractions|Al - Pd bond frac.', 'XRDPowderPattern|xrd_30', 'BondFractions|Ba - S bond frac.', 'BondFractions|Br - Cl bond frac.', 'BondFractions|Au - N bond frac.', 'BondFractions|Br - Mn bond frac.', 'BondFractions|As - Sb bond frac.', 'XRDPowderPattern|xrd_96'}
2025-05-14 04:59:52,510 - modnet - INFO - Starting target 1/1: target ...
2025-05-14 04:59:52,510 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 04:59:59,426 - modnet - INFO - Computing optimal features...
2025-05-14 05:00:08,812 - modnet - INFO - Selected 50/555 features...
2025-05-14 05:00:17,591 - modnet - INFO - Selected 100/555 features...
2025-05-14 05:00:25,511 - modnet - INFO - Selected 150/555 features...
2025-05-14 05:00:32,627 - modnet - INFO - Selected 200/555 features...
2025-05-14 05:00:38,827 - modnet - INFO - Selected 250/555 features...
2025-05-14 05:00:44,172 - modnet - INFO - Selected 300/555 features...
2025-05-14 05:00:48,611 - modnet - INFO - Selected 350/555 features...
2025-05-14 05:00:52,125 - modnet - INFO - Selected 400/555 features...
2025-05-14 05:00:54,712 - modnet - INFO - Selected 450/555 features...
2025-05-14 05:00:56,360 - modnet - INFO - Selected 500/555 features...
2025-05-14 05:00:57,049 - modnet - INFO - Selected 550/555 features...
2025-05-14 05:00:57,065 - modnet - INFO - Done with target 1/1: target.
2025-05-14 05:00:57,065 - modnet - INFO - Merging all features...
2025-05-14 05:00:57,066 - modnet - INFO - Done.
2025-05-14 05:00:57,119 - modnet - INFO - Data successfully saved as folds/matbench_jdft2d_train_moddata_f1!
2025-05-14 05:00:57,144 - modnet - INFO - Data successfully saved as folds/matbench_jdft2d_test_moddata_f1!
Preparing fold 2 ...
2025-05-14 05:00:57,147 - modnet - INFO - Loading cross NMI from 'Features_cross' file.
2025-05-14 05:00:57,188 - modnet - WARNING - Feature mismatch between precomputed `Features_cross` and `df_featurized`. Missing columns: {'XRDPowderPattern|xrd_46', 'XRDPowderPattern|xrd_72', 'BondFractions|Al - H bond frac.', 'BondFractions|As - Rb bond frac.', 'BondFractions|Br - Ca bond frac.', 'BondFractions|As - Li bond frac.', 'BondFractions|Ag - P bond frac.', 'BondFractions|Au - Cl bond frac.', 'BondFractions|C - Ta bond frac.', 'BondFractions|Cd - Cd bond frac.', 'BondFractions|Al - Te bond frac.', 'XRDPowderPattern|xrd_84', 'BondFractions|S - Sm bond frac.', 'BondFractions|B - Dy bond frac.', 'BondFractions|Au - Se bond frac.', 'BondFractions|Ba - O bond frac.', 'XRDPowderPattern|xrd_82', 'BondFractions|Br - Rh bond frac.', 'BondFractions|Br - Br bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 28', 'BondFractions|Al - Sr bond frac.', 'XRDPowderPattern|xrd_18', 'SineCoulombMatrix|sine coulomb matrix eig 23', 'BondFractions|Br - Cr bond frac.', 'BondFractions|Cl - Th bond frac.', 'BondFractions|Au - Sn bond frac.', 'BondFractions|Br - F bond frac.', 'BondFractions|Bi - P bond frac.', 'BondFractions|Br - O bond frac.', 'BondFractions|Ag - Ag bond frac.', 'BondFractions|B - Br bond frac.', 'XRDPowderPattern|xrd_2', 'BondFractions|Ag - N bond frac.', 'BondFractions|Ag - Sc bond frac.', 'XRDPowderPattern|xrd_70', 'XRDPowderPattern|xrd_64', 'BondFractions|Bi - Ge bond frac.', 'BondFractions|Nb - S bond frac.', 'BondFractions|Au - C bond frac.', 'XRDPowderPattern|xrd_90', 'BondFractions|Au - Rb bond frac.', 'BondFractions|Br - Pd bond frac.', 'BondFractions|As - Si bond frac.', 'BondFractions|Al - I bond frac.', 'BondFractions|Br - Mo bond frac.', 'BondFractions|Br - Mg bond frac.', 'BondFractions|B - Ba bond frac.', 'XRDPowderPattern|xrd_98', 'BondFractions|As - Zn bond frac.', 'BondFractions|Ba - Se bond frac.', 'BondFractions|Br - Lu bond frac.', 'BondFractions|Bi - Br bond frac.', 'BondFractions|Au - Au bond frac.', 'BondFractions|Au - K bond frac.', 'XRDPowderPattern|xrd_32', 'BondFractions|Ca - I bond frac.', 'BondFractions|Ag - Re bond frac.', 'BondFractions|Bi - Sr bond frac.', 'BondFractions|As - Te bond frac.', 'BondFractions|As - S bond frac.', 'BondFractions|Ni - Te bond frac.', 'BondFractions|Bi - In bond frac.', 'BondFractions|Bi - Cu bond frac.', 'BondFractions|As - Se bond frac.', 'BondFractions|Au - Br bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 29', 'XRDPowderPattern|xrd_62', 'BondFractions|Ca - Pb bond frac.', 'Miedema|Miedema_deltaH_amor', 'BondFractions|Br - Cu bond frac.', 'BondFractions|Ca - Sn bond frac.', 'BondFractions|Ba - F bond frac.', 'BondFractions|Br - Dy bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 26', 'XRDPowderPattern|xrd_68', 'BondFractions|C - Tb bond frac.', 'BondFractions|Cl - Y bond frac.', 'XRDPowderPattern|xrd_58', 'SineCoulombMatrix|sine coulomb matrix eig 33', 'BondFractions|C - Ho bond frac.', 'BondFractions|Ag - I bond frac.', 'BondFractions|Br - Nd bond frac.', 'XRDPowderPattern|xrd_48', 'BondFractions|Se - Ta bond frac.', 'BondFractions|Br - Ho bond frac.', 'BondFractions|Ba - Cl bond frac.', 'XRDPowderPattern|xrd_26', 'BondFractions|Br - Hg bond frac.', 'BondFractions|Bi - Cl bond frac.', 'BondFractions|As - Cl bond frac.', 'XRDPowderPattern|xrd_24', 'SineCoulombMatrix|sine coulomb matrix eig 30', 'BondFractions|Ag - Ga bond frac.', 'BondFractions|Te - W bond frac.', 'BondFractions|Br - U bond frac.', 'BondFractions|Br - Nb bond frac.', 'XRDPowderPattern|xrd_14', 'BondFractions|Ba - Ba bond frac.', 'BondFractions|Au - P bond frac.', 'BondFractions|Ca - H bond frac.', 'BondFractions|Ba - Br bond frac.', 'BondFractions|Ag - O bond frac.', 'BondFractions|Ag - Se bond frac.', 'BondFractions|Au - Mg bond frac.', 'BondFractions|As - O bond frac.', 'BondFractions|Br - Pr bond frac.', 'BondFractions|Bi - Bi bond frac.', 'BondFractions|Br - N bond frac.', 'BondFractions|Br - Ru bond frac.', 'BondFractions|I - Sb bond frac.', 'BondFractions|Al - Se bond frac.', 'BondFractions|Br - H bond frac.', 'BondFractions|Au - Te bond frac.', 'BondFractions|Pd - Se bond frac.', 'XRDPowderPattern|xrd_16', 'BondFractions|Ba - Sb bond frac.', 'BondFractions|Ag - K bond frac.', 'BondFractions|Au - S bond frac.', 'XRDPowderPattern|xrd_66', 'BondFractions|As - Hg bond frac.', 'XRDPowderPattern|xrd_8', 'BondFractions|Br - Hf bond frac.', 'XRDPowderPattern|xrd_86', 'BondFractions|Br - P bond frac.', 'XRDPowderPattern|xrd_34', 'XRDPowderPattern|xrd_12', 'XRDPowderPattern|xrd_76', 'BondFractions|C - Y bond frac.', 'XRDPowderPattern|xrd_6', 'Miedema|Miedema_deltaH_ss_min', 'SineCoulombMatrix|sine coulomb matrix eig 19', 'XRDPowderPattern|xrd_40', 'BondFractions|P - Sn bond frac.', 'XRDPowderPattern|xrd_28', 'BondFractions|B - Se bond frac.', 'BondFractions|Br - C bond frac.', 'BondFractions|Bi - Te bond frac.', 'BondFractions|Bi - F bond frac.', 'BondFractions|As - Ce bond frac.', 'BondFractions|As - Mn bond frac.', 'BondFractions|As - Br bond frac.', 'BondFractions|Br - Ir bond frac.', 'XRDPowderPattern|xrd_52', 'XRDPowderPattern|xrd_10', 'BondFractions|Se - Se bond frac.', 'BondFractions|As - Na bond frac.', 'BondFractions|Ba - I bond frac.', 'BondFractions|Ge - I bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 32', 'BondFractions|Br - Rb bond frac.', 'BondFractions|Ag - Tm bond frac.', 'BondFractions|Br - Co bond frac.', 'BondFractions|I - Nb bond frac.', 'BondFractions|Br - Pu bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 25', 'BondFractions|Br - Fe bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 24', 'BondFractions|Br - Te bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 27', 'BondFractions|Bi - Pt bond frac.', 'BondFractions|Br - Ni bond frac.', 'XRDPowderPattern|xrd_22', 'BondFractions|Ag - Cl bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 34', 'BondFractions|Br - In bond frac.', 'BondFractions|As - As bond frac.', 'BondFractions|Au - I bond frac.', 'BondFractions|Ag - Cr bond frac.', 'BondFractions|B - I bond frac.', 'BondFractions|Bi - I bond frac.', 'BondFractions|Bi - Se bond frac.', 'BondFractions|Br - Cd bond frac.', 'BondFractions|Ag - In bond frac.', 'BondFractions|Br - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 21', 'BondFractions|Ag - Er bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 22', 'BondFractions|Br - Yb bond frac.', 'XRDPowderPattern|xrd_38', 'SineCoulombMatrix|sine coulomb matrix eig 31', 'BondFractions|As - Sn bond frac.', 'BondFractions|Sb - Se bond frac.', 'XRDPowderPattern|xrd_60', 'BondFractions|As - K bond frac.', 'BondFractions|Bi - Pb bond frac.', 'BondFractions|Bi - S bond frac.', 'XRDPowderPattern|xrd_78', 'XRDPowderPattern|xrd_74', 'BondFractions|As - I bond frac.', 'XRDPowderPattern|xrd_94', 'BondFractions|Br - Pb bond frac.', 'XRDPowderPattern|xrd_88', 'BondFractions|Rh - Te bond frac.', 'XRDPowderPattern|xrd_50', 'BondFractions|Ba - Zr bond frac.', 'BondFractions|Bi - O bond frac.', 'BondFractions|Ag - Bi bond frac.', 'BondFractions|Ag - V bond frac.', 'BondFractions|As - Tl bond frac.', 'BondFractions|Bi - Rb bond frac.', 'XRDPowderPattern|xrd_36', 'BondFractions|Ag - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 20', 'XRDPowderPattern|xrd_80', 'BondFractions|Ag - Sn bond frac.', 'BondFractions|S - Ta bond frac.', 'XRDPowderPattern|xrd_92', 'BondFractions|Al - Br bond frac.', 'BondFractions|Ba - N bond frac.', 'BondFractions|Bi - Li bond frac.', 'XRDPowderPattern|xrd_4', 'BondFractions|Al - Pd bond frac.', 'XRDPowderPattern|xrd_30', 'BondFractions|Ba - S bond frac.', 'BondFractions|Br - Cl bond frac.', 'BondFractions|Au - N bond frac.', 'BondFractions|Br - Mn bond frac.', 'BondFractions|As - Sb bond frac.', 'XRDPowderPattern|xrd_96'}
2025-05-14 05:00:57,192 - modnet - INFO - Starting target 1/1: target ...
2025-05-14 05:00:57,192 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 05:01:04,083 - modnet - INFO - Computing optimal features...
2025-05-14 05:01:13,437 - modnet - INFO - Selected 50/558 features...
2025-05-14 05:01:22,210 - modnet - INFO - Selected 100/558 features...
2025-05-14 05:01:30,183 - modnet - INFO - Selected 150/558 features...
2025-05-14 05:01:37,356 - modnet - INFO - Selected 200/558 features...
2025-05-14 05:01:43,653 - modnet - INFO - Selected 250/558 features...
2025-05-14 05:01:49,077 - modnet - INFO - Selected 300/558 features...
2025-05-14 05:01:53,581 - modnet - INFO - Selected 350/558 features...
2025-05-14 05:01:57,162 - modnet - INFO - Selected 400/558 features...
2025-05-14 05:01:59,814 - modnet - INFO - Selected 450/558 features...
2025-05-14 05:02:01,517 - modnet - INFO - Selected 500/558 features...
2025-05-14 05:02:02,266 - modnet - INFO - Selected 550/558 features...
2025-05-14 05:02:02,296 - modnet - INFO - Done with target 1/1: target.
2025-05-14 05:02:02,296 - modnet - INFO - Merging all features...
2025-05-14 05:02:02,297 - modnet - INFO - Done.
2025-05-14 05:02:02,354 - modnet - INFO - Data successfully saved as folds/matbench_jdft2d_train_moddata_f2!
2025-05-14 05:02:02,567 - modnet - INFO - Data successfully saved as folds/matbench_jdft2d_test_moddata_f2!
Preparing fold 3 ...
2025-05-14 05:02:02,572 - modnet - INFO - Loading cross NMI from 'Features_cross' file.
2025-05-14 05:02:02,618 - modnet - WARNING - Feature mismatch between precomputed `Features_cross` and `df_featurized`. Missing columns: {'XRDPowderPattern|xrd_46', 'XRDPowderPattern|xrd_72', 'BondFractions|Al - H bond frac.', 'BondFractions|As - Rb bond frac.', 'BondFractions|Br - Ca bond frac.', 'BondFractions|As - Li bond frac.', 'BondFractions|Ag - P bond frac.', 'BondFractions|Au - Cl bond frac.', 'BondFractions|C - Ta bond frac.', 'BondFractions|Cd - Cd bond frac.', 'BondFractions|Al - Te bond frac.', 'XRDPowderPattern|xrd_84', 'BondFractions|S - Sm bond frac.', 'BondFractions|B - Dy bond frac.', 'BondFractions|Au - Se bond frac.', 'BondFractions|Ba - O bond frac.', 'XRDPowderPattern|xrd_82', 'BondFractions|Br - Rh bond frac.', 'BondFractions|Br - Br bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 28', 'BondFractions|Al - Sr bond frac.', 'XRDPowderPattern|xrd_18', 'SineCoulombMatrix|sine coulomb matrix eig 23', 'BondFractions|Br - Cr bond frac.', 'BondFractions|Cl - Th bond frac.', 'BondFractions|Au - Sn bond frac.', 'BondFractions|Br - F bond frac.', 'BondFractions|Bi - P bond frac.', 'BondFractions|Br - O bond frac.', 'BondFractions|Ag - Ag bond frac.', 'BondFractions|B - Br bond frac.', 'XRDPowderPattern|xrd_2', 'BondFractions|Ag - N bond frac.', 'BondFractions|Ag - Sc bond frac.', 'XRDPowderPattern|xrd_70', 'XRDPowderPattern|xrd_64', 'BondFractions|Bi - Ge bond frac.', 'BondFractions|Nb - S bond frac.', 'BondFractions|Au - C bond frac.', 'XRDPowderPattern|xrd_90', 'BondFractions|Au - Rb bond frac.', 'BondFractions|Br - Pd bond frac.', 'BondFractions|As - Si bond frac.', 'BondFractions|Al - I bond frac.', 'BondFractions|Br - Mo bond frac.', 'BondFractions|Br - Mg bond frac.', 'BondFractions|B - Ba bond frac.', 'XRDPowderPattern|xrd_98', 'BondFractions|As - Zn bond frac.', 'BondFractions|Ba - Se bond frac.', 'BondFractions|Br - Lu bond frac.', 'BondFractions|Bi - Br bond frac.', 'BondFractions|Au - Au bond frac.', 'BondFractions|Au - K bond frac.', 'XRDPowderPattern|xrd_32', 'BondFractions|Ca - I bond frac.', 'BondFractions|Ag - Re bond frac.', 'BondFractions|Bi - Sr bond frac.', 'BondFractions|As - Te bond frac.', 'BondFractions|As - S bond frac.', 'BondFractions|Ni - Te bond frac.', 'BondFractions|Bi - In bond frac.', 'BondFractions|Bi - Cu bond frac.', 'BondFractions|As - Se bond frac.', 'BondFractions|Au - Br bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 29', 'XRDPowderPattern|xrd_62', 'BondFractions|Ca - Pb bond frac.', 'Miedema|Miedema_deltaH_amor', 'BondFractions|Br - Cu bond frac.', 'BondFractions|Ca - Sn bond frac.', 'BondFractions|Ba - F bond frac.', 'BondFractions|Br - Dy bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 26', 'XRDPowderPattern|xrd_68', 'BondFractions|C - Tb bond frac.', 'BondFractions|Cl - Y bond frac.', 'XRDPowderPattern|xrd_58', 'SineCoulombMatrix|sine coulomb matrix eig 33', 'BondFractions|C - Ho bond frac.', 'BondFractions|Ag - I bond frac.', 'BondFractions|Br - Nd bond frac.', 'XRDPowderPattern|xrd_48', 'BondFractions|Se - Ta bond frac.', 'BondFractions|Br - Ho bond frac.', 'BondFractions|Ba - Cl bond frac.', 'XRDPowderPattern|xrd_26', 'BondFractions|Br - Hg bond frac.', 'BondFractions|Bi - Cl bond frac.', 'BondFractions|As - Cl bond frac.', 'XRDPowderPattern|xrd_24', 'SineCoulombMatrix|sine coulomb matrix eig 30', 'BondFractions|Ag - Ga bond frac.', 'BondFractions|Te - W bond frac.', 'BondFractions|Br - U bond frac.', 'BondFractions|Br - Nb bond frac.', 'XRDPowderPattern|xrd_14', 'BondFractions|Ba - Ba bond frac.', 'BondFractions|Au - P bond frac.', 'BondFractions|Ca - H bond frac.', 'BondFractions|Ba - Br bond frac.', 'BondFractions|Ag - O bond frac.', 'BondFractions|Ag - Se bond frac.', 'BondFractions|Au - Mg bond frac.', 'BondFractions|As - O bond frac.', 'BondFractions|Br - Pr bond frac.', 'BondFractions|Bi - Bi bond frac.', 'BondFractions|Br - N bond frac.', 'BondFractions|Br - Ru bond frac.', 'BondFractions|I - Sb bond frac.', 'BondFractions|Al - Se bond frac.', 'BondFractions|Br - H bond frac.', 'BondFractions|Au - Te bond frac.', 'BondFractions|Pd - Se bond frac.', 'XRDPowderPattern|xrd_16', 'BondFractions|Ba - Sb bond frac.', 'BondFractions|Ag - K bond frac.', 'BondFractions|Au - S bond frac.', 'XRDPowderPattern|xrd_66', 'BondFractions|As - Hg bond frac.', 'XRDPowderPattern|xrd_8', 'BondFractions|Br - Hf bond frac.', 'XRDPowderPattern|xrd_86', 'BondFractions|Br - P bond frac.', 'XRDPowderPattern|xrd_34', 'XRDPowderPattern|xrd_12', 'XRDPowderPattern|xrd_76', 'BondFractions|C - Y bond frac.', 'XRDPowderPattern|xrd_6', 'Miedema|Miedema_deltaH_ss_min', 'SineCoulombMatrix|sine coulomb matrix eig 19', 'XRDPowderPattern|xrd_40', 'BondFractions|P - Sn bond frac.', 'XRDPowderPattern|xrd_28', 'BondFractions|B - Se bond frac.', 'BondFractions|Br - C bond frac.', 'BondFractions|Bi - Te bond frac.', 'BondFractions|Bi - F bond frac.', 'BondFractions|As - Ce bond frac.', 'BondFractions|As - Mn bond frac.', 'BondFractions|As - Br bond frac.', 'BondFractions|Br - Ir bond frac.', 'XRDPowderPattern|xrd_52', 'XRDPowderPattern|xrd_10', 'BondFractions|Se - Se bond frac.', 'BondFractions|As - Na bond frac.', 'BondFractions|Ba - I bond frac.', 'BondFractions|Ge - I bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 32', 'BondFractions|Br - Rb bond frac.', 'BondFractions|Ag - Tm bond frac.', 'BondFractions|Br - Co bond frac.', 'BondFractions|I - Nb bond frac.', 'BondFractions|Br - Pu bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 25', 'BondFractions|Br - Fe bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 24', 'BondFractions|Br - Te bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 27', 'BondFractions|Bi - Pt bond frac.', 'BondFractions|Br - Ni bond frac.', 'XRDPowderPattern|xrd_22', 'BondFractions|Ag - Cl bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 34', 'BondFractions|Br - In bond frac.', 'BondFractions|As - As bond frac.', 'BondFractions|Au - I bond frac.', 'BondFractions|Ag - Cr bond frac.', 'BondFractions|B - I bond frac.', 'BondFractions|Bi - I bond frac.', 'BondFractions|Bi - Se bond frac.', 'BondFractions|Br - Cd bond frac.', 'BondFractions|Ag - In bond frac.', 'BondFractions|Br - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 21', 'BondFractions|Ag - Er bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 22', 'BondFractions|Br - Yb bond frac.', 'XRDPowderPattern|xrd_38', 'SineCoulombMatrix|sine coulomb matrix eig 31', 'BondFractions|As - Sn bond frac.', 'BondFractions|Sb - Se bond frac.', 'XRDPowderPattern|xrd_60', 'BondFractions|As - K bond frac.', 'BondFractions|Bi - Pb bond frac.', 'BondFractions|Bi - S bond frac.', 'XRDPowderPattern|xrd_78', 'XRDPowderPattern|xrd_74', 'BondFractions|As - I bond frac.', 'XRDPowderPattern|xrd_94', 'BondFractions|Br - Pb bond frac.', 'XRDPowderPattern|xrd_88', 'BondFractions|Rh - Te bond frac.', 'XRDPowderPattern|xrd_50', 'BondFractions|Ba - Zr bond frac.', 'BondFractions|Bi - O bond frac.', 'BondFractions|Ag - Bi bond frac.', 'BondFractions|Ag - V bond frac.', 'BondFractions|As - Tl bond frac.', 'BondFractions|Bi - Rb bond frac.', 'XRDPowderPattern|xrd_36', 'BondFractions|Ag - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 20', 'XRDPowderPattern|xrd_80', 'BondFractions|Ag - Sn bond frac.', 'BondFractions|S - Ta bond frac.', 'XRDPowderPattern|xrd_92', 'BondFractions|Al - Br bond frac.', 'BondFractions|Ba - N bond frac.', 'BondFractions|Bi - Li bond frac.', 'XRDPowderPattern|xrd_4', 'BondFractions|Al - Pd bond frac.', 'XRDPowderPattern|xrd_30', 'BondFractions|Ba - S bond frac.', 'BondFractions|Br - Cl bond frac.', 'BondFractions|Au - N bond frac.', 'BondFractions|Br - Mn bond frac.', 'BondFractions|As - Sb bond frac.', 'XRDPowderPattern|xrd_96'}
2025-05-14 05:02:02,622 - modnet - INFO - Starting target 1/1: target ...
2025-05-14 05:02:02,622 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 05:02:09,272 - modnet - INFO - Computing optimal features...
2025-05-14 05:02:18,475 - modnet - INFO - Selected 50/551 features...
2025-05-14 05:02:27,077 - modnet - INFO - Selected 100/551 features...
2025-05-14 05:02:34,931 - modnet - INFO - Selected 150/551 features...
2025-05-14 05:02:41,985 - modnet - INFO - Selected 200/551 features...
2025-05-14 05:02:48,144 - modnet - INFO - Selected 250/551 features...
2025-05-14 05:02:53,419 - modnet - INFO - Selected 300/551 features...
2025-05-14 05:02:57,800 - modnet - INFO - Selected 350/551 features...
2025-05-14 05:03:01,252 - modnet - INFO - Selected 400/551 features...
2025-05-14 05:03:03,771 - modnet - INFO - Selected 450/551 features...
2025-05-14 05:03:05,342 - modnet - INFO - Selected 500/551 features...
2025-05-14 05:03:05,959 - modnet - INFO - Selected 550/551 features...
2025-05-14 05:03:05,961 - modnet - INFO - Done with target 1/1: target.
2025-05-14 05:03:05,962 - modnet - INFO - Merging all features...
2025-05-14 05:03:05,962 - modnet - INFO - Done.
2025-05-14 05:03:06,007 - modnet - INFO - Data successfully saved as folds/matbench_jdft2d_train_moddata_f3!
2025-05-14 05:03:06,031 - modnet - INFO - Data successfully saved as folds/matbench_jdft2d_test_moddata_f3!
Preparing fold 4 ...
2025-05-14 05:03:06,034 - modnet - INFO - Loading cross NMI from 'Features_cross' file.
2025-05-14 05:03:06,075 - modnet - WARNING - Feature mismatch between precomputed `Features_cross` and `df_featurized`. Missing columns: {'XRDPowderPattern|xrd_46', 'XRDPowderPattern|xrd_72', 'BondFractions|Al - H bond frac.', 'BondFractions|As - Rb bond frac.', 'BondFractions|Br - Ca bond frac.', 'BondFractions|As - Li bond frac.', 'BondFractions|Ag - P bond frac.', 'BondFractions|Au - Cl bond frac.', 'BondFractions|C - Ta bond frac.', 'BondFractions|Cd - Cd bond frac.', 'BondFractions|Al - Te bond frac.', 'XRDPowderPattern|xrd_84', 'BondFractions|S - Sm bond frac.', 'BondFractions|B - Dy bond frac.', 'BondFractions|Au - Se bond frac.', 'BondFractions|Ba - O bond frac.', 'XRDPowderPattern|xrd_82', 'BondFractions|Br - Rh bond frac.', 'BondFractions|Br - Br bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 28', 'BondFractions|Al - Sr bond frac.', 'XRDPowderPattern|xrd_18', 'SineCoulombMatrix|sine coulomb matrix eig 23', 'BondFractions|Br - Cr bond frac.', 'BondFractions|Cl - Th bond frac.', 'BondFractions|Au - Sn bond frac.', 'BondFractions|Br - F bond frac.', 'BondFractions|Bi - P bond frac.', 'BondFractions|Br - O bond frac.', 'BondFractions|Ag - Ag bond frac.', 'BondFractions|B - Br bond frac.', 'XRDPowderPattern|xrd_2', 'BondFractions|Ag - N bond frac.', 'BondFractions|Ag - Sc bond frac.', 'XRDPowderPattern|xrd_70', 'XRDPowderPattern|xrd_64', 'BondFractions|Bi - Ge bond frac.', 'BondFractions|Nb - S bond frac.', 'BondFractions|Au - C bond frac.', 'XRDPowderPattern|xrd_90', 'BondFractions|Au - Rb bond frac.', 'BondFractions|Br - Pd bond frac.', 'BondFractions|As - Si bond frac.', 'BondFractions|Al - I bond frac.', 'BondFractions|Br - Mo bond frac.', 'BondFractions|Br - Mg bond frac.', 'BondFractions|B - Ba bond frac.', 'XRDPowderPattern|xrd_98', 'BondFractions|As - Zn bond frac.', 'BondFractions|Ba - Se bond frac.', 'BondFractions|Br - Lu bond frac.', 'BondFractions|Bi - Br bond frac.', 'BondFractions|Au - Au bond frac.', 'BondFractions|Au - K bond frac.', 'XRDPowderPattern|xrd_32', 'BondFractions|Ca - I bond frac.', 'BondFractions|Ag - Re bond frac.', 'BondFractions|Bi - Sr bond frac.', 'BondFractions|As - Te bond frac.', 'BondFractions|As - S bond frac.', 'BondFractions|Ni - Te bond frac.', 'BondFractions|Bi - In bond frac.', 'BondFractions|Bi - Cu bond frac.', 'BondFractions|As - Se bond frac.', 'BondFractions|Au - Br bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 29', 'XRDPowderPattern|xrd_62', 'BondFractions|Ca - Pb bond frac.', 'Miedema|Miedema_deltaH_amor', 'BondFractions|Br - Cu bond frac.', 'BondFractions|Ca - Sn bond frac.', 'BondFractions|Ba - F bond frac.', 'BondFractions|Br - Dy bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 26', 'XRDPowderPattern|xrd_68', 'BondFractions|C - Tb bond frac.', 'BondFractions|Cl - Y bond frac.', 'XRDPowderPattern|xrd_58', 'SineCoulombMatrix|sine coulomb matrix eig 33', 'BondFractions|C - Ho bond frac.', 'BondFractions|Ag - I bond frac.', 'BondFractions|Br - Nd bond frac.', 'XRDPowderPattern|xrd_48', 'BondFractions|Se - Ta bond frac.', 'BondFractions|Br - Ho bond frac.', 'BondFractions|Ba - Cl bond frac.', 'XRDPowderPattern|xrd_26', 'BondFractions|Br - Hg bond frac.', 'BondFractions|Bi - Cl bond frac.', 'BondFractions|As - Cl bond frac.', 'XRDPowderPattern|xrd_24', 'SineCoulombMatrix|sine coulomb matrix eig 30', 'BondFractions|Ag - Ga bond frac.', 'BondFractions|Te - W bond frac.', 'BondFractions|Br - U bond frac.', 'BondFractions|Br - Nb bond frac.', 'XRDPowderPattern|xrd_14', 'BondFractions|Ba - Ba bond frac.', 'BondFractions|Au - P bond frac.', 'BondFractions|Ca - H bond frac.', 'BondFractions|Ba - Br bond frac.', 'BondFractions|Ag - O bond frac.', 'BondFractions|Ag - Se bond frac.', 'BondFractions|Au - Mg bond frac.', 'BondFractions|As - O bond frac.', 'BondFractions|Br - Pr bond frac.', 'BondFractions|Bi - Bi bond frac.', 'BondFractions|Br - N bond frac.', 'BondFractions|Br - Ru bond frac.', 'BondFractions|I - Sb bond frac.', 'BondFractions|Al - Se bond frac.', 'BondFractions|Br - H bond frac.', 'BondFractions|Au - Te bond frac.', 'BondFractions|Pd - Se bond frac.', 'XRDPowderPattern|xrd_16', 'BondFractions|Ba - Sb bond frac.', 'BondFractions|Ag - K bond frac.', 'BondFractions|Au - S bond frac.', 'XRDPowderPattern|xrd_66', 'BondFractions|As - Hg bond frac.', 'XRDPowderPattern|xrd_8', 'BondFractions|Br - Hf bond frac.', 'XRDPowderPattern|xrd_86', 'BondFractions|Br - P bond frac.', 'XRDPowderPattern|xrd_34', 'XRDPowderPattern|xrd_12', 'XRDPowderPattern|xrd_76', 'BondFractions|C - Y bond frac.', 'XRDPowderPattern|xrd_6', 'Miedema|Miedema_deltaH_ss_min', 'SineCoulombMatrix|sine coulomb matrix eig 19', 'XRDPowderPattern|xrd_40', 'BondFractions|P - Sn bond frac.', 'XRDPowderPattern|xrd_28', 'BondFractions|B - Se bond frac.', 'BondFractions|Br - C bond frac.', 'BondFractions|Bi - Te bond frac.', 'BondFractions|Bi - F bond frac.', 'BondFractions|As - Ce bond frac.', 'BondFractions|As - Mn bond frac.', 'BondFractions|As - Br bond frac.', 'BondFractions|Br - Ir bond frac.', 'XRDPowderPattern|xrd_52', 'XRDPowderPattern|xrd_10', 'BondFractions|Se - Se bond frac.', 'BondFractions|As - Na bond frac.', 'BondFractions|Ba - I bond frac.', 'BondFractions|Ge - I bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 32', 'BondFractions|Br - Rb bond frac.', 'BondFractions|Ag - Tm bond frac.', 'BondFractions|Br - Co bond frac.', 'BondFractions|I - Nb bond frac.', 'BondFractions|Br - Pu bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 25', 'BondFractions|Br - Fe bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 24', 'BondFractions|Br - Te bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 27', 'BondFractions|Bi - Pt bond frac.', 'BondFractions|Br - Ni bond frac.', 'XRDPowderPattern|xrd_22', 'BondFractions|Ag - Cl bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 34', 'BondFractions|Br - In bond frac.', 'BondFractions|As - As bond frac.', 'BondFractions|Au - I bond frac.', 'BondFractions|Ag - Cr bond frac.', 'BondFractions|B - I bond frac.', 'BondFractions|Bi - I bond frac.', 'BondFractions|Bi - Se bond frac.', 'BondFractions|Br - Cd bond frac.', 'BondFractions|Ag - In bond frac.', 'BondFractions|Br - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 21', 'BondFractions|Ag - Er bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 22', 'BondFractions|Br - Yb bond frac.', 'XRDPowderPattern|xrd_38', 'SineCoulombMatrix|sine coulomb matrix eig 31', 'BondFractions|As - Sn bond frac.', 'BondFractions|Sb - Se bond frac.', 'XRDPowderPattern|xrd_60', 'BondFractions|As - K bond frac.', 'BondFractions|Bi - Pb bond frac.', 'BondFractions|Bi - S bond frac.', 'XRDPowderPattern|xrd_78', 'XRDPowderPattern|xrd_74', 'BondFractions|As - I bond frac.', 'XRDPowderPattern|xrd_94', 'BondFractions|Br - Pb bond frac.', 'XRDPowderPattern|xrd_88', 'BondFractions|Rh - Te bond frac.', 'XRDPowderPattern|xrd_50', 'BondFractions|Ba - Zr bond frac.', 'BondFractions|Bi - O bond frac.', 'BondFractions|Ag - Bi bond frac.', 'BondFractions|Ag - V bond frac.', 'BondFractions|As - Tl bond frac.', 'BondFractions|Bi - Rb bond frac.', 'XRDPowderPattern|xrd_36', 'BondFractions|Ag - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 20', 'XRDPowderPattern|xrd_80', 'BondFractions|Ag - Sn bond frac.', 'BondFractions|S - Ta bond frac.', 'XRDPowderPattern|xrd_92', 'BondFractions|Al - Br bond frac.', 'BondFractions|Ba - N bond frac.', 'BondFractions|Bi - Li bond frac.', 'XRDPowderPattern|xrd_4', 'BondFractions|Al - Pd bond frac.', 'XRDPowderPattern|xrd_30', 'BondFractions|Ba - S bond frac.', 'BondFractions|Br - Cl bond frac.', 'BondFractions|Au - N bond frac.', 'BondFractions|Br - Mn bond frac.', 'BondFractions|As - Sb bond frac.', 'XRDPowderPattern|xrd_96'}
2025-05-14 05:03:06,079 - modnet - INFO - Starting target 1/1: target ...
2025-05-14 05:03:06,079 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 05:03:13,014 - modnet - INFO - Computing optimal features...
2025-05-14 05:03:22,404 - modnet - INFO - Selected 50/558 features...
2025-05-14 05:03:31,208 - modnet - INFO - Selected 100/558 features...
2025-05-14 05:03:39,195 - modnet - INFO - Selected 150/558 features...
2025-05-14 05:03:46,351 - modnet - INFO - Selected 200/558 features...
2025-05-14 05:03:52,637 - modnet - INFO - Selected 250/558 features...
2025-05-14 05:03:58,042 - modnet - INFO - Selected 300/558 features...
2025-05-14 05:04:02,569 - modnet - INFO - Selected 350/558 features...
2025-05-14 05:04:06,151 - modnet - INFO - Selected 400/558 features...
2025-05-14 05:04:08,793 - modnet - INFO - Selected 450/558 features...
2025-05-14 05:04:10,497 - modnet - INFO - Selected 500/558 features...
2025-05-14 05:04:11,248 - modnet - INFO - Selected 550/558 features...
2025-05-14 05:04:11,278 - modnet - INFO - Done with target 1/1: target.
2025-05-14 05:04:11,278 - modnet - INFO - Merging all features...
2025-05-14 05:04:11,279 - modnet - INFO - Done.
2025-05-14 05:04:11,318 - modnet - INFO - Data successfully saved as folds/matbench_jdft2d_train_moddata_f4!
2025-05-14 05:04:11,344 - modnet - INFO - Data successfully saved as folds/matbench_jdft2d_test_moddata_f4!
Preparing fold 5 ...
2025-05-14 05:04:11,349 - modnet - INFO - Loading cross NMI from 'Features_cross' file.
2025-05-14 05:04:11,387 - modnet - WARNING - Feature mismatch between precomputed `Features_cross` and `df_featurized`. Missing columns: {'XRDPowderPattern|xrd_46', 'XRDPowderPattern|xrd_72', 'BondFractions|Al - H bond frac.', 'BondFractions|As - Rb bond frac.', 'BondFractions|Br - Ca bond frac.', 'BondFractions|As - Li bond frac.', 'BondFractions|Ag - P bond frac.', 'BondFractions|Au - Cl bond frac.', 'BondFractions|C - Ta bond frac.', 'BondFractions|Cd - Cd bond frac.', 'BondFractions|Al - Te bond frac.', 'XRDPowderPattern|xrd_84', 'BondFractions|S - Sm bond frac.', 'BondFractions|B - Dy bond frac.', 'BondFractions|Au - Se bond frac.', 'BondFractions|Ba - O bond frac.', 'XRDPowderPattern|xrd_82', 'BondFractions|Br - Rh bond frac.', 'BondFractions|Br - Br bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 28', 'BondFractions|Al - Sr bond frac.', 'XRDPowderPattern|xrd_18', 'SineCoulombMatrix|sine coulomb matrix eig 23', 'BondFractions|Br - Cr bond frac.', 'BondFractions|Cl - Th bond frac.', 'BondFractions|Au - Sn bond frac.', 'BondFractions|Br - F bond frac.', 'BondFractions|Bi - P bond frac.', 'BondFractions|Br - O bond frac.', 'BondFractions|Ag - Ag bond frac.', 'BondFractions|B - Br bond frac.', 'XRDPowderPattern|xrd_2', 'BondFractions|Ag - N bond frac.', 'BondFractions|Ag - Sc bond frac.', 'XRDPowderPattern|xrd_70', 'XRDPowderPattern|xrd_64', 'BondFractions|Bi - Ge bond frac.', 'BondFractions|Nb - S bond frac.', 'BondFractions|Au - C bond frac.', 'XRDPowderPattern|xrd_90', 'BondFractions|Au - Rb bond frac.', 'BondFractions|Br - Pd bond frac.', 'BondFractions|As - Si bond frac.', 'BondFractions|Al - I bond frac.', 'BondFractions|Br - Mo bond frac.', 'BondFractions|Br - Mg bond frac.', 'BondFractions|B - Ba bond frac.', 'XRDPowderPattern|xrd_98', 'BondFractions|As - Zn bond frac.', 'BondFractions|Ba - Se bond frac.', 'BondFractions|Br - Lu bond frac.', 'BondFractions|Bi - Br bond frac.', 'BondFractions|Au - Au bond frac.', 'BondFractions|Au - K bond frac.', 'XRDPowderPattern|xrd_32', 'BondFractions|Ca - I bond frac.', 'BondFractions|Ag - Re bond frac.', 'BondFractions|Bi - Sr bond frac.', 'BondFractions|As - Te bond frac.', 'BondFractions|As - S bond frac.', 'BondFractions|Ni - Te bond frac.', 'BondFractions|Bi - In bond frac.', 'BondFractions|Bi - Cu bond frac.', 'BondFractions|As - Se bond frac.', 'BondFractions|Au - Br bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 29', 'XRDPowderPattern|xrd_62', 'BondFractions|Ca - Pb bond frac.', 'Miedema|Miedema_deltaH_amor', 'BondFractions|Br - Cu bond frac.', 'BondFractions|Ca - Sn bond frac.', 'BondFractions|Ba - F bond frac.', 'BondFractions|Br - Dy bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 26', 'XRDPowderPattern|xrd_68', 'BondFractions|C - Tb bond frac.', 'BondFractions|Cl - Y bond frac.', 'XRDPowderPattern|xrd_58', 'SineCoulombMatrix|sine coulomb matrix eig 33', 'BondFractions|C - Ho bond frac.', 'BondFractions|Ag - I bond frac.', 'BondFractions|Br - Nd bond frac.', 'XRDPowderPattern|xrd_48', 'BondFractions|Se - Ta bond frac.', 'BondFractions|Br - Ho bond frac.', 'BondFractions|Ba - Cl bond frac.', 'XRDPowderPattern|xrd_26', 'BondFractions|Br - Hg bond frac.', 'BondFractions|Bi - Cl bond frac.', 'BondFractions|As - Cl bond frac.', 'XRDPowderPattern|xrd_24', 'SineCoulombMatrix|sine coulomb matrix eig 30', 'BondFractions|Ag - Ga bond frac.', 'BondFractions|Te - W bond frac.', 'BondFractions|Br - U bond frac.', 'BondFractions|Br - Nb bond frac.', 'XRDPowderPattern|xrd_14', 'BondFractions|Ba - Ba bond frac.', 'BondFractions|Au - P bond frac.', 'BondFractions|Ca - H bond frac.', 'BondFractions|Ba - Br bond frac.', 'BondFractions|Ag - O bond frac.', 'BondFractions|Ag - Se bond frac.', 'BondFractions|Au - Mg bond frac.', 'BondFractions|As - O bond frac.', 'BondFractions|Br - Pr bond frac.', 'BondFractions|Bi - Bi bond frac.', 'BondFractions|Br - N bond frac.', 'BondFractions|Br - Ru bond frac.', 'BondFractions|I - Sb bond frac.', 'BondFractions|Al - Se bond frac.', 'BondFractions|Br - H bond frac.', 'BondFractions|Au - Te bond frac.', 'BondFractions|Pd - Se bond frac.', 'XRDPowderPattern|xrd_16', 'BondFractions|Ba - Sb bond frac.', 'BondFractions|Ag - K bond frac.', 'BondFractions|Au - S bond frac.', 'XRDPowderPattern|xrd_66', 'BondFractions|As - Hg bond frac.', 'XRDPowderPattern|xrd_8', 'BondFractions|Br - Hf bond frac.', 'XRDPowderPattern|xrd_86', 'BondFractions|Br - P bond frac.', 'XRDPowderPattern|xrd_34', 'XRDPowderPattern|xrd_12', 'XRDPowderPattern|xrd_76', 'BondFractions|C - Y bond frac.', 'XRDPowderPattern|xrd_6', 'Miedema|Miedema_deltaH_ss_min', 'SineCoulombMatrix|sine coulomb matrix eig 19', 'XRDPowderPattern|xrd_40', 'BondFractions|P - Sn bond frac.', 'XRDPowderPattern|xrd_28', 'BondFractions|B - Se bond frac.', 'BondFractions|Br - C bond frac.', 'BondFractions|Bi - Te bond frac.', 'BondFractions|Bi - F bond frac.', 'BondFractions|As - Ce bond frac.', 'BondFractions|As - Mn bond frac.', 'BondFractions|As - Br bond frac.', 'BondFractions|Br - Ir bond frac.', 'XRDPowderPattern|xrd_52', 'XRDPowderPattern|xrd_10', 'BondFractions|Se - Se bond frac.', 'BondFractions|As - Na bond frac.', 'BondFractions|Ba - I bond frac.', 'BondFractions|Ge - I bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 32', 'BondFractions|Br - Rb bond frac.', 'BondFractions|Ag - Tm bond frac.', 'BondFractions|Br - Co bond frac.', 'BondFractions|I - Nb bond frac.', 'BondFractions|Br - Pu bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 25', 'BondFractions|Br - Fe bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 24', 'BondFractions|Br - Te bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 27', 'BondFractions|Bi - Pt bond frac.', 'BondFractions|Br - Ni bond frac.', 'XRDPowderPattern|xrd_22', 'BondFractions|Ag - Cl bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 34', 'BondFractions|Br - In bond frac.', 'BondFractions|As - As bond frac.', 'BondFractions|Au - I bond frac.', 'BondFractions|Ag - Cr bond frac.', 'BondFractions|B - I bond frac.', 'BondFractions|Bi - I bond frac.', 'BondFractions|Bi - Se bond frac.', 'BondFractions|Br - Cd bond frac.', 'BondFractions|Ag - In bond frac.', 'BondFractions|Br - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 21', 'BondFractions|Ag - Er bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 22', 'BondFractions|Br - Yb bond frac.', 'XRDPowderPattern|xrd_38', 'SineCoulombMatrix|sine coulomb matrix eig 31', 'BondFractions|As - Sn bond frac.', 'BondFractions|Sb - Se bond frac.', 'XRDPowderPattern|xrd_60', 'BondFractions|As - K bond frac.', 'BondFractions|Bi - Pb bond frac.', 'BondFractions|Bi - S bond frac.', 'XRDPowderPattern|xrd_78', 'XRDPowderPattern|xrd_74', 'BondFractions|As - I bond frac.', 'XRDPowderPattern|xrd_94', 'BondFractions|Br - Pb bond frac.', 'XRDPowderPattern|xrd_88', 'BondFractions|Rh - Te bond frac.', 'XRDPowderPattern|xrd_50', 'BondFractions|Ba - Zr bond frac.', 'BondFractions|Bi - O bond frac.', 'BondFractions|Ag - Bi bond frac.', 'BondFractions|Ag - V bond frac.', 'BondFractions|As - Tl bond frac.', 'BondFractions|Bi - Rb bond frac.', 'XRDPowderPattern|xrd_36', 'BondFractions|Ag - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 20', 'XRDPowderPattern|xrd_80', 'BondFractions|Ag - Sn bond frac.', 'BondFractions|S - Ta bond frac.', 'XRDPowderPattern|xrd_92', 'BondFractions|Al - Br bond frac.', 'BondFractions|Ba - N bond frac.', 'BondFractions|Bi - Li bond frac.', 'XRDPowderPattern|xrd_4', 'BondFractions|Al - Pd bond frac.', 'XRDPowderPattern|xrd_30', 'BondFractions|Ba - S bond frac.', 'BondFractions|Br - Cl bond frac.', 'BondFractions|Au - N bond frac.', 'BondFractions|Br - Mn bond frac.', 'BondFractions|As - Sb bond frac.', 'XRDPowderPattern|xrd_96'}
2025-05-14 05:04:11,391 - modnet - INFO - Starting target 1/1: target ...
2025-05-14 05:04:11,391 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 05:04:18,244 - modnet - INFO - Computing optimal features...
2025-05-14 05:04:27,558 - modnet - INFO - Selected 50/556 features...
2025-05-14 05:04:36,285 - modnet - INFO - Selected 100/556 features...
2025-05-14 05:04:44,203 - modnet - INFO - Selected 150/556 features...
2025-05-14 05:04:51,305 - modnet - INFO - Selected 200/556 features...
2025-05-14 05:04:57,543 - modnet - INFO - Selected 250/556 features...
2025-05-14 05:05:02,882 - modnet - INFO - Selected 300/556 features...
2025-05-14 05:05:07,325 - modnet - INFO - Selected 350/556 features...
2025-05-14 05:05:10,847 - modnet - INFO - Selected 400/556 features...
2025-05-14 05:05:13,447 - modnet - INFO - Selected 450/556 features...
2025-05-14 05:05:15,108 - modnet - INFO - Selected 500/556 features...
2025-05-14 05:05:15,817 - modnet - INFO - Selected 550/556 features...
2025-05-14 05:05:15,837 - modnet - INFO - Done with target 1/1: target.
2025-05-14 05:05:15,837 - modnet - INFO - Merging all features...
2025-05-14 05:05:15,837 - modnet - INFO - Done.
2025-05-14 05:05:15,874 - modnet - INFO - Data successfully saved as folds/matbench_jdft2d_train_moddata_f5!
2025-05-14 05:05:15,900 - modnet - INFO - Data successfully saved as folds/matbench_jdft2d_test_moddata_f5!
Training fold 1 ...
Processing fold 1 ...
2025-05-14 05:05:15.938844: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:15.941097: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:15.941131: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:15.941166: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:15.941571: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:16,042 - modnet - INFO - Targets:
2025-05-14 05:05:16,043 - modnet - INFO - 1)target: regression
2025-05-14 05:05:16,524 - modnet - INFO - Multiprocessing on 32 cores. Total of 128 cores available.
2025-05-14 05:05:16,524 - modnet - INFO - Generation number 0

  0%|          | 0/250 [00:00<?, ?it/s]2025-05-14 05:05:17.306705: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.309061: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.377091: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.377093: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.398951: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.398954: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.433370: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.450408: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.472412: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.491377: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.520005: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.605213: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.605216: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.605221: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.605235: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.615073: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.616078: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.642356: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.642357: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.674338: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.674341: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.715561: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.715561: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.718284: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.726659: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.764166: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.824913: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.827078: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.840067: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.876790: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.882010: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:17.915660: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 05:05:20.563020: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.563716: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:20.563746: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:20.563786: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:20.564143: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.595221: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.595682: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:20.595719: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:20.595759: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:20.596058: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.661689: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.662205: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:20.662243: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:20.662279: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:20.666268: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:20.666954: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.667523: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:20.678669: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:20.679629: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:20.717216: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.717816: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:20.717853: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:20.717900: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:20.718278: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.732595: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:20.733250: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:20.776560: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.777046: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:20.777077: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:20.777112: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:20.777859: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.780819: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:20.788550: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.788968: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:20.788995: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:20.789027: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:20.789259: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.792685: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:20.850121: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:20.858838: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.859329: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:20.859359: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:20.859393: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:20.859700: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.862664: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:20.873333: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:20.886695: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:20.909791: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.910262: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:20.910288: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:20.910322: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:20.910593: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:20.924565: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:20.925254: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:20.978461: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:20.979149: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:21.005035: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.005542: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:21.005570: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:21.005603: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:21.005916: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.071708: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.072217: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:21.072245: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:21.072280: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:21.072590: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.081623: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:21.082335: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:21.138812: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.139285: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:21.139314: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:21.139348: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:21.139626: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.142553: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:21.143275: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:21.202490: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.202968: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:21.202996: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:21.203029: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:21.203277: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.203327: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:21.203979: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:21.266046: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:21.266738: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:21.268106: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.268577: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:21.268605: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:21.268637: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:21.268887: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.327902: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:21.328614: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:21.336725: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.337219: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:21.337246: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:21.337279: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:21.337551: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.396721: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.397247: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:21.397273: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:21.397307: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:21.397576: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.399285: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:21.399929: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:21.465276: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.465834: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:21.465861: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:21.465894: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:21.466173: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.466559: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:21.467236: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:21.535947: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.536472: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:21.536499: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:21.536543: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:21.536798: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.540880: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:21.541608: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:21.599659: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:21.600382: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:21.609982: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.610512: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:21.610539: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:21.610572: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:21.610834: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.689257: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:21.689996: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:21.703174: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.703664: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:21.703696: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:21.703728: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:21.703965: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.804561: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.805090: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:21.805118: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:21.805150: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:21.805392: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.840438: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:21.841208: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:21.865188: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:21.865878: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:21.884932: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.885438: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:21.885467: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:21.885510: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:21.885771: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.945342: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:21.946120: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:21.986942: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:21.987457: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:21.987488: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:21.987529: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:21.987837: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.052732: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:22.053469: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:22.255792: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.256357: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:22.256385: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:22.256419: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:22.256708: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.322898: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.323477: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:22.323515: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:22.323552: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:22.323842: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.324236: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:22.324935: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:22.389815: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.390331: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:22.390358: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:22.390391: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:22.390656: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.391892: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:22.404664: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:22.449877: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:22.456326: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.456888: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:22.456917: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:22.456950: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:22.457215: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.461652: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:22.519952: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:22.527804: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.528400: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:22.528437: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:22.528473: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:22.528807: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.532648: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:22.596140: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:22.596867: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:22.610238: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.610762: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:22.610791: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:22.610825: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:22.611095: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.673915: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:22.680300: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.680864: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:22.680893: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:22.680928: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:22.681194: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.685705: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:22.746175: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:22.758667: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:22.762211: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.762785: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:22.762813: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:22.762846: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:22.763139: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.838498: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.839483: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:22.839538: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:22.839574: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:22.839880: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.844651: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:22.845510: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:22.872343: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.872854: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 05:05:22.872880: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 05:05:22.872912: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 05:05:22.873157: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 05:05:22.909276: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:22.909983: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 05:05:22.930896: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 05:05:22.943656: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz

  0%|          | 1/250 [00:13<54:12, 13.06s/it]
  1%|          | 2/250 [00:13<22:36,  5.47s/it]
  1%|          | 3/250 [00:13<13:04,  3.17s/it]
  2%|▏         | 4/250 [00:13<08:05,  1.97s/it]
  2%|▏         | 5/250 [00:14<05:46,  1.41s/it]
  2%|▏         | 6/250 [00:14<04:04,  1.00s/it]
  3%|▎         | 7/250 [00:14<03:20,  1.21it/s]
  3%|▎         | 8/250 [00:15<02:33,  1.58it/s]
  4%|▎         | 9/250 [00:15<02:20,  1.72it/s]
  4%|▍         | 10/250 [00:16<02:17,  1.75it/s]
  4%|▍         | 11/250 [00:17<03:18,  1.20it/s]
  5%|▍         | 12/250 [00:17<02:33,  1.55it/s]
  6%|▌         | 14/250 [00:18<01:37,  2.42it/s]
  6%|▌         | 15/250 [00:18<01:29,  2.62it/s]
  6%|▋         | 16/250 [00:18<01:17,  3.00it/s]
  7%|▋         | 17/250 [00:18<01:17,  3.01it/s]
  7%|▋         | 18/250 [00:19<01:58,  1.96it/s]
  8%|▊         | 19/250 [00:20<02:10,  1.77it/s]
  8%|▊         | 20/250 [00:20<01:56,  1.98it/s]
  8%|▊         | 21/250 [00:21<01:34,  2.43it/s]
  8%|▊         | 21/250 [00:21<03:49,  1.00s/it]
multiprocessing.pool.RemoteTraceback: 
"""
Traceback (most recent call last):
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/multiprocessing/pool.py", line 125, in worker
    result = (True, func(*args, **kwds))
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 609, in _map_evaluate_individual
    return _evaluate_individual(**kwargs)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 634, in _evaluate_individual
    individual.evaluate(train_data, val_data, fast=fast)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 217, in evaluate
    self.val_loss = model.evaluate(val_data)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/models/vanilla.py", line 708, in evaluate
    score.append(mean_absolute_error(y_true, y_pred[i]))
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/utils/validation.py", line 72, in inner_f
    return f(**kwargs)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/metrics/_regression.py", line 178, in mean_absolute_error
    y_type, y_true, y_pred, multioutput = _check_reg_targets(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/metrics/_regression.py", line 86, in _check_reg_targets
    y_pred = check_array(y_pred, ensure_2d=False, dtype=dtype)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/utils/validation.py", line 72, in inner_f
    return f(**kwargs)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/utils/validation.py", line 644, in check_array
    _assert_all_finite(array,
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/utils/validation.py", line 96, in _assert_all_finite
    raise ValueError(
ValueError: Input contains NaN, infinity or a value too large for dtype('float32').
"""

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "run_benchmark.py", line 861, in <module>
    results = benchmark(data, settings, n_jobs=n_jobs, fast=fast)
  File "run_benchmark.py", line 140, in benchmark
    return matbench_benchmark(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 137, in matbench_benchmark
    fold_results.append(train_fold(fold, *args, **model_kwargs))
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 277, in train_fold
    model = ga.run(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 496, in run
    val_loss, models, individuals = self.function_fitness(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 433, in function_fitness
    for res in tqdm.tqdm(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/tqdm/std.py", line 1181, in __iter__
    for obj in iterable:
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/multiprocessing/pool.py", line 868, in next
    raise value
ValueError: Input contains NaN, infinity or a value too large for dtype('float32').
Job finished on Wed May 14 05:05:38 CEST 2025

Resources Used

Total Memory used                        - MEM              : 11GiB
Total CPU Time                           - CPU_Time         : 03:16:48
Execution Time                           - Wall_Time        : 00:06:09
total programme cpu time                 - Total_CPU        : 14:59.778
Total_CPU / CPU_Time  (%)                - ETA              : 7%
Number of alloc CPU                      - NCPUS            : 32
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 48
Mobilized Resources x Execution Time     - R_Wall_Time      : 04:55:12
CPU_Time / R_Wall_Time (%)               - ALPHA            : 66%
Energy (Joules)                                             : 82756

