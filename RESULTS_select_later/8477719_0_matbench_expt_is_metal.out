Job started on Wed May 14 12:47:25 CEST 2025
Running on node(s): cnm025
2025-05-14 12:47:32.977702: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 32 jobs
Preparing nested CV run for task 'matbench_expt_gap'
Found 0 matching files for matbench_expt_gap
They are []
2025-05-14 12:47:45,522 - modnet - INFO - Loaded CompositionOnlyFeaturizer featurizer.
2025-05-14 12:47:45,525 - modnet - INFO - Computing features, this can take time...
2025-05-14 12:47:45,525 - modnet - INFO - Applying composition featurizers...
2025-05-14 12:47:45,529 - modnet - INFO - Applying featurizers (AtomicOrbitals(), AtomicPackingEfficiency(), BandCenter(), ElementFraction(), ElementProperty(data_source=<matminer.utils.data.MagpieData object at 0x7f1c1c3736a0>,
                features=['Number', 'MendeleevNumber', 'AtomicWeight',
                          'MeltingT', 'Column', 'Row', 'CovalentRadius',
                          'Electronegativity', 'NsValence', 'NpValence',
                          'NdValence', 'NfValence', 'NValence', 'NsUnfilled',
                          'NpUnfilled', 'NdUnfilled', 'NfUnfilled', 'NUnfilled',
                          'GSvolume_pa', 'GSbandgap', 'GSmagmom',
                          'SpaceGroupNumber'],
                stats=['minimum', 'maximum', 'range', 'mean', 'avg_dev',
                       'mode']), IonProperty(), Miedema(ss_types=['min'], struct_types=['inter', 'amor', 'ss']), Stoichiometry(), TMetalFraction(), ValenceOrbital(), YangSolidSolution()) to column 'composition'.

MultipleFeaturizer:   0%|          | 0/4921 [00:00<?, ?it/s]
MultipleFeaturizer:   0%|          | 1/4921 [00:00<19:06,  4.29it/s]
MultipleFeaturizer:  14%|█▍        | 702/4921 [00:00<00:01, 2659.07it/s]
MultipleFeaturizer:  26%|██▌       | 1287/4921 [00:01<00:05, 687.87it/s]
MultipleFeaturizer:  31%|███       | 1529/4921 [00:06<00:19, 174.40it/s]
MultipleFeaturizer:  34%|███▍      | 1678/4921 [00:07<00:18, 175.98it/s]
MultipleFeaturizer:  36%|███▌      | 1780/4921 [00:08<00:20, 156.63it/s]
MultipleFeaturizer:  38%|███▊      | 1851/4921 [00:08<00:18, 162.76it/s]
MultipleFeaturizer:  39%|███▉      | 1911/4921 [00:08<00:16, 180.10it/s]
MultipleFeaturizer:  40%|███▉      | 1967/4921 [00:09<00:21, 136.68it/s]
MultipleFeaturizer:  41%|████      | 2008/4921 [00:10<00:22, 128.93it/s]
MultipleFeaturizer:  41%|████▏     | 2039/4921 [00:10<00:25, 114.94it/s]
MultipleFeaturizer:  42%|████▏     | 2067/4921 [00:10<00:26, 106.13it/s]
MultipleFeaturizer:  44%|████▎     | 2145/4921 [00:11<00:19, 140.21it/s]
MultipleFeaturizer:  45%|████▌     | 2223/4921 [00:11<00:14, 181.56it/s]
MultipleFeaturizer:  46%|████▌     | 2262/4921 [00:11<00:16, 164.59it/s]
MultipleFeaturizer:  48%|████▊     | 2340/4921 [00:12<00:15, 168.64it/s]
MultipleFeaturizer:  48%|████▊     | 2379/4921 [00:12<00:13, 183.59it/s]
MultipleFeaturizer:  49%|████▉     | 2418/4921 [00:12<00:12, 205.78it/s]
MultipleFeaturizer:  50%|████▉     | 2457/4921 [00:12<00:10, 230.73it/s]
MultipleFeaturizer:  52%|█████▏    | 2535/4921 [00:12<00:09, 262.57it/s]
MultipleFeaturizer:  52%|█████▏    | 2574/4921 [00:12<00:11, 210.00it/s]
MultipleFeaturizer:  53%|█████▎    | 2613/4921 [00:13<00:11, 200.24it/s]
MultipleFeaturizer:  54%|█████▍    | 2652/4921 [00:14<00:25, 88.97it/s] 
MultipleFeaturizer:  55%|█████▍    | 2691/4921 [00:14<00:19, 112.34it/s]
MultipleFeaturizer:  55%|█████▌    | 2730/4921 [00:15<00:28, 75.61it/s] 
MultipleFeaturizer:  56%|█████▋    | 2769/4921 [00:15<00:29, 73.13it/s]
MultipleFeaturizer:  57%|█████▋    | 2808/4921 [00:16<00:25, 82.02it/s]
MultipleFeaturizer:  58%|█████▊    | 2847/4921 [00:16<00:20, 101.75it/s]
MultipleFeaturizer:  59%|█████▊    | 2886/4921 [00:21<01:32, 21.91it/s] 
MultipleFeaturizer:  59%|█████▉    | 2925/4921 [00:21<01:05, 30.25it/s]
MultipleFeaturizer:  60%|██████    | 2964/4921 [00:22<00:53, 36.64it/s]
MultipleFeaturizer:  61%|██████    | 3003/4921 [00:22<00:44, 42.89it/s]
MultipleFeaturizer:  62%|██████▏   | 3042/4921 [00:22<00:33, 56.91it/s]
MultipleFeaturizer:  63%|██████▎   | 3081/4921 [00:23<00:32, 56.82it/s]
MultipleFeaturizer:  63%|██████▎   | 3120/4921 [00:24<00:33, 53.18it/s]
MultipleFeaturizer:  64%|██████▍   | 3159/4921 [00:25<00:36, 48.45it/s]
MultipleFeaturizer:  65%|██████▍   | 3198/4921 [00:25<00:28, 61.09it/s]
MultipleFeaturizer:  66%|██████▌   | 3237/4921 [00:26<00:24, 70.13it/s]
MultipleFeaturizer:  67%|██████▋   | 3276/4921 [00:26<00:18, 89.92it/s]
MultipleFeaturizer:  67%|██████▋   | 3315/4921 [00:26<00:15, 105.67it/s]
MultipleFeaturizer:  68%|██████▊   | 3354/4921 [00:27<00:27, 57.71it/s] 
MultipleFeaturizer:  70%|██████▉   | 3432/4921 [00:28<00:16, 90.77it/s]
MultipleFeaturizer:  71%|███████   | 3471/4921 [00:28<00:13, 109.48it/s]
MultipleFeaturizer:  71%|███████▏  | 3510/4921 [00:28<00:11, 127.05it/s]
MultipleFeaturizer:  72%|███████▏  | 3549/4921 [00:28<00:10, 128.86it/s]
MultipleFeaturizer:  73%|███████▎  | 3588/4921 [00:28<00:09, 147.75it/s]
MultipleFeaturizer:  74%|███████▎  | 3627/4921 [00:29<00:11, 115.85it/s]
MultipleFeaturizer:  74%|███████▍  | 3666/4921 [00:29<00:09, 133.47it/s]
MultipleFeaturizer:  75%|███████▌  | 3705/4921 [00:29<00:07, 155.75it/s]
MultipleFeaturizer:  76%|███████▌  | 3744/4921 [00:30<00:08, 131.69it/s]
MultipleFeaturizer:  77%|███████▋  | 3783/4921 [00:30<00:08, 140.10it/s]
MultipleFeaturizer:  78%|███████▊  | 3822/4921 [00:30<00:09, 117.29it/s]
MultipleFeaturizer:  78%|███████▊  | 3861/4921 [00:30<00:07, 142.46it/s]
MultipleFeaturizer:  81%|████████  | 3978/4921 [00:31<00:03, 274.76it/s]
MultipleFeaturizer:  82%|████████▏ | 4022/4921 [00:31<00:04, 219.97it/s]
MultipleFeaturizer:  82%|████████▏ | 4057/4921 [00:31<00:05, 156.63it/s]
MultipleFeaturizer:  83%|████████▎ | 4095/4921 [00:32<00:05, 163.14it/s]
MultipleFeaturizer:  84%|████████▍ | 4134/4921 [00:32<00:07, 106.75it/s]
MultipleFeaturizer:  85%|████████▍ | 4173/4921 [00:33<00:07, 94.33it/s] 
MultipleFeaturizer:  86%|████████▌ | 4212/4921 [00:33<00:07, 97.91it/s]
MultipleFeaturizer:  87%|████████▋ | 4290/4921 [00:33<00:04, 152.88it/s]
MultipleFeaturizer:  89%|████████▉ | 4368/4921 [00:34<00:03, 150.59it/s]
MultipleFeaturizer:  90%|████████▉ | 4407/4921 [00:34<00:03, 138.43it/s]
MultipleFeaturizer:  92%|█████████▏| 4524/4921 [00:34<00:01, 215.14it/s]
MultipleFeaturizer:  93%|█████████▎| 4563/4921 [00:35<00:01, 201.40it/s]
MultipleFeaturizer:  94%|█████████▍| 4641/4921 [00:35<00:01, 217.31it/s]
MultipleFeaturizer:  95%|█████████▌| 4680/4921 [00:36<00:02, 112.87it/s]
MultipleFeaturizer:  96%|█████████▌| 4719/4921 [00:36<00:01, 112.70it/s]
MultipleFeaturizer:  97%|█████████▋| 4758/4921 [00:36<00:01, 132.87it/s]
MultipleFeaturizer:  97%|█████████▋| 4797/4921 [00:37<00:00, 135.43it/s]
MultipleFeaturizer:  98%|█████████▊| 4836/4921 [00:37<00:00, 161.30it/s]
MultipleFeaturizer:  99%|█████████▉| 4875/4921 [00:37<00:00, 128.74it/s]
MultipleFeaturizer: 100%|█████████▉| 4914/4921 [00:38<00:00, 147.23it/s]
MultipleFeaturizer: 100%|██████████| 4921/4921 [00:38<00:00, 129.44it/s]
2025-05-14 12:49:12,874 - modnet - INFO - Data has successfully been featurized!
2025-05-14 12:49:14,533 - modnet - INFO - Data successfully saved as ./precomputed/matbench_expt_is_metal_moddata.pkl.gz!
Preparing fold 1 ...
2025-05-14 12:49:14,710 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f1c1bd582e0> object, created with modnet version 0.1.13
2025-05-14 12:49:14,723 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_train_moddata_f1!
Preparing fold 2 ...
2025-05-14 12:49:14,755 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f1c1bceaaf0> object, created with modnet version 0.1.13
2025-05-14 12:49:14,771 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_train_moddata_f2!
Preparing fold 3 ...
2025-05-14 12:49:14,806 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f1c18fd2f10> object, created with modnet version 0.1.13
2025-05-14 12:49:14,820 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_train_moddata_f3!
Preparing fold 4 ...
2025-05-14 12:49:14,852 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f1c19b2ac70> object, created with modnet version 0.1.13
2025-05-14 12:49:14,866 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_train_moddata_f4!
Preparing fold 5 ...
2025-05-14 12:49:14,896 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f1c18713ca0> object, created with modnet version 0.1.13
2025-05-14 12:49:14,911 - modnet - INFO - Data successfully saved as folds/matbench_expt_gap_train_moddata_f5!
Training fold 1 ...
Processing fold 1 ...
2025-05-14 12:49:14.920406: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:14.920968: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:14.920995: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:14.921025: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:14.921248: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:15,161 - modnet - INFO - Targets:
2025-05-14 12:49:15,161 - modnet - INFO - 1)is_metal: regression
2025-05-14 12:49:15,618 - modnet - INFO - Multiprocessing on 32 cores. Total of 128 cores available.
2025-05-14 12:49:15,618 - modnet - INFO - Generation number 0

  0%|          | 0/250 [00:00<?, ?it/s]2025-05-14 12:49:16.370763: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.406975: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.414986: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.414986: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.421590: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.453633: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.454927: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.463063: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.507295: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.518226: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.521991: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.521991: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.530392: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.562968: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.595548: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.602845: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.620006: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.620126: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.656191: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.672009: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.680792: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.698913: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.699167: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.727044: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.748499: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.753752: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.782968: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.818614: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.828137: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.848583: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.859824: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:16.864219: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
2025-05-14 12:49:19.186407: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.186893: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.186919: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.186953: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.187223: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.232403: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.233305: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.233338: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.233370: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.233764: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.292618: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.293052: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.293079: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.293113: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.293373: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.308449: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.308906: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.308935: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.308969: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.309222: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.333489: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.333993: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.334029: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.334061: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.334370: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.370746: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.371191: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.371227: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.371259: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.371577: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.390973: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.391389: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.391415: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.391446: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.391754: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.405502: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.406052: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.406095: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.406133: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.406559: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.428594: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.429211: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.429247: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.429286: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.430104: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.450117: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.450543: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.450570: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.450600: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.450863: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.492871: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.493246: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.493272: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.493305: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.493562: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.527958: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.528350: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.528375: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.528405: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.528655: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.562025: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.562432: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.562456: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.562488: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.562763: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.582989: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.583424: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.583456: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.583490: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.583790: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.599449: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.599842: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.599867: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.599898: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.600155: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.700834: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.701225: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.701251: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.701284: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.701546: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.713437: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.713851: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.713877: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.713908: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.714180: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.732060: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.732485: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.732521: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.732554: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.732822: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.751658: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.752120: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.752147: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.752178: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.752468: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.778708: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.779221: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.779254: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.779286: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.779583: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.802424: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.802846: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.802873: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.802904: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.803147: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.823580: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.823732: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.823996: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.824160: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.824190: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.824222: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.824499: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.825463: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.826910: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.829654: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.830819: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.830897: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.831628: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.831746: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.832284: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.832369: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.833118: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.833220: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.835563: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.836460: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.836635: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.836668: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.837069: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.837105: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.838061: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.838143: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.838521: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.839422: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.839628: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.840906: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.842621: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.849333: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.849861: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.849887: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.849923: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.850243: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.851105: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.852694: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.866187: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.880651: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.907964: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.908935: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.908962: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.908996: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.909397: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.933114: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.934369: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.934398: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.934432: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.934817: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.947041: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.947524: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.947551: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.947583: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.947857: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.953261: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.957498: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.958483: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.958528: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.958563: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.958947: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.966646: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.978903: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:19.988018: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.988580: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:19.988607: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:19.988640: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:19.989181: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:19.992679: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:19.999494: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:20.012719: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:20.017916: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:20.019408: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:20.027955: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:20.028494: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:20.028532: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:20.028568: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:20.029161: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:20.037807: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:20.039386: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:20.066599: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:20.067085: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:20.067112: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:20.067144: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:20.067393: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:20.105775: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:20.107557: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:20.124174: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:20.124698: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:20.124725: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:20.124757: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:20.125035: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:20.139623: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:20.140092: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/********-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 12:49:20.140118: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 12:49:20.140150: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 12:49:20.140393: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 12:49:20.151775: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:20.153680: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:20.172933: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:20.174819: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:20.204551: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:20.206193: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:20.241830: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:20.243838: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:20.281108: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:20.283464: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:20.287443: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:20.289278: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:20.311415: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:20.313321: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:20.358790: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:20.360882: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:20.386706: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:20.388476: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:20.426809: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:20.428565: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
2025-05-14 12:49:20.474294: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 12:49:20.475954: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz

  0%|          | 1/250 [00:08<36:49,  8.87s/it]
  1%|          | 2/250 [00:08<15:22,  3.72s/it]
  1%|          | 3/250 [00:10<10:23,  2.52s/it]
  2%|▏         | 4/250 [00:11<07:59,  1.95s/it]
  2%|▏         | 5/250 [00:11<05:47,  1.42s/it]
  2%|▏         | 6/250 [00:13<06:50,  1.68s/it]
  3%|▎         | 7/250 [00:17<09:53,  2.44s/it]
  3%|▎         | 8/250 [00:18<07:45,  1.92s/it]
  4%|▍         | 10/250 [00:19<04:29,  1.12s/it]
  4%|▍         | 11/250 [00:19<03:28,  1.15it/s]
  5%|▍         | 12/250 [00:20<03:34,  1.11it/s]
  5%|▌         | 13/250 [00:20<03:21,  1.18it/s]
  6%|▌         | 14/250 [00:21<03:19,  1.18it/s]
  6%|▌         | 15/250 [00:21<02:29,  1.57it/s]
  7%|▋         | 17/250 [00:22<01:35,  2.43it/s]
  7%|▋         | 18/250 [00:23<02:04,  1.86it/s]
  8%|▊         | 19/250 [00:23<01:49,  2.12it/s]
  8%|▊         | 21/250 [00:24<01:39,  2.31it/s]
  9%|▉         | 22/250 [00:25<02:02,  1.86it/s]
  9%|▉         | 23/250 [00:26<02:59,  1.26it/s]
 10%|▉         | 24/250 [00:27<03:19,  1.13it/s]
 10%|█         | 25/250 [00:28<02:49,  1.33it/s]
 11%|█         | 27/250 [00:29<02:54,  1.28it/s]
 11%|█         | 28/250 [00:30<02:25,  1.53it/s]
 12%|█▏        | 30/250 [00:30<01:56,  1.88it/s]
 12%|█▏        | 31/250 [00:32<02:44,  1.33it/s]
 13%|█▎        | 32/250 [00:32<02:24,  1.51it/s]
 13%|█▎        | 33/250 [00:32<01:55,  1.88it/s]
 14%|█▍        | 35/250 [00:33<01:43,  2.07it/s]
 14%|█▍        | 36/250 [00:33<01:31,  2.33it/s]
 15%|█▍        | 37/250 [00:34<01:30,  2.36it/s]
 16%|█▌        | 39/250 [00:35<01:48,  1.95it/s]
 16%|█▋        | 41/250 [00:35<01:16,  2.74it/s]
 17%|█▋        | 42/250 [00:36<01:18,  2.65it/s]
 18%|█▊        | 44/250 [00:36<01:01,  3.36it/s]
 18%|█▊        | 45/250 [00:36<01:02,  3.26it/s]
 18%|█▊        | 46/250 [00:37<01:18,  2.60it/s]
 19%|█▉        | 47/250 [00:37<01:10,  2.87it/s]
 19%|█▉        | 48/250 [00:38<01:11,  2.84it/s]
 20%|█▉        | 49/250 [00:38<01:03,  3.15it/s]
 20%|██        | 50/250 [00:39<01:24,  2.36it/s]
 20%|██        | 51/250 [00:39<01:17,  2.57it/s]
 21%|██        | 52/250 [00:39<01:00,  3.25it/s]
 21%|██        | 53/250 [00:39<00:58,  3.34it/s]
 22%|██▏       | 54/250 [00:40<00:59,  3.32it/s]
 22%|██▏       | 56/250 [00:40<00:50,  3.84it/s]
 23%|██▎       | 58/250 [00:41<01:23,  2.29it/s]
 24%|██▎       | 59/250 [00:42<01:13,  2.61it/s]
 24%|██▍       | 60/250 [00:42<01:16,  2.48it/s]
 24%|██▍       | 61/250 [00:43<01:34,  2.00it/s]
 25%|██▍       | 62/250 [00:44<02:31,  1.24it/s]
 25%|██▌       | 63/250 [00:46<03:03,  1.02it/s]
 26%|██▌       | 64/250 [00:47<02:47,  1.11it/s]
 26%|██▌       | 65/250 [00:47<02:10,  1.42it/s]
 26%|██▋       | 66/250 [00:47<01:56,  1.58it/s]
 27%|██▋       | 67/250 [00:48<01:44,  1.75it/s]
 27%|██▋       | 68/250 [00:48<01:35,  1.91it/s]
 28%|██▊       | 69/250 [00:48<01:19,  2.28it/s]
 28%|██▊       | 70/250 [00:49<01:12,  2.49it/s]
 28%|██▊       | 71/250 [00:49<01:23,  2.15it/s]
 29%|██▉       | 72/250 [00:50<01:49,  1.63it/s]
 29%|██▉       | 73/250 [00:51<01:37,  1.82it/s]
 30%|██▉       | 74/250 [00:51<01:42,  1.72it/s]
 31%|███       | 77/250 [00:52<00:55,  3.10it/s]
 31%|███       | 78/250 [00:52<01:08,  2.50it/s]
 32%|███▏      | 79/250 [00:53<00:59,  2.88it/s]
 32%|███▏      | 81/250 [00:53<00:46,  3.65it/s]
 33%|███▎      | 82/250 [00:53<00:52,  3.19it/s]
 33%|███▎      | 83/250 [00:54<00:59,  2.80it/s]
 34%|███▍      | 85/250 [00:54<00:49,  3.36it/s]
 34%|███▍      | 86/250 [00:54<00:47,  3.49it/s]
 35%|███▍      | 87/250 [00:55<01:01,  2.66it/s]
 35%|███▌      | 88/250 [00:55<00:50,  3.20it/s]
 36%|███▌      | 89/250 [00:56<01:07,  2.40it/s]
 36%|███▌      | 90/250 [00:59<02:48,  1.05s/it]
 36%|███▋      | 91/250 [00:59<02:36,  1.02it/s]
 37%|███▋      | 93/250 [01:02<02:45,  1.05s/it]
 38%|███▊      | 94/250 [01:02<02:08,  1.21it/s]
 38%|███▊      | 95/250 [01:02<01:52,  1.38it/s]
 38%|███▊      | 96/250 [01:04<02:19,  1.11it/s]
 39%|███▉      | 97/250 [01:04<01:56,  1.31it/s]
 40%|███▉      | 99/250 [01:04<01:15,  2.01it/s]
 40%|████      | 100/250 [01:05<01:05,  2.29it/s]
 41%|████      | 102/250 [01:05<00:41,  3.54it/s]
 41%|████      | 103/250 [01:06<01:03,  2.32it/s]
 42%|████▏     | 104/250 [01:06<00:59,  2.46it/s]
 42%|████▏     | 106/250 [01:06<00:46,  3.08it/s]
 43%|████▎     | 107/250 [01:07<00:52,  2.73it/s]
 43%|████▎     | 108/250 [01:07<00:45,  3.09it/s]
 44%|████▎     | 109/250 [01:07<00:39,  3.55it/s]
 44%|████▍     | 110/250 [01:08<00:47,  2.93it/s]
 45%|████▍     | 112/250 [01:08<00:31,  4.36it/s]
 45%|████▌     | 113/250 [01:08<00:30,  4.54it/s]
 46%|████▌     | 114/250 [01:09<01:01,  2.23it/s]
 46%|████▌     | 115/250 [01:10<01:08,  1.98it/s]
 47%|████▋     | 117/250 [01:10<00:41,  3.21it/s]
 48%|████▊     | 119/250 [01:10<00:32,  4.01it/s]
 48%|████▊     | 121/250 [01:11<00:27,  4.67it/s]
 49%|████▉     | 122/250 [01:11<00:27,  4.68it/s]
 49%|████▉     | 123/250 [01:12<00:41,  3.06it/s]
 50%|████▉     | 124/250 [01:12<00:50,  2.49it/s]
 50%|█████     | 125/250 [01:13<01:10,  1.77it/s]
 50%|█████     | 126/250 [01:14<01:03,  1.97it/s]
 51%|█████     | 127/250 [01:14<00:52,  2.36it/s]
 51%|█████     | 128/250 [01:14<00:45,  2.71it/s]
 52%|█████▏    | 129/250 [01:14<00:39,  3.08it/s]
 52%|█████▏    | 130/250 [01:15<00:51,  2.35it/s]
 53%|█████▎    | 132/250 [01:15<00:38,  3.09it/s]
 54%|█████▎    | 134/250 [01:15<00:25,  4.62it/s]
 54%|█████▍    | 135/250 [01:17<00:50,  2.27it/s]
 55%|█████▍    | 137/250 [01:17<00:38,  2.97it/s]
 55%|█████▌    | 138/250 [01:19<01:09,  1.62it/s]
 56%|█████▌    | 139/250 [01:19<01:12,  1.54it/s]
 56%|█████▌    | 140/250 [01:22<02:13,  1.21s/it]
 56%|█████▋    | 141/250 [01:24<02:25,  1.33s/it]
 57%|█████▋    | 142/250 [01:25<02:05,  1.17s/it]
 57%|█████▋    | 143/250 [01:26<02:23,  1.34s/it]
 58%|█████▊    | 145/250 [01:27<01:22,  1.27it/s]
 59%|█████▉    | 147/250 [01:27<00:58,  1.76it/s]
 59%|█████▉    | 148/250 [01:28<00:57,  1.77it/s]
 60%|█████▉    | 149/250 [01:28<00:47,  2.14it/s]
 60%|██████    | 150/250 [01:28<00:42,  2.37it/s]
 61%|██████    | 152/250 [01:28<00:27,  3.54it/s]
 62%|██████▏   | 154/250 [01:29<00:21,  4.44it/s]
 62%|██████▏   | 156/250 [01:29<00:15,  6.00it/s]
 63%|██████▎   | 157/250 [01:30<00:38,  2.43it/s]
 63%|██████▎   | 158/250 [01:31<00:41,  2.23it/s]
 64%|██████▎   | 159/250 [01:31<00:37,  2.44it/s]
 64%|██████▍   | 160/250 [01:32<00:45,  1.96it/s]
 64%|██████▍   | 161/250 [01:33<01:07,  1.32it/s]
 65%|██████▍   | 162/250 [01:33<00:51,  1.71it/s]
 65%|██████▌   | 163/250 [01:34<00:45,  1.92it/s]
 66%|██████▌   | 164/250 [01:34<00:42,  2.04it/s]
 66%|██████▌   | 165/250 [01:34<00:34,  2.44it/s]
 66%|██████▋   | 166/250 [01:35<00:41,  2.01it/s]
 67%|██████▋   | 167/250 [01:35<00:35,  2.35it/s]
 67%|██████▋   | 168/250 [01:37<00:55,  1.48it/s]
 68%|██████▊   | 169/250 [01:37<00:42,  1.90it/s]
 68%|██████▊   | 170/250 [01:37<00:38,  2.06it/s]
 68%|██████▊   | 171/250 [01:37<00:30,  2.58it/s]
 69%|██████▉   | 173/250 [01:38<00:21,  3.62it/s]
 70%|██████▉   | 174/250 [01:38<00:24,  3.10it/s]
 70%|███████   | 175/250 [01:38<00:23,  3.22it/s]
 70%|███████   | 176/250 [01:39<00:22,  3.22it/s]
 71%|███████   | 177/250 [01:40<00:39,  1.87it/s]
 71%|███████   | 178/250 [01:40<00:32,  2.20it/s]
 72%|███████▏  | 180/250 [01:40<00:19,  3.62it/s]
 72%|███████▏  | 181/250 [01:40<00:16,  4.21it/s]
 73%|███████▎  | 183/250 [01:40<00:12,  5.52it/s]
 74%|███████▎  | 184/250 [01:41<00:13,  5.01it/s]
 74%|███████▍  | 185/250 [01:41<00:11,  5.65it/s]
 75%|███████▍  | 187/250 [01:41<00:08,  7.20it/s]
 75%|███████▌  | 188/250 [01:41<00:10,  6.20it/s]
 76%|███████▋  | 191/250 [01:44<00:29,  1.99it/s]
 77%|███████▋  | 192/250 [01:44<00:30,  1.90it/s]
 77%|███████▋  | 193/250 [01:46<00:38,  1.47it/s]
 78%|███████▊  | 195/250 [01:46<00:30,  1.81it/s]
 78%|███████▊  | 196/250 [01:48<00:38,  1.39it/s]
 79%|███████▉  | 197/250 [01:50<00:53,  1.01s/it]
 79%|███████▉  | 198/250 [01:51<00:58,  1.13s/it]
 80%|███████▉  | 199/250 [01:52<00:56,  1.10s/it]
 80%|████████  | 200/250 [01:53<00:51,  1.04s/it]
 80%|████████  | 201/250 [01:54<00:45,  1.09it/s]
 81%|████████  | 202/250 [01:55<00:46,  1.03it/s]
 81%|████████  | 203/250 [01:55<00:37,  1.24it/s]
 82%|████████▏ | 204/250 [01:56<00:39,  1.16it/s]
 82%|████████▏ | 205/250 [01:58<00:46,  1.04s/it]
 82%|████████▏ | 206/250 [01:58<00:40,  1.10it/s]
 83%|████████▎ | 207/250 [01:59<00:33,  1.27it/s]
 83%|████████▎ | 208/250 [02:00<00:37,  1.13it/s]
 84%|████████▎ | 209/250 [02:01<00:35,  1.17it/s]
 84%|████████▍ | 210/250 [02:03<00:59,  1.48s/it]
 84%|████████▍ | 211/250 [02:05<00:54,  1.39s/it]
 85%|████████▍ | 212/250 [02:05<00:39,  1.04s/it]
 85%|████████▌ | 213/250 [02:05<00:28,  1.29it/s]
 86%|████████▌ | 214/250 [02:05<00:22,  1.59it/s]
 86%|████████▌ | 215/250 [02:06<00:21,  1.66it/s]
 86%|████████▋ | 216/250 [02:06<00:19,  1.73it/s]
 87%|████████▋ | 218/250 [02:08<00:18,  1.70it/s]
 88%|████████▊ | 219/250 [02:08<00:14,  2.13it/s]
 88%|████████▊ | 220/250 [02:08<00:11,  2.61it/s]
 88%|████████▊ | 221/250 [02:10<00:28,  1.03it/s]
 89%|████████▉ | 222/250 [02:13<00:39,  1.42s/it]
 89%|████████▉ | 223/250 [02:13<00:29,  1.09s/it]
 90%|████████▉ | 224/250 [02:13<00:22,  1.18it/s]
 90%|█████████ | 225/250 [02:15<00:25,  1.01s/it]
 90%|█████████ | 226/250 [02:16<00:26,  1.10s/it]
 91%|█████████ | 227/250 [02:16<00:19,  1.20it/s]
 91%|█████████ | 228/250 [02:17<00:18,  1.21it/s]
 92%|█████████▏| 229/250 [02:17<00:13,  1.60it/s]
 92%|█████████▏| 230/250 [02:18<00:10,  1.90it/s]
 92%|█████████▏| 231/250 [02:19<00:12,  1.56it/s]
 93%|█████████▎| 232/250 [02:19<00:10,  1.79it/s]
 93%|█████████▎| 233/250 [02:19<00:07,  2.37it/s]
 94%|█████████▍| 235/250 [02:19<00:04,  3.59it/s]
 95%|█████████▍| 237/250 [02:20<00:03,  4.31it/s]
 95%|█████████▌| 238/250 [02:20<00:03,  3.71it/s]
 96%|█████████▌| 239/250 [02:20<00:03,  3.24it/s]
 96%|█████████▌| 240/250 [02:21<00:04,  2.18it/s]
 96%|█████████▋| 241/250 [02:22<00:05,  1.78it/s]
 97%|█████████▋| 242/250 [02:23<00:04,  2.00it/s]
 97%|█████████▋| 243/250 [02:23<00:03,  2.20it/s]
 98%|█████████▊| 244/250 [02:25<00:05,  1.11it/s]
 98%|█████████▊| 245/250 [02:27<00:06,  1.29s/it]
 98%|█████████▊| 246/250 [02:30<00:06,  1.65s/it]
 99%|█████████▉| 248/250 [02:32<00:02,  1.45s/it]
100%|█████████▉| 249/250 [02:34<00:01,  1.68s/it]
100%|██████████| 250/250 [02:35<00:00,  1.39s/it]
100%|██████████| 250/250 [02:35<00:00,  1.61it/s]
2025-05-14 12:51:51,148 - modnet - INFO - Loss per individual: ind 0: 0.125 	ind 1: 0.115 	ind 2: 0.119 	ind 3: 0.113 	ind 4: 0.289 	ind 5: 0.301 	ind 6: 0.116 	ind 7: 0.123 	ind 8: 0.109 	ind 9: 0.111 	ind 10: 0.536 	ind 11: 0.505 	ind 12: 0.510 	ind 13: 1.311 	ind 14: 0.119 	ind 15: 0.108 	ind 16: 0.137 	ind 17: 0.111 	ind 18: 0.507 	ind 19: 0.117 	ind 20: 0.513 	ind 21: 0.107 	ind 22: 0.116 	ind 23: 0.279 	ind 24: 0.109 	ind 25: 0.449 	ind 26: 0.108 	ind 27: 0.487 	ind 28: 0.145 	ind 29: 0.236 	ind 30: 0.382 	ind 31: 0.106 	ind 32: 0.193 	ind 33: 0.112 	ind 34: 0.507 	ind 35: 0.110 	ind 36: 0.116 	ind 37: 0.098 	ind 38: 0.499 	ind 39: 0.498 	ind 40: 2.523 	ind 41: 0.131 	ind 42: 0.113 	ind 43: 0.124 	ind 44: 0.356 	ind 45: 0.502 	ind 46: 0.107 	ind 47: 0.106 	ind 48: 0.109 	ind 49: 0.109 	
2025-05-14 12:51:51,149 - modnet - INFO - Generation number 1

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:09<39:58,  9.63s/it]
  1%|          | 2/250 [00:10<17:44,  4.29s/it]
  1%|          | 3/250 [00:10<10:14,  2.49s/it]
  2%|▏         | 4/250 [00:10<06:40,  1.63s/it]
  2%|▏         | 5/250 [00:11<04:50,  1.19s/it]
  2%|▏         | 6/250 [00:11<04:05,  1.01s/it]
  3%|▎         | 7/250 [00:13<04:57,  1.22s/it]
  3%|▎         | 8/250 [00:14<04:31,  1.12s/it]
  4%|▎         | 9/250 [00:14<03:33,  1.13it/s]
  4%|▍         | 11/250 [00:15<02:03,  1.94it/s]
  5%|▍         | 12/250 [00:19<05:54,  1.49s/it]
  5%|▌         | 13/250 [00:20<05:04,  1.28s/it]
  6%|▌         | 14/250 [00:21<04:52,  1.24s/it]
  6%|▋         | 16/250 [00:21<03:13,  1.21it/s]
  7%|▋         | 17/250 [00:22<03:12,  1.21it/s]
  7%|▋         | 18/250 [00:23<03:03,  1.27it/s]
  8%|▊         | 19/250 [00:24<02:59,  1.29it/s]
  8%|▊         | 21/250 [00:25<02:41,  1.42it/s]
  9%|▉         | 22/250 [00:25<02:12,  1.72it/s]
  9%|▉         | 23/250 [00:25<01:48,  2.09it/s]
 10%|▉         | 24/250 [00:25<01:32,  2.44it/s]
 10%|█         | 25/250 [00:26<01:17,  2.89it/s]
 10%|█         | 26/250 [00:27<02:07,  1.76it/s]
 11%|█         | 27/250 [00:27<02:07,  1.75it/s]
 11%|█         | 28/250 [00:28<01:47,  2.07it/s]
 12%|█▏        | 29/250 [00:30<03:54,  1.06s/it]
 12%|█▏        | 30/250 [00:30<03:01,  1.21it/s]
 12%|█▏        | 31/250 [00:30<02:19,  1.57it/s]
 13%|█▎        | 32/250 [00:31<02:42,  1.34it/s]
 13%|█▎        | 33/250 [00:32<02:02,  1.76it/s]
 14%|█▎        | 34/250 [00:32<01:49,  1.97it/s]
 14%|█▍        | 35/250 [00:33<02:03,  1.74it/s]
 14%|█▍        | 36/250 [00:33<01:56,  1.84it/s]
 15%|█▍        | 37/250 [00:34<02:23,  1.48it/s]
 15%|█▌        | 38/250 [00:35<02:21,  1.49it/s]
 16%|█▌        | 39/250 [00:36<02:43,  1.29it/s]
 16%|█▌        | 40/250 [00:37<03:23,  1.03it/s]
 16%|█▋        | 41/250 [00:38<03:35,  1.03s/it]
 17%|█▋        | 42/250 [00:39<03:24,  1.02it/s]
 17%|█▋        | 43/250 [00:43<06:38,  1.93s/it]
 18%|█▊        | 45/250 [00:45<04:26,  1.30s/it]
 18%|█▊        | 46/250 [00:45<03:35,  1.05s/it]
 19%|█▉        | 47/250 [00:46<03:17,  1.03it/s]
 19%|█▉        | 48/250 [00:46<02:38,  1.27it/s]
 20%|█▉        | 49/250 [00:47<02:27,  1.37it/s]
 20%|██        | 51/250 [00:49<02:58,  1.11it/s]
 21%|██        | 52/250 [00:49<02:45,  1.20it/s]
 21%|██        | 53/250 [00:50<02:32,  1.29it/s]
 22%|██▏       | 54/250 [00:51<02:39,  1.23it/s]
 22%|██▏       | 55/250 [00:51<02:06,  1.55it/s]
 22%|██▏       | 56/250 [00:52<01:55,  1.68it/s]
 23%|██▎       | 57/250 [00:53<02:35,  1.24it/s]
 23%|██▎       | 58/250 [00:54<02:34,  1.24it/s]
 24%|██▎       | 59/250 [00:56<04:07,  1.30s/it]
 24%|██▍       | 61/250 [00:56<02:20,  1.35it/s]
 25%|██▍       | 62/250 [00:57<02:27,  1.27it/s]
 25%|██▌       | 63/250 [00:58<02:38,  1.18it/s]
 26%|██▌       | 64/250 [00:59<02:14,  1.39it/s]
 26%|██▌       | 65/250 [01:00<03:11,  1.03s/it]
 27%|██▋       | 67/250 [01:02<02:47,  1.09it/s]
 27%|██▋       | 68/250 [01:02<02:18,  1.31it/s]
 28%|██▊       | 69/250 [01:03<01:57,  1.54it/s]
 28%|██▊       | 70/250 [01:03<01:32,  1.94it/s]
 28%|██▊       | 71/250 [01:06<03:48,  1.27s/it]
 29%|██▉       | 72/250 [01:08<04:15,  1.44s/it]
 30%|██▉       | 74/250 [01:08<02:42,  1.09it/s]
 30%|███       | 75/250 [01:09<02:33,  1.14it/s]
 30%|███       | 76/250 [01:10<02:40,  1.08it/s]
 31%|███       | 77/250 [01:10<02:07,  1.35it/s]
 31%|███       | 78/250 [01:12<02:38,  1.08it/s]
 32%|███▏      | 80/250 [01:15<03:26,  1.22s/it]
 32%|███▏      | 81/250 [01:16<03:19,  1.18s/it]
 33%|███▎      | 82/250 [01:16<02:37,  1.07it/s]
 33%|███▎      | 83/250 [01:17<02:03,  1.35it/s]
 34%|███▍      | 85/250 [01:17<01:32,  1.79it/s]
 35%|███▍      | 87/250 [01:18<01:14,  2.18it/s]
 35%|███▌      | 88/250 [01:18<01:14,  2.16it/s]
 36%|███▌      | 89/250 [01:19<01:36,  1.67it/s]
 36%|███▌      | 90/250 [01:20<01:35,  1.68it/s]
 37%|███▋      | 92/250 [01:20<01:00,  2.62it/s]
 37%|███▋      | 93/250 [01:22<01:50,  1.42it/s]
 38%|███▊      | 94/250 [01:23<02:12,  1.18it/s]
 38%|███▊      | 95/250 [01:24<02:10,  1.19it/s]
 38%|███▊      | 96/250 [01:25<01:59,  1.29it/s]
 39%|███▉      | 97/250 [01:27<02:58,  1.16s/it]
 39%|███▉      | 98/250 [01:27<02:12,  1.15it/s]
 40%|███▉      | 99/250 [01:27<01:44,  1.45it/s]
 40%|████      | 100/250 [01:27<01:22,  1.81it/s]
 40%|████      | 101/250 [01:28<01:19,  1.86it/s]
 41%|████      | 102/250 [01:28<01:10,  2.09it/s]
 41%|████      | 103/250 [01:29<01:07,  2.17it/s]
 42%|████▏     | 104/250 [01:29<01:08,  2.12it/s]
 42%|████▏     | 105/250 [01:30<01:21,  1.78it/s]
 42%|████▏     | 106/250 [01:30<01:14,  1.93it/s]
 43%|████▎     | 108/250 [01:30<00:44,  3.16it/s]
 44%|████▎     | 109/250 [01:31<00:40,  3.49it/s]
 44%|████▍     | 110/250 [01:31<00:42,  3.30it/s]
 45%|████▍     | 112/250 [01:31<00:31,  4.45it/s]
 45%|████▌     | 113/250 [01:32<00:35,  3.81it/s]
 46%|████▌     | 114/250 [01:33<01:25,  1.59it/s]
 46%|████▌     | 115/250 [01:36<02:46,  1.23s/it]
 46%|████▋     | 116/250 [01:37<02:26,  1.09s/it]
 47%|████▋     | 117/250 [01:39<02:44,  1.24s/it]
 47%|████▋     | 118/250 [01:41<03:27,  1.57s/it]
 48%|████▊     | 119/250 [01:42<03:18,  1.51s/it]
 48%|████▊     | 120/250 [01:43<02:34,  1.18s/it]
 49%|████▉     | 122/250 [01:44<01:46,  1.21it/s]
 49%|████▉     | 123/250 [01:46<02:21,  1.11s/it]
 50%|████▉     | 124/250 [01:47<02:22,  1.13s/it]
 50%|█████     | 125/250 [01:50<03:17,  1.58s/it]
 50%|█████     | 126/250 [01:50<02:30,  1.22s/it]
 51%|█████     | 127/250 [01:51<02:12,  1.08s/it]
 51%|█████     | 128/250 [01:51<02:02,  1.00s/it]
 52%|█████▏    | 129/250 [01:53<02:28,  1.22s/it]
 52%|█████▏    | 130/250 [01:54<02:24,  1.20s/it]
 52%|█████▏    | 131/250 [01:55<01:53,  1.05it/s]
 53%|█████▎    | 132/250 [01:56<02:05,  1.07s/it]
 53%|█████▎    | 133/250 [01:56<01:38,  1.19it/s]
 54%|█████▎    | 134/250 [01:57<01:39,  1.16it/s]
 54%|█████▍    | 135/250 [01:58<01:27,  1.31it/s]
 54%|█████▍    | 136/250 [01:59<01:28,  1.29it/s]
 55%|█████▍    | 137/250 [01:59<01:24,  1.34it/s]
 55%|█████▌    | 138/250 [02:01<01:55,  1.03s/it]
 56%|█████▌    | 139/250 [02:02<01:58,  1.07s/it]
 56%|█████▌    | 140/250 [02:02<01:29,  1.23it/s]
 56%|█████▋    | 141/250 [02:03<01:17,  1.40it/s]
 57%|█████▋    | 142/250 [02:03<00:59,  1.82it/s]
 57%|█████▋    | 143/250 [02:03<00:59,  1.80it/s]
 58%|█████▊    | 144/250 [02:05<01:24,  1.25it/s]
 58%|█████▊    | 145/250 [02:05<01:02,  1.69it/s]
 58%|█████▊    | 146/250 [02:06<01:06,  1.57it/s]
 59%|█████▉    | 147/250 [02:06<00:49,  2.07it/s]
 59%|█████▉    | 148/250 [02:07<01:21,  1.26it/s]
 60%|█████▉    | 149/250 [02:08<01:15,  1.34it/s]
 60%|██████    | 150/250 [02:09<01:19,  1.26it/s]
 60%|██████    | 151/250 [02:09<01:13,  1.34it/s]
 61%|██████    | 152/250 [02:10<00:56,  1.73it/s]
 61%|██████    | 153/250 [02:10<00:43,  2.23it/s]
 62%|██████▏   | 154/250 [02:10<00:42,  2.28it/s]
 62%|██████▏   | 155/250 [02:11<00:46,  2.04it/s]
 62%|██████▏   | 156/250 [02:11<00:44,  2.14it/s]
 63%|██████▎   | 157/250 [02:12<01:03,  1.46it/s]
 63%|██████▎   | 158/250 [02:13<00:56,  1.63it/s]
 64%|██████▎   | 159/250 [02:13<00:43,  2.08it/s]
 64%|██████▍   | 161/250 [02:14<00:42,  2.11it/s]
 65%|██████▌   | 163/250 [02:14<00:29,  2.99it/s]
 66%|██████▌   | 164/250 [02:15<00:32,  2.62it/s]
 66%|██████▌   | 165/250 [02:16<00:40,  2.09it/s]
 66%|██████▋   | 166/250 [02:16<00:34,  2.46it/s]
 67%|██████▋   | 168/250 [02:17<00:38,  2.13it/s]
 68%|██████▊   | 169/250 [02:20<01:18,  1.04it/s]
 68%|██████▊   | 170/250 [02:23<02:01,  1.52s/it]
 68%|██████▊   | 171/250 [02:23<01:32,  1.17s/it]
 69%|██████▉   | 173/250 [02:23<01:01,  1.26it/s]
 70%|██████▉   | 174/250 [02:24<00:52,  1.43it/s]
 70%|███████   | 175/250 [02:24<00:48,  1.55it/s]
 70%|███████   | 176/250 [02:25<00:41,  1.78it/s]
 71%|███████   | 177/250 [02:26<00:57,  1.27it/s]
 71%|███████   | 178/250 [02:27<00:55,  1.30it/s]
 72%|███████▏  | 179/250 [02:27<00:43,  1.64it/s]
 72%|███████▏  | 180/250 [02:29<01:05,  1.06it/s]
 72%|███████▏  | 181/250 [02:29<00:51,  1.34it/s]
 73%|███████▎  | 182/250 [02:30<00:45,  1.50it/s]
 73%|███████▎  | 183/250 [02:30<00:41,  1.60it/s]
 74%|███████▎  | 184/250 [02:30<00:34,  1.94it/s]
 74%|███████▍  | 185/250 [02:31<00:37,  1.74it/s]
 74%|███████▍  | 186/250 [02:32<00:44,  1.43it/s]
 75%|███████▍  | 187/250 [02:33<00:49,  1.27it/s]
 75%|███████▌  | 188/250 [02:36<01:24,  1.36s/it]
 76%|███████▌  | 189/250 [02:37<01:26,  1.42s/it]
 76%|███████▋  | 191/250 [02:41<01:34,  1.61s/it]
 77%|███████▋  | 192/250 [02:41<01:16,  1.33s/it]
 77%|███████▋  | 193/250 [02:42<01:11,  1.25s/it]
 78%|███████▊  | 195/250 [02:43<00:45,  1.22it/s]
 78%|███████▊  | 196/250 [02:44<00:47,  1.14it/s]
 79%|███████▉  | 197/250 [02:45<00:50,  1.04it/s]
 79%|███████▉  | 198/250 [02:45<00:39,  1.32it/s]
 80%|███████▉  | 199/250 [02:46<00:34,  1.46it/s]
 80%|████████  | 200/250 [02:46<00:31,  1.58it/s]
 81%|████████  | 202/250 [02:47<00:19,  2.44it/s]
 81%|████████  | 203/250 [02:49<00:40,  1.17it/s]
 82%|████████▏ | 204/250 [02:49<00:34,  1.33it/s]
 82%|████████▏ | 205/250 [02:50<00:29,  1.51it/s]
 82%|████████▏ | 206/250 [02:50<00:23,  1.87it/s]
 83%|████████▎ | 207/250 [02:51<00:26,  1.63it/s]
 83%|████████▎ | 208/250 [02:53<00:39,  1.06it/s]
 84%|████████▎ | 209/250 [02:53<00:34,  1.18it/s]
 84%|████████▍ | 211/250 [02:54<00:29,  1.33it/s]
 85%|████████▌ | 213/250 [02:55<00:24,  1.52it/s]
 86%|████████▌ | 215/250 [02:56<00:21,  1.65it/s]
 86%|████████▋ | 216/250 [02:58<00:23,  1.42it/s]
 87%|████████▋ | 218/250 [02:58<00:16,  1.98it/s]
 88%|████████▊ | 219/250 [02:58<00:14,  2.08it/s]
 88%|████████▊ | 220/250 [02:58<00:11,  2.52it/s]
 88%|████████▊ | 221/250 [02:59<00:11,  2.53it/s]
 89%|████████▉ | 222/250 [02:59<00:11,  2.38it/s]
 89%|████████▉ | 223/250 [03:00<00:11,  2.37it/s]
 90%|████████▉ | 224/250 [03:00<00:11,  2.33it/s]
 90%|█████████ | 225/250 [03:01<00:12,  2.02it/s]
 90%|█████████ | 226/250 [03:01<00:11,  2.12it/s]
 91%|█████████ | 227/250 [03:02<00:13,  1.69it/s]
 91%|█████████ | 228/250 [03:03<00:14,  1.56it/s]
 92%|█████████▏| 230/250 [03:04<00:11,  1.79it/s]
 92%|█████████▏| 231/250 [03:04<00:10,  1.79it/s]
 93%|█████████▎| 232/250 [03:05<00:11,  1.61it/s]
 93%|█████████▎| 233/250 [03:06<00:09,  1.71it/s]
 94%|█████████▎| 234/250 [03:07<00:11,  1.42it/s]
 94%|█████████▍| 235/250 [03:07<00:08,  1.87it/s]
 94%|█████████▍| 236/250 [03:08<00:09,  1.44it/s]
 95%|█████████▍| 237/250 [03:09<00:11,  1.15it/s]
 95%|█████████▌| 238/250 [03:10<00:10,  1.19it/s]
 96%|█████████▌| 239/250 [03:12<00:13,  1.26s/it]
 96%|█████████▌| 240/250 [03:13<00:10,  1.00s/it]
 96%|█████████▋| 241/250 [03:13<00:08,  1.04it/s]
 97%|█████████▋| 242/250 [03:14<00:05,  1.37it/s]
 97%|█████████▋| 243/250 [03:14<00:04,  1.45it/s]
 98%|█████████▊| 244/250 [03:15<00:03,  1.56it/s]
 98%|█████████▊| 245/250 [03:16<00:03,  1.35it/s]
 98%|█████████▊| 246/250 [03:16<00:02,  1.68it/s]
 99%|█████████▉| 247/250 [03:17<00:01,  1.63it/s]
 99%|█████████▉| 248/250 [03:19<00:02,  1.02s/it]
100%|█████████▉| 249/250 [03:19<00:00,  1.32it/s]
100%|██████████| 250/250 [03:24<00:00,  2.02s/it]
100%|██████████| 250/250 [03:24<00:00,  1.22it/s]
2025-05-14 12:55:15,380 - modnet - INFO - Loss per individual: ind 0: 0.117 	ind 1: 0.109 	ind 2: 0.112 	ind 3: 0.110 	ind 4: 0.114 	ind 5: 0.126 	ind 6: 0.110 	ind 7: 0.299 	ind 8: 0.110 	ind 9: 0.117 	ind 10: 0.107 	ind 11: 0.110 	ind 12: 0.110 	ind 13: 0.098 	ind 14: 0.105 	ind 15: 0.108 	ind 16: 0.116 	ind 17: 0.101 	ind 18: 0.111 	ind 19: 0.126 	ind 20: 0.112 	ind 21: 0.110 	ind 22: 0.103 	ind 23: 0.135 	ind 24: 0.113 	ind 25: 0.108 	ind 26: 0.124 	ind 27: 0.104 	ind 28: 0.116 	ind 29: 0.118 	ind 30: 0.111 	ind 31: 0.108 	ind 32: 0.109 	ind 33: 0.119 	ind 34: 0.133 	ind 35: 0.106 	ind 36: 0.111 	ind 37: 0.121 	ind 38: 0.123 	ind 39: 0.100 	ind 40: 0.125 	ind 41: 0.108 	ind 42: 0.106 	ind 43: 0.111 	ind 44: 0.100 	ind 45: 0.099 	ind 46: 0.111 	ind 47: 0.112 	ind 48: 0.126 	ind 49: 0.102 	
2025-05-14 12:55:15,383 - modnet - INFO - Generation number 2

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:08<37:00,  8.92s/it]
  1%|          | 2/250 [00:09<16:11,  3.92s/it]
  1%|          | 3/250 [00:09<09:31,  2.31s/it]
  2%|▏         | 4/250 [00:09<06:03,  1.48s/it]
  2%|▏         | 5/250 [00:12<07:55,  1.94s/it]
  2%|▏         | 6/250 [00:12<05:22,  1.32s/it]
  3%|▎         | 7/250 [00:13<04:06,  1.01s/it]
  3%|▎         | 8/250 [00:13<02:58,  1.36it/s]
  4%|▎         | 9/250 [00:14<04:02,  1.01s/it]
  4%|▍         | 10/250 [00:16<04:38,  1.16s/it]
  4%|▍         | 11/250 [00:16<03:38,  1.09it/s]
  5%|▍         | 12/250 [00:17<03:24,  1.16it/s]
  5%|▌         | 13/250 [00:18<03:36,  1.10it/s]
  6%|▌         | 14/250 [00:19<03:11,  1.24it/s]
  6%|▌         | 15/250 [00:19<02:47,  1.40it/s]
  6%|▋         | 16/250 [00:20<03:10,  1.23it/s]
  7%|▋         | 17/250 [00:21<03:01,  1.29it/s]
  7%|▋         | 18/250 [00:21<02:40,  1.44it/s]
  8%|▊         | 19/250 [00:22<02:09,  1.78it/s]
  8%|▊         | 20/250 [00:22<01:42,  2.25it/s]
  8%|▊         | 21/250 [00:23<02:09,  1.77it/s]
  9%|▉         | 23/250 [00:23<01:29,  2.52it/s]
 10%|▉         | 24/250 [00:24<01:35,  2.36it/s]
 10%|█         | 25/250 [00:24<01:56,  1.94it/s]
 10%|█         | 26/250 [00:25<01:47,  2.08it/s]
 11%|█         | 27/250 [00:26<03:01,  1.23it/s]
 11%|█         | 28/250 [00:27<02:16,  1.62it/s]
 12%|█▏        | 29/250 [00:28<02:41,  1.37it/s]
 12%|█▏        | 30/250 [00:28<02:52,  1.27it/s]
 12%|█▏        | 31/250 [00:29<02:47,  1.31it/s]
 13%|█▎        | 33/250 [00:29<01:44,  2.08it/s]
 14%|█▎        | 34/250 [00:30<02:09,  1.66it/s]
 14%|█▍        | 35/250 [00:31<02:05,  1.71it/s]
 14%|█▍        | 36/250 [00:32<02:36,  1.36it/s]
 15%|█▌        | 38/250 [00:33<02:32,  1.39it/s]
 16%|█▌        | 39/250 [00:34<02:37,  1.34it/s]
 16%|█▌        | 40/250 [00:35<02:33,  1.37it/s]
 16%|█▋        | 41/250 [00:35<01:57,  1.78it/s]
 17%|█▋        | 42/250 [00:37<02:48,  1.23it/s]
 17%|█▋        | 43/250 [00:37<02:40,  1.29it/s]
 18%|█▊        | 44/250 [00:39<03:41,  1.08s/it]
 18%|█▊        | 45/250 [00:41<04:10,  1.22s/it]
 18%|█▊        | 46/250 [00:41<03:07,  1.09it/s]
 19%|█▉        | 47/250 [00:42<03:02,  1.11it/s]
 19%|█▉        | 48/250 [00:42<02:18,  1.46it/s]
 20%|█▉        | 49/250 [00:43<02:31,  1.32it/s]
 20%|██        | 50/250 [00:46<05:09,  1.55s/it]
 21%|██        | 52/250 [00:46<02:53,  1.14it/s]
 21%|██        | 53/250 [00:47<02:37,  1.25it/s]
 22%|██▏       | 54/250 [00:47<02:21,  1.39it/s]
 22%|██▏       | 55/250 [00:48<02:17,  1.42it/s]
 22%|██▏       | 56/250 [00:48<01:52,  1.72it/s]
 23%|██▎       | 57/250 [00:49<01:53,  1.69it/s]
 23%|██▎       | 58/250 [00:49<01:35,  2.00it/s]
 24%|██▎       | 59/250 [00:50<01:58,  1.61it/s]
 24%|██▍       | 60/250 [00:52<03:24,  1.08s/it]
 25%|██▍       | 62/250 [00:55<03:36,  1.15s/it]
 25%|██▌       | 63/250 [00:57<04:04,  1.31s/it]
 26%|██▌       | 64/250 [00:57<03:10,  1.02s/it]
 26%|██▌       | 65/250 [00:57<02:28,  1.24it/s]
 27%|██▋       | 67/250 [00:57<01:36,  1.90it/s]
 27%|██▋       | 68/250 [00:58<01:57,  1.54it/s]
 28%|██▊       | 70/250 [00:59<01:20,  2.24it/s]
 28%|██▊       | 71/250 [00:59<01:21,  2.19it/s]
 29%|██▉       | 73/250 [01:00<01:01,  2.89it/s]
 30%|██▉       | 74/250 [01:00<01:09,  2.53it/s]
 30%|███       | 75/250 [01:00<01:07,  2.60it/s]
 31%|███       | 77/250 [01:01<01:04,  2.68it/s]
 31%|███       | 78/250 [01:01<00:59,  2.87it/s]
 32%|███▏      | 79/250 [01:02<01:00,  2.81it/s]
 32%|███▏      | 80/250 [01:02<00:50,  3.40it/s]
 32%|███▏      | 81/250 [01:02<00:44,  3.79it/s]
 33%|███▎      | 82/250 [01:03<00:58,  2.88it/s]
 34%|███▎      | 84/250 [01:03<00:42,  3.92it/s]
 34%|███▍      | 85/250 [01:03<00:46,  3.57it/s]
 34%|███▍      | 86/250 [01:04<00:50,  3.25it/s]
 35%|███▍      | 87/250 [01:05<01:30,  1.79it/s]
 35%|███▌      | 88/250 [01:05<01:15,  2.15it/s]
 36%|███▌      | 89/250 [01:05<01:05,  2.47it/s]
 36%|███▌      | 90/250 [01:06<00:51,  3.11it/s]
 36%|███▋      | 91/250 [01:06<00:41,  3.85it/s]
 37%|███▋      | 92/250 [01:07<01:24,  1.87it/s]
 37%|███▋      | 93/250 [01:07<01:10,  2.24it/s]
 38%|███▊      | 94/250 [01:07<01:04,  2.42it/s]
 38%|███▊      | 95/250 [01:09<01:39,  1.56it/s]
 38%|███▊      | 96/250 [01:10<02:15,  1.13it/s]
 39%|███▉      | 97/250 [01:10<01:41,  1.51it/s]
 39%|███▉      | 98/250 [01:11<01:56,  1.30it/s]
 40%|███▉      | 99/250 [01:12<01:45,  1.43it/s]
 40%|████      | 100/250 [01:12<01:46,  1.41it/s]
 40%|████      | 101/250 [01:13<01:32,  1.61it/s]
 41%|████      | 102/250 [01:14<01:48,  1.36it/s]
 41%|████      | 103/250 [01:14<01:25,  1.73it/s]
 42%|████▏     | 105/250 [01:15<01:17,  1.87it/s]
 42%|████▏     | 106/250 [01:16<01:32,  1.55it/s]
 43%|████▎     | 107/250 [01:16<01:22,  1.74it/s]
 43%|████▎     | 108/250 [01:17<01:37,  1.46it/s]
 44%|████▎     | 109/250 [01:18<01:17,  1.82it/s]
 44%|████▍     | 110/250 [01:18<01:09,  2.00it/s]
 44%|████▍     | 111/250 [01:19<01:45,  1.32it/s]
 45%|████▍     | 112/250 [01:20<01:23,  1.66it/s]
 45%|████▌     | 113/250 [01:20<01:12,  1.89it/s]
 46%|████▌     | 114/250 [01:20<01:07,  2.01it/s]
 46%|████▌     | 115/250 [01:22<01:55,  1.17it/s]
 46%|████▋     | 116/250 [01:22<01:25,  1.57it/s]
 47%|████▋     | 117/250 [01:22<01:05,  2.04it/s]
 47%|████▋     | 118/250 [01:24<01:41,  1.30it/s]
 48%|████▊     | 119/250 [01:25<01:46,  1.23it/s]
 48%|████▊     | 120/250 [01:26<02:02,  1.06it/s]
 48%|████▊     | 121/250 [01:26<01:43,  1.24it/s]
 49%|████▉     | 122/250 [01:27<01:23,  1.53it/s]
 49%|████▉     | 123/250 [01:27<01:25,  1.48it/s]
 50%|████▉     | 124/250 [01:29<02:01,  1.04it/s]
 50%|█████     | 125/250 [01:29<01:33,  1.34it/s]
 50%|█████     | 126/250 [01:35<04:26,  2.15s/it]
 51%|█████     | 127/250 [01:36<03:44,  1.83s/it]
 51%|█████     | 128/250 [01:38<04:11,  2.06s/it]
 52%|█████▏    | 130/250 [01:39<02:24,  1.21s/it]
 52%|█████▏    | 131/250 [01:41<02:42,  1.36s/it]
 53%|█████▎    | 132/250 [01:43<02:59,  1.52s/it]
 53%|█████▎    | 133/250 [01:45<03:11,  1.64s/it]
 54%|█████▎    | 134/250 [01:47<03:49,  1.98s/it]
 54%|█████▍    | 135/250 [01:48<03:04,  1.60s/it]
 54%|█████▍    | 136/250 [01:48<02:20,  1.23s/it]
 55%|█████▍    | 137/250 [01:49<01:45,  1.07it/s]
 55%|█████▌    | 138/250 [01:50<01:44,  1.07it/s]
 56%|█████▌    | 139/250 [01:50<01:17,  1.44it/s]
 56%|█████▌    | 140/250 [01:50<01:08,  1.61it/s]
 56%|█████▋    | 141/250 [01:50<00:50,  2.15it/s]
 57%|█████▋    | 142/250 [01:51<01:10,  1.53it/s]
 57%|█████▋    | 143/250 [01:52<00:58,  1.83it/s]
 58%|█████▊    | 144/250 [01:52<00:50,  2.12it/s]
 58%|█████▊    | 145/250 [01:53<00:53,  1.95it/s]
 58%|█████▊    | 146/250 [01:53<00:42,  2.44it/s]
 59%|█████▉    | 147/250 [01:55<01:48,  1.06s/it]
 59%|█████▉    | 148/250 [01:56<01:40,  1.02it/s]
 60%|█████▉    | 149/250 [01:57<01:40,  1.00it/s]
 60%|██████    | 150/250 [01:58<01:33,  1.07it/s]
 60%|██████    | 151/250 [01:59<01:29,  1.11it/s]
 61%|██████    | 152/250 [01:59<01:08,  1.44it/s]
 61%|██████    | 153/250 [02:02<02:23,  1.48s/it]
 62%|██████▏   | 154/250 [02:03<01:51,  1.16s/it]
 62%|██████▏   | 156/250 [02:04<01:28,  1.06it/s]
 63%|██████▎   | 157/250 [02:04<01:15,  1.23it/s]
 63%|██████▎   | 158/250 [02:05<01:06,  1.39it/s]
 64%|██████▎   | 159/250 [02:05<00:57,  1.60it/s]
 64%|██████▍   | 160/250 [02:06<00:53,  1.69it/s]
 64%|██████▍   | 161/250 [02:07<00:55,  1.60it/s]
 65%|██████▍   | 162/250 [02:08<01:12,  1.22it/s]
 65%|██████▌   | 163/250 [02:09<01:15,  1.15it/s]
 66%|██████▌   | 164/250 [02:10<01:19,  1.08it/s]
 66%|██████▌   | 165/250 [02:13<02:08,  1.51s/it]
 66%|██████▋   | 166/250 [02:13<01:38,  1.17s/it]
 67%|██████▋   | 167/250 [02:13<01:14,  1.12it/s]
 67%|██████▋   | 168/250 [02:13<00:54,  1.50it/s]
 68%|██████▊   | 170/250 [02:14<00:41,  1.94it/s]
 68%|██████▊   | 171/250 [02:15<00:36,  2.14it/s]
 69%|██████▉   | 172/250 [02:15<00:32,  2.43it/s]
 69%|██████▉   | 173/250 [02:15<00:38,  2.00it/s]
 70%|██████▉   | 174/250 [02:16<00:31,  2.43it/s]
 70%|███████   | 175/250 [02:16<00:38,  1.97it/s]
 70%|███████   | 176/250 [02:18<00:51,  1.44it/s]
 71%|███████   | 177/250 [02:18<00:41,  1.77it/s]
 71%|███████   | 178/250 [02:19<00:51,  1.40it/s]
 72%|███████▏  | 179/250 [02:20<00:58,  1.22it/s]
 72%|███████▏  | 180/250 [02:20<00:42,  1.64it/s]
 72%|███████▏  | 181/250 [02:20<00:32,  2.11it/s]
 73%|███████▎  | 183/250 [02:21<00:29,  2.27it/s]
 74%|███████▍  | 185/250 [02:22<00:32,  1.99it/s]
 74%|███████▍  | 186/250 [02:23<00:38,  1.67it/s]
 75%|███████▍  | 187/250 [02:25<00:50,  1.26it/s]
 75%|███████▌  | 188/250 [02:25<00:40,  1.52it/s]
 76%|███████▌  | 189/250 [02:27<00:57,  1.06it/s]
 76%|███████▌  | 190/250 [02:27<00:44,  1.36it/s]
 77%|███████▋  | 192/250 [02:27<00:25,  2.26it/s]
 77%|███████▋  | 193/250 [02:27<00:20,  2.75it/s]
 78%|███████▊  | 194/250 [02:28<00:26,  2.14it/s]
 78%|███████▊  | 195/250 [02:28<00:21,  2.55it/s]
 78%|███████▊  | 196/250 [02:29<00:26,  2.04it/s]
 79%|███████▉  | 197/250 [02:29<00:26,  1.97it/s]
 80%|███████▉  | 199/250 [02:29<00:15,  3.24it/s]
 80%|████████  | 200/250 [02:30<00:24,  2.01it/s]
 80%|████████  | 201/250 [02:33<00:48,  1.01it/s]
 81%|████████  | 202/250 [02:34<00:48,  1.01s/it]
 81%|████████  | 203/250 [02:35<00:46,  1.00it/s]
 82%|████████▏ | 204/250 [02:35<00:40,  1.15it/s]
 82%|████████▏ | 206/250 [02:36<00:22,  1.91it/s]
 83%|████████▎ | 207/250 [02:36<00:23,  1.87it/s]
 84%|████████▎ | 209/250 [02:37<00:18,  2.19it/s]
 84%|████████▍ | 210/250 [02:37<00:17,  2.34it/s]
 84%|████████▍ | 211/250 [02:37<00:14,  2.76it/s]
 85%|████████▍ | 212/250 [02:38<00:14,  2.58it/s]
 85%|████████▌ | 213/250 [02:38<00:12,  2.85it/s]
 86%|████████▌ | 215/250 [02:40<00:18,  1.88it/s]
 86%|████████▋ | 216/250 [02:41<00:20,  1.62it/s]
 87%|████████▋ | 217/250 [02:43<00:31,  1.04it/s]
 87%|████████▋ | 218/250 [02:44<00:39,  1.23s/it]
 88%|████████▊ | 220/250 [02:45<00:27,  1.11it/s]
 88%|████████▊ | 221/250 [02:47<00:30,  1.07s/it]
 89%|████████▉ | 222/250 [02:48<00:28,  1.02s/it]
 89%|████████▉ | 223/250 [02:48<00:23,  1.13it/s]
 90%|████████▉ | 224/250 [02:49<00:23,  1.11it/s]
 90%|█████████ | 225/250 [02:50<00:17,  1.47it/s]
 91%|█████████ | 227/250 [02:50<00:09,  2.30it/s]
 92%|█████████▏| 229/250 [02:50<00:07,  2.97it/s]
 92%|█████████▏| 231/250 [02:51<00:07,  2.46it/s]
 93%|█████████▎| 232/250 [02:51<00:06,  2.64it/s]
 93%|█████████▎| 233/250 [02:52<00:06,  2.44it/s]
 94%|█████████▎| 234/250 [02:53<00:08,  1.84it/s]
 94%|█████████▍| 236/250 [02:54<00:08,  1.73it/s]
 95%|█████████▍| 237/250 [02:55<00:07,  1.64it/s]
 96%|█████████▌| 239/250 [02:55<00:04,  2.22it/s]
 96%|█████████▌| 240/250 [02:56<00:05,  1.89it/s]
 96%|█████████▋| 241/250 [02:56<00:04,  2.07it/s]
 97%|█████████▋| 242/250 [02:58<00:06,  1.25it/s]
 97%|█████████▋| 243/250 [02:59<00:05,  1.31it/s]
 98%|█████████▊| 244/250 [02:59<00:04,  1.41it/s]
 98%|█████████▊| 245/250 [03:01<00:04,  1.01it/s]
 98%|█████████▊| 246/250 [03:09<00:11,  2.87s/it]
 99%|█████████▉| 247/250 [03:13<00:09,  3.28s/it]
 99%|█████████▉| 248/250 [03:14<00:05,  2.64s/it]
100%|█████████▉| 249/250 [03:15<00:02,  2.21s/it]
100%|██████████| 250/250 [03:18<00:00,  2.45s/it]
100%|██████████| 250/250 [03:18<00:00,  1.26it/s]
2025-05-14 12:58:34,166 - modnet - INFO - Loss per individual: ind 0: 0.131 	ind 1: 0.214 	ind 2: 0.112 	ind 3: 0.106 	ind 4: 0.108 	ind 5: 0.111 	ind 6: 0.120 	ind 7: 0.109 	ind 8: 0.126 	ind 9: 0.112 	ind 10: 0.108 	ind 11: 0.098 	ind 12: 0.111 	ind 13: 0.111 	ind 14: 0.107 	ind 15: 0.148 	ind 16: 0.117 	ind 17: 0.124 	ind 18: 0.283 	ind 19: 0.131 	ind 20: 0.109 	ind 21: 0.105 	ind 22: 29.708 	ind 23: 0.111 	ind 24: 0.123 	ind 25: 0.109 	ind 26: 0.112 	ind 27: 0.108 	ind 28: 0.109 	ind 29: 0.116 	ind 30: 0.108 	ind 31: 0.111 	ind 32: 0.116 	ind 33: 0.114 	ind 34: 0.111 	ind 35: 0.133 	ind 36: 0.107 	ind 37: 0.125 	ind 38: 0.166 	ind 39: 0.126 	ind 40: 0.112 	ind 41: 0.103 	ind 42: 0.210 	ind 43: 0.106 	ind 44: 0.107 	ind 45: 0.116 	ind 46: 0.111 	ind 47: 0.126 	ind 48: 0.111 	ind 49: 0.107 	
2025-05-14 12:58:34,167 - modnet - INFO - Generation number 3

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:08<35:33,  8.57s/it]
  1%|          | 2/250 [00:10<18:28,  4.47s/it]
  1%|          | 3/250 [00:10<10:22,  2.52s/it]
  2%|▏         | 4/250 [00:10<07:03,  1.72s/it]
  2%|▏         | 5/250 [00:11<04:46,  1.17s/it]
  2%|▏         | 6/250 [00:11<04:00,  1.02it/s]
  3%|▎         | 7/250 [00:11<02:59,  1.35it/s]
  3%|▎         | 8/250 [00:12<02:48,  1.44it/s]
  4%|▎         | 9/250 [00:13<03:13,  1.24it/s]
  4%|▍         | 10/250 [00:13<02:24,  1.66it/s]
  4%|▍         | 11/250 [00:14<02:06,  1.89it/s]
  5%|▍         | 12/250 [00:15<03:27,  1.15it/s]
  5%|▌         | 13/250 [00:16<03:24,  1.16it/s]
  6%|▌         | 14/250 [00:16<02:39,  1.48it/s]
  6%|▌         | 15/250 [00:17<02:21,  1.67it/s]
  6%|▋         | 16/250 [00:17<01:47,  2.18it/s]
  7%|▋         | 17/250 [00:18<02:36,  1.49it/s]
  7%|▋         | 18/250 [00:19<03:14,  1.19it/s]
  8%|▊         | 19/250 [00:20<03:26,  1.12it/s]
  8%|▊         | 20/250 [00:23<05:42,  1.49s/it]
  8%|▊         | 21/250 [00:23<04:09,  1.09s/it]
  9%|▉         | 22/250 [00:27<06:45,  1.78s/it]
  9%|▉         | 23/250 [00:27<04:50,  1.28s/it]
 10%|▉         | 24/250 [00:28<04:50,  1.28s/it]
 10%|█         | 25/250 [00:28<03:29,  1.07it/s]
 10%|█         | 26/250 [00:29<03:17,  1.13it/s]
 11%|█         | 28/250 [00:30<02:46,  1.34it/s]
 12%|█▏        | 29/250 [00:32<04:08,  1.13s/it]
 13%|█▎        | 32/250 [00:33<02:13,  1.63it/s]
 13%|█▎        | 33/250 [00:33<02:12,  1.63it/s]
 14%|█▎        | 34/250 [00:34<01:50,  1.96it/s]
 14%|█▍        | 35/250 [00:36<03:46,  1.05s/it]
 15%|█▍        | 37/250 [00:37<02:39,  1.34it/s]
 16%|█▌        | 39/250 [00:38<02:32,  1.39it/s]
 16%|█▌        | 40/250 [00:39<02:07,  1.65it/s]
 16%|█▋        | 41/250 [00:40<02:55,  1.19it/s]
 17%|█▋        | 42/250 [00:41<02:46,  1.25it/s]
 18%|█▊        | 44/250 [00:42<02:14,  1.53it/s]
 18%|█▊        | 45/250 [00:44<03:24,  1.00it/s]
 18%|█▊        | 46/250 [00:45<03:26,  1.01s/it]
 19%|█▉        | 47/250 [00:45<02:54,  1.17it/s]
 19%|█▉        | 48/250 [00:46<02:41,  1.25it/s]
 20%|█▉        | 49/250 [00:46<02:02,  1.63it/s]
 20%|██        | 50/250 [00:47<01:52,  1.78it/s]
 20%|██        | 51/250 [00:47<01:29,  2.24it/s]
 21%|██        | 52/250 [00:47<01:44,  1.90it/s]
 22%|██▏       | 54/250 [00:48<01:03,  3.10it/s]
 22%|██▏       | 55/250 [00:48<01:02,  3.14it/s]
 23%|██▎       | 57/250 [00:48<00:41,  4.67it/s]
 23%|██▎       | 58/250 [00:50<01:58,  1.62it/s]
 24%|██▎       | 59/250 [00:52<02:44,  1.16it/s]
 24%|██▍       | 61/250 [00:52<02:00,  1.57it/s]
 25%|██▍       | 62/250 [00:55<03:22,  1.07s/it]
 26%|██▌       | 64/250 [00:55<02:21,  1.32it/s]
 26%|██▌       | 65/250 [00:56<02:20,  1.32it/s]
 26%|██▋       | 66/250 [00:56<01:54,  1.60it/s]
 27%|██▋       | 67/250 [00:57<02:07,  1.43it/s]
 27%|██▋       | 68/250 [00:59<03:02,  1.01s/it]
 28%|██▊       | 70/250 [01:00<02:12,  1.35it/s]
 28%|██▊       | 71/250 [01:00<01:59,  1.50it/s]
 29%|██▉       | 72/250 [01:01<01:44,  1.70it/s]
 29%|██▉       | 73/250 [01:01<01:21,  2.18it/s]
 30%|██▉       | 74/250 [01:02<02:18,  1.27it/s]
 30%|███       | 75/250 [01:03<01:51,  1.57it/s]
 30%|███       | 76/250 [01:03<01:26,  2.02it/s]
 31%|███       | 77/250 [01:04<01:55,  1.49it/s]
 31%|███       | 78/250 [01:05<02:18,  1.24it/s]
 32%|███▏      | 79/250 [01:07<03:13,  1.13s/it]
 32%|███▏      | 80/250 [01:07<02:38,  1.07it/s]
 32%|███▏      | 81/250 [01:08<02:42,  1.04it/s]
 33%|███▎      | 83/250 [01:10<02:08,  1.30it/s]
 34%|███▍      | 85/250 [01:11<02:19,  1.19it/s]
 34%|███▍      | 86/250 [01:12<01:56,  1.41it/s]
 35%|███▍      | 87/250 [01:13<02:17,  1.19it/s]
 35%|███▌      | 88/250 [01:14<02:29,  1.08it/s]
 36%|███▌      | 89/250 [01:14<02:05,  1.28it/s]
 36%|███▌      | 90/250 [01:15<02:11,  1.22it/s]
 36%|███▋      | 91/250 [01:16<02:15,  1.17it/s]
 37%|███▋      | 92/250 [01:19<03:40,  1.40s/it]
 37%|███▋      | 93/250 [01:20<03:00,  1.15s/it]
 38%|███▊      | 94/250 [01:21<03:03,  1.17s/it]
 38%|███▊      | 95/250 [01:21<02:34,  1.00it/s]
 38%|███▊      | 96/250 [01:22<02:26,  1.05it/s]
 39%|███▉      | 97/250 [01:23<02:10,  1.17it/s]
 39%|███▉      | 98/250 [01:23<01:36,  1.57it/s]
 40%|███▉      | 99/250 [01:23<01:23,  1.80it/s]
 40%|████      | 100/250 [01:24<01:18,  1.91it/s]
 40%|████      | 101/250 [01:25<01:54,  1.31it/s]
 41%|████      | 103/250 [01:27<01:58,  1.24it/s]
 42%|████▏     | 104/250 [01:27<01:50,  1.32it/s]
 42%|████▏     | 105/250 [01:28<01:49,  1.32it/s]
 42%|████▏     | 106/250 [01:30<02:29,  1.04s/it]
 43%|████▎     | 107/250 [01:32<02:53,  1.21s/it]
 44%|████▎     | 109/250 [01:32<01:38,  1.43it/s]
 44%|████▍     | 110/250 [01:32<01:20,  1.73it/s]
 44%|████▍     | 111/250 [01:33<01:29,  1.55it/s]
 45%|████▍     | 112/250 [01:36<02:52,  1.25s/it]
 45%|████▌     | 113/250 [01:37<02:37,  1.15s/it]
 46%|████▌     | 114/250 [01:38<02:52,  1.27s/it]
 46%|████▌     | 115/250 [01:40<02:57,  1.32s/it]
 46%|████▋     | 116/250 [01:42<03:40,  1.64s/it]
 47%|████▋     | 117/250 [01:43<03:25,  1.54s/it]
 47%|████▋     | 118/250 [01:44<02:58,  1.35s/it]
 48%|████▊     | 119/250 [01:45<02:20,  1.07s/it]
 48%|████▊     | 120/250 [01:45<01:51,  1.17it/s]
 48%|████▊     | 121/250 [01:46<01:38,  1.31it/s]
 49%|████▉     | 122/250 [01:47<01:48,  1.18it/s]
 49%|████▉     | 123/250 [01:47<01:20,  1.58it/s]
 50%|█████     | 125/250 [01:47<01:02,  2.00it/s]
 50%|█████     | 126/250 [01:48<00:51,  2.38it/s]
 51%|█████     | 127/250 [01:48<00:49,  2.48it/s]
 51%|█████     | 128/250 [01:48<00:41,  2.91it/s]
 52%|█████▏    | 129/250 [01:48<00:37,  3.25it/s]
 52%|█████▏    | 131/250 [01:49<00:27,  4.35it/s]
 53%|█████▎    | 132/250 [01:51<01:21,  1.45it/s]
 53%|█████▎    | 133/250 [01:51<01:21,  1.44it/s]
 54%|█████▎    | 134/250 [01:52<01:02,  1.85it/s]
 54%|█████▍    | 135/250 [01:53<01:34,  1.22it/s]
 54%|█████▍    | 136/250 [01:54<01:39,  1.15it/s]
 55%|█████▍    | 137/250 [01:56<02:03,  1.09s/it]
 55%|█████▌    | 138/250 [01:57<02:03,  1.10s/it]
 56%|█████▌    | 139/250 [01:57<01:36,  1.16it/s]
 56%|█████▌    | 140/250 [01:58<01:18,  1.39it/s]
 56%|█████▋    | 141/250 [01:59<01:32,  1.17it/s]
 57%|█████▋    | 142/250 [01:59<01:10,  1.53it/s]
 57%|█████▋    | 143/250 [02:00<01:12,  1.47it/s]
 58%|█████▊    | 144/250 [02:00<01:01,  1.73it/s]
 58%|█████▊    | 146/250 [02:01<00:46,  2.23it/s]
 59%|█████▉    | 147/250 [02:02<01:19,  1.29it/s]
 59%|█████▉    | 148/250 [02:03<01:12,  1.40it/s]
 60%|█████▉    | 149/250 [02:04<01:34,  1.07it/s]
 60%|██████    | 150/250 [02:06<01:40,  1.01s/it]
 61%|██████    | 152/250 [02:06<00:57,  1.70it/s]
 61%|██████    | 153/250 [02:07<01:08,  1.42it/s]
 62%|██████▏   | 154/250 [02:07<00:59,  1.62it/s]
 62%|██████▏   | 155/250 [02:08<00:55,  1.71it/s]
 62%|██████▏   | 156/250 [02:08<00:52,  1.79it/s]
 63%|██████▎   | 157/250 [02:09<00:58,  1.60it/s]
 63%|██████▎   | 158/250 [02:10<01:06,  1.38it/s]
 64%|██████▍   | 160/250 [02:11<01:00,  1.48it/s]
 64%|██████▍   | 161/250 [02:13<01:14,  1.20it/s]
 65%|██████▍   | 162/250 [02:14<01:34,  1.08s/it]
 65%|██████▌   | 163/250 [02:15<01:28,  1.02s/it]
 66%|██████▌   | 164/250 [02:15<01:05,  1.31it/s]
 66%|██████▌   | 165/250 [02:17<01:35,  1.13s/it]
 67%|██████▋   | 167/250 [02:18<00:59,  1.40it/s]
 68%|██████▊   | 169/250 [02:18<00:38,  2.08it/s]
 68%|██████▊   | 170/250 [02:18<00:33,  2.38it/s]
 68%|██████▊   | 171/250 [02:18<00:31,  2.50it/s]
 69%|██████▉   | 172/250 [02:19<00:29,  2.62it/s]
 69%|██████▉   | 173/250 [02:19<00:31,  2.44it/s]
 70%|██████▉   | 174/250 [02:21<01:01,  1.23it/s]
 70%|███████   | 175/250 [02:24<01:46,  1.42s/it]
 70%|███████   | 176/250 [02:25<01:35,  1.29s/it]
 71%|███████   | 177/250 [02:27<01:43,  1.41s/it]
 72%|███████▏  | 179/250 [02:27<00:56,  1.26it/s]
 72%|███████▏  | 181/250 [02:28<00:42,  1.64it/s]
 73%|███████▎  | 182/250 [02:29<00:59,  1.14it/s]
 73%|███████▎  | 183/250 [02:31<01:03,  1.06it/s]
 74%|███████▎  | 184/250 [02:31<00:55,  1.19it/s]
 74%|███████▍  | 185/250 [02:31<00:46,  1.39it/s]
 74%|███████▍  | 186/250 [02:32<00:42,  1.51it/s]
 75%|███████▍  | 187/250 [02:33<00:45,  1.38it/s]
 75%|███████▌  | 188/250 [02:33<00:34,  1.81it/s]
 76%|███████▌  | 189/250 [02:33<00:27,  2.20it/s]
 76%|███████▌  | 190/250 [02:34<00:25,  2.35it/s]
 76%|███████▋  | 191/250 [02:34<00:23,  2.47it/s]
 77%|███████▋  | 192/250 [02:34<00:21,  2.66it/s]
 78%|███████▊  | 194/250 [02:36<00:33,  1.65it/s]
 78%|███████▊  | 195/250 [02:37<00:37,  1.47it/s]
 78%|███████▊  | 196/250 [02:37<00:34,  1.57it/s]
 79%|███████▉  | 197/250 [02:38<00:27,  1.95it/s]
 79%|███████▉  | 198/250 [02:40<00:58,  1.13s/it]
 80%|███████▉  | 199/250 [02:41<00:46,  1.10it/s]
 80%|████████  | 200/250 [02:42<00:46,  1.08it/s]
 80%|████████  | 201/250 [02:43<00:49,  1.01s/it]
 81%|████████  | 202/250 [02:44<00:50,  1.05s/it]
 81%|████████  | 203/250 [02:44<00:41,  1.12it/s]
 82%|████████▏ | 204/250 [02:47<01:02,  1.36s/it]
 82%|████████▏ | 205/250 [02:47<00:50,  1.12s/it]
 82%|████████▏ | 206/250 [02:49<00:54,  1.23s/it]
 83%|████████▎ | 207/250 [02:51<00:59,  1.39s/it]
 83%|████████▎ | 208/250 [02:52<01:01,  1.47s/it]
 84%|████████▎ | 209/250 [02:53<00:51,  1.24s/it]
 84%|████████▍ | 210/250 [02:54<00:42,  1.07s/it]
 84%|████████▍ | 211/250 [02:54<00:31,  1.25it/s]
 85%|████████▍ | 212/250 [02:54<00:26,  1.43it/s]
 86%|████████▌ | 214/250 [02:57<00:31,  1.16it/s]
 86%|████████▋ | 216/250 [02:57<00:21,  1.56it/s]
 87%|████████▋ | 217/250 [02:57<00:17,  1.88it/s]
 88%|████████▊ | 219/250 [02:58<00:11,  2.62it/s]
 88%|████████▊ | 220/250 [02:58<00:13,  2.18it/s]
 88%|████████▊ | 221/250 [03:00<00:24,  1.18it/s]
 89%|████████▉ | 222/250 [03:01<00:22,  1.25it/s]
 89%|████████▉ | 223/250 [03:02<00:21,  1.23it/s]
 90%|█████████ | 225/250 [03:02<00:13,  1.90it/s]
 90%|█████████ | 226/250 [03:04<00:19,  1.25it/s]
 91%|█████████ | 227/250 [03:05<00:18,  1.25it/s]
 92%|█████████▏| 230/250 [03:05<00:08,  2.24it/s]
 92%|█████████▏| 231/250 [03:05<00:07,  2.50it/s]
 93%|█████████▎| 232/250 [03:06<00:06,  2.73it/s]
 93%|█████████▎| 233/250 [03:07<00:08,  1.89it/s]
 94%|█████████▎| 234/250 [03:07<00:07,  2.21it/s]
 94%|█████████▍| 236/250 [03:07<00:04,  3.06it/s]
 95%|█████████▍| 237/250 [03:08<00:06,  2.02it/s]
 95%|█████████▌| 238/250 [03:09<00:05,  2.03it/s]
 96%|█████████▌| 239/250 [03:09<00:06,  1.76it/s]
 96%|█████████▌| 240/250 [03:10<00:04,  2.13it/s]
 96%|█████████▋| 241/250 [03:11<00:05,  1.68it/s]
 97%|█████████▋| 242/250 [03:12<00:05,  1.40it/s]
 97%|█████████▋| 243/250 [03:12<00:03,  1.79it/s]
 98%|█████████▊| 244/250 [03:12<00:03,  1.82it/s]
 98%|█████████▊| 245/250 [03:13<00:02,  2.08it/s]
 98%|█████████▊| 246/250 [03:13<00:02,  1.83it/s]
 99%|█████████▉| 247/250 [03:14<00:01,  1.63it/s]
100%|█████████▉| 249/250 [03:16<00:00,  1.20it/s]
100%|██████████| 250/250 [03:19<00:00,  1.27s/it]
100%|██████████| 250/250 [03:19<00:00,  1.25it/s]
2025-05-14 13:01:53,544 - modnet - INFO - Loss per individual: ind 0: 0.121 	ind 1: 0.107 	ind 2: 0.138 	ind 3: 0.105 	ind 4: 0.116 	ind 5: 0.100 	ind 6: 0.127 	ind 7: 0.109 	ind 8: 0.112 	ind 9: 0.119 	ind 10: 0.099 	ind 11: 0.106 	ind 12: 0.105 	ind 13: 0.112 	ind 14: 0.114 	ind 15: 7.217 	ind 16: 0.104 	ind 17: 0.109 	ind 18: 0.105 	ind 19: 0.108 	ind 20: 0.124 	ind 21: 0.103 	ind 22: 0.122 	ind 23: 0.141 	ind 24: 0.111 	ind 25: 0.111 	ind 26: 0.111 	ind 27: 0.110 	ind 28: 0.107 	ind 29: 0.118 	ind 30: 0.112 	ind 31: 0.108 	ind 32: 0.103 	ind 33: 0.106 	ind 34: 0.119 	ind 35: 0.105 	ind 36: 0.104 	ind 37: 0.105 	ind 38: 0.121 	ind 39: 0.104 	ind 40: 0.119 	ind 41: 0.111 	ind 42: 0.105 	ind 43: 0.114 	ind 44: 0.104 	ind 45: 0.140 	ind 46: 0.107 	ind 47: 0.112 	ind 48: 0.108 	ind 49: 0.118 	
2025-05-14 13:01:53,546 - modnet - INFO - Generation number 4

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:10<43:01, 10.37s/it]
  1%|          | 2/250 [00:14<28:39,  6.93s/it]
  1%|          | 3/250 [00:15<17:01,  4.14s/it]
  2%|▏         | 4/250 [00:16<10:59,  2.68s/it]
  2%|▏         | 5/250 [00:16<07:48,  1.91s/it]
  2%|▏         | 6/250 [00:17<06:02,  1.49s/it]
  3%|▎         | 7/250 [00:18<05:25,  1.34s/it]
  3%|▎         | 8/250 [00:18<03:53,  1.04it/s]
  4%|▎         | 9/250 [00:21<06:30,  1.62s/it]
  4%|▍         | 10/250 [00:25<09:19,  2.33s/it]
  4%|▍         | 11/250 [00:26<07:33,  1.90s/it]
  5%|▍         | 12/250 [00:26<05:38,  1.42s/it]
  5%|▌         | 13/250 [00:28<06:18,  1.60s/it]
  6%|▌         | 14/250 [00:30<05:54,  1.50s/it]
  6%|▌         | 15/250 [00:31<06:03,  1.55s/it]
  6%|▋         | 16/250 [00:31<04:20,  1.11s/it]
  7%|▋         | 17/250 [00:31<03:08,  1.23it/s]
  7%|▋         | 18/250 [00:32<02:53,  1.34it/s]
  8%|▊         | 19/250 [00:32<02:29,  1.54it/s]
  8%|▊         | 20/250 [00:33<02:30,  1.53it/s]
  8%|▊         | 21/250 [00:34<02:14,  1.70it/s]
  9%|▉         | 22/250 [00:34<02:04,  1.83it/s]
  9%|▉         | 23/250 [00:35<02:57,  1.28it/s]
 10%|▉         | 24/250 [00:35<02:12,  1.71it/s]
 10%|█         | 25/250 [00:36<01:40,  2.24it/s]
 11%|█         | 27/250 [00:36<01:30,  2.45it/s]
 12%|█▏        | 29/250 [00:37<01:21,  2.71it/s]
 12%|█▏        | 30/250 [00:37<01:15,  2.91it/s]
 12%|█▏        | 31/250 [00:38<01:30,  2.43it/s]
 13%|█▎        | 32/250 [00:38<01:40,  2.16it/s]
 13%|█▎        | 33/250 [00:39<01:39,  2.18it/s]
 14%|█▎        | 34/250 [00:40<01:56,  1.85it/s]
 14%|█▍        | 35/250 [00:40<02:02,  1.75it/s]
 14%|█▍        | 36/250 [00:42<03:00,  1.19it/s]
 15%|█▍        | 37/250 [00:42<02:30,  1.41it/s]
 15%|█▌        | 38/250 [00:43<02:21,  1.50it/s]
 16%|█▌        | 39/250 [00:44<02:44,  1.29it/s]
 16%|█▌        | 40/250 [00:44<02:15,  1.55it/s]
 16%|█▋        | 41/250 [00:44<01:40,  2.07it/s]
 17%|█▋        | 42/250 [00:46<02:51,  1.22it/s]
 17%|█▋        | 43/250 [00:46<02:08,  1.62it/s]
 18%|█▊        | 44/250 [00:47<03:02,  1.13it/s]
 18%|█▊        | 45/250 [00:48<03:00,  1.14it/s]
 18%|█▊        | 46/250 [00:49<02:25,  1.40it/s]
 19%|█▉        | 48/250 [00:50<02:32,  1.32it/s]
 20%|█▉        | 49/250 [00:51<02:05,  1.60it/s]
 20%|██        | 50/250 [00:51<02:14,  1.49it/s]
 21%|██        | 52/250 [00:52<01:36,  2.04it/s]
 22%|██▏       | 54/250 [00:52<01:04,  3.02it/s]
 22%|██▏       | 55/250 [00:52<01:01,  3.18it/s]
 22%|██▏       | 56/250 [00:53<01:01,  3.14it/s]
 23%|██▎       | 57/250 [00:53<01:29,  2.15it/s]
 23%|██▎       | 58/250 [00:54<01:52,  1.70it/s]
 24%|██▎       | 59/250 [00:55<01:58,  1.61it/s]
 24%|██▍       | 60/250 [00:55<01:32,  2.05it/s]
 25%|██▍       | 62/250 [00:57<02:17,  1.37it/s]
 25%|██▌       | 63/250 [00:59<02:44,  1.14it/s]
 26%|██▌       | 64/250 [01:00<03:03,  1.01it/s]
 26%|██▌       | 65/250 [01:00<02:29,  1.24it/s]
 26%|██▋       | 66/250 [01:01<02:34,  1.19it/s]
 27%|██▋       | 67/250 [01:02<02:42,  1.13it/s]
 27%|██▋       | 68/250 [01:02<02:00,  1.51it/s]
 28%|██▊       | 69/250 [01:06<04:39,  1.54s/it]
 28%|██▊       | 70/250 [01:06<03:34,  1.19s/it]
 28%|██▊       | 71/250 [01:07<02:51,  1.04it/s]
 29%|██▉       | 72/250 [01:09<04:08,  1.40s/it]
 29%|██▉       | 73/250 [01:12<05:28,  1.86s/it]
 30%|██▉       | 74/250 [01:13<04:26,  1.52s/it]
 30%|███       | 75/250 [01:14<04:22,  1.50s/it]
 31%|███       | 77/250 [01:16<03:38,  1.26s/it]
 31%|███       | 78/250 [01:19<04:31,  1.58s/it]
 32%|███▏      | 79/250 [01:20<04:19,  1.52s/it]
 32%|███▏      | 81/250 [01:21<03:01,  1.07s/it]
 33%|███▎      | 82/250 [01:21<02:32,  1.10it/s]
 33%|███▎      | 83/250 [01:22<01:58,  1.42it/s]
 34%|███▎      | 84/250 [01:22<01:39,  1.66it/s]
 34%|███▍      | 85/250 [01:23<02:25,  1.14it/s]
 34%|███▍      | 86/250 [01:24<01:54,  1.43it/s]
 35%|███▍      | 87/250 [01:25<02:15,  1.21it/s]
 35%|███▌      | 88/250 [01:26<02:36,  1.04it/s]
 36%|███▌      | 89/250 [01:27<02:37,  1.02it/s]
 36%|███▌      | 90/250 [01:27<01:54,  1.39it/s]
 37%|███▋      | 92/250 [01:28<01:25,  1.85it/s]
 37%|███▋      | 93/250 [01:29<01:35,  1.64it/s]
 38%|███▊      | 94/250 [01:33<04:00,  1.54s/it]
 38%|███▊      | 96/250 [01:34<02:38,  1.03s/it]
 39%|███▉      | 98/250 [01:34<01:55,  1.31it/s]
 40%|███▉      | 99/250 [01:35<01:47,  1.41it/s]
 40%|████      | 100/250 [01:38<03:26,  1.38s/it]
 41%|████      | 102/250 [01:39<02:08,  1.15it/s]
 41%|████      | 103/250 [01:39<01:58,  1.24it/s]
 42%|████▏     | 104/250 [01:39<01:36,  1.52it/s]
 42%|████▏     | 105/250 [01:40<01:33,  1.56it/s]
 42%|████▏     | 106/250 [01:41<01:52,  1.28it/s]
 43%|████▎     | 107/250 [01:42<01:59,  1.19it/s]
 43%|████▎     | 108/250 [01:43<01:56,  1.22it/s]
 44%|████▎     | 109/250 [01:44<01:52,  1.26it/s]
 44%|████▍     | 110/250 [01:45<02:11,  1.07it/s]
 44%|████▍     | 111/250 [01:45<01:40,  1.39it/s]
 45%|████▍     | 112/250 [01:46<01:32,  1.49it/s]
 45%|████▌     | 113/250 [01:49<03:21,  1.47s/it]
 46%|████▌     | 115/250 [01:49<01:55,  1.17it/s]
 46%|████▋     | 116/250 [01:49<01:30,  1.47it/s]
 47%|████▋     | 117/250 [01:50<01:14,  1.77it/s]
 47%|████▋     | 118/250 [01:50<01:00,  2.17it/s]
 48%|████▊     | 119/250 [01:50<00:49,  2.67it/s]
 48%|████▊     | 120/250 [01:50<00:48,  2.70it/s]
 49%|████▉     | 122/250 [01:51<00:35,  3.64it/s]
 49%|████▉     | 123/250 [01:51<00:34,  3.68it/s]
 50%|████▉     | 124/250 [01:54<02:17,  1.10s/it]
 50%|█████     | 125/250 [01:55<01:48,  1.15it/s]
 50%|█████     | 126/250 [01:55<01:37,  1.27it/s]
 51%|█████     | 127/250 [01:56<01:29,  1.38it/s]
 52%|█████▏    | 129/250 [01:56<00:51,  2.34it/s]
 52%|█████▏    | 130/250 [01:56<00:51,  2.35it/s]
 52%|█████▏    | 131/250 [01:57<01:10,  1.70it/s]
 53%|█████▎    | 132/250 [01:58<00:58,  2.03it/s]
 53%|█████▎    | 133/250 [01:58<00:47,  2.49it/s]
 54%|█████▎    | 134/250 [02:00<01:32,  1.25it/s]
 54%|█████▍    | 135/250 [02:00<01:22,  1.39it/s]
 55%|█████▍    | 137/250 [02:00<00:47,  2.39it/s]
 55%|█████▌    | 138/250 [02:01<00:53,  2.10it/s]
 56%|█████▌    | 139/250 [02:01<00:45,  2.47it/s]
 56%|█████▌    | 140/250 [02:02<00:54,  2.01it/s]
 57%|█████▋    | 142/250 [02:02<00:44,  2.45it/s]
 57%|█████▋    | 143/250 [02:03<00:49,  2.16it/s]
 58%|█████▊    | 144/250 [02:06<01:58,  1.12s/it]
 58%|█████▊    | 145/250 [02:09<02:34,  1.48s/it]
 58%|█████▊    | 146/250 [02:10<02:25,  1.40s/it]
 59%|█████▉    | 147/250 [02:11<02:27,  1.43s/it]
 59%|█████▉    | 148/250 [02:12<02:01,  1.19s/it]
 60%|█████▉    | 149/250 [02:13<01:58,  1.17s/it]
 60%|██████    | 151/250 [02:13<01:05,  1.51it/s]
 61%|██████    | 152/250 [02:13<00:55,  1.75it/s]
 61%|██████    | 153/250 [02:14<00:45,  2.11it/s]
 62%|██████▏   | 154/250 [02:14<00:36,  2.63it/s]
 62%|██████▏   | 156/250 [02:14<00:28,  3.35it/s]
 63%|██████▎   | 157/250 [02:14<00:23,  3.90it/s]
 63%|██████▎   | 158/250 [02:14<00:22,  4.03it/s]
 64%|██████▎   | 159/250 [02:15<00:30,  2.95it/s]
 64%|██████▍   | 160/250 [02:16<00:46,  1.93it/s]
 64%|██████▍   | 161/250 [02:17<00:56,  1.56it/s]
 65%|██████▌   | 163/250 [02:17<00:36,  2.41it/s]
 66%|██████▌   | 165/250 [02:19<00:46,  1.82it/s]
 66%|██████▋   | 166/250 [02:20<00:53,  1.58it/s]
 67%|██████▋   | 167/250 [02:21<01:06,  1.24it/s]
 67%|██████▋   | 168/250 [02:23<01:27,  1.07s/it]
 68%|██████▊   | 170/250 [02:24<01:08,  1.17it/s]
 68%|██████▊   | 171/250 [02:24<00:55,  1.44it/s]
 69%|██████▉   | 172/250 [02:25<00:55,  1.40it/s]
 69%|██████▉   | 173/250 [02:26<00:54,  1.40it/s]
 70%|██████▉   | 174/250 [02:26<00:55,  1.38it/s]
 70%|███████   | 176/250 [02:27<00:38,  1.90it/s]
 71%|███████   | 177/250 [02:28<00:52,  1.38it/s]
 71%|███████   | 178/250 [02:31<01:30,  1.26s/it]
 72%|███████▏  | 179/250 [02:31<01:09,  1.01it/s]
 72%|███████▏  | 180/250 [02:32<00:58,  1.19it/s]
 73%|███████▎  | 182/250 [02:32<00:39,  1.74it/s]
 73%|███████▎  | 183/250 [02:33<00:34,  1.95it/s]
 74%|███████▎  | 184/250 [02:33<00:31,  2.13it/s]
 74%|███████▍  | 185/250 [02:35<01:01,  1.06it/s]
 74%|███████▍  | 186/250 [02:36<01:00,  1.05it/s]
 75%|███████▌  | 188/250 [02:37<00:38,  1.59it/s]
 76%|███████▌  | 189/250 [02:37<00:31,  1.95it/s]
 76%|███████▌  | 190/250 [02:38<00:38,  1.55it/s]
 76%|███████▋  | 191/250 [02:38<00:30,  1.96it/s]
 77%|███████▋  | 192/250 [02:38<00:29,  1.98it/s]
 77%|███████▋  | 193/250 [02:39<00:25,  2.22it/s]
 78%|███████▊  | 195/250 [02:40<00:30,  1.82it/s]
 79%|███████▉  | 197/250 [02:40<00:20,  2.61it/s]
 80%|███████▉  | 199/250 [02:40<00:13,  3.74it/s]
 80%|████████  | 200/250 [02:41<00:11,  4.29it/s]
 81%|████████  | 202/250 [02:41<00:14,  3.37it/s]
 81%|████████  | 203/250 [02:42<00:14,  3.17it/s]
 82%|████████▏ | 204/250 [02:42<00:13,  3.39it/s]
 82%|████████▏ | 205/250 [02:43<00:18,  2.47it/s]
 82%|████████▏ | 206/250 [02:43<00:19,  2.29it/s]
 83%|████████▎ | 207/250 [02:44<00:23,  1.82it/s]
 83%|████████▎ | 208/250 [02:44<00:20,  2.06it/s]
 84%|████████▎ | 209/250 [02:45<00:15,  2.66it/s]
 84%|████████▍ | 210/250 [02:45<00:14,  2.70it/s]
 84%|████████▍ | 211/250 [02:50<01:08,  1.75s/it]
 85%|████████▍ | 212/250 [02:52<01:10,  1.85s/it]
 85%|████████▌ | 213/250 [02:55<01:15,  2.03s/it]
 86%|████████▌ | 214/250 [02:55<00:56,  1.58s/it]
 86%|████████▌ | 215/250 [02:56<00:46,  1.34s/it]
 86%|████████▋ | 216/250 [02:56<00:34,  1.03s/it]
 87%|████████▋ | 217/250 [02:56<00:24,  1.33it/s]
 87%|████████▋ | 218/250 [02:58<00:29,  1.08it/s]
 88%|████████▊ | 219/250 [02:58<00:22,  1.38it/s]
 88%|████████▊ | 220/250 [02:59<00:27,  1.09it/s]
 88%|████████▊ | 221/250 [03:00<00:22,  1.29it/s]
 89%|████████▉ | 222/250 [03:00<00:17,  1.58it/s]
 89%|████████▉ | 223/250 [03:00<00:15,  1.72it/s]
 90%|████████▉ | 224/250 [03:01<00:12,  2.16it/s]
 90%|█████████ | 226/250 [03:01<00:07,  3.33it/s]
 91%|█████████ | 227/250 [03:01<00:07,  3.07it/s]
 91%|█████████ | 228/250 [03:02<00:09,  2.43it/s]
 92%|█████████▏| 229/250 [03:03<00:11,  1.80it/s]
 92%|█████████▏| 230/250 [03:04<00:13,  1.48it/s]
 92%|█████████▏| 231/250 [03:04<00:10,  1.74it/s]
 93%|█████████▎| 232/250 [03:04<00:08,  2.19it/s]
 93%|█████████▎| 233/250 [03:05<00:08,  2.10it/s]
 94%|█████████▎| 234/250 [03:06<00:10,  1.59it/s]
 94%|█████████▍| 235/250 [03:07<00:10,  1.45it/s]
 94%|█████████▍| 236/250 [03:07<00:09,  1.42it/s]
 95%|█████████▍| 237/250 [03:09<00:11,  1.12it/s]
 95%|█████████▌| 238/250 [03:11<00:17,  1.43s/it]
 96%|█████████▌| 240/250 [03:14<00:12,  1.28s/it]
 96%|█████████▋| 241/250 [03:14<00:09,  1.02s/it]
 97%|█████████▋| 243/250 [03:15<00:05,  1.32it/s]
 98%|█████████▊| 244/250 [03:15<00:03,  1.51it/s]
 98%|█████████▊| 245/250 [03:16<00:03,  1.39it/s]
 98%|█████████▊| 246/250 [03:17<00:03,  1.13it/s]
 99%|█████████▉| 247/250 [03:18<00:02,  1.35it/s]
 99%|█████████▉| 248/250 [03:18<00:01,  1.77it/s]
100%|██████████| 250/250 [03:18<00:00,  2.00it/s]
100%|██████████| 250/250 [03:18<00:00,  1.26it/s]
2025-05-14 13:05:12,554 - modnet - INFO - Loss per individual: ind 0: 0.131 	ind 1: 0.139 	ind 2: 0.100 	ind 3: 0.101 	ind 4: 0.107 	ind 5: 0.100 	ind 6: 0.101 	ind 7: 0.121 	ind 8: 0.110 	ind 9: 0.106 	ind 10: 0.108 	ind 11: 0.108 	ind 12: 0.104 	ind 13: 0.126 	ind 14: 0.109 	ind 15: 0.108 	ind 16: 0.115 	ind 17: 0.134 	ind 18: 0.132 	ind 19: 0.103 	ind 20: 0.109 	ind 21: 0.121 	ind 22: 0.107 	ind 23: 0.122 	ind 24: 0.120 	ind 25: 0.108 	ind 26: 0.138 	ind 27: 0.106 	ind 28: 0.116 	ind 29: 0.108 	ind 30: 0.104 	ind 31: 0.110 	ind 32: 0.117 	ind 33: 0.187 	ind 34: 0.109 	ind 35: 0.100 	ind 36: 0.120 	ind 37: 0.102 	ind 38: 0.115 	ind 39: 0.130 	ind 40: 0.108 	ind 41: 0.111 	ind 42: 0.105 	ind 43: 0.120 	ind 44: 0.100 	ind 45: 0.109 	ind 46: 0.118 	ind 47: 0.120 	ind 48: 0.116 	ind 49: 0.108 	
2025-05-14 13:05:12,555 - modnet - INFO - Generation number 5

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:07<32:26,  7.82s/it]
  1%|          | 2/250 [00:09<16:36,  4.02s/it]
  2%|▏         | 4/250 [00:09<06:36,  1.61s/it]
  2%|▏         | 5/250 [00:10<05:14,  1.28s/it]
  2%|▏         | 6/250 [00:10<03:57,  1.03it/s]
  3%|▎         | 7/250 [00:10<03:07,  1.29it/s]
  3%|▎         | 8/250 [00:10<02:31,  1.60it/s]
  4%|▎         | 9/250 [00:11<02:35,  1.55it/s]
  4%|▍         | 10/250 [00:12<02:52,  1.39it/s]
  4%|▍         | 11/250 [00:13<02:36,  1.53it/s]
  5%|▍         | 12/250 [00:13<02:28,  1.60it/s]
  5%|▌         | 13/250 [00:13<01:56,  2.04it/s]
  6%|▌         | 14/250 [00:14<02:02,  1.93it/s]
  6%|▌         | 15/250 [00:14<01:35,  2.46it/s]
  6%|▋         | 16/250 [00:15<02:15,  1.72it/s]
  7%|▋         | 17/250 [00:15<01:57,  1.98it/s]
  7%|▋         | 18/250 [00:16<02:02,  1.89it/s]
  8%|▊         | 19/250 [00:16<01:37,  2.37it/s]
  8%|▊         | 20/250 [00:17<02:30,  1.52it/s]
  8%|▊         | 21/250 [00:18<03:04,  1.24it/s]
  9%|▉         | 22/250 [00:19<02:21,  1.61it/s]
  9%|▉         | 23/250 [00:19<02:17,  1.65it/s]
 10%|▉         | 24/250 [00:19<01:44,  2.15it/s]
 10%|█         | 25/250 [00:20<01:41,  2.22it/s]
 10%|█         | 26/250 [00:20<01:30,  2.47it/s]
 11%|█         | 28/250 [00:21<01:26,  2.57it/s]
 12%|█▏        | 29/250 [00:21<01:40,  2.19it/s]
 12%|█▏        | 30/250 [00:22<01:43,  2.12it/s]
 12%|█▏        | 31/250 [00:22<01:28,  2.49it/s]
 13%|█▎        | 33/250 [00:22<00:55,  3.90it/s]
 14%|█▍        | 35/250 [00:23<01:04,  3.35it/s]
 14%|█▍        | 36/250 [00:24<01:22,  2.61it/s]
 15%|█▍        | 37/250 [00:25<01:48,  1.96it/s]
 15%|█▌        | 38/250 [00:26<02:12,  1.60it/s]
 16%|█▌        | 39/250 [00:27<02:46,  1.27it/s]
 16%|█▌        | 40/250 [00:27<02:11,  1.60it/s]
 16%|█▋        | 41/250 [00:28<02:04,  1.69it/s]
 17%|█▋        | 43/250 [00:28<01:31,  2.26it/s]
 18%|█▊        | 44/250 [00:29<01:33,  2.21it/s]
 18%|█▊        | 45/250 [00:30<02:30,  1.36it/s]
 18%|█▊        | 46/250 [00:31<02:41,  1.26it/s]
 19%|█▉        | 47/250 [00:35<05:38,  1.67s/it]
 19%|█▉        | 48/250 [00:36<04:37,  1.37s/it]
 20%|██        | 50/250 [00:37<03:38,  1.09s/it]
 20%|██        | 51/250 [00:37<02:51,  1.16it/s]
 21%|██        | 52/250 [00:39<03:52,  1.17s/it]
 21%|██        | 53/250 [00:40<02:59,  1.10it/s]
 22%|██▏       | 54/250 [00:41<03:09,  1.04it/s]
 22%|██▏       | 56/250 [00:42<02:28,  1.31it/s]
 23%|██▎       | 57/250 [00:44<03:16,  1.02s/it]
 23%|██▎       | 58/250 [00:44<02:56,  1.09it/s]
 24%|██▎       | 59/250 [00:45<03:15,  1.02s/it]
 24%|██▍       | 60/250 [00:46<02:44,  1.16it/s]
 24%|██▍       | 61/250 [00:47<02:55,  1.07it/s]
 25%|██▍       | 62/250 [00:48<02:40,  1.17it/s]
 25%|██▌       | 63/250 [00:48<02:27,  1.27it/s]
 26%|██▌       | 64/250 [00:49<02:02,  1.51it/s]
 26%|██▋       | 66/250 [00:51<03:02,  1.01it/s]
 27%|██▋       | 67/250 [00:52<02:50,  1.08it/s]
 28%|██▊       | 69/250 [00:52<01:43,  1.75it/s]
 28%|██▊       | 70/250 [00:53<01:39,  1.81it/s]
 28%|██▊       | 71/250 [00:53<01:27,  2.05it/s]
 29%|██▉       | 72/250 [00:54<01:26,  2.06it/s]
 30%|██▉       | 74/250 [00:55<01:30,  1.94it/s]
 30%|███       | 75/250 [00:55<01:26,  2.02it/s]
 30%|███       | 76/250 [00:55<01:11,  2.45it/s]
 31%|███       | 77/250 [00:55<01:03,  2.72it/s]
 32%|███▏      | 79/250 [00:56<00:46,  3.66it/s]
 32%|███▏      | 80/250 [00:57<01:23,  2.02it/s]
 32%|███▏      | 81/250 [00:57<01:20,  2.11it/s]
 33%|███▎      | 82/250 [00:58<01:46,  1.57it/s]
 33%|███▎      | 83/250 [01:00<02:27,  1.13it/s]
 34%|███▎      | 84/250 [01:08<07:46,  2.81s/it]
 34%|███▍      | 85/250 [01:09<06:20,  2.31s/it]
 34%|███▍      | 86/250 [01:10<05:26,  1.99s/it]
 35%|███▌      | 88/250 [01:13<04:33,  1.69s/it]
 36%|███▌      | 89/250 [01:14<04:00,  1.49s/it]
 36%|███▌      | 90/250 [01:17<05:13,  1.96s/it]
 37%|███▋      | 92/250 [01:19<03:57,  1.50s/it]
 37%|███▋      | 93/250 [01:21<04:22,  1.67s/it]
 38%|███▊      | 94/250 [01:22<04:14,  1.63s/it]
 38%|███▊      | 95/250 [01:23<03:15,  1.26s/it]
 38%|███▊      | 96/250 [01:23<02:30,  1.02it/s]
 39%|███▉      | 97/250 [01:24<02:54,  1.14s/it]
 39%|███▉      | 98/250 [01:25<02:18,  1.10it/s]
 40%|███▉      | 99/250 [01:25<02:08,  1.17it/s]
 40%|████      | 100/250 [01:27<02:25,  1.03it/s]
 40%|████      | 101/250 [01:27<01:57,  1.27it/s]
 41%|████      | 102/250 [01:29<02:40,  1.08s/it]
 41%|████      | 103/250 [01:29<02:17,  1.07it/s]
 42%|████▏     | 105/250 [01:31<01:55,  1.25it/s]
 42%|████▏     | 106/250 [01:32<01:58,  1.22it/s]
 43%|████▎     | 107/250 [01:32<01:37,  1.47it/s]
 43%|████▎     | 108/250 [01:33<01:54,  1.24it/s]
 44%|████▍     | 110/250 [01:33<01:14,  1.88it/s]
 44%|████▍     | 111/250 [01:34<01:12,  1.91it/s]
 45%|████▍     | 112/250 [01:34<01:09,  2.00it/s]
 45%|████▌     | 113/250 [01:36<01:51,  1.23it/s]
 46%|████▌     | 114/250 [01:38<02:27,  1.08s/it]
 46%|████▌     | 115/250 [01:38<02:00,  1.12it/s]
 46%|████▋     | 116/250 [01:39<01:47,  1.24it/s]
 47%|████▋     | 117/250 [01:43<03:57,  1.79s/it]
 47%|████▋     | 118/250 [01:44<03:29,  1.59s/it]
 48%|████▊     | 119/250 [01:45<02:52,  1.32s/it]
 48%|████▊     | 120/250 [01:46<02:42,  1.25s/it]
 49%|████▉     | 122/250 [01:48<02:28,  1.16s/it]
 49%|████▉     | 123/250 [01:49<02:20,  1.11s/it]
 50%|████▉     | 124/250 [01:49<01:52,  1.12it/s]
 50%|█████     | 125/250 [01:50<01:37,  1.28it/s]
 50%|█████     | 126/250 [01:50<01:21,  1.52it/s]
 51%|█████     | 127/250 [01:52<01:52,  1.09it/s]
 51%|█████     | 128/250 [01:52<01:26,  1.41it/s]
 52%|█████▏    | 129/250 [01:52<01:07,  1.79it/s]
 52%|█████▏    | 131/250 [01:53<00:54,  2.19it/s]
 53%|█████▎    | 132/250 [01:53<00:59,  1.99it/s]
 53%|█████▎    | 133/250 [01:54<00:55,  2.12it/s]
 54%|█████▎    | 134/250 [01:55<01:18,  1.48it/s]
 54%|█████▍    | 135/250 [01:56<01:25,  1.34it/s]
 54%|█████▍    | 136/250 [01:57<01:28,  1.29it/s]
 55%|█████▌    | 138/250 [01:59<01:45,  1.07it/s]
 56%|█████▌    | 140/250 [01:59<01:07,  1.64it/s]
 56%|█████▋    | 141/250 [02:01<01:28,  1.24it/s]
 57%|█████▋    | 142/250 [02:02<01:32,  1.16it/s]
 58%|█████▊    | 144/250 [02:04<01:54,  1.08s/it]
 58%|█████▊    | 145/250 [02:06<02:03,  1.18s/it]
 58%|█████▊    | 146/250 [02:07<01:50,  1.07s/it]
 59%|█████▉    | 147/250 [02:07<01:30,  1.14it/s]
 59%|█████▉    | 148/250 [02:09<01:54,  1.12s/it]
 60%|█████▉    | 149/250 [02:09<01:37,  1.04it/s]
 60%|██████    | 150/250 [02:10<01:35,  1.05it/s]
 60%|██████    | 151/250 [02:11<01:24,  1.17it/s]
 61%|██████    | 152/250 [02:11<01:16,  1.28it/s]
 61%|██████    | 153/250 [02:12<00:58,  1.66it/s]
 62%|██████▏   | 154/250 [02:12<01:03,  1.51it/s]
 62%|██████▏   | 155/250 [02:14<01:25,  1.11it/s]
 62%|██████▏   | 156/250 [02:14<01:05,  1.44it/s]
 63%|██████▎   | 157/250 [02:15<01:04,  1.45it/s]
 63%|██████▎   | 158/250 [02:16<01:20,  1.15it/s]
 64%|██████▎   | 159/250 [02:17<01:31,  1.00s/it]
 64%|██████▍   | 160/250 [02:18<01:27,  1.03it/s]
 64%|██████▍   | 161/250 [02:19<01:10,  1.27it/s]
 65%|██████▍   | 162/250 [02:19<00:55,  1.58it/s]
 65%|██████▌   | 163/250 [02:20<00:55,  1.55it/s]
 66%|██████▌   | 164/250 [02:22<01:46,  1.24s/it]
 66%|██████▌   | 165/250 [02:23<01:39,  1.17s/it]
 66%|██████▋   | 166/250 [02:23<01:11,  1.17it/s]
 67%|██████▋   | 167/250 [02:24<01:00,  1.37it/s]
 67%|██████▋   | 168/250 [02:26<01:29,  1.09s/it]
 68%|██████▊   | 169/250 [02:27<01:40,  1.24s/it]
 68%|██████▊   | 170/250 [02:30<02:14,  1.68s/it]
 68%|██████▊   | 171/250 [02:34<03:15,  2.48s/it]
 69%|██████▉   | 172/250 [02:35<02:24,  1.85s/it]
 69%|██████▉   | 173/250 [02:36<01:59,  1.55s/it]
 70%|██████▉   | 174/250 [02:36<01:33,  1.23s/it]
 71%|███████   | 177/250 [02:37<00:49,  1.47it/s]
 71%|███████   | 178/250 [02:37<00:40,  1.78it/s]
 72%|███████▏  | 179/250 [02:38<00:39,  1.79it/s]
 72%|███████▏  | 180/250 [02:38<00:35,  1.97it/s]
 72%|███████▏  | 181/250 [02:39<00:49,  1.39it/s]
 73%|███████▎  | 183/250 [02:40<00:42,  1.59it/s]
 74%|███████▎  | 184/250 [02:42<01:02,  1.05it/s]
 74%|███████▍  | 185/250 [02:42<00:48,  1.34it/s]
 74%|███████▍  | 186/250 [02:43<00:45,  1.39it/s]
 75%|███████▍  | 187/250 [02:46<01:24,  1.35s/it]
 75%|███████▌  | 188/250 [02:47<01:10,  1.13s/it]
 76%|███████▌  | 189/250 [02:47<00:56,  1.09it/s]
 76%|███████▌  | 190/250 [02:48<00:55,  1.07it/s]
 77%|███████▋  | 192/250 [02:49<00:47,  1.22it/s]
 77%|███████▋  | 193/250 [02:50<00:48,  1.17it/s]
 78%|███████▊  | 194/250 [02:50<00:37,  1.51it/s]
 78%|███████▊  | 195/250 [02:52<00:50,  1.09it/s]
 78%|███████▊  | 196/250 [02:52<00:39,  1.37it/s]
 79%|███████▉  | 198/250 [02:52<00:22,  2.29it/s]
 80%|███████▉  | 199/250 [02:53<00:28,  1.76it/s]
 80%|████████  | 200/250 [02:54<00:27,  1.80it/s]
 80%|████████  | 201/250 [02:54<00:21,  2.25it/s]
 81%|████████  | 202/250 [02:57<00:53,  1.11s/it]
 81%|████████  | 203/250 [02:58<00:46,  1.01it/s]
 82%|████████▏ | 204/250 [02:58<00:42,  1.07it/s]
 82%|████████▏ | 205/250 [03:00<00:53,  1.18s/it]
 82%|████████▏ | 206/250 [03:01<00:42,  1.02it/s]
 83%|████████▎ | 207/250 [03:01<00:33,  1.28it/s]
 83%|████████▎ | 208/250 [03:02<00:36,  1.14it/s]
 84%|████████▎ | 209/250 [03:03<00:35,  1.14it/s]
 84%|████████▍ | 210/250 [03:03<00:28,  1.41it/s]
 84%|████████▍ | 211/250 [03:04<00:29,  1.33it/s]
 85%|████████▍ | 212/250 [03:05<00:27,  1.41it/s]
 85%|████████▌ | 213/250 [03:05<00:19,  1.87it/s]
 86%|████████▌ | 214/250 [03:05<00:16,  2.16it/s]
 86%|████████▌ | 215/250 [03:06<00:18,  1.93it/s]
 86%|████████▋ | 216/250 [03:06<00:13,  2.50it/s]
 87%|████████▋ | 217/250 [03:07<00:23,  1.41it/s]
 87%|████████▋ | 218/250 [03:08<00:26,  1.20it/s]
 88%|████████▊ | 220/250 [03:11<00:33,  1.12s/it]
 88%|████████▊ | 221/250 [03:12<00:27,  1.04it/s]
 89%|████████▉ | 222/250 [03:14<00:39,  1.40s/it]
 89%|████████▉ | 223/250 [03:15<00:29,  1.09s/it]
 90%|████████▉ | 224/250 [03:15<00:23,  1.12it/s]
 90%|█████████ | 225/250 [03:15<00:16,  1.47it/s]
 90%|█████████ | 226/250 [03:16<00:14,  1.68it/s]
 91%|█████████ | 227/250 [03:17<00:19,  1.16it/s]
 91%|█████████ | 228/250 [03:18<00:19,  1.13it/s]
 92%|█████████▏| 229/250 [03:19<00:19,  1.07it/s]
 92%|█████████▏| 230/250 [03:19<00:15,  1.32it/s]
 92%|█████████▏| 231/250 [03:20<00:13,  1.38it/s]
 93%|█████████▎| 232/250 [03:21<00:15,  1.15it/s]
 93%|█████████▎| 233/250 [03:23<00:16,  1.00it/s]
 94%|█████████▎| 234/250 [03:25<00:22,  1.40s/it]
 94%|█████████▍| 235/250 [03:27<00:21,  1.45s/it]
 94%|█████████▍| 236/250 [03:27<00:14,  1.05s/it]
 95%|█████████▍| 237/250 [03:29<00:17,  1.34s/it]
 95%|█████████▌| 238/250 [03:29<00:12,  1.01s/it]
 96%|█████████▌| 239/250 [03:30<00:12,  1.12s/it]
 96%|█████████▌| 240/250 [03:33<00:16,  1.68s/it]
 96%|█████████▋| 241/250 [03:35<00:14,  1.59s/it]
 97%|█████████▋| 242/250 [03:35<00:09,  1.20s/it]
 97%|█████████▋| 243/250 [03:41<00:18,  2.63s/it]
 98%|█████████▊| 244/250 [03:41<00:11,  1.88s/it]
 98%|█████████▊| 245/250 [03:41<00:07,  1.45s/it]
 98%|█████████▊| 246/250 [03:42<00:04,  1.20s/it]
 99%|█████████▉| 247/250 [03:46<00:05,  1.88s/it]
 99%|█████████▉| 248/250 [03:50<00:05,  2.70s/it]
100%|█████████▉| 249/250 [03:54<00:03,  3.17s/it]
100%|██████████| 250/250 [04:00<00:00,  3.88s/it]
100%|██████████| 250/250 [04:00<00:00,  1.04it/s]
2025-05-14 13:09:13,103 - modnet - INFO - Loss per individual: ind 0: 0.117 	ind 1: 0.114 	ind 2: 0.110 	ind 3: 0.106 	ind 4: 0.107 	ind 5: 0.111 	ind 6: 0.113 	ind 7: 0.107 	ind 8: 0.103 	ind 9: 0.107 	ind 10: 0.110 	ind 11: 0.108 	ind 12: 0.114 	ind 13: 0.136 	ind 14: 0.110 	ind 15: 0.148 	ind 16: 0.109 	ind 17: 0.114 	ind 18: 0.110 	ind 19: 0.127 	ind 20: 0.094 	ind 21: 0.106 	ind 22: 0.118 	ind 23: 0.119 	ind 24: 0.109 	ind 25: 0.106 	ind 26: 0.110 	ind 27: 0.107 	ind 28: 0.108 	ind 29: 0.107 	ind 30: 0.115 	ind 31: 0.116 	ind 32: 0.118 	ind 33: 0.122 	ind 34: 0.124 	ind 35: 0.110 	ind 36: 0.118 	ind 37: 0.114 	ind 38: 0.111 	ind 39: 0.117 	ind 40: 0.107 	ind 41: 0.104 	ind 42: 0.101 	ind 43: 0.104 	ind 44: 0.109 	ind 45: 0.108 	ind 46: 0.113 	ind 47: 0.105 	ind 48: 0.098 	ind 49: 0.104 	
2025-05-14 13:09:13,105 - modnet - INFO - Generation number 6

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:06<28:22,  6.84s/it]
  1%|          | 2/250 [00:08<16:16,  3.94s/it]
  1%|          | 3/250 [00:09<11:00,  2.68s/it]
  2%|▏         | 4/250 [00:11<08:37,  2.10s/it]
  2%|▏         | 5/250 [00:11<05:56,  1.45s/it]
  2%|▏         | 6/250 [00:11<04:12,  1.04s/it]
  3%|▎         | 7/250 [00:12<03:27,  1.17it/s]
  3%|▎         | 8/250 [00:12<02:56,  1.37it/s]
  4%|▎         | 9/250 [00:14<04:58,  1.24s/it]
  4%|▍         | 10/250 [00:15<04:12,  1.05s/it]
  4%|▍         | 11/250 [00:16<04:21,  1.10s/it]
  5%|▍         | 12/250 [00:17<03:43,  1.06it/s]
  5%|▌         | 13/250 [00:18<04:28,  1.13s/it]
  6%|▌         | 14/250 [00:19<03:33,  1.10it/s]
  6%|▌         | 15/250 [00:20<03:16,  1.19it/s]
  6%|▋         | 16/250 [00:22<05:27,  1.40s/it]
  7%|▋         | 17/250 [00:22<04:00,  1.03s/it]
  7%|▋         | 18/250 [00:23<03:25,  1.13it/s]
  8%|▊         | 19/250 [00:23<02:57,  1.30it/s]
  8%|▊         | 20/250 [00:24<02:36,  1.47it/s]
  8%|▊         | 21/250 [00:25<02:28,  1.54it/s]
  9%|▉         | 23/250 [00:26<02:17,  1.65it/s]
 10%|▉         | 24/250 [00:28<03:36,  1.05it/s]
 10%|█         | 25/250 [00:29<03:45,  1.00s/it]
 10%|█         | 26/250 [00:29<03:09,  1.18it/s]
 11%|█         | 27/250 [00:30<03:01,  1.23it/s]
 11%|█         | 28/250 [00:30<02:38,  1.40it/s]
 12%|█▏        | 29/250 [00:31<02:26,  1.50it/s]
 12%|█▏        | 30/250 [00:31<02:06,  1.74it/s]
 12%|█▏        | 31/250 [00:32<01:53,  1.93it/s]
 13%|█▎        | 32/250 [00:33<02:23,  1.52it/s]
 13%|█▎        | 33/250 [00:33<01:49,  1.99it/s]
 14%|█▎        | 34/250 [00:36<04:18,  1.20s/it]
 14%|█▍        | 35/250 [00:36<03:30,  1.02it/s]
 14%|█▍        | 36/250 [00:37<02:57,  1.20it/s]
 15%|█▍        | 37/250 [00:37<02:38,  1.34it/s]
 15%|█▌        | 38/250 [00:38<02:43,  1.30it/s]
 16%|█▌        | 39/250 [00:39<02:35,  1.36it/s]
 16%|█▌        | 40/250 [00:39<02:10,  1.61it/s]
 16%|█▋        | 41/250 [00:40<02:08,  1.63it/s]
 17%|█▋        | 42/250 [00:40<01:40,  2.06it/s]
 17%|█▋        | 43/250 [00:41<02:46,  1.24it/s]
 18%|█▊        | 44/250 [00:42<02:38,  1.30it/s]
 18%|█▊        | 45/250 [00:42<02:16,  1.50it/s]
 19%|█▉        | 47/250 [00:43<01:25,  2.36it/s]
 19%|█▉        | 48/250 [00:43<01:24,  2.38it/s]
 20%|█▉        | 49/250 [00:43<01:20,  2.50it/s]
 20%|██        | 50/250 [00:44<01:12,  2.75it/s]
 20%|██        | 51/250 [00:44<01:07,  2.94it/s]
 21%|██        | 52/250 [00:45<01:42,  1.94it/s]
 22%|██▏       | 54/250 [00:46<01:21,  2.40it/s]
 22%|██▏       | 55/250 [00:48<02:43,  1.19it/s]
 23%|██▎       | 57/250 [00:49<02:26,  1.32it/s]
 23%|██▎       | 58/250 [00:50<02:35,  1.24it/s]
 24%|██▎       | 59/250 [00:55<05:40,  1.78s/it]
 25%|██▍       | 62/250 [00:55<02:47,  1.13it/s]
 25%|██▌       | 63/250 [00:55<02:20,  1.33it/s]
 26%|██▌       | 64/250 [00:56<02:14,  1.39it/s]
 26%|██▌       | 65/250 [00:57<02:19,  1.33it/s]
 26%|██▋       | 66/250 [00:58<02:51,  1.07it/s]
 27%|██▋       | 67/250 [00:59<02:32,  1.20it/s]
 27%|██▋       | 68/250 [01:00<02:45,  1.10it/s]
 28%|██▊       | 69/250 [01:00<02:21,  1.28it/s]
 28%|██▊       | 70/250 [01:01<02:25,  1.24it/s]
 28%|██▊       | 71/250 [01:01<02:08,  1.39it/s]
 29%|██▉       | 72/250 [01:02<01:57,  1.51it/s]
 29%|██▉       | 73/250 [01:05<03:45,  1.27s/it]
 30%|██▉       | 74/250 [01:05<03:09,  1.08s/it]
 30%|███       | 75/250 [01:06<02:26,  1.19it/s]
 30%|███       | 76/250 [01:06<02:13,  1.30it/s]
 31%|███       | 77/250 [01:06<01:40,  1.72it/s]
 31%|███       | 78/250 [01:07<01:21,  2.12it/s]
 32%|███▏      | 79/250 [01:07<01:32,  1.84it/s]
 32%|███▏      | 80/250 [01:08<01:20,  2.11it/s]
 32%|███▏      | 81/250 [01:08<01:07,  2.50it/s]
 33%|███▎      | 82/250 [01:08<00:53,  3.16it/s]
 34%|███▎      | 84/250 [01:09<01:10,  2.34it/s]
 34%|███▍      | 85/250 [01:09<01:02,  2.65it/s]
 34%|███▍      | 86/250 [01:11<02:08,  1.28it/s]
 35%|███▍      | 87/250 [01:12<02:15,  1.20it/s]
 35%|███▌      | 88/250 [01:12<01:51,  1.46it/s]
 36%|███▌      | 89/250 [01:13<01:33,  1.72it/s]
 37%|███▋      | 92/250 [01:13<00:51,  3.05it/s]
 37%|███▋      | 93/250 [01:14<01:13,  2.14it/s]
 38%|███▊      | 94/250 [01:16<02:04,  1.25it/s]
 38%|███▊      | 95/250 [01:17<02:17,  1.13it/s]
 38%|███▊      | 96/250 [01:20<03:44,  1.46s/it]
 39%|███▉      | 97/250 [01:23<04:35,  1.80s/it]
 39%|███▉      | 98/250 [01:24<03:39,  1.44s/it]
 40%|███▉      | 99/250 [01:24<03:13,  1.28s/it]
 40%|████      | 100/250 [01:25<02:31,  1.01s/it]
 40%|████      | 101/250 [01:26<02:19,  1.07it/s]
 41%|████      | 102/250 [01:26<02:04,  1.19it/s]
 41%|████      | 103/250 [01:27<01:57,  1.25it/s]
 42%|████▏     | 104/250 [01:29<02:37,  1.08s/it]
 43%|████▎     | 107/250 [01:32<02:29,  1.05s/it]
 43%|████▎     | 108/250 [01:32<02:09,  1.09it/s]
 44%|████▎     | 109/250 [01:33<02:24,  1.03s/it]
 44%|████▍     | 110/250 [01:34<02:17,  1.02it/s]
 44%|████▍     | 111/250 [01:36<02:50,  1.23s/it]
 45%|████▍     | 112/250 [01:37<02:42,  1.18s/it]
 46%|████▌     | 114/250 [01:38<01:38,  1.38it/s]
 46%|████▌     | 115/250 [01:38<01:22,  1.63it/s]
 46%|████▋     | 116/250 [01:38<01:14,  1.79it/s]
 47%|████▋     | 117/250 [01:39<01:10,  1.88it/s]
 47%|████▋     | 118/250 [01:40<01:52,  1.18it/s]
 48%|████▊     | 120/250 [01:41<01:10,  1.85it/s]
 48%|████▊     | 121/250 [01:43<02:15,  1.05s/it]
 49%|████▉     | 122/250 [01:44<01:56,  1.10it/s]
 50%|████▉     | 124/250 [01:44<01:14,  1.69it/s]
 50%|█████     | 126/250 [01:45<00:57,  2.17it/s]
 51%|█████     | 127/250 [01:45<00:55,  2.24it/s]
 51%|█████     | 128/250 [01:45<00:54,  2.25it/s]
 52%|█████▏    | 129/250 [01:48<02:06,  1.04s/it]
 52%|█████▏    | 130/250 [01:51<02:47,  1.40s/it]
 52%|█████▏    | 131/250 [01:52<02:38,  1.33s/it]
 53%|█████▎    | 132/250 [01:54<02:57,  1.50s/it]
 53%|█████▎    | 133/250 [01:54<02:13,  1.14s/it]
 54%|█████▎    | 134/250 [01:56<02:25,  1.25s/it]
 54%|█████▍    | 135/250 [01:58<02:52,  1.50s/it]
 54%|█████▍    | 136/250 [01:58<02:07,  1.11s/it]
 55%|█████▍    | 137/250 [01:58<01:48,  1.04it/s]
 55%|█████▌    | 138/250 [02:00<01:58,  1.06s/it]
 56%|█████▌    | 139/250 [02:01<01:54,  1.03s/it]
 56%|█████▌    | 140/250 [02:03<02:36,  1.43s/it]
 56%|█████▋    | 141/250 [02:04<02:31,  1.39s/it]
 57%|█████▋    | 142/250 [02:05<02:12,  1.23s/it]
 58%|█████▊    | 144/250 [02:06<01:24,  1.26it/s]
 58%|█████▊    | 145/250 [02:06<01:19,  1.31it/s]
 58%|█████▊    | 146/250 [02:07<01:04,  1.61it/s]
 59%|█████▉    | 147/250 [02:08<01:13,  1.40it/s]
 59%|█████▉    | 148/250 [02:09<01:39,  1.02it/s]
 60%|█████▉    | 149/250 [02:11<02:00,  1.19s/it]
 60%|██████    | 150/250 [02:12<01:40,  1.01s/it]
 60%|██████    | 151/250 [02:12<01:17,  1.28it/s]
 62%|██████▏   | 154/250 [02:13<00:57,  1.66it/s]
 62%|██████▏   | 155/250 [02:14<00:57,  1.65it/s]
 62%|██████▏   | 156/250 [02:14<00:53,  1.77it/s]
 64%|██████▎   | 159/250 [02:15<00:36,  2.51it/s]
 64%|██████▍   | 160/250 [02:15<00:33,  2.67it/s]
 64%|██████▍   | 161/250 [02:15<00:30,  2.90it/s]
 65%|██████▍   | 162/250 [02:16<00:25,  3.40it/s]
 65%|██████▌   | 163/250 [02:16<00:23,  3.77it/s]
 66%|██████▌   | 164/250 [02:18<01:15,  1.14it/s]
 66%|██████▌   | 165/250 [02:19<01:15,  1.12it/s]
 66%|██████▋   | 166/250 [02:19<00:57,  1.47it/s]
 67%|██████▋   | 167/250 [02:20<00:48,  1.73it/s]
 67%|██████▋   | 168/250 [02:22<01:29,  1.09s/it]
 68%|██████▊   | 169/250 [02:24<01:44,  1.29s/it]
 68%|██████▊   | 170/250 [02:25<01:36,  1.21s/it]
 68%|██████▊   | 171/250 [02:26<01:30,  1.15s/it]
 69%|██████▉   | 172/250 [02:26<01:06,  1.17it/s]
 70%|██████▉   | 174/250 [02:27<00:54,  1.39it/s]
 70%|███████   | 175/250 [02:27<00:47,  1.59it/s]
 70%|███████   | 176/250 [02:28<00:43,  1.71it/s]
 71%|███████   | 178/250 [02:29<00:42,  1.70it/s]
 72%|███████▏  | 179/250 [02:30<00:42,  1.66it/s]
 72%|███████▏  | 180/250 [02:31<00:52,  1.33it/s]
 72%|███████▏  | 181/250 [02:31<00:42,  1.64it/s]
 73%|███████▎  | 182/250 [02:32<00:51,  1.33it/s]
 73%|███████▎  | 183/250 [02:34<01:05,  1.03it/s]
 74%|███████▍  | 185/250 [02:35<00:57,  1.13it/s]
 74%|███████▍  | 186/250 [02:36<00:57,  1.11it/s]
 75%|███████▍  | 187/250 [02:39<01:19,  1.27s/it]
 75%|███████▌  | 188/250 [02:39<01:04,  1.04s/it]
 76%|███████▌  | 189/250 [02:40<00:59,  1.02it/s]
 76%|███████▌  | 190/250 [02:41<01:01,  1.02s/it]
 76%|███████▋  | 191/250 [02:41<00:47,  1.25it/s]
 77%|███████▋  | 192/250 [02:42<00:36,  1.61it/s]
 77%|███████▋  | 193/250 [02:43<00:56,  1.00it/s]
 78%|███████▊  | 194/250 [02:44<00:49,  1.14it/s]
 78%|███████▊  | 195/250 [02:44<00:36,  1.53it/s]
 78%|███████▊  | 196/250 [02:45<00:36,  1.50it/s]
 79%|███████▉  | 197/250 [02:46<00:44,  1.19it/s]
 79%|███████▉  | 198/250 [02:47<00:44,  1.16it/s]
 80%|███████▉  | 199/250 [02:48<00:39,  1.28it/s]
 80%|████████  | 200/250 [02:49<00:46,  1.08it/s]
 80%|████████  | 201/250 [02:50<00:55,  1.13s/it]
 81%|████████  | 202/250 [02:51<00:40,  1.18it/s]
 81%|████████  | 203/250 [02:51<00:29,  1.59it/s]
 82%|████████▏ | 204/250 [02:51<00:24,  1.89it/s]
 82%|████████▏ | 205/250 [02:52<00:27,  1.66it/s]
 82%|████████▏ | 206/250 [02:53<00:30,  1.43it/s]
 83%|████████▎ | 207/250 [02:53<00:24,  1.75it/s]
 83%|████████▎ | 208/250 [02:54<00:29,  1.45it/s]
 84%|████████▎ | 209/250 [02:55<00:27,  1.47it/s]
 84%|████████▍ | 210/250 [02:55<00:29,  1.38it/s]
 84%|████████▍ | 211/250 [02:57<00:36,  1.07it/s]
 85%|████████▍ | 212/250 [02:58<00:40,  1.06s/it]
 85%|████████▌ | 213/250 [03:00<00:46,  1.26s/it]
 86%|████████▌ | 214/250 [03:02<00:48,  1.36s/it]
 86%|████████▋ | 216/250 [03:03<00:34,  1.02s/it]
 87%|████████▋ | 217/250 [03:04<00:31,  1.06it/s]
 87%|████████▋ | 218/250 [03:06<00:39,  1.22s/it]
 88%|████████▊ | 219/250 [03:06<00:31,  1.02s/it]
 88%|████████▊ | 220/250 [03:06<00:23,  1.25it/s]
 89%|████████▉ | 222/250 [03:07<00:14,  1.92it/s]
 89%|████████▉ | 223/250 [03:08<00:20,  1.34it/s]
 90%|█████████ | 225/250 [03:09<00:13,  1.83it/s]
 90%|█████████ | 226/250 [03:10<00:15,  1.57it/s]
 91%|█████████ | 227/250 [03:11<00:16,  1.37it/s]
 91%|█████████ | 228/250 [03:13<00:23,  1.06s/it]
 92%|█████████▏| 229/250 [03:13<00:21,  1.01s/it]
 92%|█████████▏| 230/250 [03:14<00:16,  1.20it/s]
 93%|█████████▎| 232/250 [03:15<00:11,  1.52it/s]
 93%|█████████▎| 233/250 [03:15<00:09,  1.78it/s]
 94%|█████████▎| 234/250 [03:18<00:17,  1.09s/it]
 94%|█████████▍| 236/250 [03:18<00:10,  1.31it/s]
 95%|█████████▍| 237/250 [03:19<00:09,  1.36it/s]
 95%|█████████▌| 238/250 [03:19<00:07,  1.51it/s]
 96%|█████████▌| 239/250 [03:20<00:08,  1.29it/s]
 96%|█████████▌| 240/250 [03:23<00:12,  1.21s/it]
 96%|█████████▋| 241/250 [03:24<00:09,  1.09s/it]
 97%|█████████▋| 242/250 [03:25<00:09,  1.15s/it]
 97%|█████████▋| 243/250 [03:25<00:06,  1.12it/s]
 98%|█████████▊| 244/250 [03:26<00:04,  1.29it/s]
 98%|█████████▊| 245/250 [03:26<00:03,  1.45it/s]
 98%|█████████▊| 246/250 [03:27<00:02,  1.49it/s]
 99%|█████████▉| 247/250 [03:29<00:03,  1.28s/it]
 99%|█████████▉| 248/250 [03:30<00:01,  1.05it/s]
100%|█████████▉| 249/250 [03:31<00:01,  1.21s/it]
100%|██████████| 250/250 [03:33<00:00,  1.35s/it]
100%|██████████| 250/250 [03:33<00:00,  1.17it/s]
2025-05-14 13:12:46,721 - modnet - INFO - Loss per individual: ind 0: 0.119 	ind 1: 0.120 	ind 2: 0.114 	ind 3: 0.108 	ind 4: 0.132 	ind 5: 0.111 	ind 6: 0.107 	ind 7: 0.111 	ind 8: 0.119 	ind 9: 0.113 	ind 10: 0.116 	ind 11: 0.113 	ind 12: 0.113 	ind 13: 0.141 	ind 14: 0.113 	ind 15: 0.100 	ind 16: 0.108 	ind 17: 0.109 	ind 18: 0.105 	ind 19: 0.099 	ind 20: 0.116 	ind 21: 0.110 	ind 22: 0.107 	ind 23: 0.113 	ind 24: 0.113 	ind 25: 0.112 	ind 26: 0.113 	ind 27: 0.102 	ind 28: 0.102 	ind 29: 0.123 	ind 30: 0.106 	ind 31: 0.108 	ind 32: 0.115 	ind 33: 0.111 	ind 34: 0.100 	ind 35: 0.106 	ind 36: 0.110 	ind 37: 0.101 	ind 38: 0.114 	ind 39: 0.105 	ind 40: 0.109 	ind 41: 0.128 	ind 42: 0.103 	ind 43: 0.115 	ind 44: 0.108 	ind 45: 0.106 	ind 46: 0.109 	ind 47: 0.109 	ind 48: 0.119 	ind 49: 0.111 	
2025-05-14 13:12:46,723 - modnet - INFO - Generation number 7

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:10<42:52, 10.33s/it]
  1%|          | 2/250 [00:11<19:29,  4.71s/it]
  1%|          | 3/250 [00:11<10:59,  2.67s/it]
  2%|▏         | 4/250 [00:11<07:38,  1.86s/it]
  2%|▏         | 5/250 [00:12<05:01,  1.23s/it]
  2%|▏         | 6/250 [00:12<04:14,  1.04s/it]
  3%|▎         | 7/250 [00:12<03:06,  1.31it/s]
  3%|▎         | 8/250 [00:13<02:54,  1.38it/s]
  4%|▎         | 9/250 [00:14<02:32,  1.58it/s]
  4%|▍         | 10/250 [00:14<02:05,  1.91it/s]
  4%|▍         | 11/250 [00:15<02:20,  1.70it/s]
  5%|▍         | 12/250 [00:16<03:16,  1.21it/s]
  6%|▌         | 14/250 [00:16<01:59,  1.97it/s]
  6%|▌         | 15/250 [00:17<02:30,  1.56it/s]
  6%|▋         | 16/250 [00:18<02:23,  1.63it/s]
  7%|▋         | 17/250 [00:20<03:37,  1.07it/s]
  7%|▋         | 18/250 [00:22<05:10,  1.34s/it]
  8%|▊         | 19/250 [00:23<04:53,  1.27s/it]
  8%|▊         | 20/250 [00:25<05:21,  1.40s/it]
  9%|▉         | 22/250 [00:25<03:14,  1.17it/s]
  9%|▉         | 23/250 [00:26<03:03,  1.24it/s]
 10%|▉         | 24/250 [00:27<03:29,  1.08it/s]
 10%|█         | 25/250 [00:28<03:27,  1.08it/s]
 11%|█         | 27/250 [00:29<02:50,  1.30it/s]
 11%|█         | 28/250 [00:29<02:15,  1.64it/s]
 12%|█▏        | 29/250 [00:29<01:48,  2.03it/s]
 12%|█▏        | 30/250 [00:30<01:49,  2.01it/s]
 12%|█▏        | 31/250 [00:30<01:47,  2.04it/s]
 13%|█▎        | 33/250 [00:31<01:24,  2.58it/s]
 14%|█▍        | 35/250 [00:32<01:18,  2.74it/s]
 14%|█▍        | 36/250 [00:32<01:12,  2.94it/s]
 15%|█▍        | 37/250 [00:32<01:20,  2.63it/s]
 15%|█▌        | 38/250 [00:33<01:22,  2.56it/s]
 16%|█▌        | 39/250 [00:33<01:27,  2.41it/s]
 16%|█▌        | 40/250 [00:35<02:38,  1.32it/s]
 16%|█▋        | 41/250 [00:35<02:18,  1.51it/s]
 17%|█▋        | 42/250 [00:38<04:23,  1.26s/it]
 17%|█▋        | 43/250 [00:39<04:21,  1.27s/it]
 18%|█▊        | 44/250 [00:41<05:07,  1.49s/it]
 18%|█▊        | 45/250 [00:42<03:59,  1.17s/it]
 18%|█▊        | 46/250 [00:44<05:22,  1.58s/it]
 19%|█▉        | 47/250 [00:45<04:11,  1.24s/it]
 19%|█▉        | 48/250 [00:46<04:22,  1.30s/it]
 20%|█▉        | 49/250 [00:46<03:09,  1.06it/s]
 20%|██        | 50/250 [00:49<04:56,  1.48s/it]
 20%|██        | 51/250 [00:49<03:50,  1.16s/it]
 21%|██        | 52/250 [00:52<04:59,  1.51s/it]
 21%|██        | 53/250 [00:52<03:53,  1.18s/it]
 22%|██▏       | 55/250 [00:53<02:32,  1.28it/s]
 22%|██▏       | 56/250 [00:54<02:39,  1.21it/s]
 23%|██▎       | 57/250 [00:54<02:26,  1.32it/s]
 23%|██▎       | 58/250 [00:55<01:58,  1.62it/s]
 24%|██▎       | 59/250 [00:55<02:03,  1.54it/s]
 24%|██▍       | 60/250 [00:56<02:06,  1.50it/s]
 24%|██▍       | 61/250 [00:57<02:15,  1.40it/s]
 25%|██▍       | 62/250 [00:57<01:52,  1.67it/s]
 25%|██▌       | 63/250 [00:57<01:29,  2.09it/s]
 26%|██▌       | 64/250 [00:58<01:32,  2.01it/s]
 26%|██▌       | 65/250 [00:59<01:47,  1.72it/s]
 26%|██▋       | 66/250 [01:00<02:44,  1.12it/s]
 27%|██▋       | 67/250 [01:01<02:35,  1.18it/s]
 27%|██▋       | 68/250 [01:01<02:13,  1.37it/s]
 28%|██▊       | 69/250 [01:03<02:34,  1.17it/s]
 28%|██▊       | 70/250 [01:05<03:40,  1.22s/it]
 28%|██▊       | 71/250 [01:06<03:16,  1.10s/it]
 29%|██▉       | 72/250 [01:07<03:14,  1.09s/it]
 29%|██▉       | 73/250 [01:07<02:28,  1.20it/s]
 30%|██▉       | 74/250 [01:07<01:54,  1.53it/s]
 30%|███       | 75/250 [01:07<01:26,  2.01it/s]
 31%|███       | 77/250 [01:08<01:02,  2.75it/s]
 31%|███       | 78/250 [01:09<01:46,  1.62it/s]
 32%|███▏      | 79/250 [01:10<01:45,  1.62it/s]
 32%|███▏      | 81/250 [01:10<01:05,  2.57it/s]
 33%|███▎      | 83/250 [01:11<01:20,  2.06it/s]
 34%|███▎      | 84/250 [01:12<01:32,  1.79it/s]
 34%|███▍      | 85/250 [01:13<02:07,  1.29it/s]
 34%|███▍      | 86/250 [01:15<02:31,  1.08it/s]
 35%|███▍      | 87/250 [01:16<02:35,  1.05it/s]
 35%|███▌      | 88/250 [01:16<02:07,  1.27it/s]
 36%|███▌      | 89/250 [01:16<01:47,  1.50it/s]
 36%|███▌      | 90/250 [01:17<01:31,  1.76it/s]
 37%|███▋      | 92/250 [01:17<01:09,  2.27it/s]
 37%|███▋      | 93/250 [01:18<01:06,  2.37it/s]
 38%|███▊      | 94/250 [01:18<01:07,  2.30it/s]
 38%|███▊      | 95/250 [01:19<01:22,  1.87it/s]
 38%|███▊      | 96/250 [01:19<01:04,  2.37it/s]
 39%|███▉      | 97/250 [01:20<01:43,  1.48it/s]
 39%|███▉      | 98/250 [01:21<01:46,  1.42it/s]
 40%|███▉      | 99/250 [01:22<01:31,  1.64it/s]
 40%|████      | 100/250 [01:22<01:26,  1.73it/s]
 40%|████      | 101/250 [01:24<02:04,  1.20it/s]
 41%|████      | 102/250 [01:24<01:47,  1.38it/s]
 41%|████      | 103/250 [01:24<01:30,  1.63it/s]
 42%|████▏     | 104/250 [01:25<01:43,  1.41it/s]
 42%|████▏     | 105/250 [01:26<01:35,  1.52it/s]
 42%|████▏     | 106/250 [01:26<01:32,  1.56it/s]
 43%|████▎     | 107/250 [01:27<01:10,  2.04it/s]
 43%|████▎     | 108/250 [01:27<01:04,  2.21it/s]
 44%|████▎     | 109/250 [01:28<01:36,  1.46it/s]
 44%|████▍     | 110/250 [01:31<02:48,  1.21s/it]
 44%|████▍     | 111/250 [01:31<02:02,  1.14it/s]
 45%|████▍     | 112/250 [01:32<02:12,  1.04it/s]
 45%|████▌     | 113/250 [01:32<01:44,  1.31it/s]
 46%|████▌     | 114/250 [01:32<01:17,  1.74it/s]
 46%|████▌     | 115/250 [01:34<02:06,  1.07it/s]
 46%|████▋     | 116/250 [01:34<01:31,  1.46it/s]
 47%|████▋     | 117/250 [01:34<01:15,  1.77it/s]
 47%|████▋     | 118/250 [01:35<01:08,  1.94it/s]
 48%|████▊     | 119/250 [01:35<00:51,  2.53it/s]
 49%|████▉     | 122/250 [01:35<00:25,  5.05it/s]
 50%|████▉     | 124/250 [01:36<00:26,  4.70it/s]
 50%|█████     | 125/250 [01:36<00:30,  4.12it/s]
 50%|█████     | 126/250 [01:37<00:42,  2.89it/s]
 51%|█████     | 127/250 [01:37<00:55,  2.22it/s]
 51%|█████     | 128/250 [01:38<00:46,  2.64it/s]
 52%|█████▏    | 129/250 [01:38<00:53,  2.25it/s]
 52%|█████▏    | 130/250 [01:39<00:59,  2.01it/s]
 52%|█████▏    | 131/250 [01:40<01:22,  1.45it/s]
 53%|█████▎    | 132/250 [01:41<01:22,  1.42it/s]
 53%|█████▎    | 133/250 [01:42<01:31,  1.28it/s]
 54%|█████▎    | 134/250 [01:43<01:42,  1.13it/s]
 54%|█████▍    | 135/250 [01:43<01:17,  1.49it/s]
 54%|█████▍    | 136/250 [01:44<01:26,  1.31it/s]
 55%|█████▍    | 137/250 [01:46<02:11,  1.16s/it]
 55%|█████▌    | 138/250 [01:47<01:43,  1.08it/s]
 56%|█████▌    | 139/250 [01:47<01:19,  1.40it/s]
 56%|█████▌    | 140/250 [01:47<01:10,  1.56it/s]
 56%|█████▋    | 141/250 [01:48<01:04,  1.68it/s]
 57%|█████▋    | 142/250 [01:48<00:52,  2.05it/s]
 58%|█████▊    | 144/250 [01:48<00:40,  2.62it/s]
 58%|█████▊    | 146/250 [01:49<00:27,  3.76it/s]
 59%|█████▉    | 147/250 [01:50<00:43,  2.37it/s]
 59%|█████▉    | 148/250 [01:50<00:38,  2.66it/s]
 60%|█████▉    | 149/250 [01:50<00:31,  3.16it/s]
 60%|██████    | 150/250 [01:50<00:27,  3.68it/s]
 60%|██████    | 151/250 [01:51<00:36,  2.73it/s]
 61%|██████    | 152/250 [01:51<00:30,  3.25it/s]
 61%|██████    | 153/250 [01:51<00:28,  3.40it/s]
 62%|██████▏   | 154/250 [01:52<00:40,  2.38it/s]
 62%|██████▏   | 156/250 [01:52<00:30,  3.12it/s]
 63%|██████▎   | 157/250 [01:54<00:59,  1.57it/s]
 63%|██████▎   | 158/250 [01:54<00:52,  1.76it/s]
 64%|██████▎   | 159/250 [01:55<00:45,  1.98it/s]
 64%|██████▍   | 160/250 [01:55<00:40,  2.25it/s]
 64%|██████▍   | 161/250 [01:58<01:54,  1.29s/it]
 65%|██████▍   | 162/250 [01:58<01:23,  1.06it/s]
 65%|██████▌   | 163/250 [01:59<01:23,  1.04it/s]
 66%|██████▌   | 165/250 [02:00<01:03,  1.33it/s]
 66%|██████▋   | 166/250 [02:01<01:03,  1.33it/s]
 67%|██████▋   | 167/250 [02:02<01:00,  1.37it/s]
 67%|██████▋   | 168/250 [02:02<00:55,  1.48it/s]
 68%|██████▊   | 169/250 [02:02<00:42,  1.92it/s]
 68%|██████▊   | 170/250 [02:03<00:33,  2.39it/s]
 68%|██████▊   | 171/250 [02:03<00:38,  2.06it/s]
 69%|██████▉   | 172/250 [02:03<00:30,  2.54it/s]
 70%|██████▉   | 174/250 [02:04<00:26,  2.82it/s]
 70%|███████   | 175/250 [02:05<00:29,  2.51it/s]
 70%|███████   | 176/250 [02:05<00:33,  2.24it/s]
 72%|███████▏  | 179/250 [02:06<00:21,  3.34it/s]
 72%|███████▏  | 180/250 [02:07<00:29,  2.38it/s]
 72%|███████▏  | 181/250 [02:08<00:47,  1.46it/s]
 73%|███████▎  | 182/250 [02:09<00:50,  1.34it/s]
 73%|███████▎  | 183/250 [02:10<00:52,  1.27it/s]
 74%|███████▎  | 184/250 [02:12<01:07,  1.03s/it]
 74%|███████▍  | 185/250 [02:15<01:40,  1.54s/it]
 74%|███████▍  | 186/250 [02:15<01:23,  1.30s/it]
 75%|███████▍  | 187/250 [02:16<01:04,  1.02s/it]
 75%|███████▌  | 188/250 [02:16<00:52,  1.18it/s]
 76%|███████▌  | 190/250 [02:17<00:44,  1.35it/s]
 77%|███████▋  | 192/250 [02:19<00:44,  1.30it/s]
 77%|███████▋  | 193/250 [02:19<00:35,  1.59it/s]
 78%|███████▊  | 194/250 [02:20<00:43,  1.29it/s]
 78%|███████▊  | 195/250 [02:21<00:46,  1.18it/s]
 78%|███████▊  | 196/250 [02:26<01:40,  1.87s/it]
 79%|███████▉  | 197/250 [02:28<01:38,  1.85s/it]
 79%|███████▉  | 198/250 [02:28<01:10,  1.36s/it]
 80%|███████▉  | 199/250 [02:29<01:07,  1.32s/it]
 80%|████████  | 200/250 [02:29<00:51,  1.03s/it]
 80%|████████  | 201/250 [02:30<00:45,  1.07it/s]
 81%|████████  | 202/250 [02:31<00:47,  1.02it/s]
 81%|████████  | 203/250 [02:32<00:36,  1.29it/s]
 82%|████████▏ | 204/250 [02:34<00:56,  1.23s/it]
 82%|████████▏ | 205/250 [02:34<00:42,  1.06it/s]
 82%|████████▏ | 206/250 [02:36<00:48,  1.10s/it]
 83%|████████▎ | 207/250 [02:38<01:03,  1.47s/it]
 83%|████████▎ | 208/250 [02:38<00:45,  1.09s/it]
 84%|████████▎ | 209/250 [02:40<00:48,  1.20s/it]
 84%|████████▍ | 210/250 [02:40<00:43,  1.08s/it]
 84%|████████▍ | 211/250 [02:41<00:34,  1.12it/s]
 85%|████████▍ | 212/250 [02:41<00:29,  1.29it/s]
 85%|████████▌ | 213/250 [02:42<00:26,  1.42it/s]
 86%|████████▌ | 214/250 [02:42<00:19,  1.84it/s]
 86%|████████▌ | 215/250 [02:43<00:27,  1.29it/s]
 86%|████████▋ | 216/250 [02:45<00:30,  1.11it/s]
 87%|████████▋ | 217/250 [02:46<00:30,  1.07it/s]
 87%|████████▋ | 218/250 [02:46<00:23,  1.34it/s]
 88%|████████▊ | 219/250 [02:48<00:38,  1.23s/it]
 88%|████████▊ | 220/250 [02:49<00:30,  1.02s/it]
 88%|████████▊ | 221/250 [02:49<00:23,  1.24it/s]
 89%|████████▉ | 222/250 [02:50<00:22,  1.25it/s]
 89%|████████▉ | 223/250 [02:51<00:21,  1.26it/s]
 90%|████████▉ | 224/250 [02:51<00:17,  1.46it/s]
 90%|█████████ | 226/250 [02:52<00:14,  1.69it/s]
 91%|█████████ | 228/250 [02:54<00:15,  1.46it/s]
 92%|█████████▏| 229/250 [02:54<00:13,  1.50it/s]
 92%|█████████▏| 231/250 [02:55<00:09,  1.99it/s]
 93%|█████████▎| 232/250 [02:55<00:08,  2.19it/s]
 93%|█████████▎| 233/250 [02:56<00:09,  1.85it/s]
 94%|█████████▎| 234/250 [02:56<00:08,  1.86it/s]
 94%|█████████▍| 235/250 [02:57<00:06,  2.26it/s]
 94%|█████████▍| 236/250 [02:57<00:06,  2.02it/s]
 95%|█████████▌| 238/250 [02:57<00:03,  3.34it/s]
 96%|█████████▌| 240/250 [02:58<00:02,  4.61it/s]
 96%|█████████▋| 241/250 [02:58<00:02,  4.33it/s]
 97%|█████████▋| 242/250 [02:58<00:01,  4.29it/s]
 97%|█████████▋| 243/250 [02:59<00:03,  2.00it/s]
 98%|█████████▊| 244/250 [03:00<00:03,  1.64it/s]
 98%|█████████▊| 245/250 [03:02<00:04,  1.13it/s]
 98%|█████████▊| 246/250 [03:02<00:03,  1.29it/s]
 99%|█████████▉| 247/250 [03:03<00:02,  1.44it/s]
 99%|█████████▉| 248/250 [03:03<00:01,  1.91it/s]
100%|█████████▉| 249/250 [03:04<00:00,  1.69it/s]
100%|██████████| 250/250 [03:06<00:00,  1.11s/it]
100%|██████████| 250/250 [03:06<00:00,  1.34it/s]
2025-05-14 13:15:53,357 - modnet - INFO - Loss per individual: ind 0: 0.125 	ind 1: 0.099 	ind 2: 0.104 	ind 3: 0.108 	ind 4: 0.114 	ind 5: 0.143 	ind 6: 0.112 	ind 7: 0.105 	ind 8: 0.117 	ind 9: 0.116 	ind 10: 0.129 	ind 11: 0.110 	ind 12: 0.107 	ind 13: 0.122 	ind 14: 0.119 	ind 15: 0.122 	ind 16: 0.104 	ind 17: 0.109 	ind 18: 0.123 	ind 19: 0.116 	ind 20: 0.110 	ind 21: 0.108 	ind 22: 0.104 	ind 23: 0.117 	ind 24: 0.103 	ind 25: 0.107 	ind 26: 0.188 	ind 27: 0.118 	ind 28: 0.109 	ind 29: 0.116 	ind 30: 0.126 	ind 31: 0.109 	ind 32: 0.118 	ind 33: 0.112 	ind 34: 0.102 	ind 35: 0.103 	ind 36: 0.116 	ind 37: 0.298 	ind 38: 0.102 	ind 39: 0.098 	ind 40: 0.103 	ind 41: 0.106 	ind 42: 0.100 	ind 43: 0.112 	ind 44: 0.120 	ind 45: 0.105 	ind 46: 0.102 	ind 47: 0.105 	ind 48: 0.115 	ind 49: 0.113 	
2025-05-14 13:15:53,359 - modnet - INFO - Generation number 8

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:05<24:36,  5.93s/it]
  1%|          | 2/250 [00:06<12:18,  2.98s/it]
  1%|          | 3/250 [00:07<07:43,  1.87s/it]
  2%|▏         | 4/250 [00:09<07:16,  1.78s/it]
  2%|▏         | 5/250 [00:09<05:35,  1.37s/it]
  2%|▏         | 6/250 [00:10<04:30,  1.11s/it]
  3%|▎         | 7/250 [00:10<03:28,  1.16it/s]
  3%|▎         | 8/250 [00:11<03:20,  1.21it/s]
  4%|▎         | 9/250 [00:13<04:47,  1.19s/it]
  4%|▍         | 10/250 [00:13<03:56,  1.02it/s]
  4%|▍         | 11/250 [00:14<03:47,  1.05it/s]
  5%|▌         | 13/250 [00:15<02:34,  1.53it/s]
  6%|▌         | 15/250 [00:15<01:56,  2.02it/s]
  7%|▋         | 17/250 [00:21<04:53,  1.26s/it]
  7%|▋         | 18/250 [00:21<04:13,  1.09s/it]
  8%|▊         | 19/250 [00:22<03:44,  1.03it/s]
  8%|▊         | 21/250 [00:22<02:20,  1.63it/s]
  9%|▉         | 22/250 [00:22<01:59,  1.90it/s]
  9%|▉         | 23/250 [00:22<01:49,  2.07it/s]
 10%|▉         | 24/250 [00:23<01:33,  2.42it/s]
 10%|█         | 25/250 [00:25<03:34,  1.05it/s]
 10%|█         | 26/250 [00:27<04:18,  1.15s/it]
 11%|█         | 27/250 [00:27<03:49,  1.03s/it]
 11%|█         | 28/250 [00:28<03:44,  1.01s/it]
 12%|█▏        | 29/250 [00:29<03:11,  1.16it/s]
 12%|█▏        | 30/250 [00:30<03:39,  1.00it/s]
 12%|█▏        | 31/250 [00:32<04:23,  1.20s/it]
 13%|█▎        | 32/250 [00:32<03:35,  1.01it/s]
 13%|█▎        | 33/250 [00:32<02:39,  1.36it/s]
 14%|█▎        | 34/250 [00:34<03:04,  1.17it/s]
 14%|█▍        | 35/250 [00:34<03:02,  1.18it/s]
 14%|█▍        | 36/250 [00:35<02:20,  1.53it/s]
 15%|█▍        | 37/250 [00:35<01:47,  1.97it/s]
 15%|█▌        | 38/250 [00:35<01:26,  2.45it/s]
 16%|█▌        | 40/250 [00:35<01:10,  2.99it/s]
 16%|█▋        | 41/250 [00:36<01:42,  2.04it/s]
 17%|█▋        | 43/250 [00:37<01:33,  2.21it/s]
 18%|█▊        | 44/250 [00:39<02:30,  1.37it/s]
 18%|█▊        | 45/250 [00:39<02:09,  1.59it/s]
 18%|█▊        | 46/250 [00:42<03:44,  1.10s/it]
 19%|█▉        | 47/250 [00:42<03:22,  1.00it/s]
 20%|█▉        | 49/250 [00:43<02:05,  1.60it/s]
 20%|██        | 50/250 [00:43<01:58,  1.69it/s]
 20%|██        | 51/250 [00:44<01:59,  1.67it/s]
 21%|██        | 53/250 [00:44<01:15,  2.59it/s]
 22%|██▏       | 54/250 [00:46<02:15,  1.45it/s]
 22%|██▏       | 55/250 [00:50<04:53,  1.50s/it]
 22%|██▏       | 56/250 [00:53<06:41,  2.07s/it]
 23%|██▎       | 57/250 [00:55<05:58,  1.86s/it]
 23%|██▎       | 58/250 [00:55<04:28,  1.40s/it]
 24%|██▎       | 59/250 [00:55<03:25,  1.08s/it]
 24%|██▍       | 60/250 [00:56<02:54,  1.09it/s]
 24%|██▍       | 61/250 [00:56<02:46,  1.13it/s]
 25%|██▍       | 62/250 [00:58<03:36,  1.15s/it]
 25%|██▌       | 63/250 [00:58<02:41,  1.16it/s]
 26%|██▌       | 64/250 [01:00<03:03,  1.01it/s]
 26%|██▌       | 65/250 [01:00<02:16,  1.35it/s]
 26%|██▋       | 66/250 [01:00<01:41,  1.82it/s]
 27%|██▋       | 67/250 [01:00<01:36,  1.90it/s]
 28%|██▊       | 69/250 [01:01<01:20,  2.24it/s]
 28%|██▊       | 70/250 [01:02<01:20,  2.23it/s]
 28%|██▊       | 71/250 [01:02<01:14,  2.40it/s]
 29%|██▉       | 72/250 [01:03<01:40,  1.77it/s]
 29%|██▉       | 73/250 [01:04<01:59,  1.48it/s]
 30%|██▉       | 74/250 [01:04<01:36,  1.83it/s]
 30%|███       | 75/250 [01:05<02:15,  1.29it/s]
 30%|███       | 76/250 [01:06<01:53,  1.53it/s]
 31%|███       | 77/250 [01:06<01:41,  1.71it/s]
 31%|███       | 78/250 [01:06<01:27,  1.96it/s]
 32%|███▏      | 79/250 [01:07<01:09,  2.46it/s]
 32%|███▏      | 80/250 [01:08<01:48,  1.56it/s]
 32%|███▏      | 81/250 [01:09<02:06,  1.34it/s]
 33%|███▎      | 82/250 [01:10<02:02,  1.37it/s]
 33%|███▎      | 83/250 [01:10<01:31,  1.82it/s]
 34%|███▎      | 84/250 [01:10<01:12,  2.27it/s]
 34%|███▍      | 85/250 [01:10<01:23,  1.98it/s]
 34%|███▍      | 86/250 [01:11<01:08,  2.39it/s]
 35%|███▌      | 88/250 [01:11<01:04,  2.51it/s]
 36%|███▌      | 89/250 [01:13<01:36,  1.68it/s]
 36%|███▋      | 91/250 [01:13<00:59,  2.69it/s]
 37%|███▋      | 92/250 [01:13<01:00,  2.60it/s]
 37%|███▋      | 93/250 [01:13<00:52,  3.00it/s]
 38%|███▊      | 94/250 [01:13<00:42,  3.64it/s]
 38%|███▊      | 95/250 [01:14<01:00,  2.55it/s]
 38%|███▊      | 96/250 [01:14<00:53,  2.89it/s]
 39%|███▉      | 97/250 [01:15<00:43,  3.55it/s]
 40%|███▉      | 99/250 [01:15<00:53,  2.85it/s]
 40%|████      | 100/250 [01:16<01:18,  1.90it/s]
 40%|████      | 101/250 [01:17<01:26,  1.73it/s]
 41%|████      | 103/250 [01:18<01:05,  2.25it/s]
 42%|████▏     | 104/250 [01:19<01:39,  1.47it/s]
 42%|████▏     | 105/250 [01:20<01:41,  1.43it/s]
 42%|████▏     | 106/250 [01:21<01:58,  1.22it/s]
 43%|████▎     | 107/250 [01:25<04:10,  1.76s/it]
 43%|████▎     | 108/250 [01:28<04:25,  1.87s/it]
 44%|████▎     | 109/250 [01:28<03:21,  1.43s/it]
 44%|████▍     | 110/250 [01:28<02:33,  1.10s/it]
 45%|████▍     | 112/250 [01:29<02:04,  1.11it/s]
 46%|████▌     | 114/250 [01:30<01:19,  1.71it/s]
 46%|████▌     | 115/250 [01:30<01:05,  2.07it/s]
 46%|████▋     | 116/250 [01:31<01:36,  1.39it/s]
 48%|████▊     | 119/250 [01:32<00:58,  2.24it/s]
 48%|████▊     | 120/250 [01:32<00:59,  2.20it/s]
 49%|████▉     | 122/250 [01:33<00:45,  2.83it/s]
 49%|████▉     | 123/250 [01:34<01:21,  1.55it/s]
 50%|████▉     | 124/250 [01:35<01:20,  1.57it/s]
 50%|█████     | 126/250 [01:35<00:53,  2.31it/s]
 51%|█████     | 127/250 [01:35<00:46,  2.62it/s]
 51%|█████     | 128/250 [01:36<00:46,  2.64it/s]
 52%|█████▏    | 130/250 [01:39<01:53,  1.06it/s]
 52%|█████▏    | 131/250 [01:40<01:40,  1.19it/s]
 53%|█████▎    | 132/250 [01:40<01:32,  1.28it/s]
 53%|█████▎    | 133/250 [01:41<01:12,  1.62it/s]
 54%|█████▎    | 134/250 [01:42<01:49,  1.06it/s]
 54%|█████▍    | 135/250 [01:46<03:28,  1.81s/it]
 54%|█████▍    | 136/250 [01:47<02:32,  1.33s/it]
 55%|█████▍    | 137/250 [01:50<03:47,  2.01s/it]
 55%|█████▌    | 138/250 [01:51<03:01,  1.62s/it]
 56%|█████▌    | 139/250 [01:52<02:29,  1.34s/it]
 56%|█████▌    | 140/250 [01:56<04:00,  2.19s/it]
 56%|█████▋    | 141/250 [01:57<03:40,  2.02s/it]
 57%|█████▋    | 142/250 [01:58<02:56,  1.63s/it]
 57%|█████▋    | 143/250 [01:58<02:12,  1.23s/it]
 58%|█████▊    | 144/250 [02:00<02:10,  1.23s/it]
 58%|█████▊    | 145/250 [02:00<01:42,  1.03it/s]
 58%|█████▊    | 146/250 [02:01<01:34,  1.10it/s]
 59%|█████▉    | 147/250 [02:01<01:20,  1.28it/s]
 59%|█████▉    | 148/250 [02:02<01:11,  1.43it/s]
 60%|█████▉    | 149/250 [02:03<01:17,  1.30it/s]
 60%|██████    | 150/250 [02:04<01:43,  1.04s/it]
 60%|██████    | 151/250 [02:05<01:21,  1.22it/s]
 61%|██████    | 152/250 [02:05<01:10,  1.39it/s]
 61%|██████    | 153/250 [02:07<01:54,  1.18s/it]
 62%|██████▏   | 154/250 [02:09<02:08,  1.34s/it]
 62%|██████▏   | 155/250 [02:09<01:35,  1.01s/it]
 62%|██████▏   | 156/250 [02:10<01:13,  1.28it/s]
 63%|██████▎   | 158/250 [02:10<00:45,  2.03it/s]
 64%|██████▍   | 160/250 [02:10<00:29,  3.07it/s]
 65%|██████▍   | 162/250 [02:10<00:21,  4.04it/s]
 66%|██████▌   | 164/250 [02:11<00:17,  4.88it/s]
 66%|██████▌   | 165/250 [02:12<00:40,  2.11it/s]
 66%|██████▋   | 166/250 [02:12<00:34,  2.42it/s]
 67%|██████▋   | 167/250 [02:13<00:38,  2.15it/s]
 67%|██████▋   | 168/250 [02:14<00:45,  1.81it/s]
 68%|██████▊   | 169/250 [02:14<00:35,  2.27it/s]
 68%|██████▊   | 170/250 [02:14<00:35,  2.27it/s]
 68%|██████▊   | 171/250 [02:16<00:51,  1.55it/s]
 69%|██████▉   | 172/250 [02:16<00:42,  1.82it/s]
 70%|██████▉   | 174/250 [02:16<00:26,  2.90it/s]
 70%|███████   | 175/250 [02:17<00:33,  2.21it/s]
 70%|███████   | 176/250 [02:17<00:26,  2.74it/s]
 71%|███████   | 177/250 [02:18<00:48,  1.51it/s]
 71%|███████   | 178/250 [02:19<00:36,  1.95it/s]
 72%|███████▏  | 179/250 [02:19<00:32,  2.19it/s]
 72%|███████▏  | 180/250 [02:19<00:28,  2.44it/s]
 72%|███████▏  | 181/250 [02:20<00:39,  1.74it/s]
 73%|███████▎  | 182/250 [02:20<00:33,  2.00it/s]
 73%|███████▎  | 183/250 [02:21<00:32,  2.05it/s]
 74%|███████▎  | 184/250 [02:23<01:10,  1.06s/it]
 74%|███████▍  | 185/250 [02:26<01:34,  1.46s/it]
 75%|███████▍  | 187/250 [02:29<01:33,  1.49s/it]
 75%|███████▌  | 188/250 [02:30<01:26,  1.39s/it]
 76%|███████▌  | 189/250 [02:31<01:18,  1.29s/it]
 76%|███████▌  | 190/250 [02:31<01:05,  1.10s/it]
 76%|███████▋  | 191/250 [02:33<01:07,  1.14s/it]
 77%|███████▋  | 192/250 [02:33<00:52,  1.11it/s]
 77%|███████▋  | 193/250 [02:34<00:50,  1.13it/s]
 78%|███████▊  | 194/250 [02:34<00:44,  1.26it/s]
 78%|███████▊  | 195/250 [02:35<00:32,  1.68it/s]
 78%|███████▊  | 196/250 [02:36<00:42,  1.26it/s]
 79%|███████▉  | 197/250 [02:36<00:37,  1.42it/s]
 79%|███████▉  | 198/250 [02:37<00:35,  1.47it/s]
 80%|███████▉  | 199/250 [02:37<00:29,  1.73it/s]
 80%|████████  | 200/250 [02:39<00:53,  1.07s/it]
 80%|████████  | 201/250 [02:40<00:44,  1.09it/s]
 81%|████████  | 202/250 [02:42<01:01,  1.27s/it]
 81%|████████  | 203/250 [02:44<01:08,  1.46s/it]
 82%|████████▏ | 204/250 [02:45<01:01,  1.34s/it]
 82%|████████▏ | 205/250 [02:45<00:44,  1.01it/s]
 82%|████████▏ | 206/250 [02:45<00:33,  1.32it/s]
 83%|████████▎ | 207/250 [02:46<00:26,  1.64it/s]
 83%|████████▎ | 208/250 [02:46<00:19,  2.11it/s]
 84%|████████▎ | 209/250 [02:47<00:29,  1.39it/s]
 84%|████████▍ | 210/250 [02:47<00:23,  1.73it/s]
 84%|████████▍ | 211/250 [02:48<00:18,  2.15it/s]
 85%|████████▍ | 212/250 [02:49<00:30,  1.26it/s]
 86%|████████▌ | 214/250 [02:50<00:19,  1.87it/s]
 86%|████████▌ | 215/250 [02:50<00:18,  1.90it/s]
 86%|████████▋ | 216/250 [02:51<00:18,  1.86it/s]
 87%|████████▋ | 217/250 [02:51<00:14,  2.27it/s]
 87%|████████▋ | 218/250 [02:52<00:20,  1.56it/s]
 88%|████████▊ | 219/250 [02:52<00:15,  1.99it/s]
 88%|████████▊ | 220/250 [02:56<00:43,  1.45s/it]
 88%|████████▊ | 221/250 [02:57<00:40,  1.40s/it]
 89%|████████▉ | 222/250 [02:57<00:29,  1.04s/it]
 89%|████████▉ | 223/250 [02:59<00:31,  1.15s/it]
 90%|████████▉ | 224/250 [03:00<00:27,  1.06s/it]
 90%|█████████ | 225/250 [03:00<00:21,  1.15it/s]
 90%|█████████ | 226/250 [03:01<00:19,  1.22it/s]
 91%|█████████ | 227/250 [03:02<00:21,  1.09it/s]
 91%|█████████ | 228/250 [03:04<00:26,  1.20s/it]
 92%|█████████▏| 229/250 [03:05<00:22,  1.06s/it]
 92%|█████████▏| 230/250 [03:05<00:17,  1.17it/s]
 92%|█████████▏| 231/250 [03:05<00:12,  1.58it/s]
 93%|█████████▎| 232/250 [03:05<00:09,  1.94it/s]
 93%|█████████▎| 233/250 [03:06<00:09,  1.87it/s]
 94%|█████████▎| 234/250 [03:06<00:06,  2.40it/s]
 94%|█████████▍| 235/250 [03:08<00:14,  1.00it/s]
 94%|█████████▍| 236/250 [03:09<00:11,  1.27it/s]
 95%|█████████▍| 237/250 [03:09<00:09,  1.37it/s]
 95%|█████████▌| 238/250 [03:11<00:10,  1.13it/s]
 96%|█████████▌| 239/250 [03:12<00:12,  1.16s/it]
 96%|█████████▌| 240/250 [03:15<00:15,  1.53s/it]
 96%|█████████▋| 241/250 [03:15<00:11,  1.31s/it]
 97%|█████████▋| 242/250 [03:18<00:13,  1.68s/it]
 97%|█████████▋| 243/250 [03:18<00:08,  1.27s/it]
 98%|█████████▊| 244/250 [03:19<00:05,  1.05it/s]
 98%|█████████▊| 245/250 [03:20<00:05,  1.11s/it]
 98%|█████████▊| 246/250 [03:23<00:06,  1.54s/it]
 99%|█████████▉| 247/250 [03:23<00:03,  1.16s/it]
 99%|█████████▉| 248/250 [03:24<00:02,  1.08s/it]
100%|█████████▉| 249/250 [03:26<00:01,  1.44s/it]
100%|██████████| 250/250 [03:28<00:00,  1.68s/it]
100%|██████████| 250/250 [03:28<00:00,  1.20it/s]
2025-05-14 13:19:22,189 - modnet - INFO - Loss per individual: ind 0: 0.099 	ind 1: 0.103 	ind 2: 0.111 	ind 3: 0.111 	ind 4: 0.107 	ind 5: 0.132 	ind 6: 0.108 	ind 7: 0.109 	ind 8: 0.125 	ind 9: 0.118 	ind 10: 0.108 	ind 11: 0.106 	ind 12: 0.109 	ind 13: 0.103 	ind 14: 0.128 	ind 15: 0.103 	ind 16: 0.114 	ind 17: 0.117 	ind 18: 0.116 	ind 19: 0.113 	ind 20: 0.131 	ind 21: 0.110 	ind 22: 0.123 	ind 23: 0.108 	ind 24: 0.108 	ind 25: 0.141 	ind 26: 0.106 	ind 27: 0.111 	ind 28: 0.105 	ind 29: 0.117 	ind 30: 0.104 	ind 31: 0.102 	ind 32: 0.100 	ind 33: 0.103 	ind 34: 0.107 	ind 35: 0.114 	ind 36: 0.112 	ind 37: 0.105 	ind 38: 0.120 	ind 39: 0.121 	ind 40: 0.121 	ind 41: 0.106 	ind 42: 0.110 	ind 43: 0.110 	ind 44: 0.103 	ind 45: 0.121 	ind 46: 0.103 	ind 47: 0.108 	ind 48: 0.103 	ind 49: 0.114 	
2025-05-14 13:19:22,191 - modnet - INFO - Generation number 9

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:13<54:08, 13.04s/it]
  1%|          | 2/250 [00:13<23:52,  5.78s/it]
  1%|          | 3/250 [00:14<14:47,  3.59s/it]
  2%|▏         | 4/250 [00:15<10:22,  2.53s/it]
  2%|▏         | 5/250 [00:16<08:22,  2.05s/it]
  2%|▏         | 6/250 [00:17<06:03,  1.49s/it]
  3%|▎         | 7/250 [00:17<04:19,  1.07s/it]
  3%|▎         | 8/250 [00:19<05:30,  1.37s/it]
  4%|▎         | 9/250 [00:20<04:35,  1.14s/it]
  4%|▍         | 11/250 [00:20<02:35,  1.54it/s]
  5%|▍         | 12/250 [00:20<02:21,  1.68it/s]
  5%|▌         | 13/250 [00:20<01:56,  2.03it/s]
  6%|▌         | 14/250 [00:21<01:55,  2.04it/s]
  6%|▌         | 15/250 [00:22<02:08,  1.83it/s]
  6%|▋         | 16/250 [00:22<01:41,  2.31it/s]
  7%|▋         | 17/250 [00:22<01:22,  2.84it/s]
  7%|▋         | 18/250 [00:24<03:16,  1.18it/s]
  8%|▊         | 19/250 [00:24<02:25,  1.58it/s]
  8%|▊         | 20/250 [00:25<02:43,  1.41it/s]
  8%|▊         | 21/250 [00:25<02:11,  1.74it/s]
  9%|▉         | 22/250 [00:26<01:56,  1.96it/s]
  9%|▉         | 23/250 [00:26<01:33,  2.42it/s]
 10%|▉         | 24/250 [00:27<02:34,  1.47it/s]
 10%|█         | 25/250 [00:28<02:16,  1.64it/s]
 11%|█         | 27/250 [00:29<02:09,  1.73it/s]
 11%|█         | 28/250 [00:30<02:40,  1.39it/s]
 12%|█▏        | 30/250 [00:30<01:48,  2.04it/s]
 12%|█▏        | 31/250 [00:31<02:24,  1.51it/s]
 13%|█▎        | 32/250 [00:32<02:12,  1.65it/s]
 13%|█▎        | 33/250 [00:32<01:47,  2.02it/s]
 14%|█▎        | 34/250 [00:35<04:12,  1.17s/it]
 14%|█▍        | 35/250 [00:37<04:51,  1.36s/it]
 14%|█▍        | 36/250 [00:38<04:22,  1.23s/it]
 15%|█▍        | 37/250 [00:38<03:50,  1.08s/it]
 15%|█▌        | 38/250 [00:40<04:08,  1.17s/it]
 16%|█▌        | 39/250 [00:41<03:52,  1.10s/it]
 16%|█▌        | 40/250 [00:42<04:16,  1.22s/it]
 16%|█▋        | 41/250 [00:43<03:46,  1.08s/it]
 17%|█▋        | 42/250 [00:43<03:09,  1.10it/s]
 17%|█▋        | 43/250 [00:44<02:38,  1.31it/s]
 18%|█▊        | 44/250 [00:45<02:35,  1.33it/s]
 18%|█▊        | 45/250 [00:45<02:34,  1.32it/s]
 18%|█▊        | 46/250 [00:47<03:11,  1.06it/s]
 19%|█▉        | 47/250 [00:55<10:39,  3.15s/it]
 19%|█▉        | 48/250 [00:56<08:06,  2.41s/it]
 20%|█▉        | 49/250 [00:56<05:56,  1.78s/it]
 20%|██        | 50/250 [00:57<04:46,  1.43s/it]
 20%|██        | 51/250 [00:58<04:28,  1.35s/it]
 21%|██        | 52/250 [00:59<03:49,  1.16s/it]
 21%|██        | 53/250 [00:59<02:54,  1.13it/s]
 22%|██▏       | 55/250 [01:01<03:10,  1.02it/s]
 22%|██▏       | 56/250 [01:04<05:10,  1.60s/it]
 23%|██▎       | 57/250 [01:05<04:02,  1.26s/it]
 23%|██▎       | 58/250 [01:06<04:21,  1.36s/it]
 24%|██▎       | 59/250 [01:07<03:22,  1.06s/it]
 24%|██▍       | 60/250 [01:07<02:32,  1.25it/s]
 24%|██▍       | 61/250 [01:07<02:01,  1.56it/s]
 25%|██▍       | 62/250 [01:08<01:49,  1.71it/s]
 25%|██▌       | 63/250 [01:08<01:35,  1.95it/s]
 26%|██▌       | 65/250 [01:11<02:47,  1.10it/s]
 27%|██▋       | 67/250 [01:13<03:03,  1.00s/it]
 28%|██▊       | 69/250 [01:13<02:02,  1.48it/s]
 28%|██▊       | 70/250 [01:13<01:49,  1.64it/s]
 28%|██▊       | 71/250 [01:14<01:33,  1.91it/s]
 29%|██▉       | 73/250 [01:14<01:10,  2.50it/s]
 30%|██▉       | 74/250 [01:14<00:58,  2.99it/s]
 30%|███       | 75/250 [01:15<01:00,  2.89it/s]
 30%|███       | 76/250 [01:15<01:15,  2.29it/s]
 31%|███       | 77/250 [01:16<01:47,  1.60it/s]
 31%|███       | 78/250 [01:17<01:59,  1.44it/s]
 32%|███▏      | 79/250 [01:18<01:40,  1.71it/s]
 32%|███▏      | 80/250 [01:18<01:18,  2.15it/s]
 32%|███▏      | 81/250 [01:19<01:39,  1.70it/s]
 33%|███▎      | 82/250 [01:19<01:18,  2.15it/s]
 33%|███▎      | 83/250 [01:19<01:03,  2.64it/s]
 34%|███▎      | 84/250 [01:19<00:59,  2.77it/s]
 34%|███▍      | 85/250 [01:20<01:02,  2.63it/s]
 34%|███▍      | 86/250 [01:22<02:53,  1.06s/it]
 35%|███▍      | 87/250 [01:24<03:05,  1.14s/it]
 35%|███▌      | 88/250 [01:26<03:47,  1.41s/it]
 36%|███▌      | 89/250 [01:27<04:00,  1.49s/it]
 36%|███▌      | 90/250 [01:29<04:13,  1.59s/it]
 36%|███▋      | 91/250 [01:32<04:48,  1.81s/it]
 37%|███▋      | 93/250 [01:32<02:51,  1.09s/it]
 38%|███▊      | 94/250 [01:32<02:16,  1.14it/s]
 38%|███▊      | 95/250 [01:33<01:47,  1.44it/s]
 38%|███▊      | 96/250 [01:33<01:50,  1.40it/s]
 39%|███▉      | 98/250 [01:33<01:08,  2.23it/s]
 40%|███▉      | 99/250 [01:35<01:49,  1.38it/s]
 40%|████      | 100/250 [01:37<02:17,  1.09it/s]
 40%|████      | 101/250 [01:37<02:11,  1.14it/s]
 41%|████      | 102/250 [01:38<01:45,  1.40it/s]
 41%|████      | 103/250 [01:39<01:57,  1.25it/s]
 42%|████▏     | 104/250 [01:39<01:43,  1.42it/s]
 42%|████▏     | 105/250 [01:39<01:24,  1.71it/s]
 42%|████▏     | 106/250 [01:43<03:27,  1.44s/it]
 43%|████▎     | 107/250 [01:44<03:20,  1.40s/it]
 44%|████▎     | 109/250 [01:45<02:06,  1.11it/s]
 44%|████▍     | 110/250 [01:45<01:41,  1.38it/s]
 45%|████▍     | 112/250 [01:46<01:30,  1.52it/s]
 45%|████▌     | 113/250 [01:48<02:12,  1.03it/s]
 46%|████▌     | 114/250 [01:50<02:32,  1.12s/it]
 46%|████▌     | 115/250 [01:50<01:57,  1.15it/s]
 46%|████▋     | 116/250 [01:51<01:58,  1.13it/s]
 47%|████▋     | 117/250 [01:55<03:42,  1.68s/it]
 47%|████▋     | 118/250 [01:55<02:53,  1.31s/it]
 48%|████▊     | 119/250 [01:56<02:30,  1.15s/it]
 48%|████▊     | 121/250 [01:56<01:33,  1.38it/s]
 49%|████▉     | 122/250 [01:57<01:21,  1.57it/s]
 49%|████▉     | 123/250 [01:57<01:06,  1.90it/s]
 50%|████▉     | 124/250 [01:57<00:55,  2.28it/s]
 50%|█████     | 125/250 [01:57<00:46,  2.68it/s]
 51%|█████     | 127/250 [01:58<00:45,  2.70it/s]
 51%|█████     | 128/250 [02:01<02:09,  1.06s/it]
 52%|█████▏    | 129/250 [02:03<02:27,  1.22s/it]
 52%|█████▏    | 130/250 [02:03<02:02,  1.02s/it]
 52%|█████▏    | 131/250 [02:03<01:33,  1.27it/s]
 53%|█████▎    | 132/250 [02:05<01:44,  1.13it/s]
 53%|█████▎    | 133/250 [02:05<01:36,  1.21it/s]
 54%|█████▎    | 134/250 [02:07<01:52,  1.04it/s]
 54%|█████▍    | 135/250 [02:07<01:47,  1.07it/s]
 54%|█████▍    | 136/250 [02:08<01:23,  1.37it/s]
 55%|█████▍    | 137/250 [02:09<01:28,  1.27it/s]
 55%|█████▌    | 138/250 [02:09<01:13,  1.53it/s]
 56%|█████▌    | 139/250 [02:10<01:11,  1.55it/s]
 56%|█████▋    | 141/250 [02:10<00:41,  2.61it/s]
 57%|█████▋    | 142/250 [02:10<00:50,  2.14it/s]
 57%|█████▋    | 143/250 [02:11<00:49,  2.17it/s]
 58%|█████▊    | 144/250 [02:12<00:56,  1.87it/s]
 59%|█████▉    | 147/250 [02:12<00:29,  3.54it/s]
 59%|█████▉    | 148/250 [02:12<00:33,  3.01it/s]
 60%|█████▉    | 149/250 [02:12<00:28,  3.50it/s]
 61%|██████    | 152/250 [02:13<00:19,  5.02it/s]
 61%|██████    | 153/250 [02:13<00:19,  4.99it/s]
 62%|██████▏   | 154/250 [02:13<00:21,  4.52it/s]
 62%|██████▏   | 155/250 [02:14<00:30,  3.08it/s]
 62%|██████▏   | 156/250 [02:14<00:25,  3.66it/s]
 63%|██████▎   | 157/250 [02:15<00:45,  2.06it/s]
 63%|██████▎   | 158/250 [02:16<00:44,  2.04it/s]
 64%|██████▍   | 160/250 [02:17<00:47,  1.91it/s]
 64%|██████▍   | 161/250 [02:18<00:51,  1.72it/s]
 65%|██████▍   | 162/250 [02:18<00:58,  1.50it/s]
 66%|██████▌   | 164/250 [02:20<00:53,  1.62it/s]
 66%|██████▌   | 165/250 [02:20<00:47,  1.77it/s]
 66%|██████▋   | 166/250 [02:20<00:42,  1.96it/s]
 67%|██████▋   | 167/250 [02:23<01:21,  1.02it/s]
 68%|██████▊   | 169/250 [02:23<00:54,  1.49it/s]
 68%|██████▊   | 170/250 [02:24<01:02,  1.28it/s]
 69%|██████▉   | 172/250 [02:25<00:43,  1.80it/s]
 70%|██████▉   | 174/250 [02:27<00:57,  1.32it/s]
 70%|███████   | 175/250 [02:28<01:04,  1.16it/s]
 71%|███████   | 177/250 [02:30<01:03,  1.15it/s]
 71%|███████   | 178/250 [02:30<00:51,  1.38it/s]
 72%|███████▏  | 179/250 [02:30<00:42,  1.66it/s]
 72%|███████▏  | 180/250 [02:31<00:48,  1.44it/s]
 72%|███████▏  | 181/250 [02:31<00:37,  1.84it/s]
 73%|███████▎  | 182/250 [02:34<01:19,  1.17s/it]
 73%|███████▎  | 183/250 [02:36<01:20,  1.20s/it]
 74%|███████▍  | 185/250 [02:37<01:05,  1.01s/it]
 74%|███████▍  | 186/250 [02:37<00:51,  1.25it/s]
 75%|███████▍  | 187/250 [02:38<00:54,  1.16it/s]
 75%|███████▌  | 188/250 [02:39<00:53,  1.16it/s]
 76%|███████▌  | 189/250 [02:41<01:13,  1.20s/it]
 76%|███████▌  | 190/250 [02:43<01:30,  1.51s/it]
 76%|███████▋  | 191/250 [02:44<01:13,  1.25s/it]
 77%|███████▋  | 193/250 [02:46<00:59,  1.04s/it]
 78%|███████▊  | 194/250 [02:46<00:46,  1.22it/s]
 78%|███████▊  | 195/250 [02:46<00:36,  1.51it/s]
 78%|███████▊  | 196/250 [02:46<00:28,  1.89it/s]
 79%|███████▉  | 197/250 [02:46<00:22,  2.37it/s]
 79%|███████▉  | 198/250 [02:48<00:33,  1.55it/s]
 80%|███████▉  | 199/250 [02:49<00:42,  1.19it/s]
 80%|████████  | 200/250 [02:52<01:09,  1.39s/it]
 80%|████████  | 201/250 [02:52<01:00,  1.23s/it]
 81%|████████  | 202/250 [02:53<00:46,  1.04it/s]
 81%|████████  | 203/250 [02:53<00:38,  1.23it/s]
 82%|████████▏ | 205/250 [02:54<00:27,  1.64it/s]
 82%|████████▏ | 206/250 [02:54<00:26,  1.67it/s]
 83%|████████▎ | 207/250 [02:57<00:44,  1.04s/it]
 83%|████████▎ | 208/250 [02:57<00:37,  1.11it/s]
 84%|████████▎ | 209/250 [02:58<00:31,  1.28it/s]
 84%|████████▍ | 210/250 [02:58<00:25,  1.58it/s]
 84%|████████▍ | 211/250 [02:59<00:22,  1.72it/s]
 85%|████████▍ | 212/250 [02:59<00:19,  1.92it/s]
 85%|████████▌ | 213/250 [03:00<00:21,  1.70it/s]
 86%|████████▌ | 214/250 [03:03<00:45,  1.27s/it]
 86%|████████▋ | 216/250 [03:03<00:28,  1.18it/s]
 87%|████████▋ | 218/250 [03:04<00:20,  1.59it/s]
 88%|████████▊ | 219/250 [03:06<00:28,  1.07it/s]
 88%|████████▊ | 220/250 [03:06<00:25,  1.18it/s]
 88%|████████▊ | 221/250 [03:07<00:22,  1.27it/s]
 89%|████████▉ | 222/250 [03:07<00:18,  1.50it/s]
 89%|████████▉ | 223/250 [03:08<00:14,  1.84it/s]
 90%|████████▉ | 224/250 [03:08<00:11,  2.33it/s]
 90%|█████████ | 225/250 [03:09<00:16,  1.56it/s]
 90%|█████████ | 226/250 [03:09<00:14,  1.65it/s]
 91%|█████████ | 227/250 [03:10<00:14,  1.62it/s]
 92%|█████████▏| 231/250 [03:11<00:08,  2.23it/s]
 93%|█████████▎| 232/250 [03:12<00:07,  2.50it/s]
 93%|█████████▎| 233/250 [03:12<00:06,  2.52it/s]
 94%|█████████▎| 234/250 [03:13<00:07,  2.15it/s]
 94%|█████████▍| 235/250 [03:13<00:05,  2.55it/s]
 94%|█████████▍| 236/250 [03:13<00:06,  2.33it/s]
 95%|█████████▍| 237/250 [03:14<00:04,  2.69it/s]
 95%|█████████▌| 238/250 [03:14<00:03,  3.03it/s]
 96%|█████████▌| 239/250 [03:14<00:03,  3.59it/s]
 96%|█████████▌| 240/250 [03:14<00:02,  4.32it/s]
 96%|█████████▋| 241/250 [03:14<00:02,  4.07it/s]
 97%|█████████▋| 243/250 [03:15<00:01,  3.92it/s]
 98%|█████████▊| 244/250 [03:15<00:01,  3.87it/s]
 98%|█████████▊| 245/250 [03:18<00:04,  1.17it/s]
 98%|█████████▊| 246/250 [03:22<00:06,  1.67s/it]
 99%|█████████▉| 247/250 [03:24<00:05,  1.75s/it]
 99%|█████████▉| 248/250 [03:25<00:03,  1.68s/it]
100%|█████████▉| 249/250 [03:27<00:01,  1.74s/it]
100%|██████████| 250/250 [03:35<00:00,  3.67s/it]
100%|██████████| 250/250 [03:35<00:00,  1.16it/s]
2025-05-14 13:22:58,032 - modnet - INFO - Loss per individual: ind 0: 0.110 	ind 1: 0.113 	ind 2: 0.101 	ind 3: 0.109 	ind 4: 0.108 	ind 5: 0.107 	ind 6: 0.149 	ind 7: 0.109 	ind 8: 0.107 	ind 9: 0.119 	ind 10: 0.108 	ind 11: 0.113 	ind 12: 0.109 	ind 13: 0.121 	ind 14: 0.121 	ind 15: 0.107 	ind 16: 0.114 	ind 17: 0.103 	ind 18: 0.109 	ind 19: 0.107 	ind 20: 0.107 	ind 21: 0.109 	ind 22: 0.183 	ind 23: 0.110 	ind 24: 0.104 	ind 25: 0.107 	ind 26: 0.103 	ind 27: 0.136 	ind 28: 0.110 	ind 29: 0.119 	ind 30: 0.104 	ind 31: 0.108 	ind 32: 0.114 	ind 33: 0.106 	ind 34: 0.098 	ind 35: 0.106 	ind 36: 0.107 	ind 37: 0.221 	ind 38: 0.134 	ind 39: 0.103 	ind 40: 0.105 	ind 41: 0.117 	ind 42: 0.105 	ind 43: 0.098 	ind 44: 0.105 	ind 45: 0.107 	ind 46: 0.110 	ind 47: 0.107 	ind 48: 0.104 	ind 49: 0.105 	
2025-05-14 13:22:58,034 - modnet - INFO - Generation number 10

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:12<52:00, 12.53s/it]
  1%|          | 2/250 [00:12<21:50,  5.28s/it]
  1%|          | 3/250 [00:13<12:46,  3.10s/it]
  2%|▏         | 4/250 [00:13<07:57,  1.94s/it]
  3%|▎         | 7/250 [00:15<04:34,  1.13s/it]
  3%|▎         | 8/250 [00:15<03:38,  1.11it/s]
  4%|▎         | 9/250 [00:15<03:10,  1.26it/s]
  4%|▍         | 10/250 [00:16<02:47,  1.44it/s]
  4%|▍         | 11/250 [00:17<02:47,  1.43it/s]
  5%|▍         | 12/250 [00:17<02:20,  1.69it/s]
  5%|▌         | 13/250 [00:17<01:57,  2.02it/s]
  6%|▌         | 14/250 [00:17<01:43,  2.28it/s]
  6%|▋         | 16/250 [00:19<02:07,  1.84it/s]
  7%|▋         | 17/250 [00:19<02:12,  1.76it/s]
  7%|▋         | 18/250 [00:20<02:01,  1.91it/s]
  8%|▊         | 19/250 [00:20<01:56,  1.98it/s]
  8%|▊         | 20/250 [00:21<01:52,  2.05it/s]
  8%|▊         | 21/250 [00:22<02:54,  1.31it/s]
  9%|▉         | 22/250 [00:22<02:10,  1.75it/s]
  9%|▉         | 23/250 [00:23<01:54,  1.98it/s]
 10%|▉         | 24/250 [00:24<02:36,  1.45it/s]
 10%|█         | 25/250 [00:24<02:18,  1.62it/s]
 10%|█         | 26/250 [00:25<02:46,  1.34it/s]
 11%|█         | 27/250 [00:26<02:27,  1.52it/s]
 11%|█         | 28/250 [00:26<02:11,  1.68it/s]
 12%|█▏        | 29/250 [00:27<02:57,  1.24it/s]
 12%|█▏        | 30/250 [00:28<02:30,  1.46it/s]
 12%|█▏        | 31/250 [00:28<02:11,  1.66it/s]
 13%|█▎        | 32/250 [00:29<01:47,  2.02it/s]
 13%|█▎        | 33/250 [00:29<02:04,  1.74it/s]
 14%|█▍        | 35/250 [00:29<01:12,  2.97it/s]
 15%|█▍        | 37/250 [00:30<01:08,  3.10it/s]
 15%|█▌        | 38/250 [00:30<00:58,  3.65it/s]
 16%|█▌        | 39/250 [00:31<01:32,  2.27it/s]
 16%|█▌        | 40/250 [00:33<02:56,  1.19it/s]
 16%|█▋        | 41/250 [00:33<02:19,  1.50it/s]
 17%|█▋        | 42/250 [00:35<03:07,  1.11it/s]
 17%|█▋        | 43/250 [00:37<04:44,  1.38s/it]
 18%|█▊        | 44/250 [00:38<04:03,  1.18s/it]
 18%|█▊        | 45/250 [00:39<03:22,  1.01it/s]
 18%|█▊        | 46/250 [00:39<03:12,  1.06it/s]
 19%|█▉        | 47/250 [00:40<02:57,  1.14it/s]
 19%|█▉        | 48/250 [00:41<02:31,  1.33it/s]
 20%|██        | 50/250 [00:41<01:31,  2.18it/s]
 20%|██        | 51/250 [00:43<03:10,  1.05it/s]
 21%|██        | 52/250 [00:43<02:27,  1.35it/s]
 21%|██        | 53/250 [00:44<02:28,  1.32it/s]
 22%|██▏       | 55/250 [00:44<01:27,  2.22it/s]
 22%|██▏       | 56/250 [00:45<01:36,  2.01it/s]
 23%|██▎       | 57/250 [00:46<02:11,  1.47it/s]
 23%|██▎       | 58/250 [00:46<01:41,  1.90it/s]
 24%|██▎       | 59/250 [00:47<01:31,  2.10it/s]
 24%|██▍       | 60/250 [00:47<01:49,  1.73it/s]
 24%|██▍       | 61/250 [00:49<02:18,  1.37it/s]
 25%|██▍       | 62/250 [00:49<01:48,  1.73it/s]
 25%|██▌       | 63/250 [00:49<01:36,  1.94it/s]
 26%|██▌       | 64/250 [00:51<02:26,  1.27it/s]
 26%|██▋       | 66/250 [00:51<01:26,  2.14it/s]
 27%|██▋       | 67/250 [00:51<01:09,  2.64it/s]
 27%|██▋       | 68/250 [00:51<01:16,  2.37it/s]
 28%|██▊       | 69/250 [00:52<01:03,  2.84it/s]
 28%|██▊       | 70/250 [00:52<01:00,  2.98it/s]
 28%|██▊       | 71/250 [00:54<02:22,  1.26it/s]
 29%|██▉       | 72/250 [00:54<02:11,  1.35it/s]
 29%|██▉       | 73/250 [00:55<02:13,  1.33it/s]
 30%|██▉       | 74/250 [00:58<04:15,  1.45s/it]
 30%|███       | 75/250 [00:59<03:48,  1.31s/it]
 30%|███       | 76/250 [01:00<02:54,  1.00s/it]
 31%|███       | 77/250 [01:00<02:18,  1.25it/s]
 31%|███       | 78/250 [01:00<01:46,  1.62it/s]
 32%|███▏      | 79/250 [01:01<02:06,  1.35it/s]
 32%|███▏      | 80/250 [01:02<02:01,  1.40it/s]
 32%|███▏      | 81/250 [01:02<01:39,  1.70it/s]
 33%|███▎      | 82/250 [01:02<01:24,  1.99it/s]
 33%|███▎      | 83/250 [01:03<01:34,  1.77it/s]
 34%|███▍      | 85/250 [01:06<02:45,  1.00s/it]
 34%|███▍      | 86/250 [01:07<02:49,  1.04s/it]
 35%|███▍      | 87/250 [01:08<02:30,  1.08it/s]
 35%|███▌      | 88/250 [01:10<03:21,  1.24s/it]
 36%|███▌      | 89/250 [01:10<02:32,  1.06it/s]
 36%|███▌      | 90/250 [01:16<06:15,  2.34s/it]
 36%|███▋      | 91/250 [01:16<04:42,  1.78s/it]
 37%|███▋      | 92/250 [01:20<06:05,  2.31s/it]
 37%|███▋      | 93/250 [01:23<06:33,  2.50s/it]
 38%|███▊      | 94/250 [01:25<06:28,  2.49s/it]
 38%|███▊      | 96/250 [01:27<04:07,  1.61s/it]
 39%|███▉      | 97/250 [01:27<03:26,  1.35s/it]
 39%|███▉      | 98/250 [01:27<02:37,  1.04s/it]
 40%|███▉      | 99/250 [01:30<03:33,  1.41s/it]
 40%|████      | 100/250 [01:31<03:09,  1.27s/it]
 40%|████      | 101/250 [01:33<03:42,  1.50s/it]
 41%|████      | 102/250 [01:33<03:04,  1.25s/it]
 41%|████      | 103/250 [01:36<04:09,  1.70s/it]
 42%|████▏     | 105/250 [01:36<02:19,  1.04it/s]
 42%|████▏     | 106/250 [01:37<01:55,  1.24it/s]
 43%|████▎     | 107/250 [01:37<01:36,  1.48it/s]
 43%|████▎     | 108/250 [01:37<01:29,  1.59it/s]
 44%|████▎     | 109/250 [01:38<01:49,  1.29it/s]
 44%|████▍     | 110/250 [01:39<01:52,  1.25it/s]
 45%|████▍     | 112/250 [01:41<01:37,  1.42it/s]
 45%|████▌     | 113/250 [01:41<01:40,  1.37it/s]
 46%|████▌     | 114/250 [01:42<01:22,  1.64it/s]
 46%|████▌     | 115/250 [01:43<01:55,  1.17it/s]
 46%|████▋     | 116/250 [01:44<01:54,  1.17it/s]
 47%|████▋     | 118/250 [01:44<01:16,  1.74it/s]
 48%|████▊     | 119/250 [01:45<01:05,  2.01it/s]
 48%|████▊     | 120/250 [01:45<01:00,  2.15it/s]
 48%|████▊     | 121/250 [01:46<01:23,  1.55it/s]
 49%|████▉     | 122/250 [01:47<01:32,  1.39it/s]
 49%|████▉     | 123/250 [01:47<01:18,  1.61it/s]
 50%|████▉     | 124/250 [01:49<02:04,  1.01it/s]
 50%|█████     | 125/250 [01:50<01:46,  1.17it/s]
 50%|█████     | 126/250 [01:51<01:56,  1.06it/s]
 51%|█████     | 127/250 [01:52<01:58,  1.04it/s]
 51%|█████     | 128/250 [01:52<01:35,  1.27it/s]
 52%|█████▏    | 129/250 [01:53<01:19,  1.53it/s]
 52%|█████▏    | 130/250 [01:53<01:15,  1.59it/s]
 52%|█████▏    | 131/250 [01:54<01:04,  1.85it/s]
 53%|█████▎    | 133/250 [01:54<00:45,  2.58it/s]
 54%|█████▎    | 134/250 [01:54<00:40,  2.86it/s]
 54%|█████▍    | 135/250 [01:55<00:51,  2.22it/s]
 54%|█████▍    | 136/250 [01:55<00:44,  2.55it/s]
 55%|█████▌    | 138/250 [01:57<00:57,  1.95it/s]
 56%|█████▌    | 139/250 [01:57<00:55,  2.01it/s]
 56%|█████▌    | 140/250 [01:58<01:18,  1.41it/s]
 56%|█████▋    | 141/250 [01:59<01:04,  1.68it/s]
 57%|█████▋    | 142/250 [01:59<00:52,  2.06it/s]
 57%|█████▋    | 143/250 [01:59<00:48,  2.20it/s]
 58%|█████▊    | 144/250 [02:00<00:59,  1.77it/s]
 58%|█████▊    | 145/250 [02:00<00:48,  2.18it/s]
 59%|█████▉    | 147/250 [02:03<01:19,  1.30it/s]
 59%|█████▉    | 148/250 [02:04<01:22,  1.23it/s]
 60%|█████▉    | 149/250 [02:04<01:21,  1.24it/s]
 60%|██████    | 150/250 [02:07<01:57,  1.18s/it]
 60%|██████    | 151/250 [02:07<01:32,  1.07it/s]
 61%|██████    | 152/250 [02:07<01:11,  1.36it/s]
 61%|██████    | 153/250 [02:08<01:26,  1.13it/s]
 62%|██████▏   | 154/250 [02:09<01:11,  1.35it/s]
 62%|██████▏   | 155/250 [02:09<00:53,  1.76it/s]
 62%|██████▏   | 156/250 [02:10<01:03,  1.47it/s]
 63%|██████▎   | 158/250 [02:10<00:45,  2.01it/s]
 64%|██████▎   | 159/250 [02:11<00:40,  2.22it/s]
 64%|██████▍   | 161/250 [02:11<00:29,  3.00it/s]
 65%|██████▍   | 162/250 [02:11<00:27,  3.24it/s]
 65%|██████▌   | 163/250 [02:11<00:23,  3.66it/s]
 66%|██████▌   | 164/250 [02:12<00:28,  3.02it/s]
 66%|██████▋   | 166/250 [02:13<00:43,  1.95it/s]
 67%|██████▋   | 167/250 [02:14<00:53,  1.54it/s]
 68%|██████▊   | 169/250 [02:15<00:34,  2.35it/s]
 68%|██████▊   | 170/250 [02:15<00:28,  2.81it/s]
 68%|██████▊   | 171/250 [02:15<00:27,  2.88it/s]
 69%|██████▉   | 172/250 [02:15<00:28,  2.78it/s]
 70%|██████▉   | 174/250 [02:17<00:47,  1.61it/s]
 70%|███████   | 175/250 [02:18<00:49,  1.51it/s]
 70%|███████   | 176/250 [02:20<01:03,  1.16it/s]
 71%|███████   | 177/250 [02:20<00:56,  1.29it/s]
 71%|███████   | 178/250 [02:21<00:59,  1.21it/s]
 72%|███████▏  | 179/250 [02:22<00:55,  1.27it/s]
 72%|███████▏  | 180/250 [02:22<00:42,  1.65it/s]
 72%|███████▏  | 181/250 [02:24<01:02,  1.11it/s]
 73%|███████▎  | 182/250 [02:24<00:53,  1.27it/s]
 73%|███████▎  | 183/250 [02:25<00:43,  1.53it/s]
 74%|███████▎  | 184/250 [02:25<00:37,  1.76it/s]
 74%|███████▍  | 185/250 [02:25<00:32,  2.01it/s]
 74%|███████▍  | 186/250 [02:26<00:27,  2.30it/s]
 75%|███████▍  | 187/250 [02:26<00:21,  2.91it/s]
 75%|███████▌  | 188/250 [02:27<00:33,  1.84it/s]
 76%|███████▌  | 189/250 [02:27<00:26,  2.33it/s]
 76%|███████▌  | 190/250 [02:27<00:26,  2.27it/s]
 76%|███████▋  | 191/250 [02:28<00:26,  2.23it/s]
 77%|███████▋  | 192/250 [02:28<00:20,  2.88it/s]
 78%|███████▊  | 194/250 [02:29<00:19,  2.82it/s]
 78%|███████▊  | 195/250 [02:29<00:19,  2.82it/s]
 78%|███████▊  | 196/250 [02:29<00:16,  3.28it/s]
 79%|███████▉  | 198/250 [02:29<00:12,  4.27it/s]
 80%|███████▉  | 199/250 [02:31<00:29,  1.70it/s]
 80%|████████  | 201/250 [02:32<00:25,  1.95it/s]
 81%|████████  | 202/250 [02:32<00:22,  2.15it/s]
 81%|████████  | 203/250 [02:33<00:22,  2.13it/s]
 82%|████████▏ | 205/250 [02:33<00:14,  3.17it/s]
 82%|████████▏ | 206/250 [02:34<00:24,  1.79it/s]
 83%|████████▎ | 207/250 [02:35<00:29,  1.44it/s]
 83%|████████▎ | 208/250 [02:36<00:27,  1.51it/s]
 84%|████████▎ | 209/250 [02:36<00:22,  1.83it/s]
 84%|████████▍ | 210/250 [02:37<00:23,  1.68it/s]
 84%|████████▍ | 211/250 [02:37<00:21,  1.85it/s]
 85%|████████▍ | 212/250 [02:38<00:18,  2.08it/s]
 86%|████████▌ | 214/250 [02:38<00:11,  3.19it/s]
 86%|████████▌ | 215/250 [02:41<00:31,  1.12it/s]
 86%|████████▋ | 216/250 [02:41<00:25,  1.33it/s]
 87%|████████▋ | 217/250 [02:41<00:21,  1.51it/s]
 87%|████████▋ | 218/250 [02:43<00:30,  1.06it/s]
 88%|████████▊ | 219/250 [02:46<00:45,  1.46s/it]
 88%|████████▊ | 220/250 [02:46<00:32,  1.09s/it]
 88%|████████▊ | 221/250 [02:46<00:23,  1.24it/s]
 89%|████████▉ | 222/250 [02:46<00:19,  1.46it/s]
 89%|████████▉ | 223/250 [02:47<00:16,  1.62it/s]
 90%|████████▉ | 224/250 [02:47<00:15,  1.68it/s]
 90%|█████████ | 226/250 [02:48<00:13,  1.83it/s]
 91%|█████████ | 227/250 [02:49<00:10,  2.20it/s]
 91%|█████████ | 228/250 [02:49<00:08,  2.48it/s]
 92%|█████████▏| 229/250 [02:50<00:10,  2.01it/s]
 92%|█████████▏| 230/250 [02:50<00:11,  1.78it/s]
 92%|█████████▏| 231/250 [02:51<00:08,  2.13it/s]
 94%|█████████▎| 234/250 [02:52<00:08,  1.84it/s]
 94%|█████████▍| 235/250 [02:53<00:07,  1.94it/s]
 94%|█████████▍| 236/250 [02:53<00:05,  2.34it/s]
 95%|█████████▍| 237/250 [02:53<00:05,  2.54it/s]
 95%|█████████▌| 238/250 [02:54<00:07,  1.68it/s]
 96%|█████████▌| 239/250 [02:55<00:06,  1.65it/s]
 96%|█████████▌| 240/250 [02:55<00:04,  2.07it/s]
 96%|█████████▋| 241/250 [02:56<00:04,  2.07it/s]
 97%|█████████▋| 242/250 [02:56<00:04,  1.87it/s]
 98%|█████████▊| 244/250 [02:58<00:03,  1.58it/s]
 98%|█████████▊| 245/250 [03:00<00:04,  1.09it/s]
 98%|█████████▊| 246/250 [03:01<00:04,  1.01s/it]
 99%|█████████▉| 247/250 [03:02<00:03,  1.05s/it]
 99%|█████████▉| 248/250 [03:02<00:01,  1.18it/s]
100%|█████████▉| 249/250 [03:03<00:00,  1.47it/s]
100%|██████████| 250/250 [03:06<00:00,  1.52s/it]
100%|██████████| 250/250 [03:06<00:00,  1.34it/s]
2025-05-14 13:26:04,830 - modnet - INFO - Loss per individual: ind 0: 0.102 	ind 1: 0.112 	ind 2: 0.101 	ind 3: 0.108 	ind 4: 0.109 	ind 5: 0.101 	ind 6: 0.113 	ind 7: 0.104 	ind 8: 0.117 	ind 9: 0.105 	ind 10: 0.113 	ind 11: 0.102 	ind 12: 0.106 	ind 13: 0.108 	ind 14: 0.153 	ind 15: 0.116 	ind 16: 0.117 	ind 17: 0.112 	ind 18: 0.104 	ind 19: 0.108 	ind 20: 0.103 	ind 21: 0.108 	ind 22: 0.106 	ind 23: 0.117 	ind 24: 0.109 	ind 25: 0.111 	ind 26: 0.111 	ind 27: 0.106 	ind 28: 0.110 	ind 29: 0.106 	ind 30: 0.099 	ind 31: 0.128 	ind 32: 0.117 	ind 33: 0.101 	ind 34: 0.105 	ind 35: 0.134 	ind 36: 0.102 	ind 37: 0.106 	ind 38: 0.113 	ind 39: 0.114 	ind 40: 0.125 	ind 41: 0.124 	ind 42: 0.124 	ind 43: 0.122 	ind 44: 0.140 	ind 45: 0.122 	ind 46: 0.110 	ind 47: 0.135 	ind 48: 0.102 	ind 49: 0.254 	
2025-05-14 13:26:04,832 - modnet - INFO - Generation number 11

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:09<40:23,  9.73s/it]
  1%|          | 2/250 [00:11<19:48,  4.79s/it]
  1%|          | 3/250 [00:12<13:25,  3.26s/it]
  2%|▏         | 4/250 [00:14<10:57,  2.67s/it]
  2%|▏         | 5/250 [00:14<07:34,  1.86s/it]
  3%|▎         | 7/250 [00:14<03:53,  1.04it/s]
  4%|▎         | 9/250 [00:16<03:41,  1.09it/s]
  4%|▍         | 10/250 [00:16<03:09,  1.26it/s]
  4%|▍         | 11/250 [00:18<03:40,  1.08it/s]
  5%|▍         | 12/250 [00:18<02:49,  1.41it/s]
  6%|▌         | 14/250 [00:18<01:48,  2.17it/s]
  6%|▌         | 15/250 [00:19<01:53,  2.07it/s]
  7%|▋         | 17/250 [00:21<02:34,  1.51it/s]
  7%|▋         | 18/250 [00:21<02:23,  1.62it/s]
  8%|▊         | 20/250 [00:21<01:32,  2.49it/s]
  8%|▊         | 21/250 [00:23<02:41,  1.42it/s]
  9%|▉         | 22/250 [00:23<02:11,  1.73it/s]
  9%|▉         | 23/250 [00:24<02:38,  1.43it/s]
 10%|▉         | 24/250 [00:24<02:05,  1.80it/s]
 10%|█         | 25/250 [00:25<02:12,  1.70it/s]
 10%|█         | 26/250 [00:25<02:00,  1.86it/s]
 11%|█         | 27/250 [00:28<04:16,  1.15s/it]
 11%|█         | 28/250 [00:30<05:21,  1.45s/it]
 12%|█▏        | 29/250 [00:31<04:36,  1.25s/it]
 12%|█▏        | 30/250 [00:32<04:35,  1.25s/it]
 12%|█▏        | 31/250 [00:35<06:20,  1.74s/it]
 13%|█▎        | 32/250 [00:35<04:45,  1.31s/it]
 13%|█▎        | 33/250 [00:36<04:14,  1.17s/it]
 14%|█▎        | 34/250 [00:37<03:30,  1.03it/s]
 14%|█▍        | 35/250 [00:37<02:40,  1.34it/s]
 14%|█▍        | 36/250 [00:38<02:43,  1.31it/s]
 15%|█▌        | 38/250 [00:38<01:45,  2.01it/s]
 16%|█▌        | 39/250 [00:38<01:32,  2.27it/s]
 16%|█▌        | 40/250 [00:39<01:53,  1.85it/s]
 17%|█▋        | 42/250 [00:41<02:01,  1.71it/s]
 17%|█▋        | 43/250 [00:41<01:55,  1.80it/s]
 18%|█▊        | 44/250 [00:42<02:06,  1.63it/s]
 18%|█▊        | 45/250 [00:42<01:40,  2.03it/s]
 18%|█▊        | 46/250 [00:43<02:09,  1.57it/s]
 19%|█▉        | 47/250 [00:44<02:27,  1.37it/s]
 19%|█▉        | 48/250 [00:45<03:11,  1.05it/s]
 20%|█▉        | 49/250 [00:46<02:53,  1.16it/s]
 20%|██        | 50/250 [00:48<03:37,  1.09s/it]
 21%|██        | 53/250 [00:48<01:39,  1.98it/s]
 22%|██▏       | 55/250 [00:48<01:20,  2.41it/s]
 22%|██▏       | 56/250 [00:49<01:13,  2.63it/s]
 23%|██▎       | 57/250 [00:49<01:27,  2.20it/s]
 23%|██▎       | 58/250 [00:49<01:12,  2.65it/s]
 24%|██▎       | 59/250 [00:51<01:52,  1.69it/s]
 24%|██▍       | 60/250 [00:51<01:32,  2.06it/s]
 24%|██▍       | 61/250 [00:52<01:46,  1.77it/s]
 25%|██▍       | 62/250 [00:52<01:23,  2.26it/s]
 25%|██▌       | 63/250 [00:52<01:28,  2.11it/s]
 26%|██▌       | 64/250 [00:54<02:54,  1.07it/s]
 26%|██▌       | 65/250 [00:55<02:24,  1.28it/s]
 26%|██▋       | 66/250 [00:55<01:49,  1.68it/s]
 27%|██▋       | 67/250 [00:56<01:49,  1.67it/s]
 27%|██▋       | 68/250 [00:57<02:25,  1.25it/s]
 28%|██▊       | 70/250 [00:58<02:02,  1.47it/s]
 28%|██▊       | 71/250 [00:58<01:47,  1.66it/s]
 29%|██▉       | 73/250 [00:58<01:06,  2.66it/s]
 30%|██▉       | 74/250 [00:59<01:03,  2.76it/s]
 30%|███       | 75/250 [01:01<02:36,  1.12it/s]
 30%|███       | 76/250 [01:02<02:36,  1.11it/s]
 31%|███       | 77/250 [01:02<01:59,  1.44it/s]
 31%|███       | 78/250 [01:03<01:44,  1.65it/s]
 32%|███▏      | 79/250 [01:04<02:23,  1.19it/s]
 32%|███▏      | 80/250 [01:05<02:08,  1.33it/s]
 33%|███▎      | 82/250 [01:05<01:19,  2.11it/s]
 33%|███▎      | 83/250 [01:06<01:39,  1.68it/s]
 34%|███▎      | 84/250 [01:06<01:23,  1.99it/s]
 34%|███▍      | 85/250 [01:07<01:46,  1.55it/s]
 34%|███▍      | 86/250 [01:07<01:26,  1.90it/s]
 35%|███▍      | 87/250 [01:08<01:48,  1.50it/s]
 35%|███▌      | 88/250 [01:09<01:29,  1.81it/s]
 36%|███▌      | 89/250 [01:09<01:22,  1.95it/s]
 36%|███▋      | 91/250 [01:09<00:54,  2.92it/s]
 37%|███▋      | 92/250 [01:10<01:13,  2.15it/s]
 37%|███▋      | 93/250 [01:11<01:12,  2.18it/s]
 38%|███▊      | 94/250 [01:11<01:02,  2.51it/s]
 38%|███▊      | 95/250 [01:12<01:21,  1.89it/s]
 38%|███▊      | 96/250 [01:12<01:11,  2.14it/s]
 39%|███▉      | 97/250 [01:12<00:56,  2.71it/s]
 39%|███▉      | 98/250 [01:12<00:52,  2.88it/s]
 40%|████      | 100/250 [01:13<01:03,  2.38it/s]
 40%|████      | 101/250 [01:14<01:13,  2.03it/s]
 41%|████      | 102/250 [01:17<02:26,  1.01it/s]
 41%|████      | 103/250 [01:18<02:42,  1.10s/it]
 42%|████▏     | 104/250 [01:19<02:33,  1.05s/it]
 42%|████▏     | 106/250 [01:22<03:12,  1.34s/it]
 43%|████▎     | 107/250 [01:23<02:38,  1.10s/it]
 43%|████▎     | 108/250 [01:24<02:49,  1.19s/it]
 44%|████▎     | 109/250 [01:25<02:20,  1.00it/s]
 44%|████▍     | 111/250 [01:25<01:23,  1.67it/s]
 45%|████▌     | 113/250 [01:25<00:53,  2.57it/s]
 46%|████▌     | 114/250 [01:25<00:47,  2.89it/s]
 46%|████▌     | 115/250 [01:25<00:47,  2.85it/s]
 46%|████▋     | 116/250 [01:26<00:39,  3.41it/s]
 47%|████▋     | 117/250 [01:26<00:52,  2.54it/s]
 47%|████▋     | 118/250 [01:27<00:59,  2.23it/s]
 48%|████▊     | 119/250 [01:27<00:47,  2.78it/s]
 48%|████▊     | 120/250 [01:28<01:23,  1.55it/s]
 48%|████▊     | 121/250 [01:28<01:04,  2.01it/s]
 49%|████▉     | 122/250 [01:29<01:09,  1.84it/s]
 50%|████▉     | 124/250 [01:29<00:40,  3.10it/s]
 50%|█████     | 125/250 [01:30<00:49,  2.52it/s]
 50%|█████     | 126/250 [01:30<00:54,  2.29it/s]
 51%|█████     | 127/250 [01:31<01:05,  1.87it/s]
 51%|█████     | 128/250 [01:32<01:31,  1.33it/s]
 52%|█████▏    | 129/250 [01:33<01:10,  1.71it/s]
 52%|█████▏    | 131/250 [01:33<00:44,  2.67it/s]
 53%|█████▎    | 132/250 [01:34<01:02,  1.88it/s]
 53%|█████▎    | 133/250 [01:36<01:44,  1.12it/s]
 54%|█████▎    | 134/250 [01:36<01:26,  1.34it/s]
 54%|█████▍    | 135/250 [01:38<02:10,  1.14s/it]
 54%|█████▍    | 136/250 [01:40<02:15,  1.19s/it]
 55%|█████▍    | 137/250 [01:41<02:03,  1.10s/it]
 55%|█████▌    | 138/250 [01:41<01:46,  1.05it/s]
 56%|█████▌    | 139/250 [01:43<02:00,  1.08s/it]
 56%|█████▌    | 140/250 [01:43<01:33,  1.18it/s]
 56%|█████▋    | 141/250 [01:44<01:30,  1.21it/s]
 57%|█████▋    | 142/250 [01:44<01:06,  1.63it/s]
 57%|█████▋    | 143/250 [01:44<00:50,  2.14it/s]
 58%|█████▊    | 144/250 [01:44<00:38,  2.76it/s]
 58%|█████▊    | 145/250 [01:44<00:30,  3.44it/s]
 58%|█████▊    | 146/250 [01:44<00:25,  4.05it/s]
 59%|█████▉    | 147/250 [01:44<00:24,  4.18it/s]
 59%|█████▉    | 148/250 [01:45<00:36,  2.83it/s]
 60%|█████▉    | 149/250 [01:46<00:38,  2.63it/s]
 60%|██████    | 151/250 [01:46<00:42,  2.32it/s]
 61%|██████    | 152/250 [01:47<00:36,  2.70it/s]
 61%|██████    | 153/250 [01:50<01:44,  1.07s/it]
 62%|██████▏   | 155/250 [01:50<01:02,  1.53it/s]
 63%|██████▎   | 157/250 [01:50<00:39,  2.33it/s]
 63%|██████▎   | 158/250 [01:50<00:34,  2.66it/s]
 64%|██████▍   | 160/250 [01:50<00:24,  3.63it/s]
 64%|██████▍   | 161/250 [01:51<00:22,  3.94it/s]
 65%|██████▍   | 162/250 [01:51<00:20,  4.29it/s]
 65%|██████▌   | 163/250 [01:52<00:31,  2.77it/s]
 66%|██████▌   | 164/250 [01:52<00:37,  2.30it/s]
 66%|██████▌   | 165/250 [01:53<00:38,  2.20it/s]
 66%|██████▋   | 166/250 [01:53<00:42,  1.99it/s]
 67%|██████▋   | 168/250 [01:54<00:35,  2.31it/s]
 68%|██████▊   | 169/250 [01:54<00:36,  2.23it/s]
 68%|██████▊   | 171/250 [01:55<00:28,  2.81it/s]
 69%|██████▉   | 172/250 [01:56<00:39,  1.97it/s]
 69%|██████▉   | 173/250 [01:57<00:44,  1.75it/s]
 70%|██████▉   | 174/250 [01:57<00:34,  2.20it/s]
 70%|███████   | 175/250 [01:57<00:34,  2.20it/s]
 70%|███████   | 176/250 [01:58<00:29,  2.50it/s]
 71%|███████   | 178/250 [01:59<00:37,  1.93it/s]
 72%|███████▏  | 179/250 [02:00<00:47,  1.49it/s]
 72%|███████▏  | 180/250 [02:01<00:45,  1.56it/s]
 72%|███████▏  | 181/250 [02:01<00:42,  1.61it/s]
 73%|███████▎  | 182/250 [02:02<00:53,  1.27it/s]
 73%|███████▎  | 183/250 [02:04<01:05,  1.02it/s]
 74%|███████▎  | 184/250 [02:05<01:00,  1.10it/s]
 74%|███████▍  | 185/250 [02:05<00:50,  1.29it/s]
 74%|███████▍  | 186/250 [02:07<01:14,  1.16s/it]
 75%|███████▌  | 188/250 [02:09<01:01,  1.00it/s]
 76%|███████▌  | 189/250 [02:09<00:56,  1.08it/s]
 76%|███████▌  | 190/250 [02:10<00:45,  1.32it/s]
 76%|███████▋  | 191/250 [02:11<00:53,  1.11it/s]
 77%|███████▋  | 192/250 [02:13<01:15,  1.30s/it]
 77%|███████▋  | 193/250 [02:14<01:00,  1.07s/it]
 78%|███████▊  | 194/250 [02:15<00:58,  1.05s/it]
 78%|███████▊  | 195/250 [02:15<00:47,  1.16it/s]
 78%|███████▊  | 196/250 [02:21<02:07,  2.37s/it]
 79%|███████▉  | 197/250 [02:23<01:51,  2.11s/it]
 79%|███████▉  | 198/250 [02:25<01:55,  2.21s/it]
 80%|███████▉  | 199/250 [02:26<01:26,  1.70s/it]
 80%|████████  | 200/250 [02:26<01:06,  1.33s/it]
 80%|████████  | 201/250 [02:28<01:07,  1.38s/it]
 81%|████████  | 202/250 [02:30<01:25,  1.79s/it]
 82%|████████▏ | 204/250 [02:31<00:49,  1.08s/it]
 82%|████████▏ | 205/250 [02:34<01:11,  1.59s/it]
 82%|████████▏ | 206/250 [02:34<00:54,  1.25s/it]
 83%|████████▎ | 207/250 [02:35<00:44,  1.03s/it]
 83%|████████▎ | 208/250 [02:35<00:36,  1.14it/s]
 84%|████████▎ | 209/250 [02:36<00:35,  1.16it/s]
 84%|████████▍ | 210/250 [02:40<01:07,  1.68s/it]
 84%|████████▍ | 211/250 [02:40<00:51,  1.32s/it]
 85%|████████▍ | 212/250 [02:40<00:36,  1.03it/s]
 85%|████████▌ | 213/250 [02:40<00:27,  1.36it/s]
 86%|████████▌ | 214/250 [02:43<00:45,  1.27s/it]
 86%|████████▌ | 215/250 [02:43<00:32,  1.08it/s]
 86%|████████▋ | 216/250 [02:45<00:38,  1.15s/it]
 87%|████████▋ | 217/250 [02:48<00:55,  1.67s/it]
 87%|████████▋ | 218/250 [02:49<00:45,  1.41s/it]
 88%|████████▊ | 219/250 [02:50<00:46,  1.49s/it]
 88%|████████▊ | 220/250 [02:50<00:32,  1.08s/it]
 88%|████████▊ | 221/250 [02:52<00:39,  1.37s/it]
 89%|████████▉ | 222/250 [02:56<00:57,  2.04s/it]
 89%|████████▉ | 223/250 [02:57<00:44,  1.66s/it]
 90%|████████▉ | 224/250 [02:59<00:44,  1.71s/it]
 90%|█████████ | 225/250 [02:59<00:31,  1.27s/it]
 90%|█████████ | 226/250 [02:59<00:25,  1.06s/it]
 91%|█████████ | 227/250 [03:00<00:18,  1.26it/s]
 91%|█████████ | 228/250 [03:01<00:20,  1.08it/s]
 92%|█████████▏| 230/250 [03:01<00:11,  1.82it/s]
 92%|█████████▏| 231/250 [03:01<00:08,  2.19it/s]
 93%|█████████▎| 232/250 [03:02<00:07,  2.26it/s]
 94%|█████████▎| 234/250 [03:03<00:09,  1.69it/s]
 94%|█████████▍| 235/250 [03:04<00:09,  1.56it/s]
 94%|█████████▍| 236/250 [03:05<00:09,  1.52it/s]
 95%|█████████▍| 237/250 [03:05<00:06,  1.95it/s]
 95%|█████████▌| 238/250 [03:05<00:05,  2.30it/s]
 96%|█████████▌| 239/250 [03:06<00:05,  2.04it/s]
 96%|█████████▌| 240/250 [03:06<00:05,  1.89it/s]
 96%|█████████▋| 241/250 [03:07<00:04,  2.24it/s]
 97%|█████████▋| 242/250 [03:08<00:05,  1.46it/s]
 97%|█████████▋| 243/250 [03:10<00:07,  1.10s/it]
 98%|█████████▊| 244/250 [03:11<00:06,  1.06s/it]
 98%|█████████▊| 245/250 [03:12<00:05,  1.20s/it]
 98%|█████████▊| 246/250 [03:14<00:05,  1.28s/it]
 99%|█████████▉| 247/250 [03:14<00:02,  1.04it/s]
 99%|█████████▉| 248/250 [03:15<00:01,  1.10it/s]
100%|█████████▉| 249/250 [03:15<00:00,  1.42it/s]
100%|██████████| 250/250 [03:19<00:00,  1.79s/it]
100%|██████████| 250/250 [03:19<00:00,  1.25it/s]
2025-05-14 13:29:24,802 - modnet - INFO - Loss per individual: ind 0: 0.100 	ind 1: 0.115 	ind 2: 0.112 	ind 3: 0.116 	ind 4: 0.107 	ind 5: 0.107 	ind 6: 0.106 	ind 7: 0.107 	ind 8: 0.107 	ind 9: 0.109 	ind 10: 0.157 	ind 11: 0.345 	ind 12: 0.109 	ind 13: 0.106 	ind 14: 0.114 	ind 15: 0.122 	ind 16: 0.104 	ind 17: 0.125 	ind 18: 0.109 	ind 19: 0.105 	ind 20: 0.102 	ind 21: 0.108 	ind 22: 0.107 	ind 23: 0.105 	ind 24: 0.109 	ind 25: 0.105 	ind 26: 0.123 	ind 27: 0.108 	ind 28: 0.109 	ind 29: 0.100 	ind 30: 0.110 	ind 31: 0.113 	ind 32: 0.111 	ind 33: 0.104 	ind 34: 0.124 	ind 35: 0.112 	ind 36: 0.112 	ind 37: 0.152 	ind 38: 0.106 	ind 39: 0.107 	ind 40: 0.112 	ind 41: 0.106 	ind 42: 0.095 	ind 43: 0.105 	ind 44: 0.105 	ind 45: 0.123 	ind 46: 0.103 	ind 47: 0.107 	ind 48: 0.113 	ind 49: 0.106 	
2025-05-14 13:29:24,804 - modnet - INFO - Generation number 12

  0%|          | 0/250 [00:00<?, ?it/s]
  0%|          | 1/250 [00:13<55:18, 13.33s/it]
  1%|          | 2/250 [00:14<25:50,  6.25s/it]
  1%|          | 3/250 [00:14<14:32,  3.53s/it]
  2%|▏         | 4/250 [00:17<13:29,  3.29s/it]
  2%|▏         | 5/250 [00:19<11:40,  2.86s/it]
  2%|▏         | 6/250 [00:20<07:59,  1.97s/it]
  3%|▎         | 7/250 [00:20<06:13,  1.54s/it]
  3%|▎         | 8/250 [00:21<05:19,  1.32s/it]
  4%|▎         | 9/250 [00:23<06:27,  1.61s/it]
  4%|▍         | 10/250 [00:24<04:53,  1.22s/it]
  4%|▍         | 11/250 [00:24<03:35,  1.11it/s]
  5%|▍         | 12/250 [00:26<04:24,  1.11s/it]
  5%|▌         | 13/250 [00:26<03:40,  1.08it/s]
  6%|▌         | 14/250 [00:27<03:18,  1.19it/s]
  6%|▌         | 15/250 [00:27<02:58,  1.32it/s]
  6%|▋         | 16/250 [00:27<02:16,  1.72it/s]
  7%|▋         | 18/250 [00:29<03:03,  1.27it/s]
  8%|▊         | 19/250 [00:31<03:36,  1.07it/s]
  8%|▊         | 20/250 [00:31<03:08,  1.22it/s]
  8%|▊         | 21/250 [00:35<06:22,  1.67s/it]
  9%|▉         | 22/250 [00:38<07:04,  1.86s/it]
 10%|▉         | 24/250 [00:38<04:07,  1.10s/it]
 10%|█         | 25/250 [00:39<04:01,  1.07s/it]
 11%|█         | 27/250 [00:42<04:56,  1.33s/it]
 11%|█         | 28/250 [00:44<05:10,  1.40s/it]
 12%|█▏        | 29/250 [00:45<05:15,  1.43s/it]
 12%|█▏        | 31/250 [00:46<03:44,  1.02s/it]
 13%|█▎        | 32/250 [00:48<04:36,  1.27s/it]
 13%|█▎        | 33/250 [00:51<05:36,  1.55s/it]
 14%|█▎        | 34/250 [00:51<04:13,  1.17s/it]
 14%|█▍        | 35/250 [00:52<03:49,  1.07s/it]
 14%|█▍        | 36/250 [00:52<03:13,  1.10it/s]
 15%|█▍        | 37/250 [00:53<02:44,  1.30it/s]
 15%|█▌        | 38/250 [00:53<02:22,  1.49it/s]
 16%|█▌        | 40/250 [00:53<01:21,  2.56it/s]
 16%|█▋        | 41/250 [00:54<01:44,  2.00it/s]
 17%|█▋        | 42/250 [00:55<01:45,  1.97it/s]
 17%|█▋        | 43/250 [00:55<01:25,  2.41it/s]
 18%|█▊        | 44/250 [00:55<01:15,  2.73it/s]
 18%|█▊        | 45/250 [00:58<03:25,  1.00s/it]
 19%|█▉        | 47/250 [00:58<01:59,  1.70it/s]
 20%|█▉        | 49/250 [00:59<01:50,  1.81it/s]
 20%|██        | 51/250 [01:00<01:58,  1.68it/s]
 21%|██        | 52/250 [01:01<01:51,  1.77it/s]
 21%|██        | 53/250 [01:01<02:03,  1.60it/s]
 22%|██▏       | 55/250 [01:02<01:35,  2.03it/s]
 23%|██▎       | 57/250 [01:02<01:06,  2.92it/s]
 23%|██▎       | 58/250 [01:04<01:54,  1.68it/s]
 24%|██▎       | 59/250 [01:04<02:01,  1.57it/s]
 24%|██▍       | 60/250 [01:05<01:45,  1.79it/s]
 24%|██▍       | 61/250 [01:05<01:47,  1.75it/s]
 25%|██▌       | 63/250 [01:07<02:10,  1.43it/s]
 26%|██▌       | 64/250 [01:08<02:20,  1.33it/s]
 26%|██▌       | 65/250 [01:09<02:05,  1.47it/s]
 26%|██▋       | 66/250 [01:09<01:43,  1.78it/s]
 27%|██▋       | 68/250 [01:10<01:50,  1.64it/s]
 28%|██▊       | 69/250 [01:11<02:15,  1.33it/s]
 28%|██▊       | 70/250 [01:12<02:32,  1.18it/s]
 28%|██▊       | 71/250 [01:13<02:01,  1.47it/s]
 29%|██▉       | 72/250 [01:13<01:54,  1.56it/s]
 29%|██▉       | 73/250 [01:14<01:49,  1.62it/s]
 30%|██▉       | 74/250 [01:14<01:25,  2.05it/s]
 30%|███       | 75/250 [01:15<02:11,  1.33it/s]
 30%|███       | 76/250 [01:16<01:54,  1.52it/s]
 31%|███       | 77/250 [01:17<02:15,  1.27it/s]
 31%|███       | 78/250 [01:17<01:45,  1.64it/s]
 32%|███▏      | 80/250 [01:17<01:05,  2.60it/s]
 32%|███▏      | 81/250 [01:18<01:00,  2.79it/s]
 33%|███▎      | 83/250 [01:18<01:05,  2.53it/s]
 34%|███▍      | 85/250 [01:19<00:57,  2.88it/s]
 34%|███▍      | 86/250 [01:20<01:04,  2.54it/s]
 35%|███▌      | 88/250 [01:20<00:53,  3.05it/s]
 36%|███▌      | 89/250 [01:21<01:02,  2.59it/s]
 36%|███▌      | 90/250 [01:21<01:02,  2.57it/s]
 36%|███▋      | 91/250 [01:22<01:34,  1.68it/s]
 37%|███▋      | 92/250 [01:23<01:44,  1.52it/s]
 37%|███▋      | 93/250 [01:24<01:38,  1.59it/s]
 38%|███▊      | 94/250 [01:26<02:38,  1.02s/it]
 38%|███▊      | 95/250 [01:27<02:34,  1.00it/s]
 39%|███▉      | 97/250 [01:27<01:29,  1.72it/s]
 39%|███▉      | 98/250 [01:27<01:20,  1.89it/s]
 40%|███▉      | 99/250 [01:27<01:13,  2.05it/s]
 40%|████      | 100/250 [01:28<01:18,  1.90it/s]
 40%|████      | 101/250 [01:29<01:50,  1.35it/s]
 41%|████      | 103/250 [01:30<01:11,  2.06it/s]
 42%|████▏     | 104/250 [01:30<01:07,  2.17it/s]
 42%|████▏     | 105/250 [01:30<00:59,  2.45it/s]
 42%|████▏     | 106/250 [01:31<00:55,  2.60it/s]
 43%|████▎     | 108/250 [01:31<00:56,  2.52it/s]
 44%|████▎     | 109/250 [01:32<00:52,  2.69it/s]
 44%|████▍     | 110/250 [01:32<00:52,  2.69it/s]
 44%|████▍     | 111/250 [01:33<01:15,  1.83it/s]
 45%|████▍     | 112/250 [01:33<00:58,  2.36it/s]
 45%|████▌     | 113/250 [01:34<00:56,  2.42it/s]
 46%|████▌     | 114/250 [01:34<01:09,  1.96it/s]
 46%|████▌     | 115/250 [01:36<02:01,  1.11it/s]
 46%|████▋     | 116/250 [01:36<01:35,  1.41it/s]
 47%|████▋     | 117/250 [01:37<01:12,  1.83it/s]
 47%|████▋     | 118/250 [01:38<01:59,  1.11it/s]
 48%|████▊     | 120/250 [01:39<01:31,  1.43it/s]
 48%|████▊     | 121/250 [01:40<01:22,  1.56it/s]
 49%|████▉     | 122/250 [01:41<01:51,  1.15it/s]
 49%|████▉     | 123/250 [01:43<02:05,  1.01it/s]
 50%|████▉     | 124/250 [01:43<01:56,  1.08it/s]
 50%|█████     | 125/250 [01:43<01:26,  1.44it/s]
 50%|█████     | 126/250 [01:45<01:51,  1.11it/s]
 51%|█████     | 127/250 [01:46<01:43,  1.18it/s]
 51%|█████     | 128/250 [01:46<01:27,  1.40it/s]
 52%|█████▏    | 129/250 [01:48<02:01,  1.00s/it]
 52%|█████▏    | 130/250 [01:48<01:38,  1.21it/s]
 52%|█████▏    | 131/250 [01:50<02:02,  1.03s/it]
 53%|█████▎    | 132/250 [01:50<01:48,  1.09it/s]
 53%|█████▎    | 133/250 [01:51<01:39,  1.17it/s]
 54%|█████▎    | 134/250 [01:56<03:59,  2.07s/it]
 54%|█████▍    | 135/250 [01:56<02:54,  1.52s/it]
 54%|█████▍    | 136/250 [01:56<02:04,  1.10s/it]
 55%|█████▍    | 137/250 [01:57<01:39,  1.14it/s]
 55%|█████▌    | 138/250 [01:57<01:16,  1.47it/s]
 56%|█████▌    | 139/250 [01:57<01:06,  1.67it/s]
 56%|█████▌    | 140/250 [01:57<00:53,  2.06it/s]
 57%|█████▋    | 143/250 [01:58<00:25,  4.13it/s]
 58%|█████▊    | 144/250 [01:58<00:24,  4.26it/s]
 58%|█████▊    | 145/250 [01:58<00:26,  3.98it/s]
 58%|█████▊    | 146/250 [02:00<01:12,  1.43it/s]
 59%|█████▉    | 147/250 [02:01<01:14,  1.38it/s]
 59%|█████▉    | 148/250 [02:02<01:16,  1.34it/s]
 60%|█████▉    | 149/250 [02:03<01:39,  1.02it/s]
 60%|██████    | 150/250 [02:04<01:20,  1.24it/s]
 60%|██████    | 151/250 [02:04<01:02,  1.58it/s]
 61%|██████    | 152/250 [02:05<01:09,  1.42it/s]
 61%|██████    | 153/250 [02:05<00:53,  1.81it/s]
 62%|██████▏   | 154/250 [02:05<00:42,  2.28it/s]
 62%|██████▏   | 155/250 [02:06<01:02,  1.52it/s]
 63%|██████▎   | 157/250 [02:07<00:41,  2.24it/s]
 64%|██████▎   | 159/250 [02:07<00:29,  3.13it/s]
 64%|██████▍   | 160/250 [02:07<00:26,  3.38it/s]
 64%|██████▍   | 161/250 [02:07<00:23,  3.73it/s]
 65%|██████▍   | 162/250 [02:08<00:41,  2.14it/s]
 65%|██████▌   | 163/250 [02:09<00:37,  2.32it/s]
 66%|██████▌   | 164/250 [02:09<00:35,  2.40it/s]
 66%|██████▌   | 165/250 [02:10<00:34,  2.44it/s]
 66%|██████▋   | 166/250 [02:10<00:28,  2.94it/s]
 67%|██████▋   | 167/250 [02:11<01:02,  1.32it/s]
 68%|██████▊   | 169/250 [02:14<01:12,  1.12it/s]
 68%|██████▊   | 170/250 [02:14<00:58,  1.36it/s]
 68%|██████▊   | 171/250 [02:15<01:17,  1.03it/s]
 69%|██████▉   | 173/250 [02:17<01:10,  1.09it/s]
 70%|███████   | 175/250 [02:18<00:56,  1.33it/s]
 70%|███████   | 176/250 [02:19<00:52,  1.42it/s]
 71%|███████   | 177/250 [02:19<00:47,  1.54it/s]
 71%|███████   | 178/250 [02:20<00:52,  1.37it/s]
 72%|███████▏  | 179/250 [02:20<00:44,  1.60it/s]
 72%|███████▏  | 180/250 [02:23<01:18,  1.13s/it]
 72%|███████▏  | 181/250 [02:26<01:55,  1.68s/it]
 73%|███████▎  | 182/250 [02:27<01:37,  1.43s/it]
 73%|███████▎  | 183/250 [02:28<01:29,  1.34s/it]
 74%|███████▎  | 184/250 [02:29<01:16,  1.15s/it]
 74%|███████▍  | 186/250 [02:29<00:45,  1.40it/s]
 75%|███████▍  | 187/250 [02:29<00:37,  1.67it/s]
 75%|███████▌  | 188/250 [02:30<00:47,  1.31it/s]
 76%|███████▌  | 189/250 [02:31<00:42,  1.44it/s]
 76%|███████▌  | 190/250 [02:34<01:14,  1.25s/it]
 77%|███████▋  | 192/250 [02:34<00:46,  1.24it/s]
 77%|███████▋  | 193/250 [02:35<00:45,  1.26it/s]
 78%|███████▊  | 194/250 [02:36<00:48,  1.15it/s]
 78%|███████▊  | 195/250 [02:37<00:43,  1.26it/s]
 78%|███████▊  | 196/250 [02:38<00:46,  1.16it/s]
 79%|███████▉  | 197/250 [02:39<00:46,  1.13it/s]
 79%|███████▉  | 198/250 [02:39<00:35,  1.44it/s]
 80%|███████▉  | 199/250 [02:39<00:34,  1.46it/s]
 80%|████████  | 200/250 [02:40<00:28,  1.73it/s]
 80%|████████  | 201/250 [02:40<00:29,  1.68it/s]
 81%|████████  | 202/250 [02:41<00:22,  2.16it/s]
 81%|████████  | 203/250 [02:44<00:59,  1.26s/it]
 82%|████████▏ | 204/250 [02:44<00:45,  1.01it/s]
 82%|████████▏ | 205/250 [02:47<01:11,  1.59s/it]
 82%|████████▏ | 206/250 [02:48<00:56,  1.28s/it]
 83%|████████▎ | 207/250 [02:49<00:56,  1.32s/it]
 83%|████████▎ | 208/250 [02:52<01:13,  1.74s/it]
 84%|████████▎ | 209/250 [02:52<00:56,  1.37s/it]
 84%|████████▍ | 211/250 [02:54<00:40,  1.04s/it]
 85%|████████▌ | 213/250 [02:54<00:24,  1.52it/s]
 86%|████████▌ | 214/250 [02:54<00:21,  1.67it/s]
 86%|████████▌ | 215/250 [02:56<00:31,  1.11it/s]
 86%|████████▋ | 216/250 [02:56<00:25,  1.36it/s]
 87%|████████▋ | 217/250 [02:57<00:22,  1.49it/s]
 87%|████████▋ | 218/250 [02:59<00:39,  1.23s/it]
 88%|████████▊ | 219/250 [03:01<00:45,  1.46s/it]
 88%|████████▊ | 220/250 [03:02<00:32,  1.08s/it]
 88%|████████▊ | 221/250 [03:02<00:27,  1.06it/s]
 89%|████████▉ | 222/250 [03:04<00:30,  1.09s/it]
 89%|████████▉ | 223/250 [03:04<00:24,  1.11it/s]
 90%|████████▉ | 224/250 [03:05<00:20,  1.27it/s]
 90%|█████████ | 225/250 [03:05<00:18,  1.32it/s]
 90%|█████████ | 226/250 [03:06<00:15,  1.59it/s]
 91%|█████████ | 227/250 [03:06<00:11,  2.00it/s]
 91%|█████████ | 228/250 [03:08<00:20,  1.05it/s]
 92%|█████████▏| 229/250 [03:09<00:19,  1.10it/s]
 92%|█████████▏| 230/250 [03:09<00:14,  1.40it/s]
 92%|█████████▏| 231/250 [03:09<00:11,  1.71it/s]
 93%|█████████▎| 232/250 [03:10<00:09,  1.88it/s]
 94%|█████████▎| 234/250 [03:10<00:06,  2.31it/s]
 94%|█████████▍| 235/250 [03:11<00:06,  2.24it/s]
 94%|█████████▍| 236/250 [03:11<00:06,  2.33it/s]
 95%|█████████▌| 238/250 [03:11<00:03,  3.44it/s]
 96%|█████████▌| 239/250 [03:11<00:02,  4.03it/s]
 96%|█████████▌| 240/250 [03:12<00:02,  3.83it/s]
 97%|█████████▋| 242/250 [03:12<00:01,  4.64it/s]
 97%|█████████▋| 243/250 [03:12<00:01,  4.88it/s]
 98%|█████████▊| 245/250 [03:13<00:01,  4.69it/s]
 98%|█████████▊| 246/250 [03:13<00:00,  4.19it/s]
 99%|█████████▉| 247/250 [03:13<00:00,  4.02it/s]
 99%|█████████▉| 248/250 [03:15<00:01,  1.87it/s]
100%|█████████▉| 249/250 [03:22<00:02,  2.27s/it]
100%|██████████| 250/250 [03:22<00:00,  1.74s/it]
100%|██████████| 250/250 [03:22<00:00,  1.23it/s]
2025-05-14 13:32:47,364 - modnet - INFO - Loss per individual: ind 0: 0.109 	ind 1: 0.120 	ind 2: 0.109 	ind 3: 0.105 	ind 4: 0.111 	ind 5: 0.109 	ind 6: 0.114 	ind 7: 0.104 	ind 8: 0.122 	ind 9: 0.107 	ind 10: 0.109 	ind 11: 0.144 	ind 12: 0.102 	ind 13: 4.318 	ind 14: 0.100 	ind 15: 0.131 	ind 16: 0.120 	ind 17: 0.115 	ind 18: 0.102 	ind 19: 0.120 	ind 20: 0.111 	ind 21: 0.113 	ind 22: 0.108 	ind 23: 0.124 	ind 24: 0.105 	ind 25: 0.108 	ind 26: 0.113 	ind 27: 0.103 	ind 28: 0.107 	ind 29: 0.099 	ind 30: 0.113 	ind 31: 0.114 	ind 32: 0.107 	ind 33: 0.110 	ind 34: 0.102 	ind 35: 0.105 	ind 36: 0.112 	ind 37: 0.108 	ind 38: 0.102 	ind 39: 0.116 	ind 40: 0.108 	ind 41: 0.103 	ind 42: 0.103 	ind 43: 0.114 	ind 44: 0.112 	ind 45: 0.115 	ind 46: 0.101 	ind 47: 0.104 	ind 48: 0.103 	ind 49: 0.109 	
2025-05-14 13:32:47,367 - modnet - INFO - Early stopping: same best model for 8 consecutive generations
2025-05-14 13:32:47,367 - modnet - INFO - Early stopping at generation number 12
2025-05-14 13:32:50,277 - modnet - INFO - Model successfully saved as results/matbench_expt_gap_best_model_fold_1.pkl!
Saved best model for fold 1 to results/matbench_expt_gap_best_model_fold_1.pkl
2025-05-14 13:32:50.317852: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 13:32:50.318551: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445485000 Hz
Traceback (most recent call last):
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 323, in train_fold
    pred_results = model.predict(test_data, **predict_kwargs)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/models/ensemble.py", line 157, in predict
    p = self.model[i].predict(test_data, return_prob=return_prob)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/models/vanilla.py", line 661, in predict
    yrange = self.max_y - self.min_y
TypeError: numpy boolean subtract, the `-` operator, is not supported, use the bitwise_xor, the `^` operator, or the logical_xor function instead.
Something went wrong benchmarking this model.
Traceback (most recent call last):
  File "run_benchmark.py", line 861, in <module>
    results = benchmark(data, settings, n_jobs=n_jobs, fast=fast)
  File "run_benchmark.py", line 140, in benchmark
    return matbench_benchmark(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 137, in matbench_benchmark
    fold_results.append(train_fold(fold, *args, **model_kwargs))
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 365, in train_fold
    if stds is not None:
UnboundLocalError: local variable 'stds' referenced before assignment
Job finished on Wed May 14 13:32:53 CEST 2025

Resources Used

Total Memory used                        - MEM              : 17GiB
Total CPU Time                           - CPU_Time         : 1-00:15:28
Execution Time                           - Wall_Time        : 00:45:29
total programme cpu time                 - Total_CPU        : 17:01:56
Total_CPU / CPU_Time  (%)                - ETA              : 70%
Number of alloc CPU                      - NCPUS            : 32
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 48
Mobilized Resources x Execution Time     - R_Wall_Time      : 1-12:23:12
CPU_Time / R_Wall_Time (%)               - ALPHA            : 66%
Energy (Joules)                                             : 790779

