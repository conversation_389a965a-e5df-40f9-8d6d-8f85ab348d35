Job started on Wed May 14 23:03:35 CEST 2025
Running on node(s): cnm024
2025-05-14 23:03:44.716627: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 32 jobs
Preparing nested CV run for task 'matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso'
Found 1 matching files for matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso
They are ['./precomputed/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_featurizedMM2020Struct.pkl.gz']
2025-05-14 23:03:52,957 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7febfd0f8d90> object, created with modnet version 0.1.13
Preparing fold 1 ...
2025-05-14 23:03:52,994 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7febfd108250> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f1
2025-05-14 23:03:53,025 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f1!
Preparing fold 2 ...
2025-05-14 23:03:53,058 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7febfcf03100> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f2
2025-05-14 23:03:53,090 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f2!
Preparing fold 3 ...
2025-05-14 23:03:53,122 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7febfcc245e0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f3
2025-05-14 23:03:53,255 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f3!
Preparing fold 4 ...
2025-05-14 23:03:53,288 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7febfcd00130> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f4
2025-05-14 23:03:53,319 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f4!
Preparing fold 5 ...
2025-05-14 23:03:53,352 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7febfc3d5cd0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f5
2025-05-14 23:03:53,384 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f5!
Training fold 1 ...
Processing fold 1 ...
2025-05-14 23:03:53.408119: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 23:03:53.408695: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/8.4.1.50-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 23:03:53.408725: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 23:03:53.408758: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm024.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 23:03:53.410016: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 23:03:54,541 - modnet - INFO - Loaded <modnet.models.ensemble.EnsembleMODNetModel object at 0x7febfd0f8eb0> object, created with modnet version 0.1.13
Loaded best model from results/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_best_model_fold_1.pkl
2025-05-14 23:03:54.566677: I tensorflow/compiler/mlir/mlir_graph_optimization_pass.cc:116] None of the MLIR optimization passes are enabled (registered 2)
2025-05-14 23:03:54.578738: I tensorflow/core/platform/profile_utils/cpu_utils.cc:112] CPU Frequency: 2445565000 Hz
[Fold 1] MAE = 34.529 , MedianAE = 19.106 , MAPE = 5.814, √MSE = 71.894 
[Fold 1] MaxAE = 721.391 , slope = 0.96, R = 0.99
Fold 1 metrics saved.
Saved fold 1 complete results to results/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_fold_1_results.pkl
Training fold 2 ...
Processing fold 2 ...
2025-05-14 23:03:57,052 - modnet - INFO - Targets:
2025-05-14 23:03:57,052 - modnet - INFO - 1)target: classification
2025-05-14 23:03:57,548 - modnet - INFO - Multiprocessing on 32 cores. Total of 128 cores available.
2025-05-14 23:03:57,548 - modnet - INFO - Generation number 0
Traceback (most recent call last):
  File "run_benchmark.py", line 861, in <module>
    results = benchmark(data, settings, n_jobs=n_jobs, fast=fast)
  File "run_benchmark.py", line 140, in benchmark
    return matbench_benchmark(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 142, in matbench_benchmark
    fold_results.append(train_fold(fold, *args, **model_kwargs))
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 286, in train_fold
    model = ga.run(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 496, in run
    val_loss, models, individuals = self.function_fitness(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 411, in function_fitness
    for train, val in splits:
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/model_selection/_split.py", line 336, in split
    for train, test in super().split(X, y, groups):
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/model_selection/_split.py", line 80, in split
    for test_index in self._iter_test_masks(X, y, groups):
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/model_selection/_split.py", line 697, in _iter_test_masks
    test_folds = self._make_test_folds(X, y)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/model_selection/_split.py", line 649, in _make_test_folds
    raise ValueError(
ValueError: Supported target types are: ('binary', 'multiclass'). Got 'continuous' instead.
Job finished on Wed May 14 23:03:58 CEST 2025

Resources Used

Total Memory used                        - MEM              : 0B
Total CPU Time                           - CPU_Time         : 00:12:16
Execution Time                           - Wall_Time        : 00:00:23
total programme cpu time                 - Total_CPU        : 00:16.944
Total_CPU / CPU_Time  (%)                - ETA              : 2%
Number of alloc CPU                      - NCPUS            : 32
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 48
Mobilized Resources x Execution Time     - R_Wall_Time      : 00:18:24
CPU_Time / R_Wall_Time (%)               - ALPHA            : 66%
Energy (Joules)                                             : 1150

