Job started on Wed May 21 17:33:12 CEST 2025
Running on node(s): cns270
2025-05-21 17:33:29.959512: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 64 jobs
Preparing nested CV run for task 'matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso'
Found 1 matching files for matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso
2025-05-21 17:33:45,393 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fbe0e492e20> object, created with modnet version 0.1.13
GA settings:
    Population size: 50
    Number of generations: 20
    Early stopping: 6
    Refit: False
Preparing fold 1 ...
2025-05-21 17:33:45,426 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fbe0e4a9250> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f1
2025-05-21 17:33:45,457 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f1!
Preparing fold 2 ...
2025-05-21 17:33:45,486 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fbe0dbacb80> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f2
2025-05-21 17:33:45,614 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f2!
Preparing fold 3 ...
2025-05-21 17:33:45,642 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fbe0d800460> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f3
2025-05-21 17:33:45,673 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f3!
Preparing fold 4 ...
2025-05-21 17:33:45,701 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fbe0dc251c0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f4
2025-05-21 17:33:45,733 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f4!
Preparing fold 5 ...
2025-05-21 17:33:45,761 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fbe0e10e610> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f5
2025-05-21 17:33:45,791 - modnet - INFO - Data successfully saved as folds/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_train_moddata_f5!
Training fold 1 ...
Fold 1 already completed. Loading saved results.
2025-05-21 17:33:45.826152: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 17:33:45.826970: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/8.4.1.50-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-21 17:33:45.827017: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-21 17:33:45.827062: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cns270.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-21 17:33:45.828321: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-21 17:33:46,710 - modnet - INFO - Loaded <modnet.models.ensemble.EnsembleMODNetModel object at 0x7fbe0e6a06d0> object, created with modnet version 0.1.13
Loaded saved model from results/matbench_phonons_matminer_megnet_ofm_mvl_all_roost_mpgap_sisso_best_model_fold_1.pkl
Training fold 2 ...
Processing fold 2 ...
2025-05-21 17:33:46,915 - modnet - INFO - Targets:
2025-05-21 17:33:46,915 - modnet - INFO - 1) target: regression
2025-05-21 17:34:12,813 - modnet - INFO - Multiprocessing on 64 cores. Total of 128 cores available.
2025-05-21 17:34:12,816 - modnet - INFO - === Generation 0 ===
Population initialized with 100 individuals.
2025-05-21 17:34:12,821 - modnet - INFO - Generating 3 splits of 80/20  for Generation 0

  0%|          | 0/300 [00:00<?, ?it/s]
  0%|          | 1/300 [00:08<41:42,  8.37s/it]
  1%|          | 2/300 [00:08<17:49,  3.59s/it]
  1%|          | 3/300 [00:09<11:17,  2.28s/it]
  1%|▏         | 4/300 [00:10<08:15,  1.67s/it]
  2%|▏         | 7/300 [00:10<03:09,  1.55it/s]
  3%|▎         | 9/300 [00:10<02:10,  2.23it/s]
  3%|▎         | 10/300 [00:10<01:52,  2.57it/s]
  4%|▍         | 12/300 [00:10<01:17,  3.71it/s]
  5%|▍         | 14/300 [00:10<00:57,  4.97it/s]
  5%|▌         | 16/300 [00:11<00:48,  5.81it/s]
  6%|▌         | 18/300 [00:11<00:49,  5.70it/s]
  6%|▋         | 19/300 [00:11<00:47,  5.92it/s]
  7%|▋         | 20/300 [00:11<00:47,  5.90it/s]
  7%|▋         | 22/300 [00:11<00:38,  7.26it/s]
  8%|▊         | 24/300 [00:12<00:53,  5.14it/s]
  9%|▊         | 26/300 [00:12<00:41,  6.56it/s]
  9%|▉         | 28/300 [00:12<00:34,  7.84it/s]
 10%|█         | 30/300 [00:12<00:29,  9.04it/s]
 11%|█         | 32/300 [00:13<00:37,  7.10it/s]
 11%|█▏        | 34/300 [00:13<00:33,  7.83it/s]
 12%|█▏        | 35/300 [00:13<00:35,  7.50it/s]
 12%|█▏        | 37/300 [00:13<00:32,  8.03it/s]
 13%|█▎        | 39/300 [00:14<00:28,  9.29it/s]
 14%|█▎        | 41/300 [00:14<00:23, 10.93it/s]
 14%|█▍        | 43/300 [00:14<00:33,  7.71it/s]
 15%|█▌        | 45/300 [00:15<00:40,  6.32it/s]
 15%|█▌        | 46/300 [00:15<00:39,  6.45it/s]
 16%|█▌        | 48/300 [00:15<00:31,  8.07it/s]
 17%|█▋        | 50/300 [00:15<00:34,  7.32it/s]
 17%|█▋        | 52/300 [00:15<00:30,  8.14it/s]
 18%|█▊        | 53/300 [00:16<00:30,  8.21it/s]
 18%|█▊        | 55/300 [00:16<00:27,  9.03it/s]
 19%|█▊        | 56/300 [00:16<00:28,  8.62it/s]
 19%|█▉        | 57/300 [00:16<00:32,  7.38it/s]
 20%|█▉        | 59/300 [00:16<00:27,  8.75it/s]slurmstepd: error: *** JOB 8646172 ON cns270 CANCELLED AT 2025-05-21T17:34:30 ***

Resources Used

Total Memory used                        - MEM              : 24GiB
Total CPU Time                           - CPU_Time         : 01:59:28
Execution Time                           - Wall_Time        : 00:01:52
total programme cpu time                 - Total_CPU        : 17:15.750
Total_CPU / CPU_Time  (%)                - ETA              : 14%
Number of alloc CPU                      - NCPUS            : 64
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 64
Mobilized Resources x Execution Time     - R_Wall_Time      : 01:59:28
CPU_Time / R_Wall_Time (%)               - ALPHA            : 100%
Energy (Joules)                                             : unknown

