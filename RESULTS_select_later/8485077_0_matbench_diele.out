Job started on Wed May 14 23:27:45 CEST 2025
Running on node(s): cnm025
2025-05-14 23:27:47.659811: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 32 jobs
Preparing nested CV run for task 'matbench_dielectric'
Found 3 matching files for matbench_dielectric
Found multiple data files ['./precomputed/matbench_dielectric_matminer_megnet_ofm_mvl_all_featurizedMM2020Struct.pkl.gz', './precomputed/matbench_dielectric_featurizedMM2020Struct.pkl.gz', './precomputed/matbench_dielectric_matminer_megnet_ofm_mvl16_featurizedMM2020Struct.pkl.gz'], loading the first ./precomputed/matbench_dielectric_featurizedMM2020Struct.pkl.gz
2025-05-14 23:27:50,238 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fa1b8b62700> object, created with modnet version 0.1.13
Preparing fold 1 ...
2025-05-14 23:27:50,289 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fa1b8b62370> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_dielectric_train_moddata_f1
2025-05-14 23:27:50,329 - modnet - INFO - Data successfully saved as folds/matbench_dielectric_train_moddata_f1!
Preparing fold 2 ...
2025-05-14 23:27:50,374 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7fa1b65034f0> object, created with modnet version 0.1.13
Loaded train data from folds/matbench_dielectric_train_moddata_f2
2025-05-14 23:27:50,414 - modnet - INFO - Data successfully saved as folds/matbench_dielectric_train_moddata_f2!
Preparing fold 3 ...
2025-05-14 23:27:50,421 - modnet - INFO - Loading cross NMI from 'Features_cross' file.
2025-05-14 23:27:50,454 - modnet - WARNING - Feature mismatch between precomputed `Features_cross` and `df_featurized`. Missing columns: {'XRDPowderPattern|xrd_58', 'SineCoulombMatrix|sine coulomb matrix eig 229', 'SineCoulombMatrix|sine coulomb matrix eig 186', 'XRDPowderPattern|xrd_30', 'SineCoulombMatrix|sine coulomb matrix eig 269', 'SineCoulombMatrix|sine coulomb matrix eig 265', 'BondFractions|As - B bond frac.', 'XRDPowderPattern|xrd_54', 'XRDPowderPattern|xrd_50', 'XRDPowderPattern|xrd_112', 'SineCoulombMatrix|sine coulomb matrix eig 233', 'XRDPowderPattern|xrd_96', 'BondFractions|Al - Hg bond frac.', 'BondFractions|Ag - Sr bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 15', 'SineCoulombMatrix|sine coulomb matrix eig 266', 'BondFractions|Au - Br bond frac.', 'BondFractions|As - Na bond frac.', 'XRDPowderPattern|xrd_114', 'BondFractions|O - Os bond frac.', 'BondFractions|Ga - Ge bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 80', 'BondFractions|In - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 236', 'BondFractions|N - Ru bond frac.', 'XRDPowderPattern|xrd_86', 'CoulombMatrix|coulomb matrix eig 16', 'BondFractions|Ag - Ag bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 261', 'XRDPowderPattern|xrd_38', 'BondFractions|Ge - N bond frac.', 'XRDPowderPattern|xrd_104', 'BondFractions|Bi - S bond frac.', 'XRDPowderPattern|xrd_74', 'SineCoulombMatrix|sine coulomb matrix eig 187', 'XRDPowderPattern|xrd_126', 'XRDPowderPattern|xrd_56', 'SineCoulombMatrix|sine coulomb matrix eig 263', 'BondFractions|Ag - Cl bond frac.', 'XRDPowderPattern|xrd_46', 'XRDPowderPattern|xrd_122', 'XRDPowderPattern|xrd_16', 'BondFractions|As - Zn bond frac.', 'BondFractions|Ag - I bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 238', 'CoulombMatrix|coulomb matrix eig 18', 'XRDPowderPattern|xrd_48', 'BondFractions|Ca - Sn bond frac.', 'XRDPowderPattern|xrd_84', 'SineCoulombMatrix|sine coulomb matrix eig 234', 'XRDPowderPattern|xrd_82', 'BondFractions|Ba - N bond frac.', 'BondFractions|Al - Se bond frac.', 'CoulombMatrix|coulomb matrix eig 13', 'XRDPowderPattern|xrd_102', 'XRDPowderPattern|xrd_116', 'BondFractions|S - Tl bond frac.', 'BondFractions|Au - Se bond frac.', 'XRDPowderPattern|xrd_40', 'XRDPowderPattern|xrd_24', 'BondFractions|Ag - Te bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 235', 'XRDPowderPattern|xrd_14', 'XRDPowderPattern|xrd_124', 'BondFractions|Br - Cd bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 173', 'BondFractions|Nb - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 270', 'BondFractions|As - As bond frac.', 'BondFractions|Al - I bond frac.', 'BondFractions|Ag - Se bond frac.', 'CoulombMatrix|coulomb matrix eig 19', 'BondFractions|Ga - Se bond frac.', 'BondFractions|Cu - Sb bond frac.', 'BondFractions|Br - Fe bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 267', 'SineCoulombMatrix|sine coulomb matrix eig 239', 'BondFractions|Sb - Ti bond frac.', 'BondFractions|N - Rb bond frac.', 'BondFractions|La - Se bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 11', 'SineCoulombMatrix|sine coulomb matrix eig 59', 'BondFractions|N - Ta bond frac.', 'BondFractions|Te - Te bond frac.', 'XRDPowderPattern|xrd_80', 'XRDPowderPattern|xrd_22', 'CoulombMatrix|coulomb matrix eig 12', 'SineCoulombMatrix|sine coulomb matrix eig 29', 'SineCoulombMatrix|sine coulomb matrix eig 268', 'SineCoulombMatrix|sine coulomb matrix eig 264', 'BondFractions|Cd - P bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 260', 'BondFractions|Ba - Hf bond frac.', 'XRDPowderPattern|xrd_98', 'BondFractions|In - O bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 258', 'BondFractions|Ba - F bond frac.', 'BondFractions|Pd - Sb bond frac.', 'BondFractions|Al - Te bond frac.', 'BondFractions|Se - Zr bond frac.', 'XRDPowderPattern|xrd_2', 'XRDPowderPattern|xrd_76', 'BondFractions|Bi - Se bond frac.', 'BondFractions|S - Sb bond frac.', 'XRDPowderPattern|xrd_88', 'SineCoulombMatrix|sine coulomb matrix eig 259', 'BondFractions|K - Se bond frac.', 'BondFractions|Ag - O bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 230', 'XRDPowderPattern|xrd_42', 'XRDPowderPattern|xrd_36', 'XRDPowderPattern|xrd_52', 'XRDPowderPattern|xrd_106', 'BondFractions|As - Te bond frac.', 'BondFractions|As - Sn bond frac.', 'XRDPowderPattern|xrd_92', 'XRDPowderPattern|xrd_64', 'BondFractions|H - P bond frac.', 'Miedema|Miedema_deltaH_ss_min', 'BondFractions|Mo - N bond frac.', 'XRDPowderPattern|xrd_66', 'BondFractions|K - Te bond frac.', 'BondFractions|Cu - Se bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 171', 'SineCoulombMatrix|sine coulomb matrix eig 232', 'XRDPowderPattern|xrd_94', 'XRDPowderPattern|xrd_34', 'SineCoulombMatrix|sine coulomb matrix eig 189', 'XRDPowderPattern|xrd_110', 'XRDPowderPattern|xrd_32', 'XRDPowderPattern|xrd_100', 'XRDPowderPattern|xrd_120', 'XRDPowderPattern|xrd_18', 'BondFractions|As - Cd bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 188', 'BondFractions|N - Sn bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 237', 'SineCoulombMatrix|sine coulomb matrix eig 240', 'BondFractions|Pt - Y bond frac.', 'XRDPowderPattern|xrd_90', 'BondFractions|Se - Se bond frac.', 'XRDPowderPattern|xrd_60', 'XRDPowderPattern|xrd_62', 'XRDPowderPattern|xrd_28', 'BondFractions|Cu - Te bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 262', 'BondFractions|Ag - Sn bond frac.', 'BondFractions|O - Y bond frac.', 'XRDPowderPattern|xrd_70', 'BondFractions|Ag - S bond frac.', 'XRDPowderPattern|xrd_78', 'XRDPowderPattern|xrd_72', 'BondFractions|Ga - P bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 12', 'XRDPowderPattern|xrd_108', 'BondFractions|O - Sr bond frac.', 'BondFractions|N - Re bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 271', 'BondFractions|Al - Tl bond frac.', 'Miedema|Miedema_deltaH_amor', 'BondFractions|As - Be bond frac.', 'BondFractions|S - Sm bond frac.', 'BondFractions|As - Ca bond frac.', 'CoulombMatrix|coulomb matrix eig 17', 'XRDPowderPattern|xrd_44', 'XRDPowderPattern|xrd_68', 'SineCoulombMatrix|sine coulomb matrix eig 241'}
2025-05-14 23:27:50,458 - modnet - INFO - Starting target 1/1: target ...
2025-05-14 23:27:50,458 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 23:29:02,228 - modnet - INFO - Computing optimal features...
2025-05-14 23:29:12,521 - modnet - INFO - Selected 50/619 features...
2025-05-14 23:29:22,230 - modnet - INFO - Selected 100/619 features...
2025-05-14 23:29:31,117 - modnet - INFO - Selected 150/619 features...
2025-05-14 23:29:39,180 - modnet - INFO - Selected 200/619 features...
2025-05-14 23:29:46,414 - modnet - INFO - Selected 250/619 features...
2025-05-14 23:29:52,806 - modnet - INFO - Selected 300/619 features...
2025-05-14 23:29:58,334 - modnet - INFO - Selected 350/619 features...
2025-05-14 23:30:02,962 - modnet - INFO - Selected 400/619 features...
2025-05-14 23:30:06,689 - modnet - INFO - Selected 450/619 features...
2025-05-14 23:30:09,499 - modnet - INFO - Selected 500/619 features...
2025-05-14 23:30:11,393 - modnet - INFO - Selected 550/619 features...
2025-05-14 23:30:12,352 - modnet - INFO - Selected 600/619 features...
2025-05-14 23:30:12,467 - modnet - INFO - Done with target 1/1: target.
2025-05-14 23:30:12,467 - modnet - INFO - Merging all features...
2025-05-14 23:30:12,467 - modnet - INFO - Done.
2025-05-14 23:30:12,514 - modnet - INFO - Data successfully saved as folds/matbench_dielectric_train_moddata_f3!
2025-05-14 23:30:12,538 - modnet - INFO - Data successfully saved as folds/matbench_dielectric_test_moddata_f3!
Preparing fold 4 ...
2025-05-14 23:30:12,547 - modnet - INFO - Loading cross NMI from 'Features_cross' file.
2025-05-14 23:30:12,576 - modnet - WARNING - Feature mismatch between precomputed `Features_cross` and `df_featurized`. Missing columns: {'XRDPowderPattern|xrd_58', 'SineCoulombMatrix|sine coulomb matrix eig 229', 'SineCoulombMatrix|sine coulomb matrix eig 186', 'XRDPowderPattern|xrd_30', 'SineCoulombMatrix|sine coulomb matrix eig 269', 'SineCoulombMatrix|sine coulomb matrix eig 265', 'BondFractions|As - B bond frac.', 'XRDPowderPattern|xrd_54', 'XRDPowderPattern|xrd_50', 'XRDPowderPattern|xrd_112', 'SineCoulombMatrix|sine coulomb matrix eig 233', 'XRDPowderPattern|xrd_96', 'BondFractions|Al - Hg bond frac.', 'BondFractions|Ag - Sr bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 15', 'SineCoulombMatrix|sine coulomb matrix eig 266', 'BondFractions|Au - Br bond frac.', 'BondFractions|As - Na bond frac.', 'XRDPowderPattern|xrd_114', 'BondFractions|O - Os bond frac.', 'BondFractions|Ga - Ge bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 80', 'BondFractions|In - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 236', 'BondFractions|N - Ru bond frac.', 'XRDPowderPattern|xrd_86', 'CoulombMatrix|coulomb matrix eig 16', 'BondFractions|Ag - Ag bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 261', 'XRDPowderPattern|xrd_38', 'BondFractions|Ge - N bond frac.', 'XRDPowderPattern|xrd_104', 'BondFractions|Bi - S bond frac.', 'XRDPowderPattern|xrd_74', 'SineCoulombMatrix|sine coulomb matrix eig 187', 'XRDPowderPattern|xrd_126', 'XRDPowderPattern|xrd_56', 'SineCoulombMatrix|sine coulomb matrix eig 263', 'BondFractions|Ag - Cl bond frac.', 'XRDPowderPattern|xrd_46', 'XRDPowderPattern|xrd_122', 'XRDPowderPattern|xrd_16', 'BondFractions|As - Zn bond frac.', 'BondFractions|Ag - I bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 238', 'CoulombMatrix|coulomb matrix eig 18', 'XRDPowderPattern|xrd_48', 'BondFractions|Ca - Sn bond frac.', 'XRDPowderPattern|xrd_84', 'SineCoulombMatrix|sine coulomb matrix eig 234', 'XRDPowderPattern|xrd_82', 'BondFractions|Ba - N bond frac.', 'BondFractions|Al - Se bond frac.', 'CoulombMatrix|coulomb matrix eig 13', 'XRDPowderPattern|xrd_102', 'XRDPowderPattern|xrd_116', 'BondFractions|S - Tl bond frac.', 'BondFractions|Au - Se bond frac.', 'XRDPowderPattern|xrd_40', 'XRDPowderPattern|xrd_24', 'BondFractions|Ag - Te bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 235', 'XRDPowderPattern|xrd_14', 'XRDPowderPattern|xrd_124', 'BondFractions|Br - Cd bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 173', 'BondFractions|Nb - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 270', 'BondFractions|As - As bond frac.', 'BondFractions|Al - I bond frac.', 'BondFractions|Ag - Se bond frac.', 'CoulombMatrix|coulomb matrix eig 19', 'BondFractions|Ga - Se bond frac.', 'BondFractions|Cu - Sb bond frac.', 'BondFractions|Br - Fe bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 267', 'SineCoulombMatrix|sine coulomb matrix eig 239', 'BondFractions|Sb - Ti bond frac.', 'BondFractions|N - Rb bond frac.', 'BondFractions|La - Se bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 11', 'SineCoulombMatrix|sine coulomb matrix eig 59', 'BondFractions|N - Ta bond frac.', 'BondFractions|Te - Te bond frac.', 'XRDPowderPattern|xrd_80', 'XRDPowderPattern|xrd_22', 'CoulombMatrix|coulomb matrix eig 12', 'SineCoulombMatrix|sine coulomb matrix eig 29', 'SineCoulombMatrix|sine coulomb matrix eig 268', 'SineCoulombMatrix|sine coulomb matrix eig 264', 'BondFractions|Cd - P bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 260', 'BondFractions|Ba - Hf bond frac.', 'XRDPowderPattern|xrd_98', 'BondFractions|In - O bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 258', 'BondFractions|Ba - F bond frac.', 'BondFractions|Pd - Sb bond frac.', 'BondFractions|Al - Te bond frac.', 'BondFractions|Se - Zr bond frac.', 'XRDPowderPattern|xrd_2', 'XRDPowderPattern|xrd_76', 'BondFractions|Bi - Se bond frac.', 'BondFractions|S - Sb bond frac.', 'XRDPowderPattern|xrd_88', 'SineCoulombMatrix|sine coulomb matrix eig 259', 'BondFractions|K - Se bond frac.', 'BondFractions|Ag - O bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 230', 'XRDPowderPattern|xrd_42', 'XRDPowderPattern|xrd_36', 'XRDPowderPattern|xrd_52', 'XRDPowderPattern|xrd_106', 'BondFractions|As - Te bond frac.', 'BondFractions|As - Sn bond frac.', 'XRDPowderPattern|xrd_92', 'XRDPowderPattern|xrd_64', 'BondFractions|H - P bond frac.', 'Miedema|Miedema_deltaH_ss_min', 'BondFractions|Mo - N bond frac.', 'XRDPowderPattern|xrd_66', 'BondFractions|K - Te bond frac.', 'BondFractions|Cu - Se bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 171', 'SineCoulombMatrix|sine coulomb matrix eig 232', 'XRDPowderPattern|xrd_94', 'XRDPowderPattern|xrd_34', 'SineCoulombMatrix|sine coulomb matrix eig 189', 'XRDPowderPattern|xrd_110', 'XRDPowderPattern|xrd_32', 'XRDPowderPattern|xrd_100', 'XRDPowderPattern|xrd_120', 'XRDPowderPattern|xrd_18', 'BondFractions|As - Cd bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 188', 'BondFractions|N - Sn bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 237', 'SineCoulombMatrix|sine coulomb matrix eig 240', 'BondFractions|Pt - Y bond frac.', 'XRDPowderPattern|xrd_90', 'BondFractions|Se - Se bond frac.', 'XRDPowderPattern|xrd_60', 'XRDPowderPattern|xrd_62', 'XRDPowderPattern|xrd_28', 'BondFractions|Cu - Te bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 262', 'BondFractions|Ag - Sn bond frac.', 'BondFractions|O - Y bond frac.', 'XRDPowderPattern|xrd_70', 'BondFractions|Ag - S bond frac.', 'XRDPowderPattern|xrd_78', 'XRDPowderPattern|xrd_72', 'BondFractions|Ga - P bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 12', 'XRDPowderPattern|xrd_108', 'BondFractions|O - Sr bond frac.', 'BondFractions|N - Re bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 271', 'BondFractions|Al - Tl bond frac.', 'Miedema|Miedema_deltaH_amor', 'BondFractions|As - Be bond frac.', 'BondFractions|S - Sm bond frac.', 'BondFractions|As - Ca bond frac.', 'CoulombMatrix|coulomb matrix eig 17', 'XRDPowderPattern|xrd_44', 'XRDPowderPattern|xrd_68', 'SineCoulombMatrix|sine coulomb matrix eig 241'}
2025-05-14 23:30:12,581 - modnet - INFO - Starting target 1/1: target ...
2025-05-14 23:30:12,581 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 23:31:24,510 - modnet - INFO - Computing optimal features...
2025-05-14 23:31:34,770 - modnet - INFO - Selected 50/620 features...
2025-05-14 23:31:44,419 - modnet - INFO - Selected 100/620 features...
2025-05-14 23:31:53,321 - modnet - INFO - Selected 150/620 features...
2025-05-14 23:32:01,487 - modnet - INFO - Selected 200/620 features...
2025-05-14 23:32:08,808 - modnet - INFO - Selected 250/620 features...
2025-05-14 23:32:15,255 - modnet - INFO - Selected 300/620 features...
2025-05-14 23:32:20,826 - modnet - INFO - Selected 350/620 features...
2025-05-14 23:32:25,497 - modnet - INFO - Selected 400/620 features...
2025-05-14 23:32:29,259 - modnet - INFO - Selected 450/620 features...
2025-05-14 23:32:32,109 - modnet - INFO - Selected 500/620 features...
2025-05-14 23:32:34,031 - modnet - INFO - Selected 550/620 features...
2025-05-14 23:32:35,009 - modnet - INFO - Selected 600/620 features...
2025-05-14 23:32:35,133 - modnet - INFO - Done with target 1/1: target.
2025-05-14 23:32:35,133 - modnet - INFO - Merging all features...
2025-05-14 23:32:35,133 - modnet - INFO - Done.
2025-05-14 23:32:35,178 - modnet - INFO - Data successfully saved as folds/matbench_dielectric_train_moddata_f4!
2025-05-14 23:32:35,203 - modnet - INFO - Data successfully saved as folds/matbench_dielectric_test_moddata_f4!
Preparing fold 5 ...
2025-05-14 23:32:35,211 - modnet - INFO - Loading cross NMI from 'Features_cross' file.
2025-05-14 23:32:35,240 - modnet - WARNING - Feature mismatch between precomputed `Features_cross` and `df_featurized`. Missing columns: {'XRDPowderPattern|xrd_58', 'SineCoulombMatrix|sine coulomb matrix eig 229', 'SineCoulombMatrix|sine coulomb matrix eig 186', 'XRDPowderPattern|xrd_30', 'SineCoulombMatrix|sine coulomb matrix eig 269', 'SineCoulombMatrix|sine coulomb matrix eig 265', 'BondFractions|As - B bond frac.', 'XRDPowderPattern|xrd_54', 'XRDPowderPattern|xrd_50', 'XRDPowderPattern|xrd_112', 'SineCoulombMatrix|sine coulomb matrix eig 233', 'XRDPowderPattern|xrd_96', 'BondFractions|Al - Hg bond frac.', 'BondFractions|Ag - Sr bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 15', 'SineCoulombMatrix|sine coulomb matrix eig 266', 'BondFractions|Au - Br bond frac.', 'BondFractions|As - Na bond frac.', 'XRDPowderPattern|xrd_114', 'BondFractions|O - Os bond frac.', 'BondFractions|Ga - Ge bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 80', 'BondFractions|In - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 236', 'BondFractions|N - Ru bond frac.', 'XRDPowderPattern|xrd_86', 'CoulombMatrix|coulomb matrix eig 16', 'BondFractions|Ag - Ag bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 261', 'XRDPowderPattern|xrd_38', 'BondFractions|Ge - N bond frac.', 'XRDPowderPattern|xrd_104', 'BondFractions|Bi - S bond frac.', 'XRDPowderPattern|xrd_74', 'SineCoulombMatrix|sine coulomb matrix eig 187', 'XRDPowderPattern|xrd_126', 'XRDPowderPattern|xrd_56', 'SineCoulombMatrix|sine coulomb matrix eig 263', 'BondFractions|Ag - Cl bond frac.', 'XRDPowderPattern|xrd_46', 'XRDPowderPattern|xrd_122', 'XRDPowderPattern|xrd_16', 'BondFractions|As - Zn bond frac.', 'BondFractions|Ag - I bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 238', 'CoulombMatrix|coulomb matrix eig 18', 'XRDPowderPattern|xrd_48', 'BondFractions|Ca - Sn bond frac.', 'XRDPowderPattern|xrd_84', 'SineCoulombMatrix|sine coulomb matrix eig 234', 'XRDPowderPattern|xrd_82', 'BondFractions|Ba - N bond frac.', 'BondFractions|Al - Se bond frac.', 'CoulombMatrix|coulomb matrix eig 13', 'XRDPowderPattern|xrd_102', 'XRDPowderPattern|xrd_116', 'BondFractions|S - Tl bond frac.', 'BondFractions|Au - Se bond frac.', 'XRDPowderPattern|xrd_40', 'XRDPowderPattern|xrd_24', 'BondFractions|Ag - Te bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 235', 'XRDPowderPattern|xrd_14', 'XRDPowderPattern|xrd_124', 'BondFractions|Br - Cd bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 173', 'BondFractions|Nb - S bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 270', 'BondFractions|As - As bond frac.', 'BondFractions|Al - I bond frac.', 'BondFractions|Ag - Se bond frac.', 'CoulombMatrix|coulomb matrix eig 19', 'BondFractions|Ga - Se bond frac.', 'BondFractions|Cu - Sb bond frac.', 'BondFractions|Br - Fe bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 267', 'SineCoulombMatrix|sine coulomb matrix eig 239', 'BondFractions|Sb - Ti bond frac.', 'BondFractions|N - Rb bond frac.', 'BondFractions|La - Se bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 11', 'SineCoulombMatrix|sine coulomb matrix eig 59', 'BondFractions|N - Ta bond frac.', 'BondFractions|Te - Te bond frac.', 'XRDPowderPattern|xrd_80', 'XRDPowderPattern|xrd_22', 'CoulombMatrix|coulomb matrix eig 12', 'SineCoulombMatrix|sine coulomb matrix eig 29', 'SineCoulombMatrix|sine coulomb matrix eig 268', 'SineCoulombMatrix|sine coulomb matrix eig 264', 'BondFractions|Cd - P bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 260', 'BondFractions|Ba - Hf bond frac.', 'XRDPowderPattern|xrd_98', 'BondFractions|In - O bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 258', 'BondFractions|Ba - F bond frac.', 'BondFractions|Pd - Sb bond frac.', 'BondFractions|Al - Te bond frac.', 'BondFractions|Se - Zr bond frac.', 'XRDPowderPattern|xrd_2', 'XRDPowderPattern|xrd_76', 'BondFractions|Bi - Se bond frac.', 'BondFractions|S - Sb bond frac.', 'XRDPowderPattern|xrd_88', 'SineCoulombMatrix|sine coulomb matrix eig 259', 'BondFractions|K - Se bond frac.', 'BondFractions|Ag - O bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 230', 'XRDPowderPattern|xrd_42', 'XRDPowderPattern|xrd_36', 'XRDPowderPattern|xrd_52', 'XRDPowderPattern|xrd_106', 'BondFractions|As - Te bond frac.', 'BondFractions|As - Sn bond frac.', 'XRDPowderPattern|xrd_92', 'XRDPowderPattern|xrd_64', 'BondFractions|H - P bond frac.', 'Miedema|Miedema_deltaH_ss_min', 'BondFractions|Mo - N bond frac.', 'XRDPowderPattern|xrd_66', 'BondFractions|K - Te bond frac.', 'BondFractions|Cu - Se bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 171', 'SineCoulombMatrix|sine coulomb matrix eig 232', 'XRDPowderPattern|xrd_94', 'XRDPowderPattern|xrd_34', 'SineCoulombMatrix|sine coulomb matrix eig 189', 'XRDPowderPattern|xrd_110', 'XRDPowderPattern|xrd_32', 'XRDPowderPattern|xrd_100', 'XRDPowderPattern|xrd_120', 'XRDPowderPattern|xrd_18', 'BondFractions|As - Cd bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 188', 'BondFractions|N - Sn bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 237', 'SineCoulombMatrix|sine coulomb matrix eig 240', 'BondFractions|Pt - Y bond frac.', 'XRDPowderPattern|xrd_90', 'BondFractions|Se - Se bond frac.', 'XRDPowderPattern|xrd_60', 'XRDPowderPattern|xrd_62', 'XRDPowderPattern|xrd_28', 'BondFractions|Cu - Te bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 262', 'BondFractions|Ag - Sn bond frac.', 'BondFractions|O - Y bond frac.', 'XRDPowderPattern|xrd_70', 'BondFractions|Ag - S bond frac.', 'XRDPowderPattern|xrd_78', 'XRDPowderPattern|xrd_72', 'BondFractions|Ga - P bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 12', 'XRDPowderPattern|xrd_108', 'BondFractions|O - Sr bond frac.', 'BondFractions|N - Re bond frac.', 'SineCoulombMatrix|sine coulomb matrix eig 271', 'BondFractions|Al - Tl bond frac.', 'Miedema|Miedema_deltaH_amor', 'BondFractions|As - Be bond frac.', 'BondFractions|S - Sm bond frac.', 'BondFractions|As - Ca bond frac.', 'CoulombMatrix|coulomb matrix eig 17', 'XRDPowderPattern|xrd_44', 'XRDPowderPattern|xrd_68', 'SineCoulombMatrix|sine coulomb matrix eig 241'}
2025-05-14 23:32:35,244 - modnet - INFO - Starting target 1/1: target ...
2025-05-14 23:32:35,244 - modnet - INFO - Computing mutual information between features and target...
2025-05-14 23:33:49,107 - modnet - INFO - Computing optimal features...
2025-05-14 23:33:59,376 - modnet - INFO - Selected 50/619 features...
2025-05-14 23:34:09,090 - modnet - INFO - Selected 100/619 features...
2025-05-14 23:34:18,020 - modnet - INFO - Selected 150/619 features...
2025-05-14 23:34:26,159 - modnet - INFO - Selected 200/619 features...
2025-05-14 23:34:33,444 - modnet - INFO - Selected 250/619 features...
2025-05-14 23:34:39,868 - modnet - INFO - Selected 300/619 features...
2025-05-14 23:34:45,415 - modnet - INFO - Selected 350/619 features...
2025-05-14 23:34:50,076 - modnet - INFO - Selected 400/619 features...
2025-05-14 23:34:53,835 - modnet - INFO - Selected 450/619 features...
2025-05-14 23:34:56,672 - modnet - INFO - Selected 500/619 features...
2025-05-14 23:34:58,581 - modnet - INFO - Selected 550/619 features...
2025-05-14 23:34:59,543 - modnet - INFO - Selected 600/619 features...
2025-05-14 23:34:59,658 - modnet - INFO - Done with target 1/1: target.
2025-05-14 23:34:59,658 - modnet - INFO - Merging all features...
2025-05-14 23:34:59,658 - modnet - INFO - Done.
2025-05-14 23:34:59,705 - modnet - INFO - Data successfully saved as folds/matbench_dielectric_train_moddata_f5!
2025-05-14 23:34:59,731 - modnet - INFO - Data successfully saved as folds/matbench_dielectric_test_moddata_f5!
Training fold 1 ...
Processing fold 1 ...
2025-05-14 23:34:59.741809: I tensorflow/compiler/jit/xla_cpu_device.cc:41] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 23:34:59.742445: W tensorflow/stream_executor/platform/default/dso_loader.cc:60] Could not load dynamic library 'libcuda.so.1'; dlerror: libcuda.so.1: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /gpfs/softs/easybuild/software/cuDNN/8.4.1.50-CUDA-11.7.0/lib:/gpfs/softs/easybuild/software/CUDA/11.7.0/nvvm/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/extras/CUPTI/lib64:/gpfs/softs/easybuild/software/CUDA/11.7.0/lib:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/sissopp_env/lib:/lib64:/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib::
2025-05-14 23:34:59.742474: W tensorflow/stream_executor/cuda/cuda_driver.cc:326] failed call to cuInit: UNKNOWN ERROR (303)
2025-05-14 23:34:59.742512: I tensorflow/stream_executor/cuda/cuda_diagnostics.cc:156] kernel driver does not appear to be running on this host (cnm025.lucia.cenaero.be): /proc/driver/nvidia/version does not exist
2025-05-14 23:34:59.742809: I tensorflow/compiler/jit/xla_gpu_device.cc:99] Not creating XLA devices, tf_xla_enable_xla_devices not set
2025-05-14 23:34:59,849 - modnet - INFO - Targets:
2025-05-14 23:34:59,849 - modnet - INFO - 1)target: classification
2025-05-14 23:35:00,304 - modnet - INFO - Multiprocessing on 32 cores. Total of 128 cores available.
2025-05-14 23:35:00,305 - modnet - INFO - Generation number 0
Traceback (most recent call last):
  File "run_benchmark.py", line 863, in <module>
    results = benchmark(data, settings, n_jobs=n_jobs, fast=fast)
  File "run_benchmark.py", line 140, in benchmark
    return matbench_benchmark(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 142, in matbench_benchmark
    fold_results.append(train_fold(fold, *args, **model_kwargs))
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 286, in train_fold
    model = ga.run(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 496, in run
    val_loss, models, individuals = self.function_fitness(
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 411, in function_fitness
    for train, val in splits:
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/model_selection/_split.py", line 336, in split
    for train, test in super().split(X, y, groups):
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/model_selection/_split.py", line 80, in split
    for test_index in self._iter_test_masks(X, y, groups):
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/model_selection/_split.py", line 697, in _iter_test_masks
    test_folds = self._make_test_folds(X, y)
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/sklearn/model_selection/_split.py", line 649, in _make_test_folds
    raise ValueError(
ValueError: Supported target types are: ('binary', 'multiclass'). Got 'continuous' instead.
Job finished on Wed May 14 23:35:01 CEST 2025

Resources Used

Total Memory used                        - MEM              : 763MiB
Total CPU Time                           - CPU_Time         : 03:52:32
Execution Time                           - Wall_Time        : 00:07:16
total programme cpu time                 - Total_CPU        : 07:26.346
Total_CPU / CPU_Time  (%)                - ETA              : 3%
Number of alloc CPU                      - NCPUS            : 32
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 48
Mobilized Resources x Execution Time     - R_Wall_Time      : 05:48:48
CPU_Time / R_Wall_Time (%)               - ALPHA            : 66%
Energy (Joules)                                             : 86478

