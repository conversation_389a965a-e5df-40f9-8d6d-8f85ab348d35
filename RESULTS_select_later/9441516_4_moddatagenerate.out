Job started on Thu Jul 10 12:52:16 CEST 2025
Running on node(s): cns266
Detected 'coGN' in feature_sets. Using specific data file: ../data/matbench_perovskites/matbench_perovskites_featurizedMM2020*_mattervial_coGNadj.csv
Using data file: ../data/matbench_perovskites/matbench_perovskites_featurizedMM2020Struct_mattervial_coGNadj.csv
Loaded data with shape: (18928, 9345)

Selecting features for feature_sets: ['megnet_mm', 'megnet_ofm', 'mvl_all', 'roost_oqmd', 'sisso', 'orb_v3', 'cogn_fold4']
 - Matching columns for 'megnet_mm' using pattern: ^MEGNet_MatMinerEncoded_v1_
   Found 758 columns for 'megnet_mm'.
 - Matching columns for 'megnet_ofm' using pattern: ^MEGNet_OFMEncoded_v1_
   Found 188 columns for 'megnet_ofm'.
 - Matching columns for 'mvl_all' using pattern: ^MVL(16|32)_
   Found 288 columns for 'mvl_all'.
 - Matching columns for 'roost_oqmd' using pattern: ^ROOST_oqmd_eform_
   Found 128 columns for 'roost_oqmd'.
 - Matching columns for 'sisso' using pattern: ^SISSO_
   Found 272 columns for 'sisso'.
 - Matching columns for 'orb_v3' using pattern: ^ORB_v3_
   Found 1792 columns for 'orb_v3'.
 - Matching columns for 'cogn_fold4' using pattern: ^coGN_.*_fold4
   Found 645 columns for 'cogn_fold4'.

Total features selected: 4071
Features DataFrame shape: (18928, 4071)
Determined task type: Regression
Creating MODData object...
2025-07-10 12:52:58,783 - modnet - INFO - Loaded DeBreuck2020Featurizer featurizer.
MODData object created with 4071 features.
Starting recursive feature elimination from 4071 features...
Target: < 800 features. Strategy: Remove ~10% per step.
  Dropped 407 features; remaining features: 3664
  Dropped 366 features; remaining features: 3298
  Dropped 329 features; remaining features: 2969
  Dropped 296 features; remaining features: 2673
  Dropped 267 features; remaining features: 2406
  Dropped 240 features; remaining features: 2166
  Dropped 216 features; remaining features: 1950
  Dropped 195 features; remaining features: 1755
  Dropped 175 features; remaining features: 1580
  Dropped 158 features; remaining features: 1422
  Dropped 142 features; remaining features: 1280
  Dropped 128 features; remaining features: 1152
  Dropped 115 features; remaining features: 1037
  Dropped 103 features; remaining features: 934
  Dropped 93 features; remaining features: 841
  Adjusting drop count to 41 to approach target threshold (800).
  Dropped 41 features; remaining features: 800
Finished recursive feature elimination. Final feature count: 800
2025-07-10 13:11:03,933 - modnet - INFO - Data successfully saved as ./matbench_perovskites/precomputed/matbench_perovskites_megnet_mm_megnet_ofm_mvl_all_roost_oqmd_sisso_orb_v3_cogn_fold4_featurizedMM2020Struct.pkl.gz!
MODData object saved to ./matbench_perovskites/precomputed/matbench_perovskites_megnet_mm_megnet_ofm_mvl_all_roost_oqmd_sisso_orb_v3_cogn_fold4_featurizedMM2020Struct.pkl.gz
Job finished on Thu Jul 10 13:11:04 CEST 2025

Resources Used

Total Memory used                        - MEM              : 6.9GiB
Total CPU Time                           - CPU_Time         : 10:02:08
Execution Time                           - Wall_Time        : 00:18:49
total programme cpu time                 - Total_CPU        : 18:43.734
Total_CPU / CPU_Time  (%)                - ETA              : 3%
Number of alloc CPU                      - NCPUS            : 32
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 48
Mobilized Resources x Execution Time     - R_Wall_Time      : 15:03:12
CPU_Time / R_Wall_Time (%)               - ALPHA            : 66%
Energy (Joules)                                             : unknown

