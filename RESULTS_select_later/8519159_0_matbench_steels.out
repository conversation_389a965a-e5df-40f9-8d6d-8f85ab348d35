Job started on Fri May 16 04:33:03 CEST 2025
Running on node(s): cnm021
2025-05-16 04:33:23.205283: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Running on 64 jobs
Preparing nested CV run for task 'matbench_steels_matminer_roost_lmp'
Found 1 matching files for matbench_steels_matminer_roost_lmp
2025-05-16 04:33:42,164 - modnet - INFO - Loaded <modnet.preprocessing.MODData object at 0x7f8066306f10> object, created with modnet version 0.1.13
Traceback (most recent call last):
  File "run_benchmark.py", line 866, in <module>
    setup_threading()
  File "run_benchmark.py", line 44, in setup_threading
    os.environ['OPENBLAS_NUM_THREADS'] = '1'
UnboundLocalError: local variable 'os' referenced before assignment
Job finished on Fri May 16 04:33:42 CEST 2025

Resources Used

Total Memory used                        - MEM              : 0B
Total CPU Time                           - CPU_Time         : 00:42:40
Execution Time                           - Wall_Time        : 00:00:40
total programme cpu time                 - Total_CPU        : 00:04.962
Total_CPU / CPU_Time  (%)                - ETA              : 0%
Number of alloc CPU                      - NCPUS            : 64
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 64
Mobilized Resources x Execution Time     - R_Wall_Time      : 00:42:40
CPU_Time / R_Wall_Time (%)               - ALPHA            : 100%
Energy (Joules)                                             : 1950

