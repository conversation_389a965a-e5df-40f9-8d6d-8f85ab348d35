Job started on Wed May 21 01:37:34 CEST 2025
Running on node(s): cnm022
2025-05-21 01:37:52.383031: I tensorflow/stream_executor/platform/default/dso_loader.cc:49] Successfully opened dynamic library libcudart.so.11.0
Traceback (most recent call last):
  File "run_benchmark.py", line 15, in <module>
    from modnet.matbench.benchmark import matbench_kfold_splits
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/matbench/benchmark.py", line 11, in <module>
    from modnet.hyper_opt import FitGenetic
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/__init__.py", line 1, in <module>
    from .fit_genetic import FitGenetic
  File "/gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/envs/modnet2020/lib/python3.8/site-packages/modnet/hyper_opt/fit_genetic.py", line 830
    for ind in indiv    iduals:
                        ^
SyntaxError: invalid syntax
Job finished on Wed May 21 01:38:08 CEST 2025

Resources Used

Total Memory used                        - MEM              : 0B
Total CPU Time                           - CPU_Time         : 00:36:16
Execution Time                           - Wall_Time        : 00:00:34
total programme cpu time                 - Total_CPU        : 00:05.275
Total_CPU / CPU_Time  (%)                - ETA              : 0%
Number of alloc CPU                      - NCPUS            : 64
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 64
Mobilized Resources x Execution Time     - R_Wall_Time      : 00:36:16
CPU_Time / R_Wall_Time (%)               - ALPHA            : 100%
Energy (Joules)                                             : 18957

