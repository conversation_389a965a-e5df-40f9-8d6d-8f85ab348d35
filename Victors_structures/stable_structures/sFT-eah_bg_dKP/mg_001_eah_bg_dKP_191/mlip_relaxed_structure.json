{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[3.6802043298723683, -0.009283604696990756, 0.0016837808989574063], [-1.8307468797408675, 3.1948750212037185, 0.002432779826411057], [-0.004940583607785587, -0.012321488937194987, 6.046975926784723]], "pbc": [true, true, true], "a": 3.680216424348657, "b": 3.682236610704161, "c": 6.046990498385951, "alpha": 90.04016670396135, "beta": 90.0203037873199, "gamma": 119.95833817343339, "volume": 70.99649154191279}, "properties": {"material_id": "mg_001_eah_bg_dKP_191"}, "sites": [{"species": [{"element": "Be", "occu": 1}], "abc": [0.6092262070388287, 0.7062547091057743, 0.695989778228837], "properties": {}, "label": "Be", "xyz": [0.9456647243288232, 2.2421640830994938, 4.211377399897192]}, {"species": [{"element": "Be", "occu": 1}], "abc": [0.27510795491693585, 0.038468310515556325, 0.19623285968175302], "properties": {}, "label": "Be", "xyz": [0.9410582425725295, 0.11792956986193, 1.1871721849891037]}, {"species": [{"element": "Se", "occu": 1}], "abc": [0.27603682700125287, 0.03879250306446797, 0.5689393604427639], "properties": {}, "label": "Se", "xyz": [0.9420417795136005, 0.11436440223131586, 3.440921775553275]}, {"species": [{"element": "Se", "occu": 1}], "abc": [0.6075797231647286, 0.7048602371952769, 0.06887904163145563], "properties": {}, "label": "Se", "xyz": [0.945256545370824, 2.245451142933683, 0.4192477075033615]}]}