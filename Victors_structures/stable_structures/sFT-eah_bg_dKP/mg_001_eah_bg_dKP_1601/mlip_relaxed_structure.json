{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[4.075997290073842, 0.0213257960581712, -0.04023003514549857], [-0.018094355560926093, 4.074797865660674, -0.042059501247458865], [-2.0029991732326455, -2.020831707055782, 3.370150672428486]], "pbc": [true, true, true], "a": 4.076251605825458, "b": 4.075055098196972, "c": 4.410632837936392, "alpha": 117.64641279403038, "beta": 117.64807833602435, "gamma": 89.9488202337263, "volume": 55.301213490562425}, "properties": {"material_id": "mg_001_eah_bg_dKP_1601"}, "sites": [{"species": [{"element": "Be", "occu": 1}], "abc": [0.7120960733126401, 0.13805309845041902, 0.5001854562511281], "properties": {}, "label": "Be", "xyz": [1.8981326279108925, -0.4330661428535422, 1.6512462572008584]}, {"species": [{"element": "B", "occu": 1}], "abc": [0.9610180947860659, 0.8883247171541386, 1.0002298446199394], "properties": {}, "label": "B", "xyz": [1.8975739349578298, 1.618941953019474, 3.294900997150932]}, {"species": [{"element": "F", "occu": 1}], "abc": [0.658359073370202, 0.5400482888321885, 0.7557713983145061], "properties": {}, "label": "F", "xyz": [1.159888487217434, 0.6873408410190014, 2.4978635158939833]}, {"species": [{"element": "F", "occu": 1}], "abc": [1.0197909663086329, -0.01057283019926897, 0.754708902334534], "properties": {}, "label": "F", "xyz": [2.6451752162572943, -1.5464739711954028, 2.5029011762397917]}, {"species": [{"element": "F", "occu": 1}], "abc": [0.3086664524518737, 0.8306582164791836, 0.24610673638808317], "properties": {}, "label": "F", "xyz": [0.7501418090996496, 2.89400658920481, 0.7820620506048852]}, {"species": [{"element": "F", "occu": 1}], "abc": [0.8574373466479724, 0.19199826544008974, 0.24434508587301573], "properties": {}, "label": "F", "xyz": [3.002015211476177, 0.30685935921764534, 0.7809096695840417]}]}