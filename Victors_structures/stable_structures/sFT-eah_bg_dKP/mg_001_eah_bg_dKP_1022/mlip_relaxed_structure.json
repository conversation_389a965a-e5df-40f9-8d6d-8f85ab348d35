{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[2.476176091559472, 0.02220270366353446, -0.05738222940095322], [-1.2480472311799757, -3.691864065628684, -0.3285467956166576], [-0.5255038382678301, -0.3825884859424038, -5.2372346856708445]], "pbc": [true, true, true], "a": 2.476940394258961, "b": 3.910936609975459, "c": 5.277419387011394, "alpha": 79.42173714654949, "beta": 94.42791776354048, "gamma": 108.99794540577349, "volume": 47.508643095804096}, "properties": {"material_id": "mg_001_eah_bg_dKP_1022"}, "sites": [{"species": [{"element": "B", "occu": 1}], "abc": [0.9004257394922448, 1.1052564081606064, 0.5914523789197633], "properties": {}, "label": "B", "xyz": [0.5393899931317098, -4.286747400886953, -3.5123718013791305]}, {"species": [{"element": "H", "occu": 1}], "abc": [1.005700606242484, 0.13259965729085565, 0.06100218242272288], "properties": {}, "label": "H", "xyz": [2.2927442803013847, -0.4905493699444416, -0.4207572810847661]}, {"species": [{"element": "S", "occu": 1}], "abc": [0.8264376317867336, 0.8460766985025027, -0.07242146405117623], "properties": {}, "label": "S", "xyz": [1.0285191813952652, -3.077543391851118, 0.05388958160419927]}, {"species": [{"element": "O", "occu": 1}], "abc": [0.4260271108858496, 0.19399057746075324, 0.47387489371036723], "properties": {}, "label": "O", "xyz": [0.5637856677532413, -0.8880269664130493, -2.5699753980209525]}]}