{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[5.192806577740972, -0.17032777791166856, 0.11894022111576047], [-2.4482552073009107, 3.976441564409985, 0.03466105959400808], [-0.12043050047955893, -1.42895843527745, 2.924672281108637]], "pbc": [true, true, true], "a": 5.196960504175234, "b": 4.669822530278376, "c": 3.257320012998651, "alpha": 110.3348551928188, "beta": 90.11548344080558, "gamma": 123.47621391270009, "volume": 59.90260859161848}, "properties": {"material_id": "mg_001_eah_bg_dKP_522"}, "sites": [{"species": [{"element": "Nb", "occu": 1}], "abc": [0.6001040334950977, 0.14612761597806, 0.25910480947428727], "properties": {}, "label": "Nb", "xyz": [2.7272623538318297, 0.1086035362230096, 0.8342380986134951]}, {"species": [{"element": "H", "occu": 1}], "abc": [0.8047788406312979, 0.14288777871822395, 0.7573680727382923], "properties": {}, "label": "H", "xyz": [3.738024892903294, -0.6511387854429038, 2.315726624001733]}, {"species": [{"element": "S", "occu": 1}], "abc": [0.5352775799900797, 0.531532187348536, -0.04801518728717243], "properties": {}, "label": "S", "xyz": [1.484048985801273, 2.0910457487242597, -0.05833918478688271]}, {"species": [{"element": "S", "occu": 1}], "abc": [0.15012279135001677, 0.7624173788998538, 0.5682702139271378], "properties": {}, "label": "S", "xyz": [-1.1554707659079473, 2.1941035577132446, 1.7062859750556167]}]}