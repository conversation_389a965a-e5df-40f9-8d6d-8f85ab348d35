{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[3.09430329010187, -0.018480640584549523, -0.027920566147686992], [0.028080552218068433, 4.676686758658556, -0.06619921932217143], [0.03292772861989472, 0.05167752460830678, 3.5376618450017134]], "pbc": [true, true, true], "a": 3.094484439002026, "b": 4.677239559041637, "c": 3.5381924949672587, "alpha": 89.97086987870517, "beta": 89.98869758690603, "gamma": 89.9908559738258, "volume": 51.21053396399488}, "properties": {"material_id": "mg_001_eah_bg_dKP_1148"}, "sites": [{"species": [{"element": "Be", "occu": 1}], "abc": [0.054429201823450075, 0.7617082929494632, 0.7955808732609323], "properties": {}, "label": "Be", "xyz": [0.21600631886493826, 3.6023788512368036, 2.7625519114739885]}, {"species": [{"element": "H", "occu": 1}], "abc": [0.5544802346782287, 0.06936739254399286, 0.5715576678119859], "properties": {}, "label": "H", "xyz": [1.736497984926196, 0.343699101708257, 2.001904284333881]}, {"species": [{"element": "H", "occu": 1}], "abc": [0.5542888653855916, 0.06908929637605504, 0.01892564498104153], "properties": {}, "label": "H", "xyz": [1.7177011039259584, 0.31384341470996013, 0.04690281572694105]}, {"species": [{"element": "N", "occu": 1}], "abc": [0.5543916281856955, 0.9289421049278582, 0.7953214383576691], "properties": {}, "label": "N", "xyz": [1.767729174872745, 4.375225972455181, 2.7366041367216067]}, {"species": [{"element": "Cl", "occu": 1}], "abc": [0.05451621002442769, 0.501148050237379, 0.29491842738228896], "properties": {}, "label": "Cl", "xyz": [0.1924731959779952, 2.357945610477701, 1.0086239347995811]}]}