{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[-3.548898876791162, 0.0009540794559775553, -0.0012547668055747508], [-0.001231899588715014, -3.558571824448895, -0.0003453186655091023], [1.736716088892245, 1.7670396639679387, 6.229097843260783]], "pbc": [true, true, true], "a": 3.5488992268585076, "b": 3.5585720544318216, "c": 6.703750583693373, "alpha": 105.29385164006025, "beta": 105.02995311493298, "gamma": 89.9955667836869, "volume": 78.65743783881742}, "properties": {"material_id": "mg_001_eah_bg_dKP_1166"}, "sites": [{"species": [{"element": "Al", "occu": 1}], "abc": [0.11356244504762136, 0.8461637469252777, 0.5323693924429845], "properties": {}, "label": "Al", "xyz": [0.5205104666425365, -2.070308288753341, 3.315746343762352]}, {"species": [{"element": "Al", "occu": 1}], "abc": [0.49706565781433204, 0.22819824273334643, 0.2957421988940391], "properties": {}, "label": "Al", "xyz": [-1.2506966370464634, -0.28899740109238176, 1.8415045906917917]}, {"species": [{"element": "N", "occu": 1}], "abc": [0.2545579301817661, 0.9872064836006553, 0.8158103282739161], "properties": {}, "label": "N", "xyz": [0.512214430836297, -2.0712331006283056, 5.081102044694536]}, {"species": [{"element": "N", "occu": 1}], "abc": [0.3551708316954601, 0.08622490498040308, 0.012131212348695426], "properties": {}, "label": "N", "xyz": [-1.2395031144342359, -0.28506232284300004, 0.07509107703835594]}, {"species": [{"element": "O", "occu": 1}], "abc": [0.05569963205571404, 0.2878572949274197, 0.41516241385331604], "properties": {}, "label": "O", "xyz": [0.5229922707189604, -0.29069926504865984, 2.585918004190304]}, {"species": [{"element": "O", "occu": 1}], "abc": [0.5553854611449093, 0.7869799068746033, 0.4137616797300942], "properties": {}, "label": "O", "xyz": [-1.2533897533125808, -2.068861341639676, 2.5763933487385207]}]}