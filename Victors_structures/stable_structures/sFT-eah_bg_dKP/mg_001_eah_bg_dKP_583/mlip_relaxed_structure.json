{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[5.0581590336788365, 0.12299415225204532, 0.2064615634515647], [-2.2334635129610256, 6.158772282410409, -0.4992677138697243], [-0.4790477817745874, -1.8363992025812967, 3.4252734550257404]], "pbc": [true, true, true], "a": 5.063864803552508, "b": 6.570243796118042, "c": 3.9159094793123534, "alpha": 117.6760528013347, "beta": 95.61958429602987, "gamma": 108.65590122501251, "volume": 104.49298234066057}, "properties": {"material_id": "mg_001_eah_bg_dKP_583"}, "sites": [{"species": [{"element": "Ga", "occu": 1}], "abc": [0.7450025089541913, 0.4422446431621861, 0.8280230372678816], "properties": {}, "label": "Ga", "xyz": [2.383941297213394, 1.2947341570085398, 2.7692212405147685]}, {"species": [{"element": "P", "occu": 1}], "abc": [0.11487763913170657, 1.065961081136568, 0.16121787362040646], "properties": {}, "label": "P", "xyz": [-1.8769468775535447, 6.283080463911483, 0.04373316791527555]}, {"species": [{"element": "S", "occu": 1}], "abc": [0.7431671682055906, 0.8277036358345641, 0.21526927143857613], "properties": {}, "label": "S", "xyz": [1.807287588244067, 4.7937231078531735, 0.47754587457010217]}, {"species": [{"element": "S", "occu": 1}], "abc": [0.9897979051088559, 0.41180461196523155, 0.3349947872208722], "properties": {}, "label": "S", "xyz": [3.9263261302251204, 2.042766024058655, 1.1462032280526175]}, {"species": [{"element": "O", "occu": 1}], "abc": [0.35788592408809805, 0.19695324789288612, 0.5757366034631449], "properties": {}, "label": "O", "xyz": [1.0945506842493276, 0.19972584039298527, 1.9476125945388294]}]}