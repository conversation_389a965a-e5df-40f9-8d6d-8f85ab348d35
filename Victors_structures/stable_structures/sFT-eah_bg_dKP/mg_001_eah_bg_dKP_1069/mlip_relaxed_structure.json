{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[2.9676643455751663, 0.005092201035301841, -0.008756709021499972], [1.4807892900612822, 2.5736102439412463, -0.009715195585961477], [0.02056465105823423, 0.01589596947038192, 6.939778878248828]], "pbc": [true, true, true], "a": 2.967681633609365, "b": 2.969225655672669, "c": 6.939827553023714, "alpha": 89.98904286409778, "beta": 89.99905327841827, "gamma": 59.986351008663405, "volume": 52.95171937562214}, "properties": {"material_id": "mg_001_eah_bg_dKP_1069"}, "sites": [{"species": [{"element": "Ga", "occu": 1}], "abc": [0.03010228192269256, 0.5223864778042134, 0.39469440331937117], "properties": {}, "label": "Ga", "xyz": [0.8709945230666424, 1.3508465276297772, 2.7337531997917863]}, {"species": [{"element": "P", "occu": 1}], "abc": [0.6954269001871026, 0.18969081024903034, 0.8010424080377002], "properties": {}, "label": "P", "xyz": [2.361158894483079, 0.5044648116821777, 5.5511246495485205]}, {"species": [{"element": "N", "occu": 1}], "abc": [0.029086376719948497, 0.5225412829368274, 0.886657685880048], "properties": {}, "label": "N", "xyz": [0.8783259444397197, 1.3590599958314955, 6.147876989004445]}, {"species": [{"element": "N", "occu": 1}], "abc": [0.6960251766834868, 0.18935887037596577, 0.5781773151630841], "properties": {}, "label": "N", "xyz": [2.3578597024331875, 0.5000709176563316, 4.004488171245892]}, {"species": [{"element": "O", "occu": 1}], "abc": [0.36319959689465725, 0.8561794654739475, 0.2717159088166929], "properties": {}, "label": "O", "xyz": [2.3512636197275483, 2.2096409161503874, 1.8741499407397737]}]}