{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[3.4906672201531537, -0.4874140775286649, 0.009809414610153926], [-0.6698378837578464, 4.313562420396982, -0.2802344239234775], [-0.7110697659640222, -1.8080215397505386, 4.050995791440463]], "pbc": [true, true, true], "a": 3.524546261498047, "b": 4.374246778307093, "c": 4.492786329499588, "alpha": 115.49127027498591, "beta": 95.65776019852836, "gamma": 106.75089844894202, "volume": 57.85032174737656}, "properties": {"material_id": "mg_001_eah_bg_dKP_1364"}, "sites": [{"species": [{"element": "H", "occu": 1}], "abc": [0.6816507792188747, 0.5967552769919271, -0.034422543296220354], "properties": {}, "label": "H", "xyz": [2.0041635685551107, 2.304131650988867, -0.2999903541821013]}, {"species": [{"element": "H", "occu": 1}], "abc": [0.485216267600439, 0.3881764538924061, 0.5160114397188478], "properties": {}, "label": "H", "xyz": [1.0667930919223594, 0.5049623267495118, 1.9863394532434737]}, {"species": [{"element": "C", "occu": 1}], "abc": [0.22406617172786986, 0.08538038868807808, 0.742806441439911], "properties": {}, "label": "C", "xyz": [0.19676221945119238, -1.083929416303144, 2.987377202068169]}, {"species": [{"element": "N", "occu": 1}], "abc": [0.4801231772910704, 0.3815791830657389, 0.7421985907790464], "properties": {}, "label": "N", "xyz": [0.8925990658908345, 0.07003579001768184, 2.9044214724213333]}, {"species": [{"element": "O", "occu": 1}], "abc": [-0.030940792536052576, 0.8188068871823432, 0.4683757539185547], "properties": {}, "label": "O", "xyz": [-0.9895197205098176, 2.700222144182308, 1.6676268205403462]}, {"species": [{"element": "O", "occu": 1}], "abc": [0.2351591191510719, 0.07374719623481932, 1.0224124678722672], "properties": {}, "label": "O", "xyz": [0.04445696863309906, -1.6450504952678793, 4.123428874713087]}]}