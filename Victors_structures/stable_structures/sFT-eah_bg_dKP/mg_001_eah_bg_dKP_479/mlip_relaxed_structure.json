{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[3.278202325330522, 0.5285133790406099, -0.7127185241761713], [-0.734656870887564, 3.229208818409917, -0.734123642426057], [-1.687667453678686, -2.4163693167083733, 6.3760944361246725]], "pbc": [true, true, true], "a": 3.3961602689406787, "b": 3.3921155394838163, "c": 7.024360637617917, "alpha": 118.1570895409033, "beta": 118.420582199048, "gamma": 90.88756142839595, "volume": 59.66316096235339}, "properties": {"material_id": "mg_001_eah_bg_dKP_479"}, "sites": [{"species": [{"element": "Be", "occu": 1}], "abc": [0.8484028217602366, 0.9198997774478455, 0.46870552491902356], "properties": {}, "label": "Be", "xyz": [1.314406351316163, 2.286375066517973, 1.7085181072982933]}, {"species": [{"element": "Be", "occu": 1}], "abc": [0.8657104514595081, -0.06124320966303821, 0.9902445258478337], "properties": {}, "label": "Be", "xyz": [1.2117633023547825, -2.133024045037111, 5.741844824484872]}, {"species": [{"element": "P", "occu": 1}], "abc": [0.1064377723262665, 0.6818181053246339, 0.7305147190544978], "properties": {}, "label": "P", "xyz": [-1.3848437188108207, 0.49279347244843347, 4.081431872708633]}, {"species": [{"element": "S", "occu": 1}], "abc": [0.10851035753764662, 0.6795439395122285, 0.23032598883389918], "properties": {}, "label": "S", "xyz": [-0.5322263929216344, 1.6951858054342803, 0.8923736419534085]}]}