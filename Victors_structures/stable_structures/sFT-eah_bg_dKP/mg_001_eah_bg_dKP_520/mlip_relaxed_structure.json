{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[7.3567079996053435, -0.6114646343345793, -0.46464374853453455], [-3.4253952763589823, 3.8467511896908118, 0.5210292554377293], [-2.3137467774531597, 0.011029837540608692, 4.040177761957772]], "pbc": [true, true, true], "a": 7.3966840816376225, "b": 5.177093682725096, "c": 4.655811653805292, "alpha": 65.2976776852464, "beta": 123.29708685201278, "gamma": 136.53670461211226, "volume": 102.44942037467092}, "properties": {"material_id": "mg_001_eah_bg_dKP_520"}, "sites": [{"species": [{"element": "K", "occu": 1}], "abc": [0.11466138134637197, 0.10111840362986285, -0.02798695968079755], "properties": {}, "label": "K", "xyz": [0.5619145370221171, 0.3185572682270188, -0.11366333961377385]}, {"species": [{"element": "Te", "occu": 1}], "abc": [0.6189110304490153, 0.11058089768789117, -0.016856378482742275], "properties": {}, "label": "Te", "xyz": [4.213365835546595, 0.04674906970264082, -0.2980600239023631]}, {"species": [{"element": "S", "occu": 1}], "abc": [0.757487771209135, 0.39110110073996535, 0.47879722809145686], "properties": {}, "label": "S", "xyz": [3.125124939453876, 1.046572697166281, 1.7862390712578653]}]}