{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[-3.3050173098501077, 0.019842636022901494, -0.051907249538419936], [-0.024140788999948374, -4.68344489520652, -0.23324815778053834], [1.5027159225847402, 2.2878275437418782, 4.245660832851248]], "pbc": [true, true, true], "a": 3.3054844593747257, "b": 4.68931163042483, "c": 5.051529067668606, "alpha": 119.71381924275805, "beta": 107.93361718483555, "gamma": 90.00384017236624, "volume": 63.58698372756082}, "properties": {"material_id": "mg_001_eah_bg_dKP_1493"}, "sites": [{"species": [{"element": "Li", "occu": 1}], "abc": [0.1572559617259921, 0.5064476422677472, 0.13217498771109285], "properties": {}, "label": "Li", "xyz": [-0.33333826265213534, -2.0664056745781156, 0.43487946438961755]}, {"species": [{"element": "Al", "occu": 1}], "abc": [0.5947907720951022, 0.9037016746877997, -0.010548513786431308], "properties": {}, "label": "Al", "xyz": [-2.0034612885874363, -4.244767958691834, -0.2864461156622494]}, {"species": [{"element": "B", "occu": 1}], "abc": [0.37140716272972846, 0.19001166003704228, 0.5396968577633661], "properties": {}, "label": "B", "xyz": [-0.42108307168659054, 0.3521938943781137, 2.227771216682184]}, {"species": [{"element": "C", "occu": 1}], "abc": [0.194290747768597, 0.0946614584530051, 0.20115101497024487], "properties": {}, "label": "C", "xyz": [-0.3421466537740072, 0.020712348726619936, 0.8218542766230306]}, {"species": [{"element": "O", "occu": 1}], "abc": [0.5254093799746784, 0.45639935210863797, 0.8489119442775034], "properties": {}, "label": "O", "xyz": [-0.4718314404947184, -0.18493158038936497, 3.4704653284708544]}, {"species": [{"element": "F", "occu": 1}], "abc": [0.4171879810932459, 0.8672349601355862, 0.5888226190251558], "properties": {}, "label": "F", "xyz": [-0.5149161099734589, -2.7062444315456973, 2.2760050936384553]}]}