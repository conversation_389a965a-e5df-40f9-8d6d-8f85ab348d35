{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[4.178791236579808, 0.16137686488855793, -0.40478189587822977], [-2.2410968913070297, -5.338474789683525, 0.5558549009574358], [-1.176915962595513, 0.5777139447366639, -4.320067157468434]], "pbc": [true, true, true], "a": 4.201450591720463, "b": 5.816425279089469, "c": 4.514627872814404, "alpha": 96.22612850224752, "beta": 99.33328100143547, "gamma": 115.32089713366469, "volume": 96.43107159929733}, "properties": {"material_id": "mg_001_eah_bg_dKP_218"}, "sites": [{"species": [{"element": "Be", "occu": 1}], "abc": [0.5653353009633443, 0.6055343929987036, 0.21336300953324963], "properties": {}, "label": "Be", "xyz": [0.7542466239188476, -3.0181352669312616, -0.8139907649874327]}, {"species": [{"element": "Ge", "occu": 1}], "abc": [0.2239653102840073, 0.8698770814929083, 0.470567556345672], "properties": {}, "label": "Ge", "xyz": [-1.5673930157830864, -4.3358206108143955, -1.6400151094684468]}, {"species": [{"element": "P", "occu": 1}], "abc": [0.7241046846438017, 0.3296363135682709, 0.5414626421297851], "properties": {}, "label": "P", "xyz": [1.649879366284457, -1.3300908869771582, -2.449029483894929]}, {"species": [{"element": "<PERSON>r", "occu": 1}], "abc": [0.9311363397462138, 0.40598457432247376, 0.0832690054110122], "properties": {}, "label": "<PERSON>r", "xyz": [2.8831729875021006, -1.968968886138578, -0.5109663130844977]}, {"species": [{"element": "<PERSON>r", "occu": 1}], "abc": [0.3908708448237137, 0.5470639555108615, 0.6791969853591673], "properties": {}, "label": "<PERSON>r", "xyz": [-0.3920134428735931, -2.4650280536601965, -2.788305850705102]}, {"species": [{"element": "Se", "occu": 1}], "abc": [0.9089054645340816, 0.9647996098772883, 0.9715899674293452], "properties": {}, "label": "Se", "xyz": [0.49243704187904286, -4.4425810073296415, -4.0289537943334155]}]}