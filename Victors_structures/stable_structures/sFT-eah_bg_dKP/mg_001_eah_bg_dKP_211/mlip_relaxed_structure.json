{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[4.400768877651735, -0.04844873285912833, 0.05942604565536311], [-0.4930725235696428, 3.588827069760241, 0.22655086039122635], [0.1411125074950292, -0.3596195331973544, 5.823349871085495]], "pbc": [true, true, true], "a": 4.401436748281861, "b": 3.6296178231994647, "c": 5.836149644200367, "alpha": 90.11064290654362, "beta": 87.80357004090588, "gamma": 98.38751095668829, "volume": 92.16997054621368}, "properties": {"material_id": "mg_001_eah_bg_dKP_211"}, "sites": [{"species": [{"element": "Be", "occu": 1}], "abc": [0.3501694213132298, 0.7618010396994672, 0.7535035725647252], "properties": {}, "label": "Be", "xyz": [1.271720308649057, 2.4460323252680385, 4.58131079716617]}, {"species": [{"element": "Be", "occu": 1}], "abc": [0.4366374455530028, 0.2843865756540897, 0.24986742903950748], "properties": {}, "label": "Be", "xyz": [1.8165766941330186, 0.9096025018355155, 1.5454411208581025]}, {"species": [{"element": "Se", "occu": 1}], "abc": [0.22083175977006897, 0.2396589518735174, 0.5841517347247079], "properties": {}, "label": "Se", "xyz": [0.9360914074414277, 0.6393231409000105, 3.469138029089151]}, {"species": [{"element": "Se", "occu": 1}], "abc": [0.5693433350667573, 0.8060778014068744, 0.0749425083388191], "properties": {}, "label": "Se", "xyz": [2.11866893919673, 2.838339081010591, 0.6528678887479351]}]}