{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[3.6310960832307715, -0.41923954381738143, -0.10961765198592803], [0.6324948139221962, 5.1038825351776165, -0.32414317456162717], [0.1358937777443839, 0.21924734371998442, 3.675191856476038]], "pbc": [true, true, true], "a": 3.65686157659556, "b": 5.153128702074542, "c": 3.684232850739173, "alpha": 89.95872588596762, "beta": 90.00569734747398, "gamma": 89.41490327255171, "volume": 69.42307098360517}, "properties": {"material_id": "mg_001_eah_bg_dKP_283"}, "sites": [{"species": [{"element": "Be", "occu": 1}], "abc": [0.36246627106304113, 0.5561419026034213, 0.795257766725494], "properties": {}, "label": "Be", "xyz": [1.7759773085626485, 2.860880902575648, 2.7027225646942146]}, {"species": [{"element": "Zn", "occu": 1}], "abc": [0.8656947037033349, 1.0555510592541644, 0.2947318441742153], "properties": {}, "label": "Zn", "xyz": [3.8511034424255457, 5.089094337594743, 0.6461509815380942]}, {"species": [{"element": "S", "occu": 1}], "abc": [0.8604049784505284, 0.7774738832736714, 0.7955158453771385], "properties": {}, "label": "S", "xyz": [3.7240669998583344, 3.781834319719504, 2.577344930469877]}, {"species": [{"element": "S", "occu": 1}], "abc": [0.3613085314934533, 0.3358431938881782, 0.29571996387983396], "properties": {}, "label": "S", "xyz": [1.5645515750151429, 1.6274652044891227, 0.9383605311619276]}]}