{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[-4.061108885460304, 0.010544972657294566, -0.032261683189219806], [-0.015570926738610321, -4.086578819089587, -0.03150143468175282], [2.0061165886416785, 2.006565172816381, 3.523241553461169]], "pbc": [true, true, true], "a": 4.061250717724182, "b": 4.086729895500575, "c": 4.523719554085822, "alpha": 116.82340043904135, "beta": 116.646581370558, "gamma": 89.926957589226, "volume": 57.95159568057306}, "properties": {"material_id": "mg_001_eah_bg_dKP_1220"}, "sites": [{"species": [{"element": "B", "occu": 1}], "abc": [0.9232052491271838, 0.8724389382963844, 0.5218035627584893], "properties": {}, "label": "B", "xyz": [-2.7160229398639895, -2.5085224559992034, 1.781172761560265]}, {"species": [{"element": "B", "occu": 1}], "abc": [0.6746542950021781, 0.12466775526823243, 0.02539944129438389], "properties": {}, "label": "B", "xyz": [-2.6908315040083575, -0.45138476269837363, 0.06379567072596118]}, {"species": [{"element": "O", "occu": 1}], "abc": [0.6511888780442854, 0.4983632451679919, 0.27322222947086755], "properties": {}, "label": "O", "xyz": [-2.1041932693911303, -1.481495702907067, 0.9259203057058093]}, {"species": [{"element": "O", "occu": 1}], "abc": [0.9471678733365962, 0.9980035548193998, 0.27305983854842714], "properties": {}, "label": "O", "xyz": [-3.3143018348629383, -3.5205199670487324, 0.9000599961043236]}, {"species": [{"element": "O", "occu": 1}], "abc": [0.798448435426017, 0.14613419129382244, 0.773083268324987], "properties": {}, "label": "O", "xyz": [-1.693966311489035, 0.9624726879416864, 2.6933963680997093]}, {"species": [{"element": "O", "occu": 1}], "abc": [0.2987640000260638, 0.8514761660076989, 0.7731933897326152], "properties": {}, "label": "O", "xyz": [0.324544677208601, -1.925011079066104, 2.6876857292237006]}]}