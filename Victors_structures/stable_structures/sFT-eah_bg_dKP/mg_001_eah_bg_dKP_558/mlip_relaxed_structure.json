{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": 0, "lattice": {"matrix": [[3.9693584812084035, 0.024414205739136764, -0.1582883332218026], [-1.436594884577041, 5.808578134432618, -0.1606761554818991], [-1.451582072376129, -2.1571839138304405, 5.186298915596114]], "pbc": [true, true, true], "a": 3.9725883252883243, "b": 5.9857498806032705, "c": 5.801571286543502, "alpha": 108.95154061724946, "beta": 106.73264141797662, "gamma": 103.46044581662588, "volume": 116.56363320632124}, "properties": {"material_id": "mg_001_eah_bg_dKP_558"}, "sites": [{"species": [{"element": "Zn", "occu": 1}], "abc": [0.8945885604838368, 0.6659681886937837, -0.09249256829096875], "properties": {}, "label": "Zn", "xyz": [2.7284767505392025, 4.08969240870808, -0.7283022470189902]}, {"species": [{"element": "P", "occu": 1}], "abc": [0.36234279994507973, 1.0366395861503153, 0.6360306541041114], "properties": {}, "label": "P", "xyz": [-0.9742133555259926, 4.658213249329511, 3.074727190484833]}, {"species": [{"element": "P", "occu": 1}], "abc": [-0.14612298448161076, 0.298563741566707, -0.03667703055530218], "properties": {}, "label": "P", "xyz": [-0.9556899315844104, 1.80978244471338, -0.2150605542984895]}, {"species": [{"element": "H", "occu": 1}], "abc": [1.0553945736285866, 0.10278590730557294, 0.7007163012855326], "properties": {}, "label": "H", "xyz": [3.0244304724443714, -0.8887673393324098, 3.450552301123096]}, {"species": [{"element": "S", "occu": 1}], "abc": [0.34142986419319543, 0.04088682446234095, 0.2829120181725502], "properties": {}, "label": "S", "xyz": [0.8858497106648718, -0.364463201122894, 1.4066523911776456]}, {"species": [{"element": "S", "occu": 1}], "abc": [0.2920939359346203, 0.6787672909018462, 0.6449868861797756], "properties": {}, "label": "S", "xyz": [-0.7519394769124447, 2.5584487502489925, 3.189798007335625]}]}